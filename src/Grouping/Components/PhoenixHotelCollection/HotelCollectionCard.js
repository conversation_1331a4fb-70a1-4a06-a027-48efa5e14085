import React, { useState } from 'react';
import { View, TouchableOpacity, Text, StyleSheet, ImageBackground } from 'react-native';
import PropTypes from 'prop-types';
import { getPackageImageV2, rupeeFormatterUtils } from '../../../utils/HolidayUtils';
import { getPackageCategoryPrice } from '../PhoenixPackageCard';
import { PHOENIX_CARD_WIDTH, IMAGE_HEIGHT, PHOENIX_CARD_HEIGHT } from './constants';
import genericCardDefaultImage from '@mmt/legacy-assets/src/no_dest_default.webp';
import { fonts } from '../../../../../../Mobile-mmt-react-native/packages/legacy-commons/Styles/globalStyles';
import getPlatformElevation from '../../../../../../Mobile-mmt-react-native/packages/legacy-commons/Styles/getPlatformElevation';
import ItinerarySection from './ItinerarySections';
import HotelInfo from './HotelDetails';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import PlusIcon from '@mmt/legacy-assets/src/plusIcon.webp';
import { holidayColors } from '../../../Styles/holidayColors';

const PlusIconContainer = () => {
  return (
    <View style={styles.plusIconContainer}>
      <HolidayImageHolder defaultImage={PlusIcon} style={styles.plusIcon} />
    </View>
  );
};
const UserRating = ({ rating }) => {
  return (
    <View style={styles.userRating}>
      <Text style={styles.ratingText}>{rating}</Text>
      <Text style={styles.outOfRatingText}>/5</Text>
    </View>
  );
};

const hotelCollectionCardDefaultPropItem = {};

const HotelCollectionCard = ({ item = hotelCollectionCardDefaultPropItem, onPackageClicked, ...rest }) => {
  const [groupImageLoadFailed, setGroupImageLoadFailed] = useState(false);
  const defaultHotelCategoryId = item.categoryDetails.defaultCategoryId || '';
  const hotelDetails = defaultHotelCategoryId
    ? item.hotelDetails.categories.filter(
        (category) => category.categoryId === defaultHotelCategoryId,
      )[0].hotels[0]
    : item.hotelDetails.categories[0].hotels[0];

  const _onGroupImageLoadError = () => {
    setGroupImageLoadFailed(true);
  };
  const onCardClick = () => {
    clickCard('CardClick');
  };

  const clickCard = (clickAction) => {
    // storing this data for tracking usage
    const {
      groupingData = {},
      fabCta = {},
      groupName = '',
      pt = '',
      aff = '',
      groupIndex = 0,
      index = 0,
      selectedDate = '',
    } = rest;
    const updatedGroupingData = { ...groupingData };
    updatedGroupingData.clickAction = clickAction;
    onPackageClicked(
      item,
      null,
      updatedGroupingData,
      fabCta,
      groupName,
      pt,
      aff,
      groupIndex,
      index,
      selectedDate,
    );
  };

  const { rating = 0 } = hotelDetails;
  const packageCategoryPrice = getPackageCategoryPrice(
    item.categoryDetails.defaultCategoryId,
    item,
  );
  let packageDiscountedPrice = packageCategoryPrice.discountedPrice;
  return (
    <View style={styles.cardWrapper}>
      <TouchableOpacity onPress={onCardClick}>
        <View style={styles.card}>
          <View>
            {rating ? (
              <View style={styles.userRatingWrap}>
                <UserRating rating={rating} />
              </View>
            ) : null}
            <ImageBackground
              source={
                groupImageLoadFailed
                  ? genericCardDefaultImage
                  : { uri: getPackageImageV2(item, `resize=${PHOENIX_CARD_WIDTH}:${IMAGE_HEIGHT}`) }
              }
              style={styles.imgHotel}
              onError={_onGroupImageLoadError}
            >
              <HotelInfo item={item} hotelDetails={hotelDetails} />
              <PlusIconContainer />
            </ImageBackground>
          </View>
          <View style={styles.cardContent}>
            <ItinerarySection packageItem={item} />
          </View>
          <View style={styles.priceWrap}>
            <Text style={styles.price}>
              {rupeeFormatterUtils(Math.abs(packageDiscountedPrice))}
            </Text>
            <Text style={styles.perPersonText}>/person</Text>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  cardWrapper: {
    marginRight: 8,
    borderRadius: 4,
  },
  card: {
    width: PHOENIX_CARD_WIDTH,
    height: PHOENIX_CARD_HEIGHT,
    backgroundColor: holidayColors.white,
    borderRadius: 8,
    ...getPlatformElevation(3),
  },
  imgHotel: {
    width: '100%',
    height: IMAGE_HEIGHT,
    resizeMode: 'cover',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  userRating: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 5,
    width: 42,
    height: 22,
    backgroundColor: '#0C58B4',
    borderBottomLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  rating: {
    fontSize: 8,
    fontFamily: fonts.bold,
    color: holidayColors.white,
  },
  userRatingWrap: {
    position: 'absolute',
    right: 0,
    top: 0,
  },
  ratingText: {
    fontSize: 11,
    fontFamily: fonts.bold,
    color: holidayColors.white,
  },
  outOfRatingText: {
    fontSize: 10,
    color: holidayColors.white,
    fontFamily: fonts.regular,
  },

  starWrap: {
    paddingRight: 2,
  },

  cardContent: {
    padding: 8,
    flexGrow: 1,
    justifyContent: 'center',
    width: '100%',
  },

  /* PRICE STYLES */
  priceWrap: {
    marginTop: 'auto',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderTopWidth: 1,
    borderTopColor: holidayColors.grayBorder,
  },
  price: {
    fontSize: 20,
    fontFamily: fonts.black,
    fontWeight: '900',
    color: holidayColors.gray,
  },
  perPersonText: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: holidayColors.gray,
    marginLeft: 3,
    marginTop: 2,
  },
  /* PRICE STYLES */

  plusIconContainer: {
    backgroundColor: holidayColors.gray,
    borderRadius: 100,
    width: 22,
    height: 22,
    borderColor: holidayColors.gray,
    borderWidth: 2,
    left: '45%',
    bottom: -10,
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },

  plusIcon: {
    width: 18,
    height: 18,
    tintColor: holidayColors.white,
    justifyContent: 'center',
  },
});
HotelCollectionCard.propTypes = {
  item: PropTypes.object,
  onPackageClicked: PropTypes.func,
};

export default HotelCollectionCard;
