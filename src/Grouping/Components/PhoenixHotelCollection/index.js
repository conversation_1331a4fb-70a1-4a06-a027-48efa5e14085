import React, { useState } from 'react';
import { View, FlatList, Text, StyleSheet, ImageBackground } from 'react-native';
import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import { getScreenWidth } from '../../../utils/HolidayUtils';
import { getPhoenixHotelCollectionCardHeight } from '../../Utils/HolidayGroupingUtils';
import { PHOENIX_HOTEL_CARD_TYPE } from '../PhoenixGroupingSectionView';
import { collectionCardColors } from '../../HolidayGroupingConstants';
import { colors, fonts } from '../../../../../../Mobile-mmt-react-native/packages/legacy-commons/Styles/globalStyles';
import genericCardDefaultImage from '@mmt/legacy-assets/src/no_dest_default.webp';
import { sectionBottomSpacing } from '../../../Styles/holidaySpacing';
import { holidayColors } from '../../../Styles/holidayColors';

const HotelCollectionContent = ({ group, groupPackagesList, slideScroll, renderCard }) => {
  const viewHeight = getPhoenixHotelCollectionCardHeight();
  const {
    name = '',
    description = '',
    bgImageUrl = '',
  } = group.data;
  const titleColor = { color: bgImageUrl ? holidayColors.white : holidayColors.black };

  return (
    <View
      style={{
        height: viewHeight,
        width: getScreenWidth(),
      }}
    >
      <View style={styles.collectionHead}>
        <View>
          <Text style={[styles.titleText, titleColor]} numberOfLines={1}>
            {name}
          </Text>
          <View style={styles.subTitleContainer}>
            <Text style={[styles.subTitleText, titleColor]} numberOfLines={2}>
              {description}
            </Text>
          </View>
        </View>
      </View>
      <FlatList
        data={groupPackagesList}
        renderItem={({ item, index }) => renderCard(item, index, PHOENIX_HOTEL_CARD_TYPE)}
        keyExtractor={(item) => item.id}
        onEndReached={slideScroll}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.contentContainerStyle}
      />
    </View>
  );
};
const PhoenixHotelCollection = (props) => {
  const { group } = props || {};
  const { bgImageUrl = '' } = group.data;
  const [groupImageLoadFailed, setGroupImageLoadFailed] = useState(false);

  const _onGroupImageLoadError = () => {
    setGroupImageLoadFailed(true);
  };
  return bgImageUrl ? (
    <ImageBackground
      source={groupImageLoadFailed ? genericCardDefaultImage : { uri: bgImageUrl }}
      style={styles.collectionWrapper}
      onError={_onGroupImageLoadError}
    >
      <HotelCollectionContent {...props} />
    </ImageBackground>
  ) : (
    <LinearGradient
      colors={[collectionCardColors[group.index % 2], holidayColors.white]}
      style={styles.collectionWrapper}
    >
      <HotelCollectionContent {...props} />
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  // Hotel Collection Section Styles
  collectionWrapper: {
    paddingVertical: 15,
    ...sectionBottomSpacing,
  },
  collectionHead: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingLeft: 20,
    paddingRight: 12,
  },
  titleText: {
    fontSize: 18,
    fontFamily: fonts.bold,
    fontWeight: '900',
  },
  subTitleText: {
    fontSize: 14,
    fontFamily: fonts.regular,

    fontWeight: '400',
  },
  durationTag: {
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 5,
    borderRadius: 10,
    marginLeft: 5,
  },
  durationText: {
    fontSize: 10,
    fontFamily: fonts.bold,
    color: colors.darkGrey1,
  },
  contentContainerStyle: {
    margin: 10,
    flexDirection: 'row',
    paddingLeft: 10,
    paddingRight: 20,
  },
  width70: { width: '70%' },
  width30: { width: '30%' },
});
PhoenixHotelCollection.propTypes = {
  groupPackagesList: PropTypes.array,
};
export default PhoenixHotelCollection;
