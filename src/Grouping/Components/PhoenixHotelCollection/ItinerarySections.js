import React from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import { INCLUSIONS_TYPE, VISA_TYPES } from '../../../PhoenixDetail/DetailConstants';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { getTitles } from '../../Utils/HolidayGroupingUtils';
import { getInclusionArray } from '../PhoenixPackageCard/Inclusions';

const getVisaType = ({ isInclusionVisa, packageItem }) => {
  if (!isInclusionVisa) {
    return '';
  }

  const visas = packageItem?.visaDetails?.visas?.[0] || {};
  return Object.keys(visas).find((key) => visas[key]);

};
const ItinerarySection = ({ packageItem }) => {
  const inclusions = getInclusionArray(packageItem, { includeVisa: true });
  const filteredInclusions =
    inclusions?.filter(item => item?.title.toLowerCase() !== INCLUSIONS_TYPE.HOTELS && item?.count > 0).slice(0, 3) || [];
  const renderInclusion = ({ item, index }) => {
    const { title: itemTitle = '', hotelCollectionIcon = '', count = 0 } = item || {};
    const isInclusionVisa = item.title.toLowerCase() === INCLUSIONS_TYPE.VISA;
    let selectedVisaType = getVisaType({ isInclusionVisa, packageItem });
    if (itemTitle) {
      return (
        <View style={styles.itinearyContainer} key={index}>
          <View style={styles.iconContainer}>
            <HolidayImageHolder imageUrl={hotelCollectionIcon} style={styles.iconFlight} />
          </View>
          <View>
            <Text style={styles.itinearyText}>
              {isInclusionVisa ? (
                <>
                  {itemTitle}
                  {'\n'}
                  {VISA_TYPES?.[selectedVisaType] || ''}
                </>
              ) : (
                <>
                  {count}
                  {'\n'}
                  {count > 1 ? itemTitle : getTitles(itemTitle)}
                </>
              )}
            </Text>
          </View>
        </View>
      );
    }
  };
  return (
    <View style={styles.inclusionContainer}>
      {filteredInclusions.map((item, index) => renderInclusion({ item, index }))}
    </View>
  );
};

const styles = StyleSheet.create({
  inclusionContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    overflow: 'scroll',
    marginTop: 5,
  },
  itinearyContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    marginTop: 5,
  },
  itinearyText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
    paddingVertical: 10,
    textAlign: 'center',
  },
  iconContainer: {
    paddingHorizontal: 20,
  },
  iconFlight: {
    width: 30,
    height: 30,
    resizeMode: 'cover',
  },
  iconVisa: {
    width: 6,
    height: 8,
    resizeMode: 'cover',
  },
  ml5: {
    marginLeft: 5,
  },
});

export default ItinerarySection;
