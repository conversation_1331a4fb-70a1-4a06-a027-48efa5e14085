import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const style = {
  viewAllCard: {
    width: 150,
    margin: 4,
    marginRight: 10,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  arrowWrapper: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderColor: '#ddf0ff',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: holidayColors.white,
  },
  iconArrowRight: {
    height: 20,
    width: 22,
  },
  viewAllText: {
    color: holidayColors.primaryBlue,
    fontFamily: fonts.regular,
    fontSize: 10,
    marginTop: 14,
  },
  moreText: {
    fontFamily: fonts.regular,
    fontSize: 10,
    color: holidayColors.lightGray,
    marginTop: 2,
  },
};

export default style;
