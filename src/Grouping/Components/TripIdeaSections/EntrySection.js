import React, { useEffect } from 'react';
import { StyleSheet, Text, View, Image, TouchableOpacity } from 'react-native';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import ArrowDropDown from '@mmt/legacy-assets/src/arrow_dropdown.webp';
import { isEmpty } from 'lodash';
import { openTIDeepLink } from './utils';
import {
  trackTIClickEvent,
  trackTILoadEvent,
} from 'mobile-holidays-react-native/src/utils/TripIdeasTrackingUtils';
import { fontStyles } from '../../../Styles/holidayFonts';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { getEntrySectionCards } from 'mobile-holidays-react-native/src/Common/Components/TripIdeas/utils';

const EntryPoint = ({
  entrySectionData,
  entryData,
  index,
  toggleTIDeepLinkOpenCheck = null,
  groupingData = {},
  fabCta = {},
  closeIntervention,
  widthPercentage = '45%',
}) => {
  const { header = '', subHeader = '', color = '', graphicUrl = '', deeplink = '' } =
    entryData || {};
  const headerColor = color?.split(',')?.[0] || holidayColors.primaryBlue;
  const subHeaderColor = color?.split(',')?.[1] || holidayColors.black;
  const openDeepLink = () => {
    trackTIClickEvent({ groupingData, fabCta, sectionData: entrySectionData, cardData: entryData });
    openTIDeepLink({ url: deeplink, toggleTIDeepLinkOpenCheck });
    closeIntervention();
  };
  return (
    <TouchableOpacity
      onPress={openDeepLink}
      style={[styles.entryPoint, { width: widthPercentage }]}
      key={index}
    >
      <HolidayImageHolder imageUrl={graphicUrl} style={styles.entryPointIcon} />
      <View style={styles.entryPointTextContainer}>
        <View style={styles.entryPointTitleContainer}>
          {!!header && (
            <Text style={[styles.entryPointTitle, { color: headerColor }]}>{header}</Text>
          )}
          <Image source={ArrowDropDown} style={styles.arrowIcon} />
        </View>
        {!!subHeader && (
          <Text style={[styles.entryPointTagline, { color: subHeaderColor }]} numberOfLines={2}>
            {subHeader}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
};
const TripIdeaEntrySection = ({
  entrySectionData = {},
  toggleTIDeepLinkOpenCheck = null,
  groupingData,
  fabCta,
  closeIntervention,
}) => {
  const { cards = [] } = entrySectionData || {};
  const entrySectionCards = getEntrySectionCards({ cards, showGallery: false });
  const ENTRY_SECTION_CARD_WIDTH_PERCENTAGE = entrySectionCards.length === 1 ? '90%' : '45%';

  useEffect(() => {
    if (!isEmpty(entrySectionData) && !isEmpty(cards)) {
      trackTILoadEvent({ groupingData, fabCta, sectionData: entrySectionData });
    }
  }, []);

  if (isEmpty(entrySectionData) || isEmpty(cards)) {
    return null;
  }
  return (
    <View style={styles.shadowContainerStyle}>
      <View style={styles.entryPointContainer}>
        {entrySectionCards.map((card, index) => (
          <EntryPoint
            entryData={card}
            index={index}
            toggleTIDeepLinkOpenCheck={toggleTIDeepLinkOpenCheck}
            groupingData={groupingData}
            fabCta={fabCta}
            entrySectionData={entrySectionData}
            closeIntervention={closeIntervention}
            widthPercentage={ENTRY_SECTION_CARD_WIDTH_PERCENTAGE}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  entryPointContainer: {
    width: '100%',
    flexDirection: 'row',
    ...paddingStyles.ph16,
    ...paddingStyles.pv4,
    alignItems: 'center',
    backgroundColor: holidayColors.white,
  },
  shadowContainerStyle: {
    shadowColor: holidayColors.gray,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    marginBottom: 5,
    paddingVertical: 5,
    backgroundColor: holidayColors.white,
  },
  entryPoint: {
    flexDirection: 'row',
    marginRight: 30,
    alignItems: 'center',
  },
  entryPointTextContainer: {
    paddingHorizontal: 10,
    width: '100%',
  },
  arrowIcon: {
    tintColor: holidayColors.primaryBlue,
    transform: [{ rotate: '270deg' }],
    width: 6,
    height: 6,
    marginLeft: 5,
  },
  entryPointTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  entryPointIcon: {
    height: 30,
    width: 30,
    padding: 4,
  },
  entryPointTitle: {
    ...fontStyles.labelSmallBold,
  },
  entryPointTagline: {
    ...fontStyles.labelSmallRegular,
  },
});

export default TripIdeaEntrySection;
