import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Image,
  FlatList,
  ImageBackground,
  TouchableOpacity,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import GalleryIcon from '@mmt/legacy-assets/src/holidays/gallery-icon.webp';
import ArrowIcon from '@mmt/legacy-assets/src/arrow_oneway.webp';
import { formatTime, sortByKey } from '../../../utils/HolidayUtils';
import { IMAGE_STORY_TYPE, VIEW_ALL_STORY_TYPE } from '../../../HolidayConstants';
import {
  trackTIClickEvent,
  trackTILoadEvent,
  VIEW_MORE_CLICK,
} from 'mobile-holidays-react-native/src/utils/TripIdeasTrackingUtils';
import { isEmpty } from 'lodash';
import placeHolderImage from '@mmt/legacy-assets/src/genericDarkTextureImage.webp';
import { openTIDeepLink } from './utils';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { sectionBottomSpacing, sectionHeaderSpacing } from '../../../Styles/holidaySpacing';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';

const TripIdeaTravelStories = ({
  travelStoriesData,
  groupingData = { pageDataMap: {}, requestId: {}, holidayLandingGroupDto: {} },
  fabCta = { interventionLoggingDetails: {} },
  omniPageName = '',
  toggleTIDeepLinkOpenCheck = null,
  closeIntervention,
  containerStyles = {},
  trackViewedSectionClickEvent = () => {},
}) => {
  if (isEmpty(travelStoriesData) || isEmpty(travelStoriesData.cards)) {
    return null;
  }
  useEffect(() => {
    trackTILoadEvent({ groupingData, fabCta, sectionData: travelStoriesData, omniPageName });
  }, []);

  const { deepLink = '', header = '', subHeader = '', cards = [] } = travelStoriesData || {};
  const getStoryData = () => {
    return [
      ...sortByKey({ obj: cards, key: 'order' }),
      ...(deepLink ? [{ graphicType: VIEW_ALL_STORY_TYPE.type }] : []),
    ];
  };

  const handleViewAllCardClick = () => {
    trackViewedSectionClickEvent();
    trackTIClickEvent({
      groupingData,
      fabCta,
      sectionData: travelStoriesData,
      type: VIEW_MORE_CLICK,
      omniPageName,
    });
    openTIDeepLink({ url: deepLink, toggleTIDeepLinkOpenCheck });
  };
  const renderViewAllCard = () => {
    return (
      <TouchableOpacity style={styles.viewAllCard} onPress={handleViewAllCardClick}>
        <Text style={styles.viewAll}>View All</Text>
        <View style={styles.viewAllIconContainer}>
          <Image source={ArrowIcon} style={styles.arrowIcon} />
        </View>
      </TouchableOpacity>
    );
  };
  const RenderStoryCard = ({ item, index }) => {
    const {
      graphicType = '',
      graphicUrl = '',
      header = '',
      subHeader = '',
      deeplink = '',
      duration = null,
    } = item || {};
    const [onImageError, setImageError] = useState(false);
    if (graphicType === VIEW_ALL_STORY_TYPE.type) {
      return renderViewAllCard();
    }

    const onImageDownloadError = () => {
      setImageError(true);
    };
    const openCardDeepLink = () => {
      trackViewedSectionClickEvent();
      trackTIClickEvent({ groupingData, fabCta, sectionData: travelStoriesData, cardData: item, omniPageName });
      closeIntervention();
      openTIDeepLink({ url: deeplink, toggleTIDeepLinkOpenCheck });
    };
    return (
      <TouchableOpacity style={styles.containerStyle} onPress={openCardDeepLink} key={index}>
        <View style={[styles.storyCardContainer, styles.storyCardImage]}>
          <ImageBackground
            source={onImageError ? placeHolderImage : { uri: graphicUrl }}
            resizeMode="cover"
            style={styles.storyCardImage}
            onError={onImageDownloadError}
          >
            <LinearGradient
              colors={['rgba(0,0,0,0.7)', holidayColors.transparent, holidayColors.transparent]}
              start={{ x: 0.0, y: 1.0 }}
              end={{ x: 0.0, y: 0.0 }}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                zIndex: 1,
              }}
            >
              <View style={styles.textContainer}>
                <View style={styles.storyType}>
                  {graphicType === IMAGE_STORY_TYPE.type ? (
                    <Image source={GalleryIcon} style={styles.galleryIcon} />
                  ) : (
                    <View style={styles.videoStoryType}>
                      <Text style={styles.storyVideoTime}>{formatTime(duration)}</Text>
                    </View>
                  )}
                </View>
                <View style={styles.storyUploadInfo}>
                  {!!header && <Text style={styles.userName}>{header}</Text>}
                  {!!subHeader && <Text style={styles.createdAt}>{subHeader}</Text>}
                </View>
              </View>
            </LinearGradient>
          </ImageBackground>
        </View>
      </TouchableOpacity>
    );
  };
  return (
    <View style={[styles.travelStoriesContainer, containerStyles]}>
      <View style={styles.headingSection}>
        <View style={styles.headingContainer}>
          {!!header && <Text style={styles.header}>{header}</Text>}
          {!!deepLink && (
            <TouchableOpacity onPress={handleViewAllCardClick} style={{ marginLeft: 'auto' }}>
              <Text style={styles.viewMoreText}>View All</Text>
            </TouchableOpacity>
          )}
        </View>
        {!!subHeader && <Text style={styles.subHeader}>{subHeader}</Text>}
      </View>
      <View style={styles.storySection}>
        <FlatList
          data={getStoryData()}
          renderItem={({ item, index }) => <RenderStoryCard item={item} index={index} />}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  travelStoriesContainer: {
    paddingHorizontal: 20,
    backgroundColor: holidayColors.lightGray2,
    ...sectionBottomSpacing,
  },
  containerStyle: {
    marginBottom: 5,
    marginRight: 8,
    marginTop: 5,
    ...holidayBorderRadius.borderRadius16,
  },
  headingSection: {
    width: '100%',
    ...sectionHeaderSpacing,
  },
  headingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  header: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  subHeader: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  storySection: {
    flexDirection: 'row',
  },
  storyCardContainer: {
    borderRadius: 5,
    overflow: 'hidden',
  },
  storyCardImage: {
    width: 100,
    height: 120,
    ...holidayBorderRadius.borderRadius16,
  },
  textContainer: {
    height: '100%',
  },
  userName: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
    paddingHorizontal: 5,
    paddingBottom: 5,
  },
  createdAt: {
    fontSize: 10,
    color: holidayColors.white,
    opacity: 0.5,
    fontWeight: '400',
  },
  storyUploadInfo: {
    marginTop: 'auto',
    alignItems: 'center',
    paddingBottom: 5,
  },
  storyType: {
    marginLeft: 'auto',
    paddingRight: 8,
    paddingTop: 8,
  },
  videoStoryType: {
    backgroundColor: holidayColors.black,
    opacity: 0.6,
    borderRadius: 4,
    padding: 4,
  },
  storyVideoTime: {
    color: holidayColors.white,
    fontSize: 10,
    fontWeight: '900',
    fontFamily: fonts.regular,
  },
  galleryIcon: {
    width: 15,
    height: 15,
  },
  viewAllCard: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: holidayColors.white,
    borderRadius: 4,
    marginTop: 5,
    width: 100,
    height: 123,
  },
  viewAll: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
    paddingVertical: 4,
  },
  viewAllIconContainer: {
    backgroundColor: holidayColors.primaryBlue,
    height: 32,
    width: 32,
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  arrowIcon: {
    tintColor: holidayColors.white,
    width: 16,
    height: 12,
  },
  viewMoreText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
  },
});



export default TripIdeaTravelStories;
