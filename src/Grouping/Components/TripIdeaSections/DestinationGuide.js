import React, { useEffect } from 'react';
import { StyleSheet, Text, View, Image, FlatList, TouchableOpacity } from 'react-native';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import ArrowDropDown from '@mmt/legacy-assets/src/arrow_dropdown.webp';
import { getScreenWidth, sortByKey } from '../../../utils/HolidayUtils';
import { isEmpty } from 'lodash';
import {
  trackTIClickEvent,
  trackTILoadEvent,
  VIEW_MORE_CLICK,
} from 'mobile-holidays-react-native/src/utils/TripIdeasTrackingUtils';
import { openTIDeepLink } from './utils';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
const IMAGE_WIDTH_SPACE = 105;

const TripIdeaDestinationGuide = ({
  destinationGuide,
  groupingData,
  fabCta,
  toggleTIDeepLinkOpenCheck,
  closeIntervention,
  omniPageName = '',
  containerStyles = {},
  trackViewedSectionClickEvent = () => {},
}) => {
  const screenWidth = getScreenWidth();
  if (isEmpty(destinationGuide) || isEmpty(destinationGuide.cards)) {
    return [];
  }

  const { header = '', subHeader = '', deepLink = '', cards = [] } = destinationGuide || {};
  const destinationGuideCards = sortByKey({ obj: cards, key: 'order' });

  useEffect(() => {
    trackTILoadEvent({ groupingData, fabCta, sectionData: destinationGuide, omniPageName });
  }, []);

  const renderGuideCard = ({ item, index }) => {
    const { header = '', deeplink = '' } = item || {};
    const openCardDeepLink = () => {
      trackViewedSectionClickEvent();
      trackTIClickEvent({
        groupingData,
        fabCta,
        sectionData: destinationGuide,
        cardData: item,
        omniPageName,
      });
      openTIDeepLink({ url: deeplink || '', toggleTIDeepLinkOpenCheck });
      closeIntervention();
    };
    return (
      <TouchableOpacity style={styles.guideCardContainer} onPress={openCardDeepLink} key={index}>
        {!!header && <Text style={styles.guideTitle}>{header}</Text>}
      </TouchableOpacity>
    );
  };

  const handleViewAllClick = () => {
    trackViewedSectionClickEvent();
    trackTIClickEvent({
      groupingData,
      fabCta,
      sectionData: destinationGuide,
      type: VIEW_MORE_CLICK,
      omniPageName,
    });
    openTIDeepLink({ url: deepLink || '', toggleTIDeepLinkOpenCheck });
  };
  return (
    <View style={[styles.destinationGuideContainer, containerStyles]}>
      <HolidayImageHolder
        imageUrl={destinationGuide?.imageUrl || ''}
        style={{ width: 100, height: '100%' }}
        resizeMode="contain"
      />
      <View style={[styles.container, { width: screenWidth - IMAGE_WIDTH_SPACE }]}>
        <TouchableOpacity onPress={handleViewAllClick}>
          <View style={styles.headingSection}>
            <View>
              {!!header && <Text style={styles.header}>{header}</Text>}
              {!!subHeader && <Text style={styles.subHeader}>{subHeader}</Text>}
            </View>
            <Image source={ArrowDropDown} style={styles.arrowIcon} />
          </View>
        </TouchableOpacity>
        <View style={styles.dividerSection} />
        <View style={styles.guideSection}>
          <FlatList
            data={destinationGuideCards || []}
            renderItem={({ item, index }) => renderGuideCard({ item, index })}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  destinationGuideContainer: {
    flexDirection: 'row',
    marginTop: 5,
    marginBottom: 30,
    height: 110,
    backgroundColor: holidayColors.lightGray2,
  },
  headingSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 10,
    width: '100%',
  },
  header: {
    ...fontStyles.labelLargeBold,
    color: holidayColors.black,
  },
  subHeader: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  arrowIcon: {
    tintColor: holidayColors.primaryBlue,
    transform: [{ rotate: '270deg' }],
    width: 10,
    height: 8,
    marginLeft: 'auto',
  },
  dividerSection: {
    backgroundColor: holidayColors.black,
    opacity: 0.1,
    height: 1,
    // width: '50%',
    marginVertical: 15,
  },
  guideSection: {
    flexDirection: 'row',
    // width: 350,
    width: '100%',
  },
  guideCardContainer: {
    borderColor: holidayColors.primaryBlue,
    paddingHorizontal: 8,
    paddingVertical: 5,
    borderWidth: 1,
    marginHorizontal: 4,
    borderRadius: 4,
    backgroundColor: holidayColors.white,
  },
  guideTitle: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
});


export default TripIdeaDestinationGuide;
