import React from 'react';
import { Image, TouchableOpacity, StyleSheet, Text, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import EditIcon from '@mmt/legacy-assets/src/holidays/EditIcon.webp';
import * as DateUtil from '../DateUtil';
import { isEmpty } from 'lodash';
import { getPaxText } from '../../../../utils/HolidayUtils';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';
import PageHeader from 'mobile-holidays-react-native/src/Common/Components/PageHeader';

const WidgetPage = (props) => {
  const { togglePopup, onBack, groupingData, travellerObject, isSearchFilter } = props || {};
  const { response } = groupingData || {};
  const { message } = response || {};
  let { userDepCity, destinationCity } = props;
  if (typeof userDepCity === 'object') {
    userDepCity = undefined;
  }
  if (typeof destinationCity === 'object') {
    destinationCity = undefined;
  }

  const renderQuickFilterView = () => {
    const {
      onFilterClicked, isFilterCTAExpanded, groupingData, selectedQuickFilterIndex,
    } = props || {};

    const FiltersList = require('../../../../SearchWidget/Components/PhoenixSearchPage/QuickFiltersHorizontalList').default;
    return (
      <FiltersList
        showShadow
        groupingData={groupingData}
        onQuickFilterClicked={onFilterClicked}
        onAllFilterClicked={onFilterClicked}
        isExpanded={isFilterCTAExpanded}
        selectedQuickFilterIndex={selectedQuickFilterIndex}
        containerStyles={styles.quickFilterContainer}
      />
    );
  };
  return (
    <View>
      <View style={styles.widgetPageWrapperRoot}>
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
          <PageHeader
            title={`${userDepCity} to ${destinationCity || 'Select Destination'}`}
            subTitle={`${DateUtil.getDateText({ ...props })}${
              isSearchFilter ? getPaxText({ trvInfo: travellerObject, seprator: ' | ' }) : ''
            }`}
            onBackPressed={onBack}
            showBackBtn
            subTitleStyles={paddingStyles.pt2}
          />
          <TouchableOpacity style={AtomicCss.pushRight} onPress={() => togglePopup('EditOverlay')}>
            <Image style={styles.EditIcon} source={EditIcon} />
          </TouchableOpacity>
        </View>
      </View>
      {renderQuickFilterView()}

      {!isEmpty(message) && (
        <View style={styles.messageContainer}>
          <Image
            style={styles.warningImage}
            source={require('@mmt/legacy-assets/src/ic-alert-red.webp')}
          />
          <Text style={styles.message}>{message}</Text>
        </View>
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  backArrow: { width: 16, height: 16, ...marginStyles.mr16 },
  EditIcon: { width: 20, height: 29, ...marginStyles.mr10 },
  widgetPageWrapperRoot: {
    backgroundColor: holidayColors.white,
    ...paddingStyles.pt12,
    ...paddingStyles.ph16,
    ...paddingStyles.pb4,
  },
  widgetPageWrapper: {
   borderWidth: 1,
   borderColor: holidayColors.grayBorder,
   ...holidayBorderRadius.borderRadius8,
   ...paddingStyles.ph10,
    backgroundColor: holidayColors.lightGray2,
  },
  messageContainer: {
    backgroundColor: holidayColors.fadedYellow,
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.ph16,
  },
  warningImage: {
    width: 17,
    height: 17,
    ...marginStyles.mr16,
    tintColor: holidayColors.yellow,
  },
  message: {
    ...fontStyles.labelSmallRegular,
    ...marginStyles.mr16,
  },
  cityText: {
    ...fontStyles.labelBaseBold,
    ...AtomicCss.marginBottom2,
    color: holidayColors.black,
  },
  dateText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  quickFilterContainer: {
    backgroundColor: holidayColors.white,
    ...paddingStyles.pb10,
  }
});
export default WidgetPage;
