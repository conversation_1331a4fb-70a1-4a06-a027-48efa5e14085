import React from 'react';
import {
  Animated,
  Dimensions,
  Easing,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableHighlight,
  TouchableOpacity,
  View,
} from 'react-native';
import fecha from 'fecha';
import {isEmpty, isEqual} from 'lodash';
import PropTypes from 'prop-types';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {CALENDAR_SELECTED_DATE_DIFF, FROM, TO} from '../../../../SearchWidget/SearchWidgetConstants';
import * as DateUtil from '../DateUtil';
import { fillDateAndTime } from '../../../../Common/HolidaysCommonUtils';
import {
  getAPWindow,
  getDateForCalendar,
  getNearByCityDataForPdt,
  getScreenWidth,
} from '../../../../utils/HolidayUtils';
import {getCityCampaignDisplayName, getCitySearchType} from '../../../../LandingNew/Utils/DestinationDepartureCityUtils';
import {getLandingMetaData} from '../../../../utils/HolidayNetworkUtils';
import MmtHolidayCalender from '../../../../Calender/MmtHolidayCalender';
import {infoIcon, SEARCH_FILTER_SECTIONS} from '../../../../HolidayConstants';
import { getEnableGeoLoc, getIsSearchFilter } from '../../../../utils/HolidaysPokusUtils';
import { holidayColors, iconColors } from '../../../../Styles/holidayColors';
import PageHeader from '../../../../Common/Components/PageHeader';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import PrimaryButton from '../../../../Common/Components/Buttons/PrimaryButton';
import { DEFAULT_SCREEN_SIZE } from '../../../../Styles/holidayFonts';
import HalfWidthSearchComponent from '../../../../Common/Components/SearchFilterComponent/HalfWidthSearchComponent';
import FullWidthSearchComponent from 'mobile-holidays-react-native/src/Common/Components/SearchFilterComponent/FullWidthSearchComponent';
import { logHolidaysGroupingPDTEvents } from 'mobile-holidays-react-native/src/PhoenixGroupingV2/Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const locationImg = require('@mmt/legacy-assets/src/holidays/locationIcon.webp');
const calendarImg = require('@mmt/legacy-assets/src/calendar.webp');
const paxImg = require('@mmt/legacy-assets/src/ic_user_gray.webp');
const crossIcon = require('@mmt/legacy-assets/src/holidays/iconCross.webp');

const EDIT_OVERLAY_CONTAINER_HEIGHT = getScreenWidth() > DEFAULT_SCREEN_SIZE ? 360 : 345;
class EditOverlay extends React.Component {
  static navigationOptions = { header: null };

  constructor(props) {
    super(props);
    let departureDate;
    const { selectedDate, packageDate, selectedCityType, citySelectionType } = this.props;
    if (selectedDate) {
      departureDate = selectedDate;
    }
    if (!departureDate && packageDate) {
      departureDate = packageDate;
    }
    let { userDepCity } = this.props;
    if (typeof userDepCity === 'object') {
      userDepCity = undefined;
    }
    let { destinationCity, destinationCityData } = this.props;
    if (typeof destinationCity === 'object') {
      destinationCity = undefined;
    }

    let filterDataString;
    if (destinationCityData.filters) {
      filterDataString = JSON.stringify(destinationCityData.filters);
    }

    this.state = {
      overlayPosition: new Animated.Value(0),
      userDepCity,
      destinationCity,
      fromDateObj: departureDate ? DateUtil.getDateObject(departureDate) : null,
      showCalendar: false,
      showDepDestPopUp: false,
      destinationCityData,
      filterDataString,
      selectedCityType,
      citySelectionType,
    };

    this.previousState = { ...this.state }; // This state will be used to determine changes
    this.labelFromOrTo = TO;
  }

  isDepartureCityChanged = () => this.previousState.userDepCity !== this.state.userDepCity;
  isDestinationCityChanged = () =>
    this.previousState.destinationCity !== this.state.destinationCity ||
    this.previousState.destinationCityData?.campaign !== this.state.destinationCityData?.campaign ||
    this.state.filterDataString !== this.previousState.filterDataString;
  isDateChanged = () => !isEqual(this.previousState.fromDateObj, this.state.fromDateObj);
  isAnythingChanged = () =>
    this.isDateChanged() ||
    this.isDepartureCityChanged() ||
    this.isDestinationCityChanged() ||
    this.props?.updatedPaxDetails;

  getCities = () => {
    const { availableHubs } = this.props;
    return availableHubs ? availableHubs.map((hub) => hub.name) : [];
  };

  getDisplayDate = () => {
    if (!this.state.fromDateObj) {
      return DateUtil.getDateText({ ...this.props });
    }
    return DateUtil.getFormattedDate(this.state.fromDateObj);
  };

  componentDidMount() {
    const bottom = 700;
    const delay = 200;
    const duration = 600;
    this.startAnimate(bottom, duration, delay);
    this.getData();
  }

  getData() {
    this.enableNewGeoLocation = getEnableGeoLoc();
    this.isSearchFilter = getIsSearchFilter();
    this.setState({ updatePokus: true });
    const { userDepCity, destinationCityData } = this.state || {};
    this.getMetaData({ userDepCity, destinationCityData });
  }

  getLandingMetadatObject = async ({ userDepCity, destinationCityData }) => {
    const { campaign, destinationCity } = destinationCityData || {};
    const city = userDepCity;

    if (isEmpty(destinationCity) && !isEmpty(campaign)) {
      return { departure: { text: city }, campaign: campaign };
    } else if (!isEmpty(destinationCity) && !isEmpty(campaign)) {
      return {
        departure: { text: city },
        destination: { text: destinationCity },
        campaign: campaign,
      };
    }
    return { departure: { text: city }, destination: { text: destinationCity } };
  };

  getMetaData = async ({ userDepCity, destinationCityData }) => {
    const metaRequestObject = await this.getLandingMetadatObject({
      userDepCity,
      destinationCityData,
    });
    const metaData = await getLandingMetaData(metaRequestObject);
    this.setState({
      metaData: metaData,
    });
  };

  startAnimate(bottom, duration, delay) {
    Animated.timing(this.state.overlayPosition, {
      toValue: bottom,
      easing: Easing.easeInOut,
      duration,
      delay,
    }).start();
  }
  openDepartureCity = () => {
    this.openCitySelect(true);
  };
  openDestinationCity = () => {
    this.openCitySelect(false);
  };
  closeCitySelection = () => {
    this.setState({ showDepDestPopUp: false }, () => {
      const eventName = `back_${this.labelFromOrTo === FROM ? 'hub' : 'destination'}`;
      this.captureClickEvents({
        eventName,
        value: eventName,
      });
    });
  };
  openCitySelect = (isDeparture) => {
    if (isDeparture) {
      this.captureClickEvents({eventName : 'change_hub'})
      this.labelFromOrTo = FROM;
    } else {
      this.captureClickEvents({eventName : 'change_destination'})
      this.labelFromOrTo = TO;
    }
    this.setState({ showDepDestPopUp: true, isDeparture });
  };

  captureClickEvents = ({
    eventName = '',
    prop1 = {},
    value = '',
    actionType = {},
    pdtExtraData = {},
  }) => {
    logHolidaysGroupingPDTEvents({
      actionType:  !isEmpty(actionType) ? actionType : PDT_EVENT_TYPES.buttonClicked,
      value: value || eventName,
    });
    this.props.trackLocalClickEvent({
      eventName,
      prop1,
      pdtExtraData,
    });
  };
  onCitySelect = (city, branch, locusId, data = {}) => {
    const { primaryCityObj, displayName = '', type = '' } = data || {};
    const { primaryCityName, primaryCityType } = primaryCityObj || {};
    let sourceCityData = {};
    let prop1;
    let { userDepCity, destinationCity, destinationCityData, campaign, filterDataString } =
      this.state;
    if (this.labelFromOrTo === TO) {
      prop1 = `${primaryCityName} | ${primaryCityType} | ${displayName} | ${type}`;
      destinationCity = city;
      campaign = data.campaign;
      if (data && !isEmpty(data)) {
        destinationCityData = data;
      } else {
        destinationCityData = {
          campaign,
          destinationCity,
        };
      }
      if (destinationCityData.filters) {
        filterDataString = JSON.stringify(destinationCityData.filters);
      } else {
        filterDataString = '';
      }
    } else {
      const { citySearchType, citySelectionType } = data || {};
      prop1 = `${city} | ${citySearchType}`;
      this.setState({ selectedCityType: citySearchType, citySelectionType });
      userDepCity = city;
      sourceCityData = { ...sourceCityData, ...data, userDepCity };
    }

    this.getMetaData({ userDepCity, destinationCityData });
    this.setState(
      {
        showDepDestPopUp: false,
        destinationCity,
        userDepCity,
        destinationCityData,
        campaign,
        filterDataString,
        locusId,
      },
      () => {
        this.trackCitySelectEvent(destinationCityData, sourceCityData, prop1);
      },
    );
  };

  onCalendarDone = (date) => {
    const pdtExtraData = { ap_window: getAPWindow(date) };
    this.setState({ showCalendar: false, fromDateObj: DateUtil.getDateObject(date) }, () => {
      const eventName = this.isDateChanged() ? 'select_date' : 'back_date';
      const dateSelected = fecha.format(date, 'YYYY-MM-DD');
      this.captureClickEvents({
        eventName,
        value: eventName + '|' + dateSelected,
        prop1: this.getEditSearchTrackingData(),
        pdtExtraData,
      });
    });
  };

  toggleCalendar = () => {
    this.setState({ showCalendar: !this.state.showCalendar }, () => {
      if (this.state.showCalendar) {
        this.captureClickEvents({eventName : 'change_date'})
      } else {
        this.captureClickEvents({eventName : 'back_date'})
      }
    });
  };

  getEditSearchTrackingData = () => {
    let trackingData = {};
    trackingData.departureCity = this.state.userDepCity;
    trackingData.dest = this.state.destinationCity;
    trackingData.packageDate = this.state.fromDateObj
      ? fillDateAndTime(this.state.fromDateObj.selectedDate, 'DD-MM-YYYY')
      : undefined;
    return trackingData;
  };

  trackCitySelectEvent = (destinationCityData, sourceCityData = {}, prop1) => {
    if (this.labelFromOrTo === TO) {
      const { destinationCity, primaryCityObj, type, campaign = '' } = destinationCityData || {};
      const { primaryCityName, primaryCityType } = primaryCityObj || {};
      const toCitySelectData = {
        destcity_selected: destinationCity,
        destcity_selected_type: type,
        destcity_selected_parent: primaryCityName,
      };
      const event = 'select_destination_' + (destinationCity || campaign);
      const value = 'select_destination|' + (destinationCity || campaign);
      this.captureClickEvents({
        eventName: event,
        value,
        prop1,
        actionType: PDT_EVENT_TYPES.valueSelected,
        pdtExtraData: toCitySelectData,
      });
    } else {
      const { userDepCity, citySearchType, citySelectionType, primaryCityObj, nearByCities } =
        sourceCityData || {};
      const { primaryCityName } = primaryCityObj || {};
      const nearByCityDataForPdt = getNearByCityDataForPdt(nearByCities);

      //Below data only needs be populated when city hans changed by selection.
      const fromCitySelectData = this.isDepartureCityChanged()
        ? {
            fromcity_selected: userDepCity,
            fromcity_selected_type: citySelectionType,
            fromcity_search_type: citySearchType,
            fromcity_selected_parent: primaryCityName ? primaryCityName : userDepCity,
            fromcity_suggestions: nearByCityDataForPdt ? nearByCityDataForPdt : '',
          }
        : {};

      const event = 'select_hub _' + userDepCity;
      const value = `select_hub|${userDepCity}`;
      this.captureClickEvents({
        eventName: event,
        value,
        actionType: PDT_EVENT_TYPES.valueSelected,
        prop1,
        pdtExtraData: fromCitySelectData,
      });
    }
  };

  togglePaxLocal = () => {
    this.props.togglePax(this.state.metaData?.pax);
  };
  getPax = () => {
    const { adult, child, noOfRooms } = this.props || {};
    return `${adult > 1 ? `${adult} Adults` : `${adult} Adult`}${
      child > 1 ? `, ${child} Children` : child > 0 ? `, ${child} Child` : ''
    }${noOfRooms > 1 ? ` | ${noOfRooms} Rooms` : ` | ${noOfRooms} Room`}`;
  };
  renderEditOverlay = () => {
    const {
      destinationCityData,
      userDepCity,
      overlayPosition,
      fromDateObj,
      destinationCity,
      selectedCityType,
      citySelectionType,
    } = this.state || {};
    const { trackViewedSectionClickEvent = () => {} } = this.props || {};
    const destinationName = getCityCampaignDisplayName(destinationCityData);

    const handleBackPress = () => {
      this.props.togglePopup('');
      this.captureClickEvents({
        eventName: 'edit_intent_close',
      });
    };

    const handleSearchClick = () => {
      if (this.isAnythingChanged()) {
        this.props.onEditSearchDone({
          date: fromDateObj ? fromDateObj.selectedDate : undefined,
          userDepCity,
          destinationCity,
          cityModified: this.isDepartureCityChanged() || this.isDestinationCityChanged(),
          destinationCityData,
          selectedCityType,
          citySelectionType,
          locusId: this.state.locusId,
        });
        trackViewedSectionClickEvent();
      } else {
        this.props.togglePopup('');
      }
    };

    return (
      <View style={styles.overlayContainer}>
        <TouchableHighlight onPress={() => this.props.togglePopup('')} style={styles.overlayBg}>
          <Text>.</Text>
        </TouchableHighlight>
        <Animated.View
          style={[
            styles.overlayContent,
            { bottom: overlayPosition },
            this.isSearchFilter?.searchV2 ? styles.heightForFilter : [],
          ]}
        >
          <PageHeader
            title={'Edit your Search'}
            showBackBtn
            iconSource={crossIcon}
            onBackPressed={handleBackPress}
            headerWrapperStyle={styles.headerWrapperStyle}
          />
          <ScrollView style={paddingStyles.ph12}>
            <View style={[AtomicCss.flexRow, { justifyContent: 'space-between' }]}>
              <HalfWidthSearchComponent
                title={SEARCH_FILTER_SECTIONS.STARTING_FROM}
                value={userDepCity && userDepCity.trim().length ? userDepCity : 'Select'}
                onPress={this.openDepartureCity}
                icon={locationImg}
                iconStyles={styles.locationImg}
                containerStyles={styles.searchSectionRow}
              />
              <View style={marginStyles.mh4} />
              <HalfWidthSearchComponent
                title={SEARCH_FILTER_SECTIONS.TRAVELLING_TO}
                iconStyles={styles.locationImg}
                value={
                  destinationName && destinationName.trim().length ? destinationName : 'Select'
                }
                onPress={this.openDestinationCity}
                icon={locationImg}
                containerStyles={styles.searchSectionRow}
              />
            </View>
            <FullWidthSearchComponent
              title={SEARCH_FILTER_SECTIONS.TRAVELLING_DATE}
              titleStyles={styles.searchHeader}
              icon={calendarImg}
              iconStyles={styles.calendarImg}
              value={this.getDisplayDate()}
              onPress={this.toggleCalendar}
              containerStyles={styles.searchSection}
            />
            {this.isSearchFilter?.searchV2 && (
              <FullWidthSearchComponent
                title={SEARCH_FILTER_SECTIONS.ROOM_GUESTS}
                titleStyles={styles.searchHeader}
                icon={paxImg}
                value={this.getPax()}
                onPress={this.togglePaxLocal}
                containerStyles={styles.searchSection}
              />
            )}
            <View style={[AtomicCss.marginTop5]}>
              <PrimaryButton buttonText={'SEARCH'} handleClick={handleSearchClick} />
            </View>
          </ScrollView>
        </Animated.View>
      </View>
    );
  };

  onDayClicked = (day) => {
    const value = `click_calendar_date|${day?.dateString}`
    this.captureClickEvents({
      eventName: 'click_calendar_date',
      value,
      actionType: PDT_EVENT_TYPES.valueSelected,
    });
  };

  render() {
    const { isDeparture = false } = this.state || {};
    let Selector;
    if (
      (this.enableNewGeoLocation && isDeparture) ||
      (this.isSearchFilter?.searchV2 && !isDeparture)
    ) {
      Selector =
        require('../../../../SearchWidget/Components/DepartureDestinationSelector').default;
    } else {
      Selector = require('../../../../SearchWidget/Components/FilterDestinationSelector').default;
    }

    if (this.state.showCalendar) {
      return (
        <View style={styles.calendarContainer}>
          <MmtHolidayCalender
            selectedDate={
              this.state.fromDateObj ? this.state.fromDateObj.selectedDate : getDateForCalendar()
            }
            onDone={this.onCalendarDone}
            onCalendarBack={this.toggleCalendar}
            isDefaultSelected={this.state.fromDateObj ? false : true}
            availableDates={this.state.metaData?.dates?.availableDates}
            message={this.state.metaData?.dates?.msg}
            icon={infoIcon}
            onDayClicked={this.onDayClicked}
          />
        </View>
      );
    } else if (this.state.showDepDestPopUp) {
      const { availableHubs, setPdtTrackingData } = this.props;
      return (
        <View style={styles.selectorContainer}>
          <Selector
            autoCompleteObj={{ label: this.labelFromOrTo, availableHubs }}
            availableHubs={availableHubs}
            forceShowInput
            showAdavanceSearch={false}
            citySelect={this.onCitySelect}
            onBack={this.closeCitySelection}
            trackClickEvent={this.props.trackClickEvent}
            scrollable
            showGetCurrLocation={this.labelFromOrTo === FROM}
            showa
            searchType={getCitySearchType(this.labelFromOrTo)}
            setPdtTrackingData={setPdtTrackingData}
            destinationMapInfo={this.props.destinationMapInfo}
          />
        </View>
      );
    }
    return this.renderEditOverlay();
  }
}

const styles = StyleSheet.create({
  overlayContainer: {
    justifyContent: 'flex-start',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 110,
    elevation: 3,
  },
  overlayBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 3,
    elevation: 3,
  },
  heightForFilter:{
    height: EDIT_OVERLAY_CONTAINER_HEIGHT,
  },
  overlayContent: {
    backgroundColor: holidayColors.white,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 4,
    zIndex: 4,
    position: 'absolute',
    top: 0,
    width: '100%',
    height: 272,
    shadowOffset: {
      width: 0,
      height: 0,
    },
  },
  searchSectionRow: {
    width: (Dimensions.get('screen').width - 32) / 2,
    ...marginStyles.mb12,
    ...paddingStyles.ph12,
    ...paddingStyles.pv6,
  },
  searchSection: {
    ...marginStyles.mb12,
    ...paddingStyles.pv6,
  },
  searchHeader: {
    ...marginStyles.mb4,
  },
  locationImg: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  calendarImg: {
    tintColor: iconColors.searchFilterIcons,
    width: 20,
    height: 20,
  },
  calendarContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    zIndex: 110,
    width:'100%',
  },
  selectorContainer:{
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 110,
  },
  headerWrapperStyle: {
    elevation: 0,
    zIndex: 0,
    shadowOpacity: 0
  }
});
EditOverlay.propTypes = {
  togglePopup: PropTypes.func.isRequired,
  userDepCity: PropTypes.string.isRequired,
  destinationCity: PropTypes.string.isRequired,
  packageDate: PropTypes.string,
  selectedDate: PropTypes.string,
  onEditSearchDone: PropTypes.func.isRequired,
  trackClickEvent: PropTypes.func.isRequired,
};
export default EditOverlay;
