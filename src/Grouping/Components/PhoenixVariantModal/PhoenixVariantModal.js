import React, { useEffect, useState } from 'react';
import { Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import leftArrow from '@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp';
import { isNotNullAndEmptyCollection } from '../../../utils/HolidayUtils';
import { fetchVariantData } from '../../../utils/HolidayNetworkUtils';
import styles from '../PhoenixVariantModalStyles';
import { isEmpty } from 'lodash';
import { DETAILS_DEPARTUTE_DATE_FORMAT, variantEnum } from '../../../HolidayConstants';
import fecha from 'fecha';
import {
  CARD_HEIGHT_DEFAULT,
  CARD_HEIGHT_MULTI_HOTEL,
  LIST_COUNT,
  SELECT_HOTEL_CATEGORY,
  TravelType,
} from './PhoenixVariantModalConstants';
import { getList, sortData, sortPackageVaraintData } from './PhoenixVarientUtils';
import FlightVariantSelection from './FlightVariantSelection';
import { BottomSheetHeading } from 'mobile-holidays-react-native/src/Common/Components/BottomSheetOverlay';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const PhoenixVariantModal = ({
  packageVariantData,
  close,
  onPackageClickedFromVariantModal,
  departureCity,
  variantTypeFromPokus,
  rooms,
}) => {
  const { variantTitle, packageVariants, packageDetail, variantType } = packageVariantData || {};
  const {
    name,
    id,
    categoryDetails: { defaultCategoryId } = {},
    priceDetails: { categoryPrices } = {},
    departureDetails = {},
    requestMeta = {},
  } = packageDetail || {};
  const [variantData, setVariantData] = useState();
  const [error, setError] = useState(false);
  const parentNodeIndex = packageVariants?.findIndex((el) => el?.isParent);
  const variantDataList = packageVariants ? sortPackageVaraintData(packageVariants, parentNodeIndex) : [];
  const departureDate = departureDetails?.departureDate || '';

  useEffect(() => {
    async function getVariantData() {
      setError(false);
      let depDate;
      if (isNotNullAndEmptyCollection(categoryPrices)) {
        const categoryPrice = categoryPrices.find(
          ({ categoryId }) => categoryId === defaultCategoryId,
        );
        if (categoryPrice) {
          depDate = fecha.format(categoryPrice.departureDate, DETAILS_DEPARTUTE_DATE_FORMAT);
        }
      }
      else if (departureDate) {
        depDate = fecha.format(new Date(departureDate), DETAILS_DEPARTUTE_DATE_FORMAT);
      }

      const response = await fetchVariantData({
        id,
        departureCity,
        depDate,
        rooms,
        variantId: requestMeta?.variantId || '',
      });
      response?.listingPackageVariantsDetails
        ? setVariantData(response.listingPackageVariantsDetails)
        : setError(!!response.error);
    }

    getVariantData();
  }, []);

  // gets out variant card from package/variants data using key from variantData of listing api
  const getUpdatedData = (variant) => {
    return variantData?.packageVariants.find((el) => {
      return el.uniqueId === variant.uniqueId;
    });
  };

  let singleCardHeight = CARD_HEIGHT_DEFAULT;
  if (variantTypeFromPokus === variantEnum.OTHERS && variantType && variantType === TravelType.TRANSFER) {
    singleCardHeight = CARD_HEIGHT_MULTI_HOTEL;
  }
  const cardsHeight  = variantDataList?.length * singleCardHeight || 0;
  const height = cardsHeight > 400 ? 400 : cardsHeight;

  return (
    <View>
      {!isEmpty(variantTitle) && <Text style={styles.variantTitle}>{variantTitle}</Text>}
      <View style={{ height: height }}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={AtomicCss.marginTop15}>
            {variantDataList && variantDataList?.map((variant, index) => {
              const packageVariantUpdatedData = getUpdatedData(variant);
              const additionInfoListToShow = getList(
                packageVariantUpdatedData?.additionalInfo,
                LIST_COUNT,
              );
              const cardError = !isEmpty(variantData) && isEmpty(packageVariantUpdatedData);
              const disable = (error || cardError) && !variant?.isParent;
              const handlePress = () => {
                if ((!error && !cardError && !isEmpty(variantData)) || variant?.isParent) {
                  onPackageClickedFromVariantModal({
                    variant,
                    data: {
                      ...packageVariantUpdatedData?.listingPackageVariantUrlInfo,
                      requestMeta: packageVariantUpdatedData?.requestMeta || {},
                    },
                    packageDetail: packageVariantData?.packageDetail,
                    cardNo: index,
                    length: variantDataList.length,
                  });
                }
              };

              return (
                <TouchableOpacity onPress={handlePress}>
                  <View style={styles.card}>
                    <FlightVariantSelection
                      variant={variant}
                      variantData={variantData}
                      additionInfoListToShow={additionInfoListToShow}
                      packageVariantUpdatedData={packageVariantUpdatedData}
                      cardError={cardError}
                      completeListLength={packageVariantUpdatedData?.additionalInfo?.length}
                      packageDetail={packageDetail}
                      disable={disable}
                    />

                    <View style={(AtomicCss.flex1, AtomicCss.justifyCenter)}>
                      <Image
                        source={leftArrow}
                        style={[
                          styles.rightArrow,
                          disable ? styles.headingFontDisabled : [],
                          variant?.error || cardError ? marginStyles.mb4 : {},
                        ]}
                      />
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

export default PhoenixVariantModal;
