import React, { Component, Fragment } from 'react';
import { ActivityIndicator, View, SectionList, Dimensions, StyleSheet, Text } from 'react-native';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import cloneDeep from 'lodash/cloneDeep';
import { openPackageFromParams, openPhoenixPackagePageNew } from '../Actions/HolidayGroupingActions';
// import ACMEHolidayGroupingView from './ACMEHolidayGroupingView';
import { fetchGroupingData } from '../../utils/HolidayNetworkUtils';
import { GROUPING_TRACKING_PAGE_NAME, viewTypes } from '../HolidayGroupingConstants';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import {
  addGroupsToGroupData,
  addSectionsToGroupData,
  createEmptyLoggingMap,
} from '../Utils/HolidayGroupingUtils';
import { getAcmeXSellConfig,
  getLowestPackagePrice,
  getTravelDateForRecentPackages, 
  getDimensionsForAd, 
  isMobileClient,
  isdisplayPoweredbyMMT,
  isNonGIAffiliate,
  rupeeFormatterUtils,isNonMMTAffiliate } from '../../utils/HolidayUtils';
import HolidayEmiPersuasion from '../../Common/Components/HolidayEmiPersuasion';
import HolidayFunctionalPersuasion from '../../Common/Components/HolidayFunctionalPersuasion';
import HolidayBlackPersuasion from '../../Common/Components/HolidayBlackPersuasion';
import HolidayCashbackPersuasion from '../../Common/Components/HolidayCashbackPersuasion';
import { trackHolidayGroupingLoadEvent } from '../../utils/HolidayGroupingTrackingUtils';
import { PDT_PAGE_LOAD, PDT_PAGE_VIEW, PDT_RAW_EVENT, AFFILIATES, variantEnum } from '../../HolidayConstants';
import { AbConfigKeyMappings, getAbConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import HolidaySafeBanner from '../../Common/Components/CovidSafety/HolidaySafeBannerListing';
import { getBannerDetails } from '../../Common/Components/CovidSafety/HolidaySafeDataHolder';
import PhoenixListingHeader from './PhoenixListingHeader';
import PhoenixGroupingSectionView from './PhoenixGroupingSectionView';
import HolidayCollectionBanner from '../../Common/Components/Phoenix/HolidayCollectionBanner';
import HolidayDidYouKnowBanner from '../../Common/Components/Phoenix/HolidayDidYouKnowBanner';
import * as PhoenixSectionCardClickHandler from './PhoenixSectionCardClickHandler';
import RecentlyViewedCarousal from '../../LandingNew/Components/RecentlyViewedCarousal';
import HolidayWalletSurgePersuasion from '../../Common/Components/HolidayWalletSurgePersuasion';
import SMEHorizontalSection from '../../SmeDetails/components/SMEHorizontalSection';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import GroupingPersuasionV2 from './GroupingPersuasionV2';
import BottomSheetOverlay from '../../Common/Components/BottomSheetOverlay';
import PhoenixVariantModal from './PhoenixVariantModal/PhoenixVariantModal';
import HolidaysMessageStrip from '../../Common/Components/HolidaysMessageStrip';
import TripIdeasEntrySection from './TripIdeaSections/EntrySection';
import TripIdeasDestinationGuide from './TripIdeaSections/DestinationGuide';
import TripIdeasTravelStories from './TripIdeaSections/TravelStories';
import { holidayColors } from '../../Styles/holidayColors';
import { sectionTrackingPageNames, setPageSectionVisitResponse } from '../../utils/SectionVisitTracking';
import { sectionBottomSpacing } from '../../Styles/holidaySpacing';
import HolidayAdCard from '../../Common/Components/HolidayAdCard';
import { sendRecentPackagesData } from '../../utils/ThirdPartyUtils';
import { getAffiliate } from '../../theme';

import MembershipCard from '../../Common/Components/Membership/MembershipCard';
import { CONTENT_TYPES } from '../../Common/Components/Membership/utils/constants';
class PhoenixGroupingListView extends Component {
  constructor(props) {
    super(props);
    this.state = {
      groupData: props.groupingData.response.groupDetailsList,
      packageDetails: props.groupingData.response.packageDetails,
      nextOffset: props.groupingData.response.nextOffset,
      noMoreGroups: props.groupingData.response.noMoreGroups,
      groupItems: props.groupingData.groupItems,
      holidayLandingGroupDto: props.groupingData.holidayLandingGroupDto,
      sections: props.groupingData.sections,
      sectionData: this.getSectionsData(props.groupingData.groupItems),
      loadingMoreItems: false,
      isAcmeRecomAdded: false,
      xSellConfig: getAcmeXSellConfig(),
      isPackageVariantModal: false,
    };
    this.groupingPageSectionVisits = {};
    const { groupingData, fabCta } = this.props;
    const {
      aff, pt, cmp, affRefId,
    } = props.groupingData.holidayLandingGroupDto;
    PhoenixSectionCardClickHandler.setData({
      pt,
      aff,
      affRefId,
      cmp,
      groupingData,
      fabCta,
    });
    this.listScrollEvent = {
      contentOffset: { y: 0 },
      contentSize: { height: Dimensions.get('screen').height },
    };
  }
  componentDidMount() {
    // sending data to add recent searches in GI
    if ( getAffiliate() === AFFILIATES.GI ) {
      /* Get the lowest package price and then call sendRecentSearchesData function */
      getLowestPackagePrice({
        packages: this.props?.groupingData?.response?.packageDetails || [], 
        sendRecentSearchesData: this.sendRecentSearchesData
      })
    }
  }

  sendRecentSearchesData = ({ startingPrice }) => {
    const { holidayLandingGroupDto } = this.props.groupingData || {}
    sendRecentPackagesData({ 
      userDepCity: holidayLandingGroupDto?.userDepCity, 
      usrDestCity: holidayLandingGroupDto?.destinationCity, 
      travelDate: getTravelDateForRecentPackages({ date: holidayLandingGroupDto?.fromDate, fallBackDate: holidayLandingGroupDto?.packageDate}),
      subTitle: `Starting from ${rupeeFormatterUtils(startingPrice)} per person`,
    })
  }
  scrollTo = (sectionIndex, itemIndex, viewPosition) => {
    const { groupItems } = this.props.groupingData;
    if (this.listRef && groupItems && sectionIndex >= 0 && sectionIndex < groupItems.length) {
      this.listRef.scrollToLocation({
        sectionIndex,
        itemIndex,
        viewPosition,
      });
    }
  }
  scrollToNextSection = () => {
    if (this.listRef) {
      const offset = this.listScrollEvent.contentOffset.y + (Dimensions.get('screen').height >> 1);
      this.listRef._wrapperListRef._listRef.scrollToOffset({ offset, animated: true });
    }
  }
  closeVariantModal = (track) => {
    if (track) {
      this.props.trackLocalClickEvent({ eventName: 'back_variantcard' });
    }
    this.setState({
      isPackageVariantModal: false,
    });
  }
  getSectionName = ({ type, data }) => {
    return type === viewTypes.GROUP_RECENT_PACKAGES
      ? 'Recently Viewed'
      : data?.header || data?.name;
  };

  getSectionsData = (groupItems) => {
    const newSections = [];
    groupItems.forEach((dataItem, index) => {
      newSections.push({
        key: `section-${index}`,
        data: [dataItem],
        renderItem: ({ item }) => this._rowRenderer(item.type, item, index),
        sectionName: this.getSectionName({ type: dataItem?.type, data: dataItem?.data }),
      });
    });
    return newSections;
  };
  setPackageVariant = ({ data = {}, packageDetail, isHotel = false, groupName, groupIndex, index, variantTypeFromPokus }) => {
    const hotelLen = packageDetail?.hotelDetails?.categories?.length - 1;
    if (!this.isPackageVariantModal) {
      this.props.trackLocalClickEvent({
        eventName: `Variant_${isHotel ? variantEnum.HOTELS : data?.variantType}_${isHotel ? hotelLen + 1 : data?.packageVariants?.length
          }`,
      });
    }
    this.groupName = groupName;
    this.groupIndex = groupIndex;
    this.groupIndex = index;
    this.variantTypeFromPokus = variantTypeFromPokus;
    this.setState({
      isPackageVariantModal: !this.state.isPackageVariantModal,
      packageVariantData: { ...data, packageDetail },
    });
    this.isHotel = isHotel;
  }
  onPackageClickedLocal = (...params) => {
    const { onPackageClicked = () => { }, detailsData = {} } = this.props || {};
    onPackageClicked(...params, detailsData);
  }
  onPackageClickedFromVariantModal = ({ variant, data = null, packageDetail, categoryId, cardNo, length, groupName, groupIndex, index }) => {
    this.closeVariantModal();
    this.props.trackLocalClickEvent({
      eventName: `CardClick|Variant_${this.isHotel ? variantEnum.HOTELS : variant?.variantType
        }_${length}_pos_${cardNo}|package_${cardNo}_${packageDetail.id}_collection_${this.groupIndex}`,
    });
    const { groupingData, fabCta, pt, aff, rooms, selectedDate, detailsData = {} } = this.props || {};
    this.props.onPackageClickedFromVariant(
      packageDetail,
      null,
      groupingData,
      fabCta,
      this.groupName,
      pt,
      aff,
      this.groupIndex,
      this.groupIndex,
      data,
      categoryId,
      rooms,
      selectedDate,
      detailsData,
    );
  }
  getUuid = groupDataItem => groupDataItem?.data?.cards?.map(el => el.contextId) || [];

  onPressLocal = ({ card, data }) => {
    PhoenixSectionCardClickHandler.genericOnPressHandlingFromGrouping(card, data);
  }

  _rowRenderer = (type, groupDataItem, index) => {
    const { data = {} } = groupDataItem || {};
    const { toggleTIDeepLinkOpenCheck = null } = this.props || {};
    switch (type) {
      case viewTypes.GROUP_DEFAULT:
      case viewTypes.GROUP_DEFAULT_WITH_ACTIVITIES:
        return (
          <PhoenixGroupingSectionView
            key={`groupingView${groupDataItem.data.id}`}
            group={groupDataItem}
            packageDetails={this.state.packageDetails}
            walletDetail={this.props.groupingData.response.walletDetail}
            groupingData={this.props.groupingData}
            fabCta={this.props.fabCta}
            pt={this.props.pt}
            aff={this.props.aff}
            onPackageClicked={this.onPackageClickedLocal}
            openUrl={this.props.openUrl}
            handleDeeplink={PhoenixSectionCardClickHandler.genericOnPressHandlingFromGrouping}
            index={index}
            showPackageVariant={this.setPackageVariant}
            selectedDate={this.props.selectedDate}
          />
        );
      // case viewTypes.GROUP_ACME_DEFAULT:
      //   const acmeSharedModule = AcmeSharedModuleHolder.get();
      //   if (acmeSharedModule == null) {
      //     throw new Error('Acme-shared module not bootstrapped');
      //   }
      //   const { AFFILIATE_SOURCE } = acmeSharedModule.getSharedAcmeConstants();
      //   return (
      //     <ACMEHolidayGroupingView
      //       key={`groupingView${groupDataItem.data.id}`}
      //       xSellConfig={this.state.xSellConfig?.HOLIDAY}
      //       group={groupDataItem}
      //       source={AFFILIATE_SOURCE.HOLIDAYS}
      //       prevPage={HolidayDataHolder.getInstance().getPrevPageOmni(GROUPING_TRACKING_PAGE_NAME)}
      //     />
      //   );
      case viewTypes.GROUP_EMI_PERSUASION:
        return (
          <HolidayEmiPersuasion
            key={`emiPersuasion${groupDataItem.data.message}`}
            emiPersuasionData={groupDataItem.data}
            style={emiPersuasionStyle}
          />
        );
      case viewTypes.GROUP_FUNCTIONAL_PERSUASION:
        return (
          <HolidayFunctionalPersuasion
            key={`functionalPersuasion${groupDataItem.data.message}`}
            functionalPersuasionData={groupDataItem.data}
            groupingData={this.props.groupingData}
            fabCta={this.props.fabCta}
            section={groupDataItem}
          />
        );
      case viewTypes.GROUP_BLACK_PERSUASION:
        return (
          <HolidayBlackPersuasion
            key={`blackPersuasion${groupDataItem.data.message}`}
            blackPersuasionData={groupDataItem.data.mmtBlackDetail}
            groupingData={this.props.groupingData}
            fabCta={this.props.fabCta}
            section={groupDataItem}
          />
        );
      case viewTypes.GROUP_CASHBACK_PERSUASION:
        return (
          <HolidayCashbackPersuasion
            key={`cashbackPersuasion${groupDataItem.data.message}`}
            cashbackPersuasionData={groupDataItem.data}
            groupingData={this.props.groupingData}
            fabCta={this.props.fabCta}
            section={groupDataItem}
          />
        );
      case viewTypes.MMT_BLACK:
        return (
          <MembershipCard
            containerStyles={styles.membershipCard}
            contentType={CONTENT_TYPES.DETAIL}
            mmtBlackDetail={groupDataItem.data.mmtBlackDetail}
          />
        );
      case viewTypes.GROUP_WPM_PERSUASION:
        return (
          <HolidayWalletSurgePersuasion
            key={`HolidayWalletSurgePersuasion${groupDataItem.data.message}`}
            walletPersuasion={groupDataItem.data}
            groupingData={this.props.groupingData}
            fabCta={this.props.fabCta}
            section={groupDataItem}
            wpmToggle={() => { }}
          />
        );
      case viewTypes.GROUP_RECENT_PACKAGES:
        return (
          <RecentlyViewedCarousal
            key="recentPackageView"
            recentPackages={groupDataItem.data}
            group={groupDataItem}
            groupingData={this.props.groupingData}
            walletDetail={this.props.groupingData.response.walletDetail}
            fabCta={this.props.fabCta}
            pt={this.props.pt}
            aff={this.props.aff}
            onPackageClicked={this.onPackageClickedLocal}
          />
        );
      case viewTypes.GROUP_DID_YOU_KNOW:
        return (
          <View style={{ marginBottom: 20 }}>
            <HolidayDidYouKnowBanner
              key={`HolidayDidYouKnowBanner_${groupDataItem.data.id}`}
              data={groupDataItem.data}
              onPress={this.onPressLocal}
            />
          </View>
        );
      case viewTypes.GROUP_COLLECTIONS:
        return (
          <HolidayCollectionBanner
            key={`HolidayCollectionBanner_${groupDataItem.data.id}`}
            data={groupDataItem.data}
            onPressCard={this.onPressLocal}
            onPressExplore={this.onPressLocal}
            containerStyles={sectionBottomSpacing}
          />
        );
      case viewTypes.GROUP_SME:
        return (
          <SMEHorizontalSection
            key="SMESection"
            data={groupDataItem.data.cards}
            header={groupDataItem.data.header}
            subHeader={groupDataItem.data.subHeader}
            onCardClick={profileId => HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.SME_DETAIL, { profileId })}
            style={{ paddingVertical: 12 }}
          />);
      case viewTypes.PERSUASION_V2:
        return (
          <GroupingPersuasionV2
            group={groupDataItem}
            lastPage={'GROUPING'}
            trackLocalClickEvent={(eventName) =>
              this.props.trackLocalClickEvent({
                eventName: `PersuasionClick_${index}_${eventName}`,
              })
            }
          />
        );
      case viewTypes.DYNAD:
        //  const  getuuidLists = this.getUuid(groupDataItem);    will be required for multi-ad
        const { height, width } = getDimensionsForAd();
        return (
          <HolidayAdCard
            card={groupDataItem?.data?.cards?.[0]}
            data={groupDataItem?.data}
            adStyles={{ styles, resizeMode: 'cover' }}
            adDimensions={{ width, height }}
            onPress={() => { }}
          />
        );
      case viewTypes.GROUP_6E_INDIGO:
        return (
          <PhoenixGroupingSectionView
            key={'viewTypes.GROUP_6E_INDIGO'}
            group={{ data: groupDataItem.data.groupDetailsList[0] }}
            packageDetails={groupDataItem.data.packageDetails}
            groupingData={{ ...this.props.groupingData, host: AFFILIATES.INDIGO }}
            fabCta={this.props.fabCta}
            pt={this.props.pt}
            aff={this.props.aff}
            onPackageClicked={this.onPackageClickedLocal}
            handleDeeplink={PhoenixSectionCardClickHandler.genericOnPressHandlingFromGrouping}
            index={index}
            showPackageVariant={this.setPackageVariant}
          />
        );
      case viewTypes.TRAVELLER_STORIES:
        return (
          <TripIdeasTravelStories
            travelStoriesData={data}
            groupingData={this.props.groupingData}
            fabCta={this.props.fabCta}
            toggleTIDeepLinkOpenCheck={toggleTIDeepLinkOpenCheck}
            closeIntervention={this.props.closeIntervention}
          />
        );
      case viewTypes.DESTINATION_GUIDE:
        return (
          <TripIdeasDestinationGuide
            destinationGuide={data}
            groupingData={this.props.groupingData}
            fabCta={this.props.fabCta}
            toggleTIDeepLinkOpenCheck={toggleTIDeepLinkOpenCheck}
            closeIntervention={this.props.closeIntervention}
          />
        );
      default:
        return (
          <PhoenixGroupingSectionView
            key={`groupingView${groupDataItem.data.id}`}
            group={groupDataItem}
            packageDetails={this.state.packageDetails}
            groupingData={this.props.groupingData}
            fabCta={this.props.fabCta}
            pt={this.props.pt}
            aff={this.props.aff}
            onPackageClicked={this.onPackageClickedLocal}
            handleDeeplink={PhoenixSectionCardClickHandler.genericOnPressHandlingFromGrouping}
            index={index}
            showPackageVariant={this.setPackageVariant}
          />
        );
    }
  };

  async fetchMoreData() {
    const errorBody = {};
    const newGroups = await fetchGroupingData(
      this.state.holidayLandingGroupDto,
      this.state.nextOffset, [],
      errorBody, this.props.groupingData.searchQuery, false
    );
    let isAcmeRecomAdded = this.state.isAcmeRecomAdded;
    if (newGroups === null) {
      isAcmeRecomAdded = true;
      const groupItemsWithAcme = this.addACMERecommendationToGroup(cloneDeep(this.state.groupItems));
      this.setState({
        groupItems: groupItemsWithAcme,
        sectionData: this.getSectionsData(groupItemsWithAcme),
        noMoreGroups: true,
        loadingMoreItems: false,
        isAcmeRecomAdded,
      });
      trackHolidayGroupingLoadEvent({
        logOmni: false,
        pdtData: {
          pageDataMap: this.props.groupingData?.pageDataMap || createEmptyLoggingMap(),
          eventType: PDT_RAW_EVENT,
          activity: 'complete_scroll_down',
          requestId: this.props.groupingData.requestId,
          branch: this.props.groupingData.holidayLandingGroupDto.branch,
        },
      });
      return;
    }
    let newGroupItems = addGroupsToGroupData(cloneDeep(this.state.groupItems), newGroups.groupDetailsList);
    newGroupItems = addSectionsToGroupData(newGroupItems, this.state.sections);
    if (newGroups.noMoreGroups) {
      newGroupItems = this.addACMERecommendationToGroup(newGroupItems);
      isAcmeRecomAdded = true;
    }
    this.setState({
      groupItems: newGroupItems,
      sectionData: this.getSectionsData(newGroupItems),
      groupData: this.state.groupData ? this.state.groupData.concat(newGroups.groupDetailsList) : newGroups.groupDetailsList,
      packageDetails: this.state.packageDetails.concat(newGroups.packageDetails),
      nextOffset: newGroups.nextOffset,
      noMoreGroups: newGroups.noMoreGroups,
      loadingMoreItems: false,
      isAcmeRecomAdded,
    });
    trackHolidayGroupingLoadEvent({
      logOmni: true,
      omniPageName: GROUPING_TRACKING_PAGE_NAME,
      pdtData: {
        pageDataMap: this.props.groupingData.pageDataMap ? this.props.groupingData.pageDataMap : createEmptyLoggingMap(),
        eventType: PDT_PAGE_VIEW,
        activity: PDT_PAGE_LOAD,
        requestId: this.props.groupingData.requestId,
        branch: this.props.groupingData.holidayLandingGroupDto.branch,
      },
    });
  }

  addACMERecommendationToGroup = (newGroupItems) => {
    const { acmeRecomData } = this.props;
    const isAcmeRecomEnabled = getAbConfig(AbConfigKeyMappings.isAcmeRecomEnabled, true);
    if (isMobileClient() && isAcmeRecomEnabled
      && !this.state.isAcmeRecomAdded
      && acmeRecomData
      && acmeRecomData.isSuccess) {
      return newGroupItems.concat(acmeRecomData.affiliateResponse || []);
    }
    return newGroupItems;
  }


  handleListEnd = () => {
    if (!this.state.noMoreGroups && !this.state.loadingMoreItems) {
      this.setState({
        loadingMoreItems: true,
      }, () => {
        this.fetchMoreData();
      });
    }
  };

  renderHeader = () => {
    const { branch } = this.props.groupingData.holidayLandingGroupDto;
    const isSafe = getBannerDetails(branch) !== null;
    const {deviceTypes}=this.props || {};
    return (
      <View style={styles.sectionSpacing}>
        <Fragment>
          <PhoenixListingHeader
            {...this.props.headerProps}
            groupingData={this.props.groupingData}
            fabCta={this.props.fabCta}
            trackClickEvent={this.props.trackLocalClickEvent}
          />
          {isMobileClient() && <TripIdeasEntrySection
            entrySectionData={this.props.groupingData.tripIdeasEntrySection}
            toggleTIDeepLinkOpenCheck={this.props.toggleTIDeepLinkOpenCheck}
            groupingData={this.props.groupingData}
            fabCta={this.props.fabCta}
            closeIntervention={this.props.closeIntervention}
          />}
        </Fragment>
        {this.props.filterRemoved ?
          <View style={{ paddingHorizontal: 10, paddingBottom: 10, backgroundColor: 'white' }}>
            <HolidaysMessageStrip
              shouldShow={true}
              message={'We had to remove your Filter selection as no matching results were found. Please re-apply the required Filters.'}
              trackEvent={this.trackNoPckFoundMessageShown}
            />
          </View> : null}
          {isdisplayPoweredbyMMT(this.props.aff, deviceTypes) &&<View style={styles.affBanner}>
          <PoweredByMMT />
        </View>}
        {!isNonMMTAffiliate(this.props.aff) && isSafe && <HolidaySafeBanner branch={branch} />}
      </View>
    );
  }
  renderFooter = () => (
    this.state.loadingMoreItems ?
      <ActivityIndicator style={{ margin: 10 }} size="large" color="black" /> :
      <View style={{ height: 50 }} />
  );

  trackNoPckFoundMessageShown = () =>
    this.props.trackLocalClickEvent({ eventName: 'enhancement_no_pkg_found' });

  handleListScroll = (event) => {
    if (this.props.onScroll) {
      this.props.onScroll(event);
    }
    this.listScrollEvent = {
      contentOffset: event.nativeEvent.contentOffset,
      contentSize: event.nativeEvent.contentSize,
    };
  }

  onViewableItemsChanged = ({ viewableItems, changed }) => {
    viewableItems?.forEach((viewableItem) => {
      const { section = {}, isViewable } = viewableItem;
      const { sectionName, data, key } = section;
      if (!sectionName || !isViewable) {
        return;
      }
      if (!this.groupingPageSectionVisits[sectionName]) {
        this.groupingPageSectionVisits[sectionName] = 1;
        setPageSectionVisitResponse({
          pageName: sectionTrackingPageNames.PHOENIX_GROUPING_PAGE,
          value: this.groupingPageSectionVisits,
        });
        const eventName = `Viewed_${data?.[0]?.type}_${key}`;
        this.props.trackLocalClickEvent({ eventName, prop1: sectionName });
      }
    });
  };
 handleBottomSheet = () => {
   this.closeVariantModal(true)
}
  render() {
    return (
      <Fragment>
        <SectionList
          ref={ref => this.listRef = ref}
          sections={this.state.sectionData}
          ListHeaderComponent={this.renderHeader()}
          ListFooterComponent={this.renderFooter()}
          onEndReached={({ distanceFromEnd }) => {
            if (distanceFromEnd < -1) {
              return;
            }
            this.handleListEnd();
          }}
          stickySectionHeadersEnabled
          stickyHeaderIndices={[0]}
          onScroll={this.handleListScroll}
          scrollEventThrottle={20}
          showsVerticalScrollIndicator={false}
          onScrollToIndexFailed={() => { }}
          bounces={false}
          contentContainerStyle={styles.sectionListContainer}
          onViewableItemsChanged={this.onViewableItemsChanged}
        />
        {this.state.isPackageVariantModal && (
          <BottomSheetOverlay
            title={this.state.packageVariantData?.packageDetail?.name}
            visible={this.state.isPackageVariantModal}
            toggleModal={this.handleBottomSheet}
            containerStyles={styles.bottomsheetContainer}
          >
            <PhoenixVariantModal
              packageVariantData={this.state.packageVariantData}
              close={() => this.closeVariantModal(true)}
              variantTypeFromPokus={this.variantTypeFromPokus}
              onPackageClickedFromVariantModal={this.onPackageClickedFromVariantModal}
              isHotel={this.isHotel}
              departureCity={this.props.departureCity}
              rooms={this.props.rooms}
            />
          </BottomSheetOverlay>
        )}
      </Fragment>


    );
  }
}

const styles = StyleSheet.create({
  headingContainerStyles: {
    padding: 20
  },
  sectionListContainer: {
    backgroundColor: holidayColors.lightGray2,
  },
  sectionSpacing: {
    marginBottom: 20,
  },
  closeButtonStyle: {
    position: 'absolute', top: 15, right: 15,
  },
  adCardContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 8
  },
  adImage: {
    width: getDimensionsForAd()?.width,
    height: getDimensionsForAd()?.height,
    borderRadius: 8,
  },
  bottomsheetContainer: {
    padding: 20,
    paddingBottom: 40,
  },
});

PhoenixGroupingListView.propTypes = {
  onScroll: PropTypes.func.isRequired,
  groupingData: PropTypes.object.isRequired,
  fabCta: PropTypes.object.isRequired,
  onPackageClicked: PropTypes.func.isRequired,
  acmeRecomData: PropTypes.object,
  pt: PropTypes.string,
  aff: PropTypes.string,
  tabsData: PropTypes.array,
  enableHolidaysSuitableForHeaderFilters: PropTypes.bool,
};

PhoenixGroupingListView.defaultProps = {
  pt: null,
  aff: null,
  tabsData: [],
  enableHolidaysSuitableForHeaderFilters: false,
};

const emiPersuasionStyle = {
  marginTop: 2,
};


const mapStateToProps = state => ({
  groupingData: state.holidaysGrouping.groupingData,
  acmeRecomData: state.holidaysGrouping.acmeRecomData,
  fabCta: state.holidaysGrouping.fabCta,
});

const mapDispatchToProps = () => ({
  onPackageClicked: (
    packageDetails, userPackageMeta, groupingData,
    fabCta, groupName, pt, aff, groupIndex, index, selectedDate, detailsData
  ) =>
    openPhoenixPackagePageNew({
      pt,
      aff,
      index,
      fabCta,
      groupName,
      groupIndex,
      detailsData,
      selectedDate,
      groupingData,
      packageDetails,
      userPackageMeta,
    }),
  onPackageClickedFromVariant: (
    packageDetails, userPackageMeta, groupingData,
    fabCta, groupName, pt, aff, groupIndex, index, params, categoryId, rooms, selectedDate, detailsData,
  ) => (
    openPhoenixPackagePageNew({
      pt,
      aff,
      index,
      fabCta,
      groupName,
      groupIndex,
      groupingData,
      packageDetails,
      userPackageMeta,
      params,
      categoryId,
      rooms,
      selectedDate,
      detailsData,
    })
  ),
});

export default connect(mapStateToProps, mapDispatchToProps, null, { withRef: true })(PhoenixGroupingListView);
