import { StyleSheet } from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
export const PHOENIX_ACTIVITY_COLLECTION_CARD_HEIGHT = 200;

export default StyleSheet.create({
  // Activity Card Styles
  card: {
    width: 136,
    height: PHOENIX_ACTIVITY_COLLECTION_CARD_HEIGHT,
    backgroundColor: holidayColors.white,
    borderRadius: 4,
    ...getPlatformElevation(3),
  },
  cardHead: {
    backgroundColor: '#f4e7e6',
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
    padding: 10,
  },
  packageHeading: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
    textAlign: 'center',
  },
  activityHeading: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.black,
  },
  cardFooter: {
    backgroundColor: '#f7f8f9',
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  imgHotel: {
    width: '100%',
    height: 54,
    resizeMode: 'cover',
  },
  starImg: {
    width: 8,
    height: 8,
    resizeMode: 'contain',
  },
  cardContent: {
    padding: 8,
    height: 135,
    justifyContent: 'space-between',
  },
  suitableForText: {
    fontSize: 10,
    fontFamily: fonts.italic,
    color: holidayColors.red,
    paddingTop: 3,
  },
  activitiesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  activityPointer: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.black,
    marginRight: 5,
  },
  activityText: {
    fontSize: 10,
    fontFamily: fonts.regular,
    color: holidayColors.gray,
    paddingRight: 4,
  },
  moreActivitiesContainer: {
    marginTop: 5,
    marginLeft: 12,
  },
  moreActivitiesText: {
    fontSize: 10,
    color: holidayColors.gray,
    fontFamily: fonts.regular,
  },
  priceSymbol: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  priceText: {
    fontSize: 11,
    fontFamily: fonts.regular,
    color: holidayColors.gray,
  },
  viewDetailsBtn: {
    fontSize: 10,
    fontFamily: fonts.black,
    color: holidayColors.primaryBlue,
  },
  starWrap: {
    paddingRight: 2,
  },
  activityList: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  priceWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 'auto',
  },
  imgHotelLogoWrap: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: holidayColors.white,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    right: 5,
    top: 35,
    ...getPlatformElevation(3),
  },
  imgHotelLogo: {
    width: '70%',
    resizeMode: 'contain',
  },
  cardWrapper: {
    marginRight: 8,
  },

  // Collection Styles
  collectionWrapper: {
    paddingVertical: 15,
  },
  collectionHead: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingLeft: 20,
    paddingRight: 12,
    paddingVertical: 6,
    backgroundColor: holidayColors.white,
  },
  durationText: {
    fontSize: 10,
    fontFamily: fonts.bold,
    color: holidayColors.gray,
  },
  titleText: {
    fontSize: 10,
    fontFamily: fonts.bold,
    color: holidayColors.black,
  },
  subHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subTitleText: {
    fontSize: 26,
    fontFamily: fonts.black,
    color: holidayColors.black,
  },
  destinationText: {
    fontSize: 10,
    fontFamily: fonts.bold,
    color: holidayColors.black,
  },
  inclusionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inclusionCount: {
    fontSize: 10,
    fontFamily: fonts.bold,
    color: holidayColors.black,
  },
  textWithShadow: {
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: -1, height: 1 },
    textShadowRadius: 10,
  },
  durationTag: {
    backgroundColor: holidayColors.lightGray,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 5,
    borderRadius: 10,
    marginLeft: 5,
  },
  destinations: {
    flexDirection: 'row',
    marginBottom: 2,
  },
  iconFlight: {
    width: 10,
    height: 10,
    resizeMode: 'cover',
  },
  iconTransfer: {
    width: 10,
    height: 10,
    resizeMode: 'cover',
  },
  iconHotel: {
    width: 7.5,
    height: 9,
    resizeMode: 'cover',
  },
  iconVisa: {
    width: 6,
    height: 8,
    resizeMode: 'cover',
  },
  headerRightWrap: {
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  contentContainerStyle: {
    margin: 10,
    flexDirection: 'row',
    paddingLeft: 10,
    paddingRight:20,
  },
  width70: { width: '70%' },
  width30: { width: '30%' },
});
