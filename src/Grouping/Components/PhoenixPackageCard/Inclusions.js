import { getImageUrl, IMAGE_ICON_KEYS } from 'mobile-holidays-react-native/src/Common/Components/HolidayImageUrls';
import React from 'react';
import { Image, Text, View, StyleSheet } from 'react-native';
import {capitalizeText} from '../../../../src/utils/textTransformUtil'

export const INCLUSIONS = {
  hotels: {
    title: 'Hotels',
    icon: require('@mmt/legacy-assets/src/holidays/inc-hotel.webp'),
    hotelCollectionIcon : '',
    order: 1,
  },
  flights: {
    title: 'Flights',
    icon: require('@mmt/legacy-assets/src/holidays/inc-flight.webp'),
    hotelCollectionIcon: getImageUrl(IMAGE_ICON_KEYS.FLIGHT_INCLUSION),
    order: 0,
  },
  activities: {
    title: 'Activities',
    icon: require('@mmt/legacy-assets/src/holidays/inc-activity.webp'),
    hotelCollectionIcon: getImageUrl(IMAGE_ICON_KEYS.ACTIVITY_INCLUSION),
    order: 2,
  },
  transfers: {
    title: 'Transfers',
    icon: require('@mmt/legacy-assets/src/holidays/inc-car.webp'),
    hotelCollectionIcon : getImageUrl(IMAGE_ICON_KEYS.TRANSFER_INCLUSION),
    order: 3,
  },
};

export const EXTRA_INCLUSION = {
  inclusion: {
    title: 'Inclusions',
    lowerCaseTitle: 'inclusions',
  },
  visa: {
    title: 'Visa',
    lowerCaseTitle: 'visa',
    icon: require('@mmt/legacy-assets/src/holidays/inc-visa.webp'),
    hotelCollectionIcon: getImageUrl(IMAGE_ICON_KEYS.VISA_INCLUSION),
    order: 4,
    count: 1,
  },
};

export const getInclusionArray = (packageDetail, { includeVisa = false } = {}) => {
  const components = packageDetail?.listingStaticData?.componentTags || {};
  const { inclusionDetails = {} } = packageDetail || {};
  let uniqueKeys = {
    hotels: 1,
    flights: 0,
    activities: 0,
    transfers: 0,
  };

  /* Create Array with Unique keys and add all data available */
  let inclusionArray = Object.keys(uniqueKeys)
    .map((key) => {
      return { ...INCLUSIONS?.[key], ...components?.[key] };
    })
    .sort((d1, d2) => d1.order - d2.order);

  /* Add Extra Inclusions in the inclusion array */
  if (includeVisa && inclusionDetails.visa) {
    inclusionArray.push({ ...EXTRA_INCLUSION.visa });
  }

  /* Update Inclusion Array for different cases */
  Object.keys(components).map((component) => {
    switch (component) {
      case EXTRA_INCLUSION.inclusion.lowerCaseTitle:
        /* Adding Count of inclusion in Activites count */
        const activityIndex = inclusionArray.findIndex(
          (item) => item.title === INCLUSIONS.activities.title,
        );
        const activityInclusion = inclusionArray[activityIndex] || {};
        const inclusionCount = components[EXTRA_INCLUSION.inclusion.lowerCaseTitle].count || 0;
        if (activityInclusion.count) {
          activityInclusion.count += inclusionCount;
        } else {
          activityInclusion.count = inclusionCount;
        }
        activityInclusion.isInclusionIncluded = true;
        /* Adding Count of inclusion in Activites count */
        break;
      default:
        // do nothing
        break;
    }
  });
  return inclusionArray;
};

export const getInclusion = ((elm) => {
  switch (elm) {
    case 'hotels': return elm;
      break;
    case 'flights': return elm;
      break;
    case 'activities': return elm;
      break;
    case 'visa': return elm;
      break;
    case 'carItinerary':
    case 'cityDrops':
    case 'airportTransfers': return 'transfers';
      break;
  }
});

const MAX_INCLUSIONS = 5;

export default ({ inclusions }) => {

  const renderIconAndText = inc => (
    <View style={{ justifyContent: 'center', alignItems: 'center', marginRight: 4 }}>
      <Image style={styles.icon} source={inc.icon} />
      <Text style={styles.text}>{capitalizeText(inc.title)}</Text>
    </View>
  );
  const renderIcons = inc => (
    <View style={{ justifyContent: 'center', alignItems: 'center', marginRight: 4 }}>
      <Image style={styles.icon} source={inc.icon} />
    </View>
  );
  const showIconsOnly = inclusions.length > MAX_INCLUSIONS;
  return ((inclusions && inclusions.length) ?
    <View style={styles.container}>
      {
        inclusions.map(inc => (
          showIconsOnly ? renderIcons(inc) : renderIconAndText(inc)
        ))
      }
    </View> : null
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  icon: {
    width: 14,
    height: 14,
    resizeMode: 'contain',
    marginBottom: 2,
  },
  text: {
    fontSize: 10,
    color: '#000000',
    fontFamily: 'Lato-Regular',
  },
});
