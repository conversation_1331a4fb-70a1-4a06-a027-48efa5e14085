import React from 'react';
import {ImageBackground, StyleSheet, TouchableOpacity} from 'react-native';
import {isEmpty} from 'lodash';
import {convertUrlToHttps} from '../../../utils/HolidayNetworkUtils';
import {isValidURL} from '@mmt/legacy-commons/Helpers/validationHelpers';
import ContentCardModalContainer from 'mobile-holidays-react-native/src/Common/Components/ContentCardModal/ContentCardModalContainer';
import queryString from 'query-string';
import { PHOENIX_HOTEL_COLLECTION_CARD_HEIGHT } from '../PhoenixHotelCollection/constants';
import { PHOENIX_ACTIVITY_COLLECTION_CARD_HEIGHT } from '../PhoenixActivityCollection/styles';
import { PHOENIX_ACTIVITY_CARD_TYPE, PHOENIX_HOTEL_CARD_TYPE } from '../PhoenixGroupingSectionView';
import { PACKAGE_OUTER_CARD_HEIGHT } from './styles';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

export const IMAGE_WIDTH = 142;
export const IMAGE_HEIGHT = 295;

export default ({index, deepLink, imageUrl, group, groupingData, fabCta, id,modalVisible,closeModal,onContentCardClick, type,outerCardStyle}) => {
  let openPopUp = false;
  let  profileId;
  if (isValidURL(deepLink)) {
    const { query } = queryString.parseUrl(deepLink);
    profileId = query.profileId;
    let pop = query.pop;
    if (!isEmpty(profileId) && !isEmpty(pop) && pop) {
      openPopUp = true;
    }
  }



  if (isEmpty(imageUrl)){
    return [];
  }

  const contentCardDimensions = {
    // Maintain content card ratio at 142 : 295
    width: (IMAGE_WIDTH / IMAGE_HEIGHT) * PACKAGE_OUTER_CARD_HEIGHT,
    height: PACKAGE_OUTER_CARD_HEIGHT,
  };
  if (type === PHOENIX_HOTEL_CARD_TYPE) {
    contentCardDimensions.height = PHOENIX_HOTEL_COLLECTION_CARD_HEIGHT;
    contentCardDimensions.width =
      (IMAGE_WIDTH / IMAGE_HEIGHT) * PHOENIX_HOTEL_COLLECTION_CARD_HEIGHT;
  }
  if (type === PHOENIX_ACTIVITY_CARD_TYPE) {
    contentCardDimensions.height = PHOENIX_ACTIVITY_COLLECTION_CARD_HEIGHT;
    contentCardDimensions.width =
      (IMAGE_WIDTH / IMAGE_HEIGHT) * PHOENIX_ACTIVITY_COLLECTION_CARD_HEIGHT;
  }

  return (
    <>
    <TouchableOpacity
    style={[
      contentCardDimensions,
      styles.container,
      { marginLeft: index === 0 ? 16 : 0 },
      outerCardStyle,
    ]}
    onPress={() => onContentCardClick(deepLink,openPopUp,id,group,groupingData,fabCta,index)}
  >
    <ImageBackground style={[styles.image, contentCardDimensions]} resizeMode="stretch" source={{uri: convertUrlToHttps(imageUrl)}} />
  </TouchableOpacity>
      {openPopUp &&
  <ContentCardModalContainer modalVisible={modalVisible} closeModal={closeModal} profileId={profileId}  />
  }
  </>

  );
};

const styles = StyleSheet.create({
  container: {
    marginRight: 10,
    ...holidayBorderRadius.borderRadius16,
  },
  image: {
    resizeMode: 'contain',
    overflow: 'hidden',
    ...holidayBorderRadius.borderRadius16,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
});
