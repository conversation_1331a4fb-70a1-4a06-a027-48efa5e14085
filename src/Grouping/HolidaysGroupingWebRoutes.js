import React from 'react';
import { View } from 'react-native';
import { Route, Switch, withRouter } from 'react-router';
import { connect } from 'react-redux';
import HolidaysGrouping from './Containers/HolidayGroupingContainer';
import { injectAsyncReducer } from '@mmt/legacy-commons/AppState/asyncStore';
import { withRouterState } from 'web/WebRouter';
import HolidaysWebRoute from '../HolidayWebRoute';
import HolidayDeeplinkParser from '../utils/HolidayDeeplinkParser';

/* Reducers */
import holidaysGrouping from './Reducers/HolidayGroupingReducers';
import holidaysPhoenixGroupingV2 from '../PhoenixGroupingV2/Reducers';
import holidaysSearchWidget from '../SearchWidget/Reducers/HolidaySearchWidgetReducers';
import holidaysDetail from '../PhoenixDetail/Reducers/HolidayDetailReducers';
class HolidaysGroupingWeb extends HolidaysWebRoute {
  constructor(props) {
    super(props, 'holidaysGrouping');
    const isDeeplink = HolidayDeeplinkParser.isDeviceTypeAvailableinLink(window.location.href)
    let holidaysGroupingData = HolidayDeeplinkParser.parseListingGroupingPageDeeplink(
      window.location.href,isDeeplink
    );
    this.state = {
      ...holidaysGroupingData,
      ...this.props,
    };
  }

  render() {
    return <HolidaysGrouping {...this.state} />;
  }
}

const mapStateToProps = (state) => ({
  ...state.holidaysGrouping,
});

injectAsyncReducer('holidaysGrouping', holidaysGrouping);
injectAsyncReducer('holidaysPhoenixGroupingV2', holidaysPhoenixGroupingV2);
injectAsyncReducer('holidaysSearchWidget', holidaysSearchWidget);
injectAsyncReducer('holidaysDetail', holidaysDetail);

export const HolidaysGroupingContainer = connect(mapStateToProps, null)(HolidaysGroupingWeb);

const HolidaysGroupingRoutes = () => (
  <View style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
    <Switch>
      <Route
        exact
        path="/holidays/international/group"
        component={withRouterState(HolidaysGroupingContainer)}
      />
      <Route
        exact
        path="/holidays/india/group"
        component={withRouterState(HolidaysGroupingContainer)}
      />
      <Route
        exact
        path="/holidays/international/search"
        component={withRouterState(HolidaysGroupingContainer)}
      />
      <Route
        exact
        path="/holidays/india/search"
        component={withRouterState(HolidaysGroupingContainer)}
      />
    </Switch>
  </View>
);

export default withRouter(HolidaysGroupingRoutes);
