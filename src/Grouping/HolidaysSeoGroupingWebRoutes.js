import React from 'react';
import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import {connect} from 'react-redux';
import HolidaysGrouping from './Containers/HolidayGroupingContainer';
import {injectAsyncReducer} from '@mmt/legacy-commons/AppState/asyncStore';
import holidaysGrouping from './Reducers/HolidayGroupingReducers';
import {withRouterState} from 'web/WebRouter';
import url from 'url';
import HolidaysWebRoute from '../HolidayWebRoute';

class HolidaysSeoGroupingWeb extends HolidaysWebRoute {

  constructor(props) {
    super(props, 'holidaysGrouping');
    const urlObj = url.parse(window.location.href, window.location.search);
    const {path} = urlObj;
    const pathArr = path.split('/');
    const seoPageName = pathArr[pathArr.length - 1];
    let filters = [];
    const tokenArr = seoPageName.split('-');
    let destinationCity = tokenArr[0].replace(/_/g, ' ');
    if (seoPageName.indexOf('-in-') !== -1) {
        destinationCity = tokenArr[tokenArr.length - 1].split('.')[0].replace(/_/g, ' ');
    }
    let departureCity = '';
    if (seoPageName.indexOf('-travel-packages') === -1 &&
      seoPageName.indexOf('-vacation-tour-packages') === -1 &&
      seoPageName.indexOf('-tour-packages') === -1 &&
      seoPageName.indexOf('-packages-from-') === -1 &&
      seoPageName.indexOf('-in-') === -1 &&
      seoPageName.indexOf('-packages') !== -1) {
      let themeIndex = 1;
      if (tokenArr.length === 2) {
        destinationCity = '';
        themeIndex = 0;
      }
      filters = [
        {
          id: 18,
          values: [tokenArr[themeIndex].replace(/_/g, ' ')],
        },
      ];
    }
    if (seoPageName.indexOf('-packages-from-') !== -1) {
      const depCityArr = tokenArr[3].split('.');
      departureCity = depCityArr[0].replace(/_/g, ' ');
    }
    const refreshLanding = '';
    const fromSeo = true;
    this.state = {
      destinationCity,
      departureCity,
      filters,
      refreshLanding,
      fromSeo,
    };
  }

  render() {
    return (
      <HolidaysGrouping {...this.state} />
    );
  }
}

const mapStateToProps = state => ({
  ...state.holidaysGrouping,
});

injectAsyncReducer('holidaysGrouping', holidaysGrouping);

export const HolidaysSeoGroupingContainer = connect(mapStateToProps, null)(HolidaysSeoGroupingWeb);

const HolidaysSeoGroupingRoutes = () => (
  <View style={{
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
  }}>
    <Switch>
      <Route path="/holidays-international"
             component={withRouterState(HolidaysSeoGroupingContainer)}/>
      <Route path="/holidays-india" component={withRouterState(HolidaysSeoGroupingContainer)}/>
    </Switch>
  </View>
);

export default withRouter(HolidaysSeoGroupingRoutes);
