import React, { useCallback,useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import { View, StatusBar, StyleSheet, Platform, BackHandler } from 'react-native';
import { holidayColors } from '../../Styles/holidayColors';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { FROM_LISTING_GROUPING_DEEPLINK, FUNNEL_ENTRY_TYPES, PDT_RAW_EVENT } from '../../HolidayConstants';
import { entryLocalNotification, exitLocalNotification, isRawClient } from '../../utils/HolidayUtils';
import { GROUPING_LOCAL_NOTIFICATION_PAGE_NAME } from '../../Grouping/HolidayGroupingConstants';
import { createHolidayLandingGroupingData , createListingViewData } from '../Utils/PhoenixGroupingV2Utils';
import BranchIOTracker from '../../utils/HolidayBranchSDKEventTracker';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import { getTIDeeplinkOpened, setTIDeeplinkOpened } from '../../Common/Components/TripIdeas/utils';
import { HARDWARE_BACK_PRESS } from '../../SearchWidget/SearchWidgetConstants';
import withIntervention from '../../Common/Components/Interventions/withIntervention';
import {
  GROUPING_PAGE_VERSION,
  isLeaveIntent,
  PHOENIX_GROUPING_V2_TRACKING_EVENTS,
} from '../Contants';
import { PHOENIX_GROUPING_V2_PAGE_NAMES } from '../Contants';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import { trackHolidayGroupingLoadEvent } from '../../utils/HolidayGroupingTrackingUtils';
import { isEmpty } from 'lodash';
import {
  createPhoenixGroupingV2PdtData,
  trackPhoenixGroupingV2ClickEvent,
} from '../Utils/PhoenixGroupingV2TrackingUtils';
import {
  clearPageVisitResponses,
  sectionTrackingPageNames,
} from '../../utils/SectionVisitTracking';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import useBackHandler from '../../hooks/useBackHandler';

/* Action Imports */
import {
  initialLoadAction,
  resetPhoenixGroupingV2Data,
  holidayLandingGroupDtoUpdate,
} from '../Actions/groupingV2Actions';
import { fetchFabCtaAction } from '../Actions/fabCtaActions';

/* Component Imports */
import PhoenixGroupingV2Loader from './PhoenixGroupingV2Loader';
import PhoenixGroupingV2Error from './PhoenixGroupingV2Error';
import PhoenixGroupingV2Page from './PhoenixGroupingV2Page';
import { clearCuesStepPositions } from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCueStepsUtils';
import { trackDeeplinkRececived } from '../../utils/HolidayTrackingUtils';
import { clearViewTackingObj, getViewTrackingObj } from '../Utils/SectionTrackingUtils';
import { showNewRVSSection } from '../../utils/HolidaysPokusUtils';
import {
  initGroupingPDTObj,
  logHolidaysGroupingPDTEvents,
  onUpdateSearchWidgetPDT,
} from '../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { EVENT_NAMES, PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { setMmtBlackReviewPagePopupFlag } from '../../utils/HolidayNetworkUtils';
import { initAbConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';

const isFocus = Platform.OS === 'web' ? { focus: true } : useIsFocused(); // Always true for web, use hook for native

import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import { modifyPDTObj } from '../../utils/HolidayPDTTrackingV3';
import HolidayPDTMetaIDHolder from '../../utils/HolidayPDTMetaIDHolder';
import { HolidayNavigation } from '../../Navigation';
let isGuidedSearchShown = false;
let interventionShowed = false;
let coachMarkShown = false;
// let interventionPaused = false;

const initialSetup = (holidayLandingGroupDto) => {
  const { destinationCity = '' } = holidayLandingGroupDto || {};
  entryLocalNotification(destinationCity, GROUPING_LOCAL_NOTIFICATION_PAGE_NAME);
  BranchIOTracker.trackPageView({
    pageName: BranchIOTracker.PAGE.GROUPING,
    [BranchIOTracker.KEYS.EXTRA_DATA]: {},
  });
};

const PhoenixGroupingV2 = (props) => {
  const {
    initialLoadAction = null,
    fetchFabCtaAction = null,
    resetPhoenixGroupingV2Data = null,
    listingPackagesState = null,
    holidayLandingGroupDtoUpdate,
    fabCta = {},
    metaDataState = {},
    userDepCity = '',
    error = {},
    rootTag,
    searchQuery,
    destinationCityData,
    updateInterventionData,
    pauseIntervention,
    playIntervention,
    leaveIntent,
    close,
    unmountInterention,
    filterData = {},
    recentlySeenPackages,
    /* only use for click events data */
    updatedHolidayLandingGroupDtoState,
    groupMetaState={},
    recentPackages,
    groupSections,
    ...rest // Add back the rest parameter
  } = props || {};
  const {
    isMetaDataLoading = false,
    isMetaDataSuccess = false,
    isMetaDataError = false,
    metaDataDetail = {},
  } = metaDataState || {};
  const focus = isFocus;
  const { headerDetail = {}} = metaDataDetail || {};
  const { subTitle = 'Holiday Packages' } = headerDetail || {};
  const { isListingPackageDataLoading, isListingPackageDataError } = listingPackagesState || {};
  const isFromDeeplink = props[FROM_LISTING_GROUPING_DEEPLINK];
  const holidayLandingGroupDto = createHolidayLandingGroupingData(props); // this is being used in loader and page
  const [showFilterBottomsheet, setShowFilterBottomsheet] = useState(false); // used to open filter page as bottom sheet from intervention
  const [showCoachMarks, setShowCoachMarks] = useState(false);
  const [popupValue, setPopup] = useState('');
  const popupValueRef = useRef(null);
  const prevShowCoachMarks = useRef(showCoachMarks);
  const pdtLogIndex= useRef(0);
  const reduxStateRef = useRef({
    listingPackagesState,
    groupMetaState,
    metaDataState,
    recentPackages,
    groupSections,
  });
  const hasCalledPDTRef = useRef(false);
  const hasInitializedGroupingRef = React.useRef(false);
  const lastProcessedMetadataRef = useRef(null);

  const initGroupingV2Page = async() => {
        await initAbConfig();
        await HolidayDataHolder.getInstance().setSubFunnel();
        initGroupingPDTObj({ holidayLandingGroupDto, isMetaDataError });
        logHolidaysGroupingPDTEvents({
          actionType: PDT_EVENT_TYPES.pageEntry,
          value:EVENT_NAMES.PAGE_ENTRY,
          shouldTrackToAdobe:false
        });
  }

  useEffect(() => {
    HolidayDataHolder.getInstance().setCmp(holidayLandingGroupDto.cmp);
    HolidayPDTMetaIDHolder.getInstance().setPdtId();
    HolidayDataHolder.getInstance().setFunnelEntry(FUNNEL_ENTRY_TYPES.ONLINE)
    if (holidayLandingGroupDto?.banner) {
      HolidayDataHolder.getInstance().setBanner(holidayLandingGroupDto.banner);
    }
    if (isFromDeeplink && holidayLandingGroupDto.cmp) {
      trackDeeplinkRececived({ [TRACKING_EVENTS.M_V81]: holidayLandingGroupDto.cmp });
    }
    const fetchInitialLoadData = async () => {
      initialSetup(holidayLandingGroupDto);
      await initialLoadAction({
        holidayLandingGroupDto,
        searchQuery,
        resetIfPackageNotFound: true,
        isFromDeeplink,
        trackLocalClickEvent,
      });
    };
    clearPageVisitResponses({
      pages: [
        sectionTrackingPageNames.PHOENIX_GROUPING_V2_PAGE,
        sectionTrackingPageNames.PHOENIX_GROUPING_PAGE_V2_RVS,
        sectionTrackingPageNames.PHOENIX_GROUPING_PAGE_V2_RVS_V2,
      ],
    });

    const initializeGrouping = async () => {
      await initGroupingV2Page();
      hasInitializedGroupingRef.current = true;
    };

    resetPhoenixGroupingV2Data();
    clearCuesStepPositions();
    fetchInitialLoadData();
    initializeGrouping();

    return () => {
      interventionShowed = false;
      coachMarkShown = false;
      isGuidedSearchShown = false;
    };
  }, []);

  // Call onUpdateSearchWidgetPDT whenever metadata API call gives success
  useEffect(() => {
    const metaDataDetail = props.metaDataState.metaDataDetail;
    const isSuccess = props.metaDataState.isMetaDataSuccess;

    // Check if metadata API was successful and we have new metadata
    if(
      isSuccess &&
      !isEmpty(metaDataDetail) &&
      metaDataDetail !== lastProcessedMetadataRef.current
    ){
      const {departureCity={},destinationCities=[]}=metaDataDetail.destinationMeta || {}
      const {locusDetails:depLocusDetail}=departureCity
      const {locusDetails:destlocusDetail}=destinationCities[0] || {}
      const {locusCode:depLocusCode}=depLocusDetail || {}
      const {locusCode:destLocusCode}=destlocusDetail || {}

      onUpdateSearchWidgetPDT({
        holidayLandingGroupDto:{
          ...updatedHolidayLandingGroupDtoState,
          destinationMeta:metaDataDetail.destinationMeta,
          departureLocusCode:depLocusCode,
          destinationCityData:destLocusCode
        }
      })

      // Update the reference to track that we've processed this metadata
      lastProcessedMetadataRef.current = metaDataDetail;
    }
  },[props.metaDataState.metaDataDetail, props.metaDataState.isMetaDataSuccess, updatedHolidayLandingGroupDtoState])

  // Use Effect to enable auto MMT Black Bottomsheet Popup on Review page
  useEffect(() => {
    if (focus) {
      setMmtBlackReviewPagePopupFlag(true);
    }
  }, [focus]);
  /* Main Use Effect to initiate data calls */

  /* Intervention Conditions */
  useEffect(() => {
    if (isListingPackageDataLoading || isListingPackageDataError) {
      pauseIntervention();
    }
  }, [isListingPackageDataLoading, isListingPackageDataError]);

  // Used to open Guided Search Bottom Sheet and Intervention Condition

  useEffect(() => {
    const coachMarkCondition = coachMarkShown // check if coach mark was shown
      ? prevShowCoachMarks.current && !showCoachMarks
      : true;

    /* Intervention Conditions */
    if (popupValue || getTIDeeplinkOpened() || showCoachMarks) {
      // interventionPaused = true;
      pauseIntervention();
    }
    if (isEmpty(popupValue) && coachMarkCondition && !interventionShowed) {
      interventionShowed = true; // this condition is added so that playIntervention is not called again and again
      playIntervention();
    }
    /* Intervention Conditions */

    /* Guided Search Conditions */
    const showGuidedSearch =
      !isEmpty(filterData) &&
      !(holidayLandingGroupDto?.filters?.length > 0) &&
      !isGuidedSearchShown;

    if (
      showGuidedSearch &&
      coachMarkCondition &&
      !popupValue /* this condition is stop guided search to open if a popup is open */
    ) {
      holidayLandingGroupDtoUpdate({
        ...holidayLandingGroupDto,
        interventionFilterData: filterData,
      });
      setShowFilterBottomsheet(true);
      isGuidedSearchShown = true; // to stop guided search intervention to multiple times
    }
    /* Guided Search Conditions */
    prevShowCoachMarks.current = showCoachMarks;
    if (showCoachMarks) {
      coachMarkShown = true;
    }
  }, [filterData, popupValue, showCoachMarks]);

  /* Check if after commenting there is no issue */
  useEffect(() => {
    holidayLandingGroupDtoUpdate(holidayLandingGroupDto);
  }, [userDepCity]);
  useEffect(() => {
    reduxStateRef.current = {
      listingPackagesState,
      groupMetaState,
      metaDataState,
      recentPackages,
      groupSections,
    };
  }, [listingPackagesState, groupMetaState, metaDataState, recentPackages, groupSections]);
  function handlePageExit() {
    const {
      listingPackagesState,
      groupMetaState,
      metaDataState,
      recentPackages,
      groupSections,
    } = reduxStateRef.current;
    const modifiedData = createListingViewData({
      listingPackagesState,
      groupMetaState,
      metaDataState,
      recentlySeenPackages: recentPackages,
      groupSections,
      loadingMoreItems: true,
    }).slice(0, pdtLogIndex.current + 1);
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.pageExit,
      value: 'components_seen',
      compData: modifyPDTObj(modifiedData, subTitle),
    });
  }

  if (isRawClient()) {
    useEffect(() => handlePageExit, []);
  } else {
    useFocusEffect(
      useCallback(() => handlePageExit, [])
    );
  }

  const backHandlerCallback = React.useCallback(() => {
    onBackPress();
    return true;
  }, [popupValue, leaveIntent]);
  useBackHandler(backHandlerCallback);

  const togglePopup = ({ popup = '' } = {}) => {
    popupValueRef.current = popup;
    setPopup(popup);
  };
  const trackBackPressEvents = () => {
    const eventName = 'back';
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    trackViewedSectionClickEvent();
    trackLocalClickEvent({ eventName });
  };
  const onBackPress = () => {
    // this is to check if back button was pressed from Trip Ideas page
    if (getTIDeeplinkOpened()) {
      setTIDeeplinkOpened(false);
      return true;
    }
    if (leaveIntent?.toUpperCase() === isLeaveIntent) {
      close();
      return true;
    } else {
      if (popupValueRef.current) {
        togglePopup({ popup: '' });
        return true;
      }
      trackBackPressEvents();
      exitLocalNotification(GROUPING_LOCAL_NOTIFICATION_PAGE_NAME);
      // resetPhoenixGroupingV2Data(); //TODO fix onback from detail page
      const isFromDeeplink =
        props[FROM_LISTING_GROUPING_DEEPLINK] && !holidayLandingGroupDto.lastPage;
      if (isFromDeeplink) {
        if (Platform.OS === 'ios') {
          ViewControllerModule.popViewController(rootTag);
        } else {
          BackHandler.exitApp();
        }
      } else {
        HolidayNavigation.pop();
      }
      return true;
    }
  };
  const trackViewedSectionClickEvent = () => {
    const eventDetails = getViewTrackingObj();
    const { eventName = '', prop1 = '' } = eventDetails || {};
    if (!isEmpty(eventName)) {
      logHolidaysGroupingPDTEvents({
        actionType: PDT_EVENT_TYPES.contentSeen,
        value: eventName + '|' + prop1,
        shouldTrackToAdobe:false
      });
    }
    trackLocalClickEvent(eventDetails);
    clearViewTackingObj();
  };
  /* Note: Pass this function only to props any click event and not import groupingclickevent */
  const trackLocalClickEvent = (eventDetails) => {
    trackPhoenixGroupingV2ClickEvent({
      eventDetails,
      holidayLandingGroupDto: updatedHolidayLandingGroupDtoState,
      fabCta,
    });
  };

  const trackErrorLocalClickEvent = ({ eventName = '', suffix = '', evar22 = '', prop66 = '' }) => {
    trackPhoenixGroupingV2ClickEvent({
      holidayLandingGroupDto: updatedHolidayLandingGroupDtoState,
      eventDetails: {
        prop66,
        eventName,
        suffix,
        omniData: {
          [TRACKING_EVENTS.M_V22]: evar22 ? `HLD:${evar22}` : '',
        },
      },
      fabCta,
    });
  };

  const trackPageExit = () => {
    trackHolidayGroupingLoadEvent({
      logOmni: false,
      pdtData: {
        ...createPhoenixGroupingV2PdtData({
          groupingData: updatedHolidayLandingGroupDtoState,
          fabCta,
        }),
        eventType: PDT_RAW_EVENT,
        activity: PHOENIX_GROUPING_V2_TRACKING_EVENTS.PAGE_EXIT_EVENT,
      },
    });
  };

  return (
    <View style={styles.viewContainer}>
      {isMetaDataLoading && <PhoenixGroupingV2Loader userDepCity={userDepCity} />}
      {!isMetaDataLoading && isMetaDataSuccess && (
        <PhoenixGroupingV2Page
          onBackPress={onBackPress}
          updateInterventionData={updateInterventionData}
          playIntervention={playIntervention}
          pauseIntervention={pauseIntervention}
          unmountInterention={unmountInterention}
          closeIntervention={close}
          popup={popupValue}
          destinationCityData={destinationCityData}
          togglePopup={togglePopup}
          leaveIntent={leaveIntent}
          trackPageExit={trackPageExit}
          trackErrorLocalClickEvent={trackErrorLocalClickEvent}
          showFilterBottomsheet={showFilterBottomsheet} // used for intervention guided search
          setShowFilterBottomsheet={setShowFilterBottomsheet}
          showCoachMarks={showCoachMarks}
          setShowCoachMarks={setShowCoachMarks}
          trackViewedSectionClickEvent={trackViewedSectionClickEvent}
          pdtLogIndex={pdtLogIndex}
          hasCalledPDTRef={hasCalledPDTRef}
          {...rest} // Pass through all other props from withIntervention
        />
      )}
      {isMetaDataError && !isMetaDataSuccess && (
        <PhoenixGroupingV2Error
          error={error}
          onBackPress={onBackPress}
          trackErrorLocalClickEvent={trackErrorLocalClickEvent}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  viewContainer: {
    flex: 1,
    backgroundColor: holidayColors.lightGray2,
    ...Platform.select({
      ios: {
        marginTop: -statusBarHeightForIphone,
        paddingTop: statusBarHeightForIphone,
      },
    }),
  },
});
const mapStateToProps = (state) => {
  return {
    userDepCity: state.holidaysSearchWidget.userDepCity,
    metaDataState: state.holidaysPhoenixGroupingV2.metaDataDetail,
    groupMetaState: state.holidaysPhoenixGroupingV2.groupMetaDetail,
    error: state.holidaysPhoenixGroupingV2.metaDataDetail.error,
    listingPackagesState: state.holidaysPhoenixGroupingV2.listingPackagesData,
    fabCta: state.holidaysPhoenixGroupingV2.fabCtaDetail?.fabCtaDetail,
    updatedHolidayLandingGroupDtoState: {
      ...state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
    },
    recentPackages: state.holidaysPhoenixGroupingV2.listingPackagesData.recentPackages,
    groupSections: state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.groupSections,
  };
};

const mapDispatchToProps = {
  initialLoadAction,
  fetchFabCtaAction,
  resetPhoenixGroupingV2Data,
  holidayLandingGroupDtoUpdate,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(
  withIntervention(
    PhoenixGroupingV2,
    PHOENIX_GROUPING_V2_PAGE_NAMES.LISTING__PAGE_NAME,
    `${PHOENIX_GROUPING_V2_PAGE_NAMES.PAGE_NAME}-${GROUPING_PAGE_VERSION}`,
  ),
);
