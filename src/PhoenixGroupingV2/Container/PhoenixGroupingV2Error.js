import React, { useEffect } from 'react';
import { connect } from 'react-redux';
import ErrorPage from '../../Common/Components/ErrorPage';
import HolidayGroupingError from '../../Grouping/Components/HolidayGroupingError';
import { GROUPING_TRACKING_PAGE_NAME } from '../../Grouping/HolidayGroupingConstants';
import { DOM_BRANCH } from '../../HolidayConstants';
import { logHolidaysGroupingPDTEvents } from '../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

const PheonixGroupingV2Error = ({
  holidayLandingGroupDto,
  error,
  onBackPress,
  trackErrorLocalClickEvent,
  ...rest
}) => {
  const { destinationCity = '', branch = DOM_BRANCH, aff = '', fromSeo = false } =
    holidayLandingGroupDto || {};
  useEffect(() => {
    logHolidaysGroupingPDTEvents({
      actionType : PDT_EVENT_TYPES.pageRenedered,
      value : 'Error_occurred',             //log error event when metadata API failed
      errorDetails : {
        code : error.code,
        message : error.message,
      },
    })
  },[])
  const { fabCta = {} } = rest || {};

  return (
    <ErrorPage
      showFabCtaOnError
      fabData={{
        destinationCity,
        branch,
        aff,
        fromSeo,
      }}
      error={error}
      pageName={GROUPING_TRACKING_PAGE_NAME}
      Component={HolidayGroupingError}
      componentProps={{ fabCta }}
      onBackPress={onBackPress}
      trackErrorLocalClickEvent={trackErrorLocalClickEvent}
      trackPDTV3ErrorEvent={logHolidaysGroupingPDTEvents}
    />
  );
};

const mapStateToProps = (state) => {
  return {
    fabCta: state.holidaysPhoenixGroupingV2.fabCtaDetail?.fabCtaDetail,
  };
};

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(PheonixGroupingV2Error);
