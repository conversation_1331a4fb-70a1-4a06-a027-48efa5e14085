import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { View, Animated, StyleSheet, ActivityIndicator } from 'react-native';
import { connect } from 'react-redux';
import { isEmpty } from 'lodash';
import {
  DEFAULT_GROUP_KEY,
  GROUPING_PAGE_VERSION,
  HEADER_HEIGHT,
  PHOENIX_GROUPING_V2_PAGE_NAMES,
  PHOENIX_GROUPING_V2_POPUPS,
  PHOENIX_GROUPING_V2_SECTIONS,
  PHOENIX_GROUPING_V2_TRACKING_EVENTS,
} from '../Contants';
import HolidayFabAnimationContainer from '../../Common/Components/Widgets/FabAnimation/FabAnimationContainer';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import { fetchQueueIdentifier } from '../../utils/HolidayNetworkUtils';
import PhoenixV2Popups from '../Components/Popups';

import { DOM_BRANCH, HLD_PAGE_NAME, WEEKEND_GETAWAY_PAGE_TYPE } from '../../HolidayConstants';
import { holidayColors } from '../../Styles/holidayColors';
import { roomDefault } from '../../utils/RoomPaxUtils';
import { createGroupingFabData } from '../../Common/Components/Widgets/FabAnimation/FabAnimationUtils';
import {
  getPhoenixVariantType,
  showFabAnimationExtended,
  showHolAgentOnLandingAndListingPage,
  showMMTPersonalization,
  showMMTPersonalizationV2,
} from '../../utils/HolidaysPokusUtils';
import {
  createListingViewData,
  getSelectedGroup,
  getTripIdeasSection,
} from '../Utils/PhoenixGroupingV2Utils';
import { marginStyles } from '../../Styles/Spacing';
import {
  resetAbortController,
  getAbortController,
} from 'mobile-holidays-react-native/src/utils/NetworkUtils/AbortController';
import { trackHolidayGroupingLoadEvent } from '../../utils/HolidayGroupingTrackingUtils';
import {
  createPhoenixGroupingV2PdtData,
  getPhoenixGroupingV2PageName,
  getProp42Data,
  populateCommonOnmiData,
  trackPhoenixGroupingV2ClickEvent,
} from '../Utils/PhoenixGroupingV2TrackingUtils';
import * as PhoenixSectionCardClickHandler from '../Utils/PhoenixGroupingSectionCardClickHandler';
import {
  sectionTrackingPageNames,
  setPageSectionVisitResponse,
} from '../../utils/SectionVisitTracking';
import { saveRecentSearch } from '../../utils/HolidayNetworkUtils';
import { saveHolMeta } from '../../utils/HolidayUtils';
import { setFirebaseTrackingForListing } from '../../utils/FirebaseUtils/FirebaseTracking';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import { setViewedListingCardLastindex, updateViewTrackingObj, viewedListingCardLastIndex,  } from '../Utils/SectionTrackingUtils'

/* ACTIONS */
import { loadListingAction } from '../Actions/groupingV2Actions';

/* COMPONENTS */
import GroupingSectionView from '../Components/GroupingSectionView';
import PhoenixGroupingV2Header from '../Components/Header/PhoenixGroupingV2Header';
import PhoenixGroupingV2StickyHeader from '../Components/Header/PhoenixGroupingV2StickyHeader';
import PhoenixGroupingV2CoachMarks from './PhoenixGroupingV2CoachMarks';
import {   logHolidaysGroupingPDTEvents, onUpdateSearchWidgetPDT } from '../Utils/PhoenixGroupingV2PDTTrackingUtils';

import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

import DownloadApp from '../../Common/Components/DownloadApp';
import { sendMMTGtmEvent } from '../../utils/ThirdPartyUtils';
import CommonOverlay from "mobile-holidays-react-native/src/Common/Components/CommonOverlay";
import { hideCommonOverlay, showCommonOverlay } from '../../Common/Components/CommonOverlay/CommonOvelayAction';
import { getGroupingPDTObj } from '../Utils/PhoenixGroupingV2PDTDataHolder';
let phoenixGroupingPageV2SectionVisits = {};
const COLLECTION_GROUPS_HEIGHT = 56;

const PhoenixGroupingV2Page = ({
  /* State to props */
  metaDataState,
  listingPackagesState = {},
  recentPackages,
  groupSections,
  groupMetaState = {},
  fabCta = {},
  /* State to props */
  fetchCta,
  loadListingAction,
  holidayLandingGroupDto = {},
  onBackPress,
  trackErrorLocalClickEvent = null,
  popup = false,
  showCoachMarks = false,
  setShowCoachMarks = null,
  togglePopup = () => {},
  trackPageExit = () => {},
  showFilterBottomsheet = false,
  setShowFilterBottomsheet = () => {},
  leaveIntent,
  trackViewedSectionClickEvent = () => {},
  pdtLogIndex,
  hasCalledPDTRef,
  registerFabAnimationRef, // Callback to register FabAnimationContainer ref with withIntervention
  ...rest
}) => {
  const { updateInterventionData, playIntervention, closeIntervention, unmountIntervention } =
    rest || {};
  const { isMetaDataLoading, isMetaDataError, isMetaDataSuccess } = metaDataState || {};
  const { selectedGroupCollectionKey, groupMetaDetail } = groupMetaState || {};
  const {
    userDepCity: userDepartureCity = '',
    destinationCity = '',
    cmp = '',
    branch = '',
    packageDate = '',
    campaign = '',
    rooms = [],
    destinationCityData = {},
  } = holidayLandingGroupDto || {};

  let flatListRef = useRef();
  const prevScrollY = useRef(0);
  const barTranslation = useRef(-COLLECTION_GROUPS_HEIGHT);
  const scrollY = useRef(new Animated.Value(0)).current;
  const barAnim = useRef(new Animated.Value(0)).current;
  const [mmtBlackBottomSheetDetails, setMmtBlackBottomSheetDetails] = useState({});
  const [mmtBlackBucketDetail, setMmtBlackBucketDetail] = useState({});
  const [callGetCoachMarks, setCallGetCoachMarks] = useState(false);
  const [roomDetails, setRoomDetails] = useState(rooms.length > 0 ? rooms : [roomDefault]);
  const [selectedQuickFilterIndex, setSelectedQuickFilterIndex] = useState(0); // used to see which quick filter was clicked
  const [isFilterCTAExpanded, setIsFilterCTAExpanded] = useState(true); // used to hide and show filter text in quick horizontal filter
  const [quickFilter, setQuickFilter] = useState(null);
  const [packageVariantData, setPackageVariantData] = useState({}); // setPackageVariantData is used to when clicked on package and variantData is used in variant modal popup
  const [fabTextShrinked, setFabTextShrinked] = useState(false);
  const [loadingMoreItems, setLoadingMoreItems] = useState(false);
  const [variantTypeFromPokus, setVariantType] = useState('');
  const [travelPlexAttr4, setTravelPlexAttr4] = useState(null);

  useEffect(() => {
    // Set the current page name for HolidayDataHolder
    HolidayDataHolder.getInstance().setCurrentPage('holidaysGrouping');

    PhoenixSectionCardClickHandler.setData({
      ...holidayLandingGroupDto, // needed pt, aff, affRefId, cmp,
      fabCta,
    });
    saveRecentSearch(holidayLandingGroupDto);
    saveHolMeta(
      holidayLandingGroupDto.pt && holidayLandingGroupDto.pt === WEEKEND_GETAWAY_PAGE_TYPE,
      holidayLandingGroupDto.aff,
      holidayLandingGroupDto.pt,
      holidayLandingGroupDto.cmp
    );

    // Fetch attr4 data for TravelPlex
    const fetchAttr4Data = async () => {
      try {
        const attr4Data = await fetchQueueIdentifier('collections');
        setTravelPlexAttr4(attr4Data);
      } catch (error) {
        console.log('Error fetching TravelPlex attr4 data:', error);
      }
    };

    fetchAttr4Data();

    return () => {
      phoenixGroupingPageV2SectionVisits = {};
    };
  }, []);

  const trackMemberShipRenderedLoad = (eventName = '', cardType = '') => {
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.pageRenedered,
      value: cardType ? `${eventName}|${cardType}` : eventName,
      shouldTrackToAdobe:false
    });
    trackClickEvent({ eventName, prop1: cardType });
  }

const trackMemberShipRenderedEvent = (groupSections) => {
    if( groupSections?.length <= 0){
      return;
    }
    const mmtblackSection = groupSections.find((section) => section.sectionCode === PHOENIX_GROUPING_V2_SECTIONS.MMT_BLACK);
    const pznSection = groupSections.find((section) => section.sectionCode === PHOENIX_GROUPING_V2_SECTIONS.PERSONALIZATION);
    if(mmtblackSection){
      trackMemberShipRenderedLoad('RENDERED_SECTION_MMTBLACK', '');
    }
    if(pznSection && pznSection?.cards && pznSection?.cards.length > 0){
      const pznCardType = pznSection?.cards[0]?.personalizationDetail?.section?.type;
      trackMemberShipRenderedLoad('RENDERED_SECTION_PZN',pznCardType);
    }
  }

  // useEffect(() => {
  //   if (isMetaDataSuccess && !isMetaDataLoading && groupSections?.length > 0) {
  //     let omniData = {
  //       ...populateCommonOnmiData({
  //         holidayLandingGroupDto,
  //         roomDetails,
  //       }),
  //     };
      logHolidaysGroupingPDTEvents({
        actionType: PDT_EVENT_TYPES.pageRenedered,
        value: getProp42Data(groupSections),
        shouldTrackToAdobe:false
      })
  //     setTimeout(()=>{
  //     trackHolidayGroupingLoadEvent({
  //       logOmni: true,
  //       omniData,
  //       omniPageName: getPhoenixGroupingV2PageName({
  //         isListing: holidayLandingGroupDto?.isListing,
  //       }),
  //       pdtData: {
  //         ...createPhoenixGroupingV2PdtData({
  //           groupingData: holidayLandingGroupDto,
  //           fabCta,
  //         }),
  //          activity: 'getProp42Data(groupSections)',
  //       },
  //     });
      trackMemberShipRenderedEvent(groupSections);
  //   }, 200);
  //   }
  // }, [groupSections]);

  useEffect(() => {
    setVariantType(getPhoenixVariantType());
  }, [userDepartureCity]);

  useEffect(() => {
    if (listingPackagesState?.isListingPackageDataSuccess) {
      updateInterventionData({
        destinationCity,
        branch: branch || DOM_BRANCH,
        cmp,
        roomData: roomDetails,
        campaign,
        departureDate: packageDate || '',
        pdtData: createPhoenixGroupingV2PdtData({
          groupingData: holidayLandingGroupDto,
          fabCta,
        }),
      });
      playIntervention();
      setCallGetCoachMarks(true);
    }
  }, [listingPackagesState?.isListingPackageDataSuccess]);
  useEffect(() => {
    if (loadingMoreItems) {
      const selectedGroup = getSelectedGroup({
        selectedGroupKey: selectedGroupCollectionKey,
        groupMetaDetail,
      });

      async function fetchMorePackages() {
        const selectedListingGroup =
          listingPackagesState?.[selectedGroupCollectionKey || DEFAULT_GROUP_KEY];
        resetAbortController();
        await loadListingAction({
          isLoadMore: true,
          holidayLandingGroupDto,
          metaDataResponse: metaDataState.metaDataDetail,
          selectedGroupCollection: selectedGroup,
          ...(selectedListingGroup?.noPackageFoundResetFilter && {
            noPackageFoundResetFilter: selectedListingGroup?.noPackageFoundResetFilter,
          }),
          offset: selectedListingGroup?.nextOffset || 0,
          abortController: getAbortController(),
        });
        setLoadingMoreItems(false);
      }

      fetchMorePackages();
    }
  }, [loadingMoreItems]);

  const trackClickEvent = (eventDetails) => {
    /* Note: Pass this function only to props any click event and not import groupingclickevent */
    trackPhoenixGroupingV2ClickEvent({ eventDetails, holidayLandingGroupDto, roomDetails, fabCta });
  };

  const trackFabEvents = (eventDetails) => {
    trackClickEvent(eventDetails);
    const { eventName = '', suffix = '' } = eventDetails;
    sendMMTGtmEvent({
      eventName: eventName + suffix,
      data: {
        pageName: 'listing',
        branch: holidayLandingGroupDto?.branch || DOM_BRANCH,
      },
    });
  };

  const setPackageVariant = ({ data = {}, packageDetail, groupName, groupIndex, index }) => {
    togglePopup({ popup: PHOENIX_GROUPING_V2_POPUPS.VARIANT });
    setPackageVariantData({ ...data, packageDetail, index });
  };
  const trackFilterClickEvents=({eventName = '', value = '' }) => {
    logHolidaysGroupingPDTEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value : value || eventName,
    })
    trackClickEvent({eventName});
  }
  const handleFilterClick = (item, index) => {
    filterClick(item, index);
  };

  const filterClick = (quickFilter, index) => {
    setQuickFilter(quickFilter);
    if (quickFilter) {
      setShowFilterBottomsheet(true);
    } else {
      togglePopup({ popup: PHOENIX_GROUPING_V2_POPUPS.FILTER_PAGE });
    }

    setIsFilterCTAExpanded(false);
    setSelectedQuickFilterIndex(index || selectedQuickFilterIndex);
    if (quickFilter) {
      trackFilterClickEvents({
        eventName: `filter_quick_${quickFilter.urlParam}`,
        value: 'quick_filter|' + quickFilter.urlParam,
      });
    } else {
      trackFilterClickEvents({ eventName: 'filter' });
    }
    HolidayDataHolder.getInstance().setCurrentPage('holidaysSearchWidget');
    trackPageExit();
  };

  const getQuickFilterViewProps = () => ({
    onFilterClicked: handleFilterClick,
    isFilterCTAExpanded,
    selectedQuickFilterIndex,
    metaData: metaDataState?.metaDataDetail,
  });

  const handleScrollToTop = () => {
    if (flatListRef.current) {
      flatListRef.current.scrollToOffset({ offset: 0, animated: true });
    }
  };

  const setBarTranslationOnScroll = (prevScrollY, scrollOffset) => {
    const delta = prevScrollY.current - scrollOffset;
    barTranslation.current = barTranslation.current + delta;
    if (barTranslation.current < -COLLECTION_GROUPS_HEIGHT) {
      barTranslation.current = -COLLECTION_GROUPS_HEIGHT;
    } else if (barTranslation.current > 0) {
      barTranslation.current = 0;
    }
    Animated.timing(barAnim, {
      toValue: barTranslation.current,
      duration: 0,
      useNativeDriver: true,
    }).start();
  };
  const pageScroll = (event) => {
    const scrollOffset = event.nativeEvent.contentOffset.y;
    // Handle FAB button animation here
    if (scrollOffset > 50 && !fabTextShrinked) {
      setFabTextShrinked(true);
    }
    setBarTranslationOnScroll(prevScrollY, scrollOffset);
    prevScrollY.current = scrollOffset;
  };

  const onScrollEndDrag = () => {
    /* if collection tabs are not in full view while scrolling get in full view */
    if (barTranslation.current > -COLLECTION_GROUPS_HEIGHT && barTranslation.current < 0) {
      barTranslation.current = 0;
      Animated.timing(barAnim, {
        toValue: barTranslation.current,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }
  };
  const onScrollReachedEnd = ({ distanceFromEnd }) => {
    const { packageDetails = [], noMorePackages = false } =
      listingPackagesState?.[selectedGroupCollectionKey || DEFAULT_GROUP_KEY] || {};
    if (packageDetails?.length > 0 && !noMorePackages && !loadingMoreItems) {
      setLoadingMoreItems(true);
    }
  };

  const data = useMemo(
    () =>
      createListingViewData({
        listingPackagesState,
        groupMetaState,
        metaDataState,
        recentlySeenPackages: recentPackages,
        groupSections,
        loadingMoreItems,
        hasCalledPDTRef
      }),
    [
      listingPackagesState?.noPackageFoundResetFilter, // if no packages were found after applying filter
      listingPackagesState?.isListingPackageDataLoading, // if listing/v2 call is happening again
      groupMetaState?.isGroupMetaDataLoading,
      loadingMoreItems, // loading more packages for a collection
      selectedGroupCollectionKey, // selecting another collection group
    ],
  );

  const renderHeaderComponent = useMemo(() => {
    return (
      <PhoenixGroupingV2Header
        holidayLandingGroupDto={holidayLandingGroupDto}
        onBackPress={onBackPress}
        togglePopup={togglePopup}
        getQuickFilterViewProps={getQuickFilterViewProps}
        trackClickEvent={trackClickEvent}
        closeIntervention={closeIntervention}
        scrollY={scrollY}
        trackViewedSectionClickEvent={trackViewedSectionClickEvent}
      />
    );
  }, [
    holidayLandingGroupDto.filters,
    holidayLandingGroupDto.userDepCity,
    holidayLandingGroupDto?.rooms,
    holidayLandingGroupDto?.selectedDate,
    holidayLandingGroupDto?.packageDate,
    leaveIntent,
    selectedGroupCollectionKey,
  ]);

  const renderListingView = ({ item, index }) => {
    return (
      <GroupingSectionView
        item={item}
        index={index}
        key={`id-${item.sectionCode}_${index}`}
        variantTypeFromPokus={variantTypeFromPokus}
        setPackageVariant={setPackageVariant}
        trackClickEvent={trackClickEvent}
        trackPageExit={trackPageExit}
        closeIntervention={closeIntervention}
        togglePopup={togglePopup}
        showCommonOverlay={showCommonOverlay}
        hideCommonOverlay={hideCommonOverlay}
        trackViewedSectionClickEvent={trackViewedSectionClickEvent}
        {...{ setMmtBlackBottomSheetDetails, setMmtBlackBucketDetail }}
      />
    );
  };
  const trackViewableItemChangedEvents = ({ eventName = '', prop1 = '', isPremium = false}) => {
    prop1 += (isPremium ? `|Premium` : ``);
    logHolidaysGroupingPDTEvents({
      actionType : PDT_EVENT_TYPES.contentSeen,
      value : eventName + '|' + prop1,
      category: isPremium ? 'Premium' : '',
      shouldTrackToAdobe:false

    })
    trackClickEvent({ eventName, prop1 });
  };
  let indexForPDTItems=-1;
  const _onViewableItemsChanged = React.useCallback(({ viewableItems, changed }) => {
    viewableItems?.forEach((viewableItem) => {
      if(viewableItem.index > indexForPDTItems ){
        indexForPDTItems= viewableItem.index ? viewableItem.index : 1;
      }
      pdtLogIndex.current = indexForPDTItems;
      const { item = {}, isViewable, index } = viewableItem;
      const {
        sectionCode,
        selectedGroupKey,
        packageDetails = {},
      } = item;
      const {
        listingPricingDetails = {},
        id: packageId = '',
        cardIndex = '',
        isPremium = false,
      } = packageDetails || {};
      const isErrorPage = sectionCode === PHOENIX_GROUPING_V2_SECTIONS.ERROR_PAGE;
      if (!sectionCode || !isViewable || isErrorPage) {
        return;
      }
      const selectedGroupCollectionIndex =
        getSelectedGroup({ selectedGroupKey, groupMetaDetail })?.index || 0;

      const groupCollection = selectedGroupKey
        ? `Collection_${selectedGroupCollectionIndex}`
        : 'listing_page';

      const sectionName = `${groupCollection}_${sectionCode}_${index}`;

      // Set last visited listing card event
      if (sectionCode === PHOENIX_GROUPING_V2_SECTIONS.LISTING_CARD) {
        if (cardIndex >= viewedListingCardLastIndex) {
          setViewedListingCardLastindex(cardIndex);
          const eventName = `Viewed_Package_${cardIndex}`;
          updateViewTrackingObj({ eventName: eventName, prop1: `Viewed_${groupCollection}` });
        }
      }

      // set each section visit event
      if (!phoenixGroupingPageV2SectionVisits[sectionName]) {
        phoenixGroupingPageV2SectionVisits[sectionName] = 1;
        setPageSectionVisitResponse({
          pageName: sectionTrackingPageNames.PHOENIX_GROUPING_V2_PAGE,
          value: phoenixGroupingPageV2SectionVisits,
        });
        if (sectionCode === PHOENIX_GROUPING_V2_SECTIONS.PERSONALIZATION) {
          if (!showMMTPersonalization() || !showMMTPersonalizationV2()) {
            return;
          }
        }
        const eventName = `Viewed_Section_${sectionCode}_${index}`;
        const priceEventName = `${listingPricingDetails?.priceKey || 'Default'}`;
        if (sectionCode !== PHOENIX_GROUPING_V2_SECTIONS.LISTING_CARD) {
          trackViewableItemChangedEvents({ eventName, prop1: `Viewed_${groupCollection}` });
        } else {
          if (!isEmpty(listingPricingDetails)) {
            let prop1Val = packageId;

            trackViewableItemChangedEvents({ eventName: priceEventName, prop1: prop1Val, isPremium });
          }
        }
      }
    });
  }, []);

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50,
  };
  const renderFooter = () =>
    loadingMoreItems ? (
      <ActivityIndicator style={marginStyles.ma10} size="large" color="black" />
    ) : (
      <View style={styles.height80} />
    );

  return (
    <>
    <DownloadApp />
     <CommonOverlay />
    <View style={styles.container}>
      <Animated.FlatList
        data={data}
        ref={(ref) => (flatListRef.current = ref)}
        keyExtractor={(item, i) => `id-${item.sectionCode}_${i}`}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={renderHeaderComponent}
        ListFooterComponent={renderFooter}
        scrollEventThrottle={16}
        onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
          useNativeDriver: true,
          listener: (event) => pageScroll(event),
        })}
        onScrollEndDrag={onScrollEndDrag}
        onEndReached={onScrollReachedEnd}
        renderItem={renderListingView}
        contentContainerStyle={{ paddingBottom: 32 }}
        extraData={selectedGroupCollectionKey}
        viewabilityConfig={viewabilityConfig}
        onViewableItemsChanged={_onViewableItemsChanged}
        onEndReachedThreshold={0.5}
      />

      <PhoenixGroupingV2StickyHeader
        scrollY={scrollY}
        togglePopup={togglePopup}
        onBackPressed={onBackPress}
        getQuickFilterViewProps={getQuickFilterViewProps}
        handleScrollToTop={handleScrollToTop}
        trackClickEvent={trackClickEvent}
        barAnim={barAnim}
        barAnimVal={barTranslation}
        trackViewedSectionClickEvent={trackViewedSectionClickEvent}
      />

      <PhoenixV2Popups
        /* States needed */
        showFilterBottomsheet={showFilterBottomsheet}
        quickFilter={quickFilter}
        popup={popup}
        packageVariantData={packageVariantData}
        /* States needed */
        togglePopup={togglePopup}
        variantTypeFromPokus={variantTypeFromPokus}
        roomDetails={roomDetails}
        setRoomDetails={setRoomDetails}
        trackClickEvent={trackClickEvent}
        trackViewedSectionClickEvent={trackViewedSectionClickEvent}
        trackPageExit={trackPageExit}
        unmountIntervention={unmountIntervention}
        updateStateFunctions={{
          setShowFilterBottomsheet,
          setQuickFilter,
        }}
        {...{ mmtBlackBottomSheetDetails, setMmtBlackBottomSheetDetails, mmtBlackBucketDetail }}
      />
      <HolidayFabAnimationContainer
          ref={(ref) => {
            if (registerFabAnimationRef) {
              registerFabAnimationRef(ref);
            }
          }}
        isListing={true}
        fabData={createGroupingFabData({
          holidayLandingGroupDto,
          pageDataMap: holidayLandingGroupDto.pageDataMap,
          trackingData: holidayLandingGroupDto.trackingData,
          pageVersion: GROUPING_PAGE_VERSION,
          isListing: holidayLandingGroupDto?.isListing,
        })}
        textShrinked={fabTextShrinked}
        fabCta={fabCta}
        pageName={getPhoenixGroupingV2PageName({
          isListing: holidayLandingGroupDto?.isListing,
        })}
        destinationCityData={destinationCityData}
          configId={HLD_PAGE_NAME.LISTING}unmountIntervention={unmountIntervention}
        setLocatorState={togglePopup}
        trackPageExit={trackPageExit}
        trackLocalClickEvent={trackFabEvents}
        trackErrorLocalClickEvent={trackErrorLocalClickEvent}
        trackPDTV3Event={logHolidaysGroupingPDTEvents}
          travelPlexConfigData={{
            ...getGroupingPDTObj(),
            attr1: holidayLandingGroupDto.branch || DOM_BRANCH,
            attr2: holidayLandingGroupDto.destinationCity,
            attr4: travelPlexAttr4?.allocationDetail?.queueIdentifier,
            hideInput: !showHolAgentOnLandingAndListingPage(),
          }}
      />
      <PhoenixGroupingV2CoachMarks
        trackLocalClickEvent={trackClickEvent}
        showCoachMarks={showCoachMarks}
        callGetCoachMarks={callGetCoachMarks}
        setCoachMarksOverlay={setShowCoachMarks}
        listView={flatListRef}
        isTripIdeasEntrySectionEnabled={getTripIdeasSection(groupSections)?.cards?.length > 0}
      />
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: holidayColors.lightGray2,
  },
  height80: {
    height: 10,
  },
});

const mapStateToProps = (state) => {
  return {
    fabCta: state.holidaysPhoenixGroupingV2.fabCtaDetail?.fabCtaDetail,
    metaDataState: state.holidaysPhoenixGroupingV2.metaDataDetail,
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
    listingPackagesState: state.holidaysPhoenixGroupingV2.listingPackagesData,
    recentPackages: state.holidaysPhoenixGroupingV2.listingPackagesData.recentPackages,
    groupSections: state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.groupSections,
    groupMetaState: state.holidaysPhoenixGroupingV2.groupMetaDetail,
  };
};

const mapDispatchToProps = {
  loadListingAction,
  showCommonOverlay,
  hideCommonOverlay,
};

export default connect(mapStateToProps, mapDispatchToProps)(PhoenixGroupingV2Page);
