import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import {
  createCuesSteps,
  hasOnBoardingCuesLastVisit,
  isAndroidClient,
  isOnBoardingCuesDelayOver,
  removeCuesStepsShown,
} from '../../utils/HolidayUtils';
import { HLD_CUES_POKUS_KEYS } from '../../HolidayConstants';
import {
  updatedCuesSteps,
  getValidCuesSteps,
} from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCueStepsUtils';
import { getCuesConfig } from '../../utils/HolidaysPokusUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import {
  getDataFromStorage,
  setDataInStorage,
  KEY_HOL_ONBOARDING_PAGE_VISIT,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../Common/Components/HolidayImageUrls';
import { isEmpty } from 'lodash';

/* Components */
import CoachMarksV2 from '@mmt/legacy-commons/Common/Components/CoachMarks/CoachMarksV2';
import BottomSheetOverlay from 'mobile-holidays-react-native/src/Common/Components/BottomSheetOverlay';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { logHolidaysGroupingPDTEvents } from '../Utils/PhoenixGroupingV2PDTTrackingUtils';

const COACH_MARK_KEYS = {
  collectionGroups: 'collectionGroups',
  sortAndFilter: 'sortAndFilter',
  editTravelDates: 'editTravelDates',
  fabCta: 'fabCta',
};
const PhoenixGroupingV2CoachMarks = ({
  listView,
  groupingData,
  showCoachMarks,
  callGetCoachMarks,
  setCoachMarksOverlay,
  trackLocalClickEvent,
  isTripIdeasEntrySectionEnabled,
}) => {
  const [finalCuesSteps, setFinalCuesSteps] = useState({});
  const [showCoachMarksBottomSheet, setShowCoachMarksBottomSheet] = useState(false);

  useEffect(() => {
    if (callGetCoachMarks) {
      getCoachMarks();
    }
  }, [callGetCoachMarks]);

  useEffect(() => {
    if (showCoachMarks && showCoachMarksBottomSheet) {
      trackClickEvent('cue_onboarding_seen', { pdtActionType: PDT_EVENT_TYPES.contentSeen });
    }
  }, [showCoachMarks, showCoachMarksBottomSheet]);

  const savePageVisitTime = async () => {
    let obj = await getDataFromStorage(KEY_HOL_ONBOARDING_PAGE_VISIT);
    if (isEmpty(obj)) {
      obj = {};
    }
    obj[HLD_CUES_POKUS_KEYS.GROUPING_V2] = new Date().getTime();
    await setDataInStorage(KEY_HOL_ONBOARDING_PAGE_VISIT, obj);
  };

  const trackClickEvent = (event, { pdtActionType = {} } = {}) => {
    logHolidaysGroupingPDTEvents({
      actionType:  !isEmpty(pdtActionType) ? pdtActionType : PDT_EVENT_TYPES.buttonClicked,
      value: event,
      shouldTrackToAdobe:isEmpty(pdtActionType)
    });
    trackLocalClickEvent({ eventName: event });
  };

  const getCoachMarks = async () => {
    const hasVisited = await hasOnBoardingCuesLastVisit(HLD_CUES_POKUS_KEYS.GROUPING_V2);
    if (!hasVisited) {
      await showCoachMarksOverlay();
      return;
    }
    const delayOver = await isOnBoardingCuesDelayOver(HLD_CUES_POKUS_KEYS.GROUPING_V2);
    if (delayOver) {
      await removeCuesStepsShown(HLD_CUES_POKUS_KEYS.GROUPING_V2);
      await showCoachMarksOverlay();
    }
  };

  const showCoachMarksOverlay = async () => {
    let finalSteps = {};
    const cuesConfig = getCuesConfig();
    const localSteps = require('./PhoenixGroupingV2PageCoachMarks.json');
    finalSteps = await createCuesSteps({
      pokusSteps: cuesConfig[HLD_CUES_POKUS_KEYS.GROUPING_V2],
      localSteps,
      pageName: HLD_CUES_POKUS_KEYS.GROUPING_V2,
    });
    finalSteps = getValidCuesSteps(finalSteps, { isArrowRequired: false }); // New Coach Mark does not need arrow type
    finalSteps = updatedCuesSteps({
      updatedCuesSteps: {
        editTravelDates: {
          shape: {
            top: isTripIdeasEntrySectionEnabled ? 125 : 180,
          },
        },
        ...(isAndroidClient() && {
          fabCta: {
            shape: {
              bottom: 10,
            },
          },
        }),
      },
      steps: finalSteps,
    });
    setFinalCuesSteps(finalSteps);
    if(finalSteps.length > 0) {
      setCoachMarksOverlay(true);
      setShowCoachMarksBottomSheet(true);
    }
  };

  const hideCoachMarksOverlay = () => {
    setCoachMarksOverlay(false);
    listView?.current.scrollToOffset({ animated: true, offset: 0 });
  };

  const handleScrollForCoachMarks = (step) => {
    if (step.key === COACH_MARK_KEYS.collectionGroups) {
      listView?.current.scrollToOffset({ animated: true, offset: 0 });
    }
  };
  const renderCoachMarks = () => {
    return finalCuesSteps?.length > 0 ? (
      <CoachMarksV2
        steps={finalCuesSteps}
        onDone={hideCoachMarksOverlay}
        onSkip={hideCoachMarksOverlay}
        onStart={handleScrollForCoachMarks}
        onStepChange={(step) => {}}
        pageName={HLD_CUES_POKUS_KEYS.GROUPING_V2}
        trackEvent={trackClickEvent}
      />
    ) : null;
  };

  const renderCoachMarksBottomSheet = () => {
    const handleShowMeAround = () => {
      trackClickEvent('show_cue_onboarding');
      setShowCoachMarksBottomSheet(false);
    };

    const handleCloseShowMeAround = () => {
      setShowCoachMarksBottomSheet(false);
      savePageVisitTime();
      hideCoachMarksOverlay();
    };
    const skipShowMeAround = () => {
      trackClickEvent('skip_cue_onboarding');
      handleCloseShowMeAround();
    };

    const closeShowMeAroundButton = () => {
      trackClickEvent('cue_onboarding_close');
      handleCloseShowMeAround();
    };

    return (
      <BottomSheetOverlay 
        toggleModal={() => setShowCoachMarksBottomSheet(!showCoachMarksBottomSheet)}
        visible={showCoachMarksBottomSheet}
        containerStyles={styles.containerStyles}
      >
        <View style={styles.bottomSheetContainer}>
          <HolidayImageHolder
            imageUrl={getImageUrl(IMAGE_ICON_KEYS.GROUPING_V2_CUES)}
            resizeMode={'contain'}
            style={styles.image}
          />
          <Text style={styles.heading}>Take a Quick Tour</Text>
          <Text style={styles.description}>
            We’ve made some updates. Take a quick tour to find relevant packages faster
          </Text>
        </View>
        <PrimaryButton
          handleClick={handleShowMeAround}
          buttonText={'Show Me Around'}
          btnContainerStyles={styles.primaryButtonContainer}
        />
        <TouchableOpacity activeOpacity={1} onPress={skipShowMeAround}>
          <Text style={styles.skipbtn}>Skip</Text>
        </TouchableOpacity>
      </BottomSheetOverlay>
    );
  };
  return (
    <>
      {showCoachMarks && showCoachMarksBottomSheet ? renderCoachMarksBottomSheet() : null}
      {showCoachMarks && !showCoachMarksBottomSheet ? renderCoachMarks() : null}
    </>
  );
};

const styles = StyleSheet.create({
  containerStyles: {
    ...paddingStyles.pa16,
  },
  bottomSheetContainer: {
    alignItems: 'center',
  },
  image: {
    width: 255,
    height: 170,
  },
  heading: {
    ...paddingStyles.pv10,
    ...fontStyles.headingMedium,
    color: holidayColors.black,
  },
  description: {
    ...marginStyles.mb30,
    ...fontStyles.labelMediumRegular,
    color: holidayColors.black,
    textAlign: 'center',
  },
  primaryButtonContainer: {
    ...marginStyles.mb16,
    ...marginStyles.mh16,
  },
  skipbtn: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.primaryBlue,
    ...marginStyles.mb16,
    textAlign: 'center',
  },
});
export default PhoenixGroupingV2CoachMarks;
