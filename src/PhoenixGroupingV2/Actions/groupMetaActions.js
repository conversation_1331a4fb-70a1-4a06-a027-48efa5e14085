import { fetchGroupMetaData } from '../../utils/NetworkUtils/groupingApis';
import { GROUP_META_ACTION_TYPES } from './PhoenixGroupingV2ActionTypes';

export const fetchGroupMetaDataDetail = ({
  holidayLandingGroupDto,
  searchQuery = '',
  newKeyRequired = '',
  offset,
}) => async (dispatch) => {
  try {
    dispatch(groupMetaLoading());
    const errorBody = {};
    const groupMetaResponseBody = await fetchGroupMetaData({
      holidayLandingGroupDto,
      offset,
      errorBody,
      searchQuery,
      newKeyRequired,
    });

    if (!groupMetaResponseBody) {
      dispatch(
        groupMetaError({
          code: errorBody?.error_code || null,
          message: errorBody?.error_message || null,
        }),
      );
      return;
    }

    dispatch(groupMetaSuccess(groupMetaResponseBody));

    return groupMetaResponseBody;
  } catch (e) {
    console.log({ e }, '** fetchGroupMetaDataDetail **');
  }
};

export const groupMetaError = ({ groupMetaDataError }) => ({
  type: GROUP_META_ACTION_TYPES.GROUP_META_ERROR,
  error: groupMetaDataError,
});

export const groupMetaSuccess = (groupMetaDetail) => ({
  type: GROUP_META_ACTION_TYPES.GROUP_META_SUCCESS,
  groupMetaDetail,
});

export const groupMetaLoading = () => ({
  type: GROUP_META_ACTION_TYPES.GROUP_META_LOADING,
});

export const resetGroupMetaState = () => ({
  type: GROUP_META_ACTION_TYPES.RESET_GROUP_META,
});

export const setSelectedGroupKey = (selectedGroupCollectionKey) => ({
  type: GROUP_META_ACTION_TYPES.SET_SELECTED_GROUP,
  selectedGroupCollectionKey,
});
