import { fetchListingDataUsingRequest } from '../../utils/NetworkUtils/groupingApis';
import { ERROR_NO_PACKAGE_FOUND, HOLIDAY_CORRELATION_LISTING_KEY } from '../../HolidayConstants';
import { createBaseRequest } from '../../utils/HolidayNetworkUtils';
import { getDepartureCity, getPlatformIdentifier } from '../../utils/HolidayUtils';
import { LISTING_PACKAGES_ACTION_TYPE } from './PhoenixGroupingV2ActionTypes';
import { DEFAULT_GROUP_KEY } from '../Contants';
import { isEmpty } from 'lodash';

export const createListingRequest = async ({
  holidayLandingGroupDto,
  packageIds,
  departureCity,
  offset = 0,
  selectedGroupCollection = [],
  noPackageFoundResetFilter = false,
}) => {
  let listingRequest = await createBaseRequest();
  const { criterias: groupCriterias, id = '' } = selectedGroupCollection || {};
  const {
    departureCity: depCity,
    destinationCity,
    fromDate = '',
    toDate = '',
    packageDate = '',
    departureLocusCode = '',
    destinationLocusCode = '',
    rooms = [],
    campaign = '',
    searchQuery = '',
    sorterCriterias = [],
    filters = [],
    cmp,
  } = holidayLandingGroupDto || {};
  const criterias = [
    ...(noPackageFoundResetFilter ? [] : groupCriterias || []),
    ...(noPackageFoundResetFilter ? [] : filters || []),
  ];
  listingRequest = {
    ...listingRequest,
    channel: getPlatformIdentifier(),
    departureCity: depCity || departureCity,
    destinationCity,
    criterias,
    sorterCriterias,
    packageIds,
    offset,
    cmp,
    ...(fromDate && { fromDate }),
    ...(toDate && { toDate }),
    ...(packageDate && { packageDate }),
    ...(departureLocusCode && { departureLocusCode }),
    ...(destinationLocusCode && { destinationLocusCode }),
    ...(rooms.length > 0 && { rooms }),
    ...(campaign && { campaign }),
    ...(searchQuery && { searchText: searchQuery }),
    /* Might be needed in future */
    // ...(!isEmpty(filters) && { userFilters: filters }),
    // ...(!isEmpty(selectedGroupCollection?.criterias || []) && { groupCriterias }),
    /* Might be needed in future */
    ...(!!id && { groupId: id }),
  };

  return listingRequest;
};

export const createPackageIdsSet = (listingMetaDataResponseBody) => {
  let packageIds = [];
  if (listingMetaDataResponseBody?.details?.length > 0) {
    const packageList = listingMetaDataResponseBody.details;
    packageList.forEach((item) => {
      packageIds.push(item.packageId);
    });
  }
  return packageIds;
};

export const fetchListingPackages =
  ({
    holidayLandingGroupDto = {},
    metaDataDetail = {},
    selectedGroupCollection = {},
    sendPackageIds = false,
    offset = null,
    abortController,
    noPackageFoundResetFilter = false,
  }) =>
  async (dispatch) => {
    try {
      const errorBody = {};
      const packageIdsSet = sendPackageIds ? createPackageIdsSet(metaDataDetail) : [];
      const departureCity = await getDepartureCity();
      const listingRequest = await createListingRequest({
        holidayLandingGroupDto,
        packageIds: packageIdsSet,
        selectedGroupCollection,
        departureCity,
        offset,
        noPackageFoundResetFilter,
      });
      const listingResponseBody = await fetchListingDataUsingRequest({
        listingRequest,
        errorBody,
        newCorrelationKey: true,
        correlationKey: HOLIDAY_CORRELATION_LISTING_KEY,
        abortController,
      });
      // State when we after applying filter there are no package found -> remove group filters and show packages with applied filters
      if (
        listingResponseBody?.noPackageFound &&
        holidayLandingGroupDto?.filters?.length > 0 &&
        !noPackageFoundResetFilter
      ) {
        return dispatch(
          fetchListingPackages({
            holidayLandingGroupDto,
            metaDataDetail,
            selectedGroupCollection,
            offset,
            abortController,
            sendPackageIds,
            noPackageFoundResetFilter: true,
          }),
        );
      }
      if (listingResponseBody?.error) {
        dispatch(listingPackagesError(listingResponseBody?.error));
        return listingResponseBody;
      }
      if (listingResponseBody?.packageDetails.length === 0 && offset === 0) {
        dispatch(listingPackagesError(ERROR_NO_PACKAGE_FOUND.error));
        return listingResponseBody;
      }
      if (listingResponseBody?.success) {
        dispatch(
          listingPackagesSuccess({
            groupKey: selectedGroupCollection?.groupKey || DEFAULT_GROUP_KEY,
            listingResponseBody,
            noPackageFoundResetFilter,
          }),
        );
      }
    } catch (e) {
      console.log({ e }, '** fetchListingPackages **');
    }
  };

export const listingPackagesError = (listingPackagesDataError) => ({
  type: LISTING_PACKAGES_ACTION_TYPE.LISTING_PACKAGES_ERROR,
  error: listingPackagesDataError,
});

export const listingPackagesSuccess = (payload) => ({
  type: LISTING_PACKAGES_ACTION_TYPE.LISTING_PACKAGES_SUCCESS,
  payload,
});

export const listingPackagesLoading = () => ({
  type: LISTING_PACKAGES_ACTION_TYPE.LISTING_PACKAGES_LOADING,
});

export const recentPackageSuccess = (recentPackages) => ({
  type: LISTING_PACKAGES_ACTION_TYPE.RECENT_PACKAGES_SUCCESS,
  recentPackages,
});
export const recentPackageLoading = (recentPackages) => ({
  type: LISTING_PACKAGES_ACTION_TYPE.RECENT_PACKAGES_LOADING,
});

export const resetListingPackages = () => ({
  type: LISTING_PACKAGES_ACTION_TYPE.RESET_LISTING_PACKAGES,
});
export const resetListingState = () => ({
  type: LISTING_PACKAGES_ACTION_TYPE.RESET_LISTING_STATE,
});
