import { createEmptyLoggingMap } from '../../Grouping/Utils/HolidayGroupingUtils';
import { DOM_BRANCH, PDT_PAGE_ENTRY_EVENT, PDT_RAW_EVENT } from '../../HolidayConstants';
import { trackHolidayGroupingLoadEvent } from '../../utils/HolidayGroupingTrackingUtils';
import { fetchPackagesMetaDataV2 } from '../../utils/NetworkUtils/groupingApis';
import { holidayLandingGroupDtoUpdate } from './groupingV2Actions';

import {
  FILTER_SEARCH_PAGE_ACTION_TYPES,
  META_DATA_ACTIONS_TYPES,
} from './PhoenixGroupingV2ActionTypes';

export const reFetchMetaIfFilters = ({ holidayLandingGroupDto }) => async (dispatch) => {
  const updatedHolidayLandingGroupDto = {
    ...holidayLandingGroupDto,
    filters: [],
    ...(Object.keys(holidayLandingGroupDto?.queryParams || {})?.length > 0 && {
      queryParams: {},
    }),
  };

  dispatch(holidayLandingGroupDtoUpdate(updatedHolidayLandingGroupDto));
  dispatch(resetMetaIfNoPackage(true));
  // Add action to reset filters in data
  const response = await dispatch(
    fetchMetaDataDetail({
      holidayLandingGroupDto: updatedHolidayLandingGroupDto,
      selectedFiltersList: [],
      didResetFilter: true,
    }),
  );
  return response;
};
export const fetchMetaDataDetail = ({
  holidayLandingGroupDto,
  setPersonalisedData = false,
  resetIfPackageNotFound = false,
  didResetFilter = false,
}) => async (dispatch) => {
  trackHolidayGroupingLoadEvent({
    logOmni: false,
    pdtData: {
      pageDataMap: createEmptyLoggingMap(holidayLandingGroupDto.fromDeepLink),
      eventType: PDT_RAW_EVENT,
      activity: PDT_PAGE_ENTRY_EVENT,
      requestId: holidayLandingGroupDto?.requestId,
      branch: holidayLandingGroupDto?.branch || DOM_BRANCH,
    },
  });
  const errorBody = {};
  const metaResponseBody = await fetchPackagesMetaDataV2({
    holidayLandingGroupDto,
    errorBody,
    setPersonalisedData,
    resetIfPackageNotFound,
  });

  if (!metaResponseBody || !metaResponseBody?.success) {
    dispatch(
      metaError({ metaDataError: {
        code: errorBody?.error_code || '',
        message: errorBody?.error_message || '',
  }}),
    );
    return null;
  }

  if (metaResponseBody?.numFound < 1 && resetIfPackageNotFound) {
    const response = await dispatch(reFetchMetaIfFilters({ holidayLandingGroupDto }));
    return response;
  }

  if (metaResponseBody?.numFound < 1) {
    dispatch(
      metaError({
        code: '',
        message: 'No Package Found',
      }),
    );
    return null;
  }

  const resultMetaResponseBody = { ...metaResponseBody, didResetFilter };
  return resultMetaResponseBody;
};

export const metaError = ({ metaDataError }) => ({
  type: META_DATA_ACTIONS_TYPES.META_DATA_ERROR,
  error: metaDataError,
});

export const metaSuccess = (metaDataDetail) => ({
  type: META_DATA_ACTIONS_TYPES.META_DATA_SUCCESS,
  metaDataDetail,
});

export const isMetaDataLoading = () => ({
  type: META_DATA_ACTIONS_TYPES.META_DATA_LOADING,
});

export const resetMetaIfNoPackage = (resetMetaIfNoPackage) => ({
  type: META_DATA_ACTIONS_TYPES.RESET_META_NO_PKG_FOUND,
  resetMetaIfNoPackage,
});
export const loadPhoenixSearchPageData = () => async (dispatch) => {
  dispatch({
    type: FILTER_SEARCH_PAGE_ACTION_TYPES.LOAD_PHOENIX_SEARCH_PAGE,
  });
};
