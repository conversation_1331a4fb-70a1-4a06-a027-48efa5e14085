export const FAB_CTA_ACTION_TYPES = {
  FAB_CTA_DATA_LOADING: 'FAB_CTA_DATA_LOADING',
  FAB_CTA_DATA_SUCCESS: 'FAB_CTA_DATA_SUCCESS',
  FAB_CTA_DATA_ERROR: 'META_DATA_ERROR',
};

export const META_DATA_ACTIONS_TYPES = {
  META_DATA_LOADING: 'META_DATA_LOADING',
  META_DATA_SUCCESS: 'META_DATA_SUCCESS',
  META_DATA_ERROR: 'META_DATA_ERROR',
  RESET_META_NO_PKG_FOUND: 'RESET_META_NO_PKG_FOUND',
};

export const GROUP_META_ACTION_TYPES = {
  GROUP_META_LOADING: 'GROUP_META_LOADING',
  GROUP_META_SUCCESS: 'GROUP_META_SUCCESS',
  GROUP_META_ERROR: 'GROUP_META_ERROR',
  RESET_GROUP_META: 'RESET_GROUP_META',
  SET_SELECTED_GROUP: 'SET_SELECTED_GROUP',
};

export const LISTING_PACKAGES_ACTION_TYPE = {
  LISTING_PACKAGES_LOADING: 'LISTING_PACKAGES_LOADING',
  LISTING_PACKAGES_SUCCESS: 'LISTING_PACKAGES_SUCCESS',
  LISTING_PACKAGES_ERROR: 'LISTING_PACKAGES_ERROR',
  RECENT_PACKAGES_SUCCESS: 'RECENT_PACKAGES_SUCCESS',
  RECENT_PACKAGES_LOADING: 'RECENT_PACKAGES_LOADING',
  RESET_LISTING_PACKAGES: 'RESET_LISTING_PACKAGES',
  RESET_LISTING_STATE: 'RESET_LISTING_STATE',
};

export const HOLIDAY_LANDING_GROUP_DTO_ACTION_TYPE = {
  HOLIDAY_LANDING_GROUP_DTO_UPDATE: 'HOLIDAY_LANDING_GROUP_DTO_UPDATE',
  GROUP_SECTION_FETCH_SUCCESS: 'GROUP_SECTION_FETCH_SUCESS',
};

export const FILTER_SEARCH_PAGE_ACTION_TYPES = {
  LOAD_PHOENIX_SEARCH_PAGE: 'LOAD_PHOENIX_SEARCH_PAGE',
};

export const PHOENIX_GROUPING_V2_ACTION_TYPES = {
  RESET_STATE: 'RESET_STATE',
  RESET_STATE_AFTER_EDIT: 'RESET_STATE_AFTER_EDIT',
};
