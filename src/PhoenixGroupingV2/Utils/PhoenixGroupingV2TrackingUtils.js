import { isEmpty } from 'lodash';
import {
  createEmptyLoggingMap,
  createLocusDetailsLoggingMap,
} from '../../Grouping/Utils/HolidayGroupingUtils';
import { DOM_BRANCH, PDT_PAGE_LOAD } from '../../HolidayConstants';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import { LANDING_PAGE_NAME } from '../../LandingNew/LandingConstants';
import {
  createFiltersLoggingMap,
  createRequestDetailsLoggingMap,
} from '../../Listing/Utils/HolidayListingUtils';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import { trackHolidayGroupingClickEvent } from '../../utils/HolidayGroupingTrackingUtils';
import { createRandomString, getAPWindow, getPaxDetails } from '../../utils/HolidayUtils';
import {
  GROUPING_PAGE_VERSION,
  PHOENIX_GROUPING_V2_PAGE_NAMES,
  PHOENIX_GROUPING_V2_TRACKING_EVENTS,
} from '../Contants';

export const populateCommonOnmiData = ({ holidayLandingGroupDto, roomDetails } = {}) => {
  try {
    const { selectedDate, packageDate, trackingData, userDepCity } = holidayLandingGroupDto || {};
    let travelDate;
    if (selectedDate) {
      travelDate = selectedDate;
    }
    if (!travelDate && packageDate) {
      travelDate = packageDate;
    }
    const AP_WINDOW = travelDate ? getAPWindow(travelDate) : undefined;

    const { adult, child, noOfRooms, infantCount } = getPaxDetails({ roomDetails });
    const { categoryTrackingEvent = '' } = trackingData || {};
    return {
      m_v4: AP_WINDOW,
      m_v3: userDepCity,
      m_v21: `adults:${adult}_children:${child}_infants:${infantCount}`,
      m_v20: noOfRooms,
      m_v32: userDepCity,
      prop83: HolidayDataHolder.getInstance().getBanner(),
      [TRACKING_EVENTS.M_V57]: categoryTrackingEvent,
    };
  } catch (e) {
    console.log(e);
  }
};

export const initializePhoenixGroupingLoggingMap = () => {
  const loggingMap = {
    filterDetails: {},
    otherDetails: {},
    sorterDetails: {},
    interventionDetails: {},
    persuasionDetails: {},
    requestDetails: {},
    errorDetails: {},
  };
  return loggingMap;
};

export const createFiltersIdNameMap = (listingFilters) => {
  const filtersIdNameMap = {};
  for (let i = 0; i < listingFilters?.length; i++) {
    const listingFilter = listingFilters[i];
    filtersIdNameMap[listingFilter.id] = listingFilter.urlParam;
  }
  return filtersIdNameMap;
};

export const convertToFullDateFormat = (dateStr) => {
  let returnDateStr = '';
  if (dateStr) {
    returnDateStr = `${dateStr} 00:00:00`;
  }
  return returnDateStr;
};

export const createPhoenixGroupingV2LoggingMap = ({
  holidayLandingGroupDto,
  metaResponse = {},
  filters = [],
}) => {
  const {
    packageDate = '',
    fromDate = '',
    cmp = '',
    fromDeepLink = false,
    destinationCity = '',
    source = '',
    masterListingFilters = [],
  } = holidayLandingGroupDto || {};

  let loggingMap = initializePhoenixGroupingLoggingMap();
  const filtersIdNameMap = createFiltersIdNameMap(masterListingFilters) || [];
  loggingMap = {
    ...loggingMap,
    filterDetails: createFiltersLoggingMap(filters, filtersIdNameMap),
    requestDetails: createRequestDetailsLoggingMap(cmp),
    locusDetails: createLocusDetailsLoggingMap(metaResponse),
    otherDetails: {
      number_of_packages_shown: metaResponse?.numFound || 0,
      travel_start_date: convertToFullDateFormat(packageDate || fromDate || ''),
      dest_list: destinationCity ? destinationCity.split(',') : [],
      last_page_name: fromDeepLink ? '' : LANDING_PAGE_NAME,
    },
    trackingDetails: {
      source: source || '',
    },
  };

  return loggingMap;
};

export const createPhoenixGroupingV2PdtData = ({ groupingData, fabCta }) => {
  return {
    pageDataMap: { ...(groupingData?.pageDataMap || createEmptyLoggingMap()), fabCta },
    interventionDetails: fabCta?.interventionLoggingDetails || {},
    requestId: groupingData?.requestId || createRandomString(),
    branch: groupingData?.branch || DOM_BRANCH,
  };
};

export const getPhoenixGroupingV2PageName = ({ isListing = false, isMetaDataError = false }) => {
  return `${
    isListing && isMetaDataError
      ? PHOENIX_GROUPING_V2_PAGE_NAMES.LISTING_ERROR_PAGE_NAME
      : isListing
      ? PHOENIX_GROUPING_V2_PAGE_NAMES.LISTING__PAGE_NAME
      : isMetaDataError
      ? PHOENIX_GROUPING_V2_PAGE_NAMES.ERROR_PAGE_NAME
      : PHOENIX_GROUPING_V2_PAGE_NAMES.PAGE_NAME
  }-${GROUPING_PAGE_VERSION}`;
};
export const getProp42Data = (sections) => {
  if (!(sections && sections.length)) {
    return PDT_PAGE_LOAD;
  }
  let data = 'Load_CQCH';
  sections.forEach((s) => {
    data = `${data}$${s.sectionCode}_${s.order}${s.id ? `_${s.id}` : ''}`;
  });
  return data;
};

export const getCardCategoryForPdt = ({ isPremium = false }) => {
  return isPremium ? 'Premium' : ''; 
}

export const getCardProp1Value = ({cardItem}) => {
  const {isPremium = false, id = ''} = cardItem || {};
  if(isPremium){
    return  `${id}|Premium`;
  }
  return '';
}

export const getCardClickEventName = ({
  cardItem,
  cardIndex,
  sourceInfo = {},
  groupCollection,
}) => {
  let eventName = 'CardClick';
  /* this is used if package card is clicked from any other section (Recently Viewed, Variant) */
  if (!isEmpty(sourceInfo)) {
    eventName = `${eventName}${
      sourceInfo?.name && sourceInfo?.key ? `|${sourceInfo?.name}_${sourceInfo?.key}` : ''
    }${sourceInfo?.noPackageFoundResetFilter ? '|No_Package_Found' : ''}`;
  }
  eventName = `${eventName}|package_${cardIndex}${
    groupCollection?.groupKey ? `_collection_${groupCollection?.index}` : ''
  }_${cardItem?.id || cardItem?.packageId || ''}`;
  return eventName;
};
export const getCardClickPDTEventName = ({
  cardItem,
  cardIndex,
  sourceInfo = {},
  groupCollection,
}) => {
  let eventName = 'CardClick';
  eventName = `${eventName}|package_${cardIndex}${
    groupCollection?.groupKey ? `_collection_${groupCollection?.index}` : ''
  }_${cardItem?.id || cardItem?.packageId || ''}`;

  /* this is used if package card is clicked from any other section (Recently Viewed, Variant) */
  if (!isEmpty(sourceInfo)) {
    eventName = `${eventName}${
      sourceInfo?.name && sourceInfo?.key ? `|${sourceInfo?.name}_${sourceInfo?.key}` : ''
    }${sourceInfo?.noPackageFoundResetFilter ? '|No_Package_Found' : ''}`;
  }
  return eventName;
};

export const trackPhoenixGroupingV2ClickEvent = ({
  holidayLandingGroupDto,
  roomDetails,
  eventDetails,
  fabCta,
}) => {
  const { eventName = '', suffix = '', prop1 = '', prop66 = '', omniData = {}, pdtExtraData = {} } =
    eventDetails || {};
  let updatedOmniData = {
    ...populateCommonOnmiData({
      holidayLandingGroupDto,
      roomDetails,
    }),
    ...omniData,
  };

  const pdtData = {
    ...createPhoenixGroupingV2PdtData({
      groupingData: holidayLandingGroupDto,
      fabCta,
    }),
    eventType: PHOENIX_GROUPING_V2_TRACKING_EVENTS.CLICK_EVENT,
    activity: eventName,
    extraData: pdtExtraData,
  };

  trackHolidayGroupingClickEvent({
    omniPageName: getPhoenixGroupingV2PageName({
      isListing: holidayLandingGroupDto?.isListing,
    }),
    omniEventName: eventName + suffix,
    pdtData,
    prop66,
    omniData: {
      ...updatedOmniData,
      [TRACKING_EVENTS.M_C1]: prop1,
    },
  });
};
