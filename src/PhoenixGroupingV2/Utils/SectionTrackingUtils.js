export const eventVariableNames = {
    EVENT_NAME : 'eventName',
    PROP1 : 'prop1',
}
// object stores the last visiting listing card of the collection 
export let viewTrackingObj = {
    [eventVariableNames.EVENT_NAME]:'',
    [eventVariableNames.PROP1]:'',
  }
export const updateViewTrackingObj= ({eventName ='', prop1 = ''})=> {
      viewTrackingObj[eventVariableNames.EVENT_NAME] = eventName;
      viewTrackingObj[eventVariableNames.PROP1] = prop1;
  }
export let viewedListingCardLastIndex = 0;

export let groupingCollectionVisits = {}

export const setViewedListingCardLastindex = (index) => {
  viewedListingCardLastIndex = index;
}

export const getViewTrackingObj = () => {
    return viewTrackingObj;
}
export const clearViewTackingObj = () => {
  viewedListingCardLastIndex = 0;
  viewTrackingObj = {};
  groupingCollectionVisits = {};
}
