import URL from 'url';
import queryString from 'query-string';
import isEmpty from 'lodash/isEmpty';
import { NativeModules } from 'react-native';
import {
  DEEPLINK_LISTING_GROUPING_PARAM,
  PDT_RAW_EVENT,
  DEEPLINK_OPENOUTSIDE_PARAM,
  DEEPLINK_OPENWEB_PARAM,
  DEEPLINK_SEND_QUERY,
  DEEPLINK_REFER_AND_EARN_PARAM,
} from '../../HolidayConstants';
import { handleQueryFormDeeplink, isNonMMTAffiliate } from '../../utils/HolidayUtils';
import { generateSectionKey, generateCardKey } from '../../LandingNew/Utils/HolidayLandingUtils';
import {
  DETAIL_PAGE_INDIA,
  DETAIL_PAGE_INTERNATIONAL,
} from '../../Grouping/HolidayGroupingConstants';
import { trackHolidayGroupingClickEvent } from '../../utils/HolidayGroupingTrackingUtils';
import HolidayDeeplinkParser from '../../utils/HolidayDeeplinkParser';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import { LISTING_TRACKING_PAGE_NAME } from '../../Listing/ListingConstants';
import { PHOENIX_GROUPING_V2_PAGE_NAMES } from '../Contants';
import { createEmptyLoggingMap } from '../../Grouping/Utils/HolidayGroupingUtils';
import { DETAIL_TRACKING_PAGE_NAME } from '../../PhoenixDetail/DetailConstants';

const LAST_PAGE = 'grouping';

let pt;
let aff;
let cmp;
let requestId;
let branch;
let fabCta;
let pageDataMap;

export const setData = (
  data = {
    pt: undefined,
    aff: undefined,
    affRefId: undefined,
    cmp: undefined,
    fabCta: {},
    pageDataMap: {},
    requestId: '',
    branch: '',
  },
) => {
  if (data.pt) {
    pt = data.pt;
  }

  if (data.aff) {
    aff = data.aff;
  }

  if (data.cmp) {
    cmp = data.cmp;
  }
  if (data.requestId) {
    requestId = data.requestId;
  }
  if (data.branch) {
    branch = data.branch;
  }
  if (data.fabCta) {
    fabCta = data.fabCta;
  }
  if (data.pageDataMap) {
    pageDataMap = data.pageDataMap;
  }
};

export const genericOnPressHandling = (card, data, cardKey = null) => {
  const sectionKey = generateSectionKey(data);
  let cardName = cardKey;
  if (!cardName) {
    cardName = generateCardKey(card);
  }
  if (!card.dynamic) {
    handleDeeplink(card, sectionKey, cardName, false);
  } else {
    onCardClicked(card, sectionKey, cardName, false);
  }
};
export const genericOnPressHandlingFromGrouping = (card, data, cardKey = null) => {
  const sectionKey = generateSectionKey(data);
  let cardName = cardKey;
  if (!cardName) {
    cardName = generateCardKey(card);
  }
  if (!card.dynamic) {
    handleDeeplink(card, sectionKey, cardName, true);
  } else {
    onCardClicked(card, sectionKey, cardName, true);
  }
};

export const handleDeeplinkFromGrouping = (card, sectionKey, cardKey) => {
  handleDeeplink(card, sectionKey, cardName, true);
};

export const handleDeeplink = (card, sectionKey, cardKey, fromGrouping = false) => {
  const deepLinkUrl = card.deeplink;
  if (!deepLinkUrl) {
    return;
  }
  const eventName = 'generic_card';
  if (!card?.disableTracking) {
    trackCardClickEvent(eventName, `${sectionKey}|${cardKey}`, card.destination);
  }
  handleDeepLinkUrl(deepLinkUrl, LAST_PAGE, fromGrouping);
};
export const handleDeepLinkUrl = (deepLinkUrl, lastpage, fromGrouping, currentPage) => {
  const urlValue = `${deepLinkUrl}&lastPage=${lastpage}`;
  if (urlValue) {
    const urlParams = URL.parse(urlValue);
    const queryParams = queryString.parse(urlParams.query);
    if (urlValue.includes(DEEPLINK_OPENOUTSIDE_PARAM)) {
      const { HolidayModule } = NativeModules;
      HolidayModule.handleWebDeeplink &&
        HolidayModule.handleWebDeeplink({ url: decodeURIComponent(urlValue) });
    } else if (urlValue.includes(DEEPLINK_OPENWEB_PARAM)) {
      const { HolidayModule } = NativeModules;
      HolidayModule.handleWebDeeplink &&
        HolidayModule.handleWebDeeplink({ url: decodeURIComponent(urlValue) });
    } else if (queryParams && urlValue.includes(DEEPLINK_LISTING_GROUPING_PARAM)) {
      handleGroupDeeplink(
        queryParams,
        urlValue,
        fromGrouping || currentPage === LISTING_TRACKING_PAGE_NAME,
        queryParams.redirectionpage,
      );
    } else if (
      urlValue.includes(DETAIL_PAGE_INDIA) ||
      urlValue.includes(DETAIL_PAGE_INTERNATIONAL)
    ) {
      handleDetailDeeplink(urlValue, currentPage === DETAIL_TRACKING_PAGE_NAME ? true : false);
    } else if(urlValue.includes(DEEPLINK_REFER_AND_EARN_PARAM)){
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.REFER_AND_EARN_PAGE);
    } else if (queryParams && urlValue.includes(DEEPLINK_SEND_QUERY)) {
      const queryDto = HolidayDeeplinkParser.parseQueryFormDeeplink(urlValue);
      handleQueryFormDeeplink(queryDto);
    } else {
      const { HolidayModule } = NativeModules;
      HolidayModule.openWebView({
        url: deepLinkUrl,
        // event: eventName,
        cmp,
      });
    }
  }
};

const onCardClicked = (card, sectionKey, cardKey, fromGrouping) => {
  const eventName = 'generic_card';
  if (!card?.disableTracking) {
    trackCardClickEvent(eventName, `${sectionKey}|${cardKey}`, card.destination);
  }
  const { destination, filters } = card;
  const refreshLanding = '';
  if (fromGrouping) {
    HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.GROUPING, {
      destinationCity: destination,
      filters,
      refreshLanding,
      holCampaign: card.campaign,
      pt,
      aff,
      cmp,
      redirectionPage: card.redirectionPage,
    });
  } else {
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GROUPING, {
      destinationCity: destination,
      filters,
      refreshLanding,
      holCampaign: card.campaign,
      pt,
      aff,
      cmp,
      redirectionPage: card.redirectionPage,
    });
  }
};

const handleGroupDeeplink = (queryParams, urlValue, fromGrouping, redirectionpage) => {
  const holidaysGroupingDeeplinkData = HolidayDeeplinkParser.parseListingGroupingPageDeeplink(
    urlValue,
  );
  if (isNonMMTAffiliate(aff)) {
    holidaysGroupingDeeplinkData.aff = aff;
  }
  if (!isEmpty(cmp) && isEmpty(holidaysGroupingDeeplinkData.cmp)) {
    holidaysGroupingDeeplinkData.cmp = cmp;
  }
  holidaysGroupingDeeplinkData.lastPage = LAST_PAGE;
  if (fromGrouping) {
    HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.GROUPING, {
      ...holidaysGroupingDeeplinkData,
    });
  } else {
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GROUPING, {
      ...holidaysGroupingDeeplinkData,
      redirectionPage: redirectionpage,
    });
  }
};
const handleDetailDeeplink = (urlValue, fromDetail) => {
  let holidaysDetailData = HolidayDeeplinkParser.parseDetailPageDeeplink(urlValue, false);

  if (fromDetail) {
    HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.DETAIL, {
      holidaysDetailData,
    });
  } else {
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL, {
      holidaysDetailData,
    });
  }
};

const trackCardClickEvent = (eventName, eventNameOmni) => {
  trackHolidayGroupingClickEvent({
    omniPageName: PHOENIX_GROUPING_V2_PAGE_NAMES.PAGE_NAME,
    omniEventName: eventNameOmni,
    pdtData: {
      pageDataMap: pageDataMap || createEmptyLoggingMap(),
      interventionDetails: fabCta?.interventionDetails || {},
      eventType: PDT_RAW_EVENT,
      activity: eventName,
      requestId: requestId,
      branch: branch,
    },
  });
};
