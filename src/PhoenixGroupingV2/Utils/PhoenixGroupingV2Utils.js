import { FROM_LISTING_GROUPING_DEEPLINK } from 'mobile-holidays-react-native/src/HolidayConstants';
import HolidayDeeplinkParser from 'mobile-holidays-react-native/src/utils/HolidayDeeplinkParser';
import { isEmpty } from 'lodash';
import {
  KEY_USER_DEP_CITY,
  setDataInStorage,
} from '../../../../../Mobile-mmt-react-native/packages/legacy-commons/AppState/LocalStorage';

import {
  isEmptyString,
  isLuxeFunnel,
  isNotNullAndEmptyCollection,
  isNullOrEmptyCollection,
  isRawClient,
  isPokusEnabledForSection,
} from '../../utils/HolidayUtils';
import { openPackageDetailV2 } from '../../utils/HolidayOpenPackageUtils';
import { createFiltersIdNameMap } from './PhoenixGroupingV2TrackingUtils';
import {
  DEFAULT_GROUP_KEY,
  ERROR_MESSAGES,
  LISTING_CARD_HEADER_TYPE,
  PHOENIX_GROUPING_V2_PAGE_NAMES,
  PHOENIX_GROUPING_V2_SECTIONS,
  PHOENIX_GROUPING_V2_TRACKING_EVENTS,
} from '../Contants';
import { DETAILS_DEPARTUTE_DATE_FORMAT } from '../../HolidayConstants';
import fecha from 'fecha';
import { TRIP_IDEA_ENTRY_SECTION_CODE } from '../../Grouping/HolidayGroupingConstants';
import { showNewRVSSection } from '../../utils/HolidaysPokusUtils';
import { modifyPDTObj } from '../../utils/HolidayPDTTrackingV3';
import { EVENT_NAMES, PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { logHolidaysGroupingPDTEvents } from './PhoenixGroupingV2PDTTrackingUtils';

const getErrorSection = ({ message, subMessage, selectedGroupKey, imageStyles }) => {
  return {
    sectionCode: PHOENIX_GROUPING_V2_SECTIONS.ERROR_PAGE,
    selectedGroupKey,
    data: {
      errorType: 'noPackages',
      errorMessage: message,
      errorSubMessage: subMessage,
      errorImageStyles: imageStyles,
    },
  };
};
// add sections from to specific index
const insertItemsAtIndex = (array, newItem, key) => {
  const updatedArray = [...array];
  const insertIndex = newItem[key];
  updatedArray.splice(insertIndex, 0, newItem);
  return updatedArray;
};

/*  this is used to get out all the common sections from section/fetch -> section code
    and groupmeta -> sections of selected group,
*/
const findCommonTypes = (array1 = [], array2 = []) => {
  // Extract unique types from the first array
  const typesSet1 = new Set(array1.map(obj => obj.sectionCode));

  // Extract unique types from the second array
  const typesSet2 = new Set(array2.map(obj => obj.sectionCode));

  // Find the common types between the two sets
  const commonTypes = [...typesSet1].filter(type => typesSet2.has(type));

  return commonTypes;
};


/* Function used to create data to show on grouping page - this integrates section/fetch, content card, all packages */
export const getListingViewData = ({
  listingPackagesState,
  groupSections,
  groupMetaState,
  recentlySeenPackages,
}) => {
  const data = [];
  const { selectedGroupCollectionKey = 0, groupMetaDetail = {} } = groupMetaState || {};
  const selectedGroup = getSelectedGroup({
    selectedGroupKey: selectedGroupCollectionKey,
    groupMetaDetail,
  });

  /* get all the common sections from section/fetch and groupMeta and remove them from section fetch call */
  const duplicateSections =  findCommonTypes(groupSections, selectedGroup?.sections || []);
  const excludedTypes = [TRIP_IDEA_ENTRY_SECTION_CODE, isRawClient() ? PHOENIX_GROUPING_V2_SECTIONS.TRIP_IDEAS_STORIES : '', ...duplicateSections];

  /* add listing data in master data */
  const listingData = listingPackagesState?.[selectedGroup?.groupKey || DEFAULT_GROUP_KEY];
  const isNoPackageFoundError = listingData?.noPackageFound || listingData?.noPackageFoundResetFilter || false;
let isNonPremium = false;
  listingData?.packageDetails?.forEach((packageItem, index) => {
    let headerType = '';
    if (isLuxeFunnel() && !isNoPackageFoundError) {
      if (index === 0) {
        isNonPremium = !packageItem?.isPremium;
        headerType = packageItem?.isPremium
          ? LISTING_CARD_HEADER_TYPE.PREMIUM
          : LISTING_CARD_HEADER_TYPE.ALL_NON_PREMIUM;
      } else if (isNonPremium === false && !packageItem?.isPremium) {
        isNonPremium = true;
        headerType = LISTING_CARD_HEADER_TYPE.NON_PREMIUM;
      }
    }
    data.push({
      sectionCode: PHOENIX_GROUPING_V2_SECTIONS.LISTING_CARD,
      packageDetails: { ...packageItem, cardIndex: index },
      selectedGroupKey: selectedGroup?.groupKey || '',
      headerType,
      selectedGroupCollectionIndex: selectedGroup?.index || 0,
      noPackageFoundResetFilter:
        listingPackagesState?.[selectedGroupCollectionKey || DEFAULT_GROUP_KEY]
          ?.noPackageFoundResetFilter,
    });
  });

  // add section/fetch items in main listing data
  let updatedItems = groupSections
    .filter((item) => !excludedTypes.includes(item?.sectionCode)) // exclude specific section codes
    .reduce((acc, newItem) => {
      let item = newItem;
      if (item?.sectionCode === PHOENIX_GROUPING_V2_SECTIONS.RECENTLY_VIEWED) {
        item = { recentlySeenPackages, ...newItem };
      }
      if (item?.sectionCode === PHOENIX_GROUPING_V2_SECTIONS.PAYMENT_DROP_OFF) {
        item = { ...newItem, ...recentlySeenPackages };
      }

      if (isPokusEnabledForSection({sectionCode : item?.sectionCode})) {
        return insertItemsAtIndex(
          acc,
          { ...item, cardIndex: newItem?.order, selectedGroupKey: selectedGroup?.groupKey || '' },
          'order',
        );
      } else {
        return acc;
      }
    }, data);

  // add content card in main listing data
  if (selectedGroup?.groupCards?.length > 0) {
    updatedItems = selectedGroup?.groupCards?.reduce((acc, newItem) => {
      return insertItemsAtIndex(
        acc,
        {
          ...newItem,
          sectionCode: PHOENIX_GROUPING_V2_SECTIONS.CONTENT_CARD,
          cardIndex: newItem?.position,
          selectedGroupKey: selectedGroup?.groupKey || '',
        },
        'position',
      );
    }, updatedItems);
  }

  // add sections from group data in main listing data
  if (selectedGroup?.sections?.length > 0) {
    updatedItems = selectedGroup?.sections.reduce((acc, newItem) => {
      return insertItemsAtIndex(
        acc,
        { ...newItem, cardIndex: newItem?.order, selectedGroupKey: selectedGroup?.groupKey || '' },
        'order',
      );
    }, updatedItems);
  }
  return updatedItems;
};
export const createListingViewData = ({
  listingPackagesState,
  groupMetaState,
  metaDataState,
  recentlySeenPackages,
  groupSections,
  loadingMoreItems,
  hasCalledPDTRef = { current: false },
}) => {
  const data = [];
  const { isListingPackageDataLoading = false, isListingPackageDataError = false } =
    listingPackagesState || {};
  const { isGroupMetaDataLoading = false, selectedGroupCollectionKey = 0 } =
    groupMetaState || {};
    const { isMetaDataLoading = false ,metaDataDetail={}} = metaDataState || {};
    const { headerDetail = {}} = metaDataDetail || {};
    const { subTitle = 'Holiday Packages' } = headerDetail || {};
  if (isMetaDataLoading || isGroupMetaDataLoading || isListingPackageDataLoading) {
    return [
      {
        sectionCode: PHOENIX_GROUPING_V2_SECTIONS.LOADER_PAGE,
        data: [],
      },
    ];
  }
  if (
    listingPackagesState?.[selectedGroupCollectionKey || DEFAULT_GROUP_KEY]?.noPackageFound ||
    isListingPackageDataError
  ) {
    // if no packages were present show error card
    data.push(
      getErrorSection({
        message: 'No Package Found',
        subMessage: listingPackagesState?.error?.message || '',
        selectedGroupKey: selectedGroupCollectionKey || DEFAULT_GROUP_KEY,
        imageStyles: { width: 200, height: 120 },
      }),
    );
    return data;
  } else {
    // create rest of the data
    const items = getListingViewData({
      listingPackagesState,
      groupSections,
      groupMetaState,
      recentlySeenPackages,
    });
    if (
      listingPackagesState?.[selectedGroupCollectionKey || DEFAULT_GROUP_KEY]
        ?.noPackageFoundResetFilter
    ) {
      items.unshift({
        sectionCode: PHOENIX_GROUPING_V2_SECTIONS.NO_PACKAGE_FOUND_SHOW_SIMILAR,
        data: { heading: 'Here are other similar packages that meet your filter criteria' },
      });
      items.unshift(
        getErrorSection({
          message: ERROR_MESSAGES.NO_PACKAGES_FOUND_AFTER_FILTER_TITLE,
          subMessage: ERROR_MESSAGES.NO_PACKAGES_FOUND_AFTER_FILTER_SUB_TITLE,
          selectedGroupKey: selectedGroupCollectionKey || DEFAULT_GROUP_KEY,
        }),
      );
    }
    if(listingPackagesState?.isListingPackageDataSuccess && !loadingMoreItems && !hasCalledPDTRef.current){
      logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.pageRenedered,
      value:EVENT_NAMES.PAGE_RENEDERED,
      compData: modifyPDTObj(items,subTitle),
    });
    hasCalledPDTRef.current= true;
    }
    return items;
  }
};

// to create dictionary of query paramteres found in url
const getQueryParams = (url) => {
  const queryParams = {};
  const queryString = url.split('?')[1];

  if (queryString) {
    const paramPairs = queryString.split('&');

    for (const pair of paramPairs) {
      const [key, value] = pair.split('=');
      queryParams[decodeURIComponent(key)] = decodeURIComponent(value);
    }
  }

  return queryParams;
};

// create one data group which contains props from previous page, meta data response, tracking details
export const createHolidayLandingGroupingData = (props) => {
  const {
    query,
    redirectionPage,
    fromDate,
    toDate,
    packageDate,
    selectedDate,
    userDepCity,
    destinationCity,
    filters,
    cmp,
    holCampaign,
    campaign,
    refreshLanding,
    fromSeo,
    pt,
    aff,
    rooms,
    packageIds,
    destinationCityData,
    source = '',
    banner,
    departureCity = '',
    searchWidgetData,
    masterData,
    header,
    holidaysListingData,
  } = props;
  let holidayLandingGroupDto = {};
  if (props[FROM_LISTING_GROUPING_DEEPLINK]) {
    const queryParserResult = HolidayDeeplinkParser.parseListingGroupingPageDeeplink(
      query,
      true,
      redirectionPage,
      true,
    );
    holidayLandingGroupDto = {
      ...queryParserResult,
      ...(queryParserResult?.departureCity // set depCity if found in deeplink
        ? { userDepCity: queryParserResult?.departureCity }
        : typeof userDepCity !== 'object'
        ? { userDepCity }
        : ''),
    };

    if (departureCity) {
      setDataInStorage(KEY_USER_DEP_CITY, departureCity);
      holidayLandingGroupDto.userDepCity = departureCity;
    }
  } else {
    holidayLandingGroupDto = {
      fromDate,
      toDate,
      packageDate,
      selectedDate,
      userDepCity,
      destinationCity,
      filters,
      fromDeepLink: false,
      cmp: cmp,
      query,
      campaign: holCampaign, // this is from landing (Example New Year)
      refreshLanding,
      fromSeo,
      pt,
      aff,
      rooms,
      packageIds,
      redirectionPage,
      destinationCityData: destinationCityData,
      banner,
      source,
    };
  }

  const recentSearchExtraData = {
    searchWidgetData,
    masterData,
    header,
    holidaysListingData,
  };

  // Some updations are done after data is created either by query or by props
  holidayLandingGroupDto = {
    ...holidayLandingGroupDto,
    recentSearchExtraData,
    cmp: holidayLandingGroupDto.cmp || campaign,
    ...(!isEmpty(query) && { queryParams: getQueryParams(query) }),
    destinationCityData: holidayLandingGroupDto.destinationCityData || {
      campaign: holidayLandingGroupDto.campaign, // this campaign comes from notification from native code
      destinationCity: holidayLandingGroupDto.destinationCity,
    },
  };
  return holidayLandingGroupDto;
};

export const getFirstGroupWithPackages = ({ groupList, selectedGroup }) => {
  if (!isEmpty(selectedGroup)) {
    return groupList?.find((group) => group?.groupKey === selectedGroup?.groupKey);
  }
  return groupList?.find((group) => group?.totalPackageCount > 0) || groupList?.[0];
};

export const checkIsListingRedirection = ({
  holidayLandingGroupDto = {},
  listingRedirection = false,
}) => {
  const { redirectionPage = '', packageIds = [] } = holidayLandingGroupDto || {};
  return listingRedirection || redirectionPage.toLowerCase() === 'listing' || packageIds.length > 0;
};

export const updateObjectWithLocusInformation = (obj, response) => {
  if (isEmpty(response)) {
    return obj;
  }
  if (response?.destinationMeta) {
    const { departureCity, destinationCities } = response.destinationMeta;
    const destLocusCodes = [];
    if (destinationCities?.length) {
      destinationCities.forEach((destObj) => {
        const { locusDetails = {} } = destObj;
        const { locusCode = '' } = locusDetails || {};
        destLocusCodes.push(locusCode);
      });
    }
    if (departureCity && departureCity.cityName) {
      setDataInStorage(KEY_USER_DEP_CITY, departureCity.cityName);
    }
    obj.departureLocusCode = departureCity?.locusDetails?.locusCode;
    obj.destinationLocusCode = destLocusCodes.join(',');
  }
  return obj;
};

const getDestinations = (destinationDetail) => {
  if (destinationDetail?.destinations?.length === 0) {
    return null;
  }
  const { destinations = [], showCountryNames } = destinationDetail || {};
  const result = [];

  destinations.forEach((destination) => {
    const { nights = 0, name = '', countryName = '' } = destination;
    result.push(`${nights}N ${showCountryNames ? countryName : name}`);
  });

  return result;
};

export const createDestinationList = (destinationDetails) => {
  const destinationList = getDestinations(destinationDetails);
  return destinationList;
};

export const createPackageHighlightList = (highlightsData) => {
  return highlightsData.flatMap((data) =>
    data.highlights.map((item) => ({
      highlight: item,
      colorCode: data.colorCode,
      iconUrl: data.iconUrl,
    })),
  );
};

export const getFiltersFromQueryParams = (filters = [], queryParams = {}) => {
  if (isNullOrEmptyCollection(filters)) {
    return [];
  }
  const pickedFilters = [];
  const queryParamKeys = Object.keys(queryParams);
  if (isNotNullAndEmptyCollection(queryParamKeys)) {
    queryParamKeys.forEach((query) => {
      if (!isEmptyString(queryParams[query])) {
        const filter = query;
        const filterValue = queryParams[query];
        const selectedFilters = filters.filter(
          (item) => item.urlParam?.toLowerCase() === filter?.toLowerCase(),
        );
        if (isNotNullAndEmptyCollection(selectedFilters)) {
          const pickedFilter = {
            id: selectedFilters[0].id,
            values: filterValue.split('~'),
            urlParam: selectedFilters?.[0]?.urlParam || '',
          };
          pickedFilters.push(pickedFilter);
        }
      }
    });
  }
  return pickedFilters;
};

export const openPhoenixPackagePageFromGroupingV2 = ({
  holidayLandingGroupDto,
  packageCard,
  userPackageMeta = null,
  categoryId = null,
  params = {},
  rooms = [],
}) => {
  const {
    destinationCity = '',
    filters = [],
    masterListingFilters = [],
    pageDataMap = {},
    trackingData = {},
    requestId = '',
    pt,
    aff,
    rooms: holidayLandingGroupDtoRooms,
    packageDate,
    campaign,
  } = holidayLandingGroupDto || {};
  const filtersIdNamePlatformMap = createFiltersIdNameMap(masterListingFilters);
  const searchCriteriaObj = {
    criterias: filters,
    filterIdMap: filtersIdNamePlatformMap,
    destinationCity: destinationCity,
  };
  const departureDate = userPackageMeta?.departureDetail?.departureDate
    ? userPackageMeta.departureDetail?.departureDate
    : fecha.format(packageCard.departureDetails?.departureDate, DETAILS_DEPARTUTE_DATE_FORMAT);

  openPackageDetailV2({
    holidayLandingGroupDto,
    groupingData: {
      holidayLandingGroupDto,
      pageDataMap: pageDataMap,
      trackingData: trackingData,
      filtersIdNamePlatformMap,
      requestId,
    },
    packageDetails: {
      ...packageCard,
      nights: packageCard?.noOfNights,
      tagDestination: holidayLandingGroupDto?.destinationCity,
    },
    userPackageMeta,
    packageEvent: PHOENIX_GROUPING_V2_TRACKING_EVENTS.PACKAGE_CLICKED,
    searchCriteriaObj,
    lastPageName: PHOENIX_GROUPING_V2_PAGE_NAMES.PAGE_NAME,
    pt,
    aff,
    rooms: !isEmpty(rooms) ? rooms :  holidayLandingGroupDtoRooms,
    params: {
      departureDate,
      ...params,
    },
    categoryId,
    selectedDate: packageDate,
    detailsData: {
      campaign,
    },
  });
};

export const getSelectedGroup = ({ groupMetaDetail, selectedGroupKey }) => {
  const filteredArrayWithIndex = groupMetaDetail?.groupDetailsList?.reduce(
    (result, group, index) => {
      if (group?.groupKey === selectedGroupKey) {
        result.push({ ...group, index });
      }
      return result;
    },
    [],
  );

  return filteredArrayWithIndex?.[0] || {};
};

export const createFiltersIdMap = (listingFilters) => {
  return listingFilters?.reduce((filterIdMap, filter) => {
    filterIdMap[filter.id] = filter;
    return filterIdMap;
  }, {});
};

export const getTripIdeasSection = (groupSections = []) => {
  const [filteredSection] = groupSections.filter(
    (section) => section.sectionCode === TRIP_IDEA_ENTRY_SECTION_CODE,
  );
  return filteredSection || [];
};

export const getDestinationCityName = (destinationCityData) => {
  const { campaign = '', destinationCity = '' } = destinationCityData || {};
  return destinationCity || campaign;
}
