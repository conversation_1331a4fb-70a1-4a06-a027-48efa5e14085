export const PHOENIX_GROUPING_V2_PAGE_NAMES = {
  PAGE_NAME: 'collections',
  LISTING__PAGE_NAME: 'listing',
  ERROR_PAGE_NAME: 'collections_error',
  LISTING_ERROR_PAGE_NAME: 'listing_error',
};
export const MAX_RECENTLY_SEEN_PACKAGES_GROUPING = 50;
export const DEFAULT_GROUP_KEY = 'default_group';
export const HEADER_HEIGHT = 275;
export const isLeaveIntent = 'Y';
export const PHOENIX_GROUPING_V2_POPUPS = {
  EDIT_OVERLAY: 'EDIT_OVERLAY',
  FILTER_PAGE: 'FILTER_PAGE',
  VARIANT: 'Variant',
  BRANCH_OVERLAY: 'branch_locator',
  MMT_BLACK_BOTTOMSHEET: 'MMT_BLACK_BOTTOMSHEET',
};

export const PHOENIX_GROUPING_V2_TRACKING_EVENTS = {
  PACKAGE_CLICKED: 'package clicked',
  CLICK_EVENT: 'click',
  PAGE_EXIT_EVENT: 'page_exit',
};

export const PHOENIX_GROUPING_V2_SECTIONS = {
  ERROR_PAGE: 'error_page', // hardcode by us to show error screen
  LOADER_PAGE: 'loader_page', // hardcode by us to show error screen
  MEMBERSHIP_CARD: 'MEMBERSHIP_CARD',
  RECENTLY_VIEWED: 'RVS',
  LISTING_CARD: 'listing_card',
  PERSUASIONS: 'PRS',
  TRIP_IDEAS_STORIES: 'UGCS',
  DID_YOU_KNOW: 'DYK',
  COLLECTION_BANNER: 'CLB',
  AD_BANNER: 'DYNAD',
  TRIP_IDEAS_DESTINATION_GUIDE: 'DEST',
  CONTENT_CARD: 'content_card',
  NO_PACKAGE_FOUND_SHOW_SIMILAR: 'NO_PACKAGE_FOUND_SHOW_SIMILAR',
  PAYMENT_DROP_OFF: 'PDO',
  MMT_BLACK: 'BLACK',
  PREMIUM_PACKAGES_HEADER_CARD: 'PREMIUM_PACKAGES_HEADER_CARD',
  NON_PREMIUM_PACKAGES_HEADER_CARD: 'NON_PREMIUM_PACKAGES_HEADER_CARD',
  PERSONALIZATION: 'PZN',
};

export const PHOENIX_GROUPING_V2_CARD_SECTION_LIMIT = {
  INCLUSIONS: 6,
  HIGHLIGHTS: 3,
};

export const LISTING_CARD_HEADER_TYPE = {
  PREMIUM: 'PREMIUM',
  ALL_NON_PREMIUM: 'ALL_NON_PREMIUM',
  NON_PREMIUM: 'NON_PREMIUM',
}

export const GROUPING_PAGE_VERSION = 'v1';

export const ERROR_MESSAGES = {
  NO_PACKAGES_FOUND_AFTER_FILTER_TITLE:
    'No Packages Found for your filter criteria in this collection',
  NO_PACKAGES_FOUND_AFTER_FILTER_SUB_TITLE: 'Try again after removing some applied filters',
};
