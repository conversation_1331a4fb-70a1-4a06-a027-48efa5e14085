import React, { useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import HelpBtn from '../../../Grouping/Components/HolidayHelpBtn';
import { fetchFabCta, fetchFabData } from '../../../utils/HolidayNetworkUtils';
import {
  startNoPkCall,
  startNoPkChat,
  startNoPkQuery,
} from '../../../Common/Components/Widgets/FabAnimation/FabAnimationUtils';
import {
  GROUPING_TRACKING_ERROR_PAGE_NAME,
  GROUPING_TRACKING_PAGE_NAME,
} from '../../../Grouping/HolidayGroupingConstants';
import { logHolidaysGroupingPDTEvents } from '../../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { LISTING_TRACKING_PAGE_NAME } from '../../../Listing/ListingConstants';
import {
  getPhoenixGroupingV2PageName,
  populateCommonOnmiData,
  trackPhoenixGroupingV2ClickEvent,
} from '../../Utils/PhoenixGroupingV2TrackingUtils';
import { TRACKING_EVENTS } from '../../../HolidayTrackingConstants';
import { trackHolidayGroupingLoadEvent } from '../../../utils/HolidayGroupingTrackingUtils';
import { PDT_PAGE_VIEW } from '../../../HolidayConstants';
import { roomDefault } from 'mobile-holidays-react-native/src/utils/RoomPaxUtils';
import Shimmer from '@Frontend_Ui_Lib_App/Shimmer';

const queryIcon = require('@mmt/legacy-assets/src/iconQueryError.webp');
const callIcon = require('@mmt/legacy-assets/src/iconCall.webp');
const chatIcon = require('@mmt/legacy-assets/src/iconChatError.webp');

const FabCtaError = ({ error, holidayLandingGroupDto, fabCta }) => {
  const [fabDataError, setFabDataError] = useState({
    showCall: false,
    showQuery: false,
    showChat: false,
  });
  const [isLoading, setLoading] = useState(true);
  const { showCall, showQuery, showChat } = fabDataError || {};
  const {
    destinationCity = '',
    cmp = '',
    branch = DOM_BRANCH,
    aff = '',
    fromSeo = false,
  } = holidayLandingGroupDto || {};

  const fetchFabDataForError = async () => {
    try {
      const data = {
        destinationCity,
        cmp,
      };
      const response = (await fetchFabCta(data, GROUPING_TRACKING_ERROR_PAGE_NAME, '')) || {};
      setLoading(false);
      setFabDataError({
        showCall: response?.showCall,
        showChat: response?.showChat,
        showQuery: response?.showQuery,
      });
      let eventName = 'load_fab_none';
      const evar22 = error?.code ? `HLD:${error.code}:${error?.message || ''}` : '';
      if (response?.showFab) {
        eventName = `load_${response?.showCall ? 'C' : ''}${response?.showQuery ? 'Q' : ''}${
          response?.showChat ? 'Ch' : ''
        }${response?.branchLocator ? 'B' : ''}`;
      }
      trackHolidayGroupingLoadEvent({
        logOmni: true,
        omniPageName: getPhoenixGroupingV2PageName({ isListing: true }),
        omniData: {
          ...populateCommonOnmiData({
            holidayLandingGroupDto,
            roomDetails: holidayLandingGroupDto?.rooms || [roomDefault],
          }),
          [TRACKING_EVENTS.M_V22]: evar22,
        },
        pdtData: {
          activity: eventName,
          eventType: PDT_PAGE_VIEW,
        },
      });
    } catch (error) {
      console.error('Failed to fetch FAB data:', error);
    }
  };
  useEffect(() => {
    fetchFabDataForError();
  }, []);
  const handleNoPkCall = () => {
    startNoPkCall({
      fabData,
      error,
      trackErrorLocalClickEvent,
      trackPDTV3ErrorEvent: logHolidaysGroupingPDTEvents,
      fabCta: fabCta,
    });
  };
  const handleNoPkChat = () => {
    startNoPkChat({
      isListing: checkIsListingOrGrouping(GROUPING_TRACKING_ERROR_PAGE_NAME),
      fabData,
      error,
      trackErrorLocalClickEvent,
      trackPDTV3ErrorEvent: logHolidaysGroupingPDTEvents,
      fabCta: fabCta,
    });
  };

  const handleNoPkQuery = () => {
    startNoPkQuery({
      error,
      fabData,
      trackErrorLocalClickEvent,
      trackPDTV3ErrorEvent: logHolidaysGroupingPDTEvents,
      isListing: checkIsListingOrGrouping(GROUPING_TRACKING_ERROR_PAGE_NAME),
      pageName: GROUPING_TRACKING_ERROR_PAGE_NAME,
      fabCta: fabCta,
    });
  };
  const trackErrorLocalClickEvent = ({
    eventName = '',
    suffix = '',
    evar22 = '',
    prop66 = '',
    prop1 = '',
  }) => {
    trackPhoenixGroupingV2ClickEvent({
      holidayLandingGroupDto: holidayLandingGroupDto,
      eventDetails: {
        prop66,
        eventName,
        suffix,
        omniData: {
          [TRACKING_EVENTS.M_V22]: evar22 ? `HLD:${evar22}` : '',
        },
        prop1,
      },
      fabCta,
      roomDetails: holidayLandingGroupDto?.rooms,
    });
  };
  const checkIsListingOrGrouping = (pageName) => {
    return pageName === GROUPING_TRACKING_PAGE_NAME || LISTING_TRACKING_PAGE_NAME;
  };
  const iconQuery = {
    width: 19,
    height: 13,
    marginRight: 5,
  };
  const iconCall = {
    width: 20,
    height: 20,
    marginRight: 5,
  };
  const iconChat = {
    width: 22,
    height: 19,
    marginRight: 5,
  };
  const fabData = {
    destinationCity,
    branch,
    aff,
    fromSeo,
  };
  if (isLoading) {
    return (
      <View style={styles.horizontalLoader}>
        {Array(3)
          .fill(0)
          .map((key) => (
            <Shimmer
              width={200}
              height={10}
              marginBottom={10}
              borderRadius={5}
              animatedInnerColor="#F9F9F9"
              animationRange="#E7E7E7"
            />
          ))}
      </View>
    );
  }
  return (
    <View style={styles.helpSection}>
      {showQuery && (
        <HelpBtn
          source={queryIcon}
          iconStyle={iconQuery}
          text="SEND QUERY"
          startAction={handleNoPkQuery}
          isLast={!(showCall || showChat)}
        />
      )}
      {showCall && (
        <HelpBtn
          source={callIcon}
          iconStyle={iconCall}
          text="CALL US"
          startAction={handleNoPkCall}
          isLast={!showChat}
        />
      )}
      {showChat && (
        <HelpBtn
          source={chatIcon}
          iconStyle={iconChat}
          text="CHAT"
          startAction={handleNoPkChat}
          isLast
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  helpSection: {
    paddingHorizontal: 25,
  },
  horizontalLoader: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
});

export default FabCtaError;
