import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { connect } from 'react-redux';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import ErrorBackGround from '@mmt/legacy-assets/src/error.webp';
import { getSelectedGroup } from '../../Utils/PhoenixGroupingV2Utils';
/* Components */
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import { TRACKING_EVENTS } from 'mobile-holidays-react-native/src/HolidayTrackingConstants';
import { logHolidaysGroupingPDTEvents } from '../../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import FabCtaError from './FabCtaError';

const ErrorScreen = ({
  /* State values  */
  holidayLandingGroupDto,
  fabCta,
  groupMetaState,
  /* State values  */
  errorType,
  errorImage,
  errorImageStyles,
  errorMessage,
  errorSubMessage,
  togglePopup,
  trackClickEvent,
  error,
  children = () => {},
}) => {
  const { groupMetaDetail, selectedGroupCollectionKey } = groupMetaState || {};
  const selectedGroup = getSelectedGroup({
    groupMetaDetail,
    selectedGroupKey: selectedGroupCollectionKey,
  });
  const trackErrorPageViewEvents = () => {
    const eventName = `collection_${selectedGroup?.index}_${errorMessage}`;
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.contentSeen,
      value: eventName,
      errorDetails: {
        code: error?.code || '',
        message: error?.messgae || '',
      },
    });
    trackClickEvent({
      eventName,
      omniData: {
        [TRACKING_EVENTS.M_V22]: `HLD:${eventName}`,
      },
    });
  };
  useEffect(() => {
    trackErrorPageViewEvents();
  }, []);

  const errorHeading = 'No packages found';
  const errorSubheading =
    'But we will be delighted to plan a customized holiday for you. Connect with our Holiday Experts below.';

  return (
    <>
      <View style={styles.container}>
        <View style={styles.contentContainer}>
          <HolidayImageHolder
            defaultImage={errorImage || ErrorBackGround}
            style={{ ...styles.errorImageStyle, ...errorImageStyles }}
            resizeMode={'contain'}
          />
          <Text style={styles.messageHeading}>{errorHeading}</Text>
          <Text style={styles.messageSubText}>{errorSubheading}</Text>
        </View>
        {children}
      </View>
      <FabCtaError error={error} holidayLandingGroupDto={holidayLandingGroupDto} fabCta={fabCta} />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.lightGray2,
    width: '100%',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    ...paddingStyles.pt10,
    ...paddingStyles.ph16,
  },

  contentContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    ...marginStyles.mb16,
  },
  messageHeading: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
    ...marginStyles.mb8,
    textAlign: 'center',
  },
  messageSubText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
    textAlign: 'center',
    lineHeight: 19,
  },
  errorImageStyle: {
    width: 100,
    height: 100,
    ...marginStyles.mb10,
  },
});

const mapStateToProps = (state) => {
  return {
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
    groupMetaState: state.holidaysPhoenixGroupingV2.groupMetaDetail,
    error: state.holidaysPhoenixGroupingV2.listingPackagesData?.error,
    fabCta: state.holidaysPhoenixGroupingV2.fabCtaDetail?.fabCtaDetail,
  };
};
export default connect(mapStateToProps)(ErrorScreen);
