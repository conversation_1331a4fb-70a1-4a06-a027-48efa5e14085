import React from 'react';
import FiltersList from 'mobile-holidays-react-native/src/SearchWidget/Components/PhoenixSearchPage/QuickFiltersHorizontalList';
import { holidayColors } from '../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';

const QuickFilterView = (props) => {
  const {
    holidayLandingGroupDto,
    onFilterClicked,
    isFilterCTAExpanded,
    selectedQuickFilterIndex,
    isStickyHeader = false,
    metaData = {},
    trackViewedSectionClickEvent = () => {},
  } = props || {};
  const { filters = [] } = holidayLandingGroupDto || {};
  return (
    <FiltersList
      showNewFilterUI
      metaData={metaData}
      showShadow={!isStickyHeader}
      groupingData={{ filtersSize: filters?.length, holidayLandingGroupDto }}
      onQuickFilterClicked={onFilterClicked}
      onAllFilterClicked={onFilterClicked}
      isExpanded={isFilterCTAExpanded}
      selectedQuickFilterIndex={selectedQuickFilterIndex}
      trackViewedSectionClickEvent={trackViewedSectionClickEvent}
      containerStyles={{ backgroundColor: holidayColors.white, ...paddingStyles.pb10, ...paddingStyles.pt10 }}
    />
  );
};

export default QuickFilterView;
