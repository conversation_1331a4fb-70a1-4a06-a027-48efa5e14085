import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import editIcon from '@mmt/legacy-assets/src/holidays/editIconCircle.png';
import LinearGradient from 'react-native-linear-gradient';
import * as DateUtil from '../../../../utils/HolidayDateUtils';
import { getPaxWithRoomText } from '../../../../utils/HolidayUtils';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { PHOENIX_GROUPING_V2_POPUPS } from '../../../Contants';
import { createTravellerObj } from 'mobile-holidays-react-native/src/utils/RoomPaxUtils';
import { shapeTypes } from '@mmt/legacy-commons/Common/Components/CoachMarks';

/* Components */
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import PackageGuideGallery, {
  isTiEntrySectionAvailable,
} from 'mobile-holidays-react-native/src/Common/Components/TripIdeas/PackageGuideGallery';
import { logHolidaysGroupingPDTEvents } from '../../../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const PackageHeaderDetails = ({
  holidayLandingGroupDto,
  metaDataDetail,
  togglePopup,
  containerStyles,
  trackClickEvent,
  tiSectionDetails,
  closeIntervention,
}) => {
  const { selectedDate, packageDate, rooms } = holidayLandingGroupDto || {};
  const { headerDetail = {} } = metaDataDetail || {};
  const { mainTitle = '', subTitle = 'Holiday Packages' } = headerDetail || {};
  const date = DateUtil.getDateText({ selectedDate, packageDate, removeDayName: true });
  const paxDetail = getPaxWithRoomText({ trvInfo: createTravellerObj(rooms), seprator: ', ' });
  const isTiSectionAvailable = isTiEntrySectionAvailable(tiSectionDetails?.cards || []);

  const trackEventsWithPDTV3 = () => {
    const eventName = 'edit_intent';
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value : eventName
    });
    trackClickEvent({ eventName });
  };
  const handleOpenEditOverlay = () => {
    togglePopup({ popup: PHOENIX_GROUPING_V2_POPUPS.EDIT_OVERLAY });
    trackEventsWithPDTV3();
  };
  return (
    <LinearGradient
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
      colors={['transparent', 'rgba(0, 0, 0, 0.5)', 'rgba(0, 0, 0, 0.8)']}
      locations={[0.1, 0.3, 1]}
      style={[styles.container, containerStyles]}
    >
      { isTiSectionAvailable && (
        <View style={styles.tiSectionView} />
      )}
      <View style={[paddingStyles.ph14, paddingStyles.pb12]}>
        <Text style={styles.packageNameFrom}>{mainTitle}</Text>
        <Text style={styles.packageNameTo}>{subTitle}</Text>
        <View style={styles.editDetailsRow}>
          <Text style={styles.packageDesc}>
            {date}
            {paxDetail}
          </Text>
          <DynamicCoachMark
            isSetCustomShape
            cueStepKey={'editTravelDates'}
            offsetHeight={isTiSectionAvailable ? 50 : 0}
            shapeObject={{
              type: shapeTypes.circle,
              right: '30%',
              radius: 20,
            }}
          >
            <TouchableOpacity activeOpacity={0} onPress={handleOpenEditOverlay}>
              <HolidayImageHolder style={styles.editIconStyle} defaultImage={editIcon} />
            </TouchableOpacity>
          </DynamicCoachMark>
        </View>
      </View>
      {isTiSectionAvailable && (
        <View style={marginStyles.mb10}>
          <PackageGuideGallery
            tiSectionDetails={tiSectionDetails}
            closeIntervention={closeIntervention}
            trackLocalClickEvent={trackClickEvent}
          />
        </View>
      )}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingTop: 50,
  },
  packageNameFrom: {
    color: holidayColors.white,
    ...fontStyles.labelBaseRegular,
    lineHeight: 17,
  },
  packageNameTo: {
    color: holidayColors.white,
    ...fontStyles.headingMedium,
  },
  packageDesc: {
    color: holidayColors.white,
    ...fontStyles.labelBaseRegular,
    lineHeight: 16,
  },
  editDetailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  editIconStyle: {
    width: 22,
    height: 22,
    ...marginStyles.ml10,
  },
  tiSectionView: {
    height: 30,
    width: '100%',
    bottom: 0,
    position: 'absolute',
    backgroundColor: holidayColors.white,
  },
});
export default PackageHeaderDetails;
