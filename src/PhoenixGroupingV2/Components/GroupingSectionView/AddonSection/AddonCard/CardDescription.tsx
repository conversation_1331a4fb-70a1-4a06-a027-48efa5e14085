import React from 'react';
import { StyleSheet, Text, TextStyle, View } from 'react-native';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { CardDescriptionProps, DescriptionProps } from '../AddonSectionType';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const DescriptionText = (props: DescriptionProps) => {
  return (
    <Text style={styles.descriptionContainer}>
      {props?.description?.map((item, index) => (
        <Text key={index} style={{ color: item.colour, fontWeight: item.bold ? 'bold' : 'normal', fontFamily: 'Lato' }}>
          {item.text + ' '}
        </Text>
      ))}
    </Text>
  );
};

const CardDescription = (props: CardDescriptionProps) => {
  return (
    <View style={styles.container}>
      <Text style={styles.header}>{props.header}</Text>
      {!!props.subHeader && <Text style={styles.subHeader}>{props.subHeader}</Text>}
      <DescriptionText description={props.description} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...marginStyles.ml12,
    width: '90%',
  },
  header: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  } as TextStyle,
  subHeader: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  } as TextStyle,
  descriptionContainer: {
    flexDirection: 'row',
    ...paddingStyles.pt6,
  },
});

export default CardDescription;
