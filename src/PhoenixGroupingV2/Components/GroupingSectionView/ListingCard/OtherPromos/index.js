import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { isEmpty } from 'lodash';

const OtherPromos = ({ otherPromo }) => {
  if (isEmpty(otherPromo)) {
    return null;
  }
  const { icon, info } = otherPromo || {};
  return (
    <View style={styles.offersContainer}>
      {!!icon && (
        <HolidayImageHolder imageUrl={icon} style={styles.promoIcon} resizeMode="contain" />
      )}
      {info?.length > 0 && (
        <Text style={styles.infoContainer} numberOfLines={1}>
          {info?.map((infoItem, index) => (
            <Text
              style={[infoItem.isEmphasized ? styles.infoTextBlack : styles.infoText]}
              key={`promo-info-item-${index}`}
            >
              {infoItem?.text}{' '}
            </Text>
          ))}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  promoIcon: {
    width: 20,
    height: 20,
    ...marginStyles.mr8,
  },
  offersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pt14,
    ...marginStyles.ml8,
  },
  infoContainer: {
    flex: 1,
  },
  infoText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  infoTextBlack: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.black,
  },
});
export default OtherPromos;
