import React from 'react';
import { LISTING_CARD_HEADER_TYPE, PHOENIX_GROUPING_V2_SECTIONS } from '../../../Contants';
import MMTLUXEHeader from '../../../../Common/Components/MMTLUXEHeader';
import MMTLUXENonPremiumHeader from '../../../../Common/Components/MMTLUXEHeader/MMTLuxeNonPremiumHeader';


interface ListingCardHeaderProps {
    type: string;
    cityName?: string;
}

const getHeaderData = (props: ListingCardHeaderProps) => {
    const { type, cityName = '' } = props;
    const cityNameToDisplay = cityName ?   `${cityName}${' '}` : '';
    const nonPremiumSubHeading = `Showing all other ${cityNameToDisplay}packages below`;

    switch (type) {
        case LISTING_CARD_HEADER_TYPE.PREMIUM:
            return {
                heading: 'Handpicked Holiday Packages',
                subHeading: 'Only For You',
            };
        case LISTING_CARD_HEADER_TYPE.NON_PREMIUM:
            return {
                heading: `That's all the premium holiday packages`,
                subHeading: nonPremiumSubHeading,
            };
        case LISTING_CARD_HEADER_TYPE.ALL_NON_PREMIUM:
            return {
                heading: 'Handpicked Holiday Packages',
                subHeading: 'Only for you',
            };
        default:
            return {
                heading: '',
                subHeading: '',
            };
    }
}
const ListingCardHeader: React.FC<ListingCardHeaderProps> = (props) => {
    const headerData = getHeaderData(props);
    if(props.type === LISTING_CARD_HEADER_TYPE.NON_PREMIUM) {
        return (
            <MMTLUXENonPremiumHeader {...headerData} />
        );

    }
    return (
        <MMTLUXEHeader {...headerData} />
    );
}

export default ListingCardHeader;