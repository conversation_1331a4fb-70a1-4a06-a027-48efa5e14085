import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import { holidayColors } from '../../../../Styles/holidayColors';

const NoPackageFound = ({ item }) => {
  const { heading } = item?.data || {};

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{heading}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...paddingStyles.ph16,
  },
  heading: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
});
export default NoPackageFound;
