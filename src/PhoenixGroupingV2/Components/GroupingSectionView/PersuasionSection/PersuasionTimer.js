import React from 'react';
import { isEmpty } from 'lodash';
import TrapazoidDesign from '../../../../ExpiryTimer/TrapazoidDesign';
import { getOfferTimerMobile } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';

export const TIMER_DESIGN_TYPES = {
  CHIP_DESIGN: 'chip_design',
  BOTTOM_TRAPAZOID_DESIGN: 'bottom_trapzoid_design',
};

class ExpiryTimer extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      label: '',
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
    };
  }

  componentWillMount() {
    this.updateExpiryTimerData();
  }

  componentDidMount() {
    this.interval = setInterval(() => this.updateExpiryTimerData(), 1000);
  }
  componentWillUnmount() {
    clearInterval(this.interval);
  }
  updateExpiryTimerData = () => {
    const { startTime, endTime, timerStartTime } = this.props;
    let time = -1;
    let labelText = '';
    const dealStartTime = startTime; // new Date(startTime).getTime();
    const dealEndTime = endTime; // new Date(endTime).getTime();
    const currentTime = new Date().getTime();

    // Check whether to show the timer or not
    if (timerStartTime > currentTime) {
      if (!isEmpty(this.state.label)) {
        this.setState({
          label: '',
          days: 0,
          hours: 0,
          minutes: 0,
          seconds: 0,
        });
      }
      return;
    }

    if (dealStartTime > currentTime) {
      time = dealStartTime - currentTime;
      labelText = 'Offer starts in';
    } else if (currentTime > dealStartTime && currentTime < dealEndTime) {
      time = dealEndTime - currentTime;
      labelText = 'Offer ends in';
    }
    if (time < 0) {
      this.setState({
        label: '',
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
      });
    } else {
      const s = Math.floor((time / 1000) % 60);
      const m = Math.floor((time / 1000 / 60) % 60);
      const h = Math.floor((time / (1000 * 60 * 60)) % 24);
      const d = Math.floor(time / (1000 * 60 * 60 * 24));
      this.setState({
        label: labelText,
        days: d,
        hours: h,
        minutes: m,
        seconds: s,
      });
    }
  };

  render() {
    const showOfferTimer = getOfferTimerMobile();
    if (isEmpty(this.state.label) || !showOfferTimer) {
      return [];
    }
    switch (this.props.type) {
      case TIMER_DESIGN_TYPES.BOTTOM_TRAPAZOID_DESIGN:
        return <TrapazoidDesign {...this.state} />;
      default:
        return [];
    }
  }
}

export default ExpiryTimer;
