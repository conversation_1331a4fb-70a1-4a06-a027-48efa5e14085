import React from 'react';
import { View, Text, StyleSheet, Image, ScrollView, TouchableOpacity } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import arrowIcon from '@mmt/legacy-assets/src/blueArrow.webp';
import { isEmpty } from 'lodash';
// import HTMLView from 'react-native-htmlview';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import { getDensityImageUrl } from '@mmt/legacy-commons/Common/utils/AppUtils';
import PersuasionTimer, { TIMER_DESIGN_TYPES } from './PersuasionTimer';
import { PHOENIX_GROUPING_V2_PAGE_NAMES } from '../../../Contants';
import { handleDeepLinkUrl } from '../../../Utils/PhoenixGroupingSectionCardClickHandler';
import { isRawClient } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import HTML from 'mobile-holidays-react-native/src/Common/Components/HTML';
const HTMLView= isRawClient() ? HTML :  'react-native-htmlview';
const MMT_BLACK_PERSUASION = 'MMT_BLACK_PERSUASION';
const tapableArea = {
  left: 10,
  top: 10,
  right: 10,
  bottom: 10,
};
const OffersCard = ({ persuasionDetails, trackClickEvent, trackViewedSectionClickEvent = () => {} }) => {
  const { header = '', cards = [], subHeader = '' } = persuasionDetails || {};

  if (cards.length === 0) {
    return null;
  }

  const renderPersuasionCard = ({ item, index }) => {
    const {
      header = '',
      text = '',
      color = '',
      iconUrl = '',
      subType = '',
      sectionCardTimeData = {},
      tncUrl = '',
      cta = '',
      deeplink,
    } = item || {};
    const [ftColor = holidayColors.black] = color?.split(',') || [];
    const getHeight = () => {
      if (!tncUrl && !cta) {
        return { height: 60 };
      } else {
        if (!header) {
          return { height: 40 };
        } else {
          return [];
        }
      }
    };

    const openUrl = (event) => {
      event.stopPropagation();
      trackViewedSectionClickEvent();
      trackClickEvent({ eventName: `persuasions_${index}_TnC` });
      handleDeepLinkUrl(tncUrl);
    };
    const openDeepLink = () => {
      const lastPage = PHOENIX_GROUPING_V2_PAGE_NAMES.PAGE_NAME;
      if (deeplink) {
        trackClickEvent({eventName: `${index}_persuasion_deeplink`});
        handleDeepLinkUrl(deeplink, lastPage, true);
      }
    };
    const handleTabSelect = () => {
      openDeepLink();
    };
    return (
      <TouchableOpacity
        style={[
          index === 0 && styles.firstTab,
          index === cards.length - 1 && styles.lastTab,
          styles.tab,
        ]}
        activeOpacity={cta || deeplink ? 0 : 1}
        onPress={handleTabSelect}
      >
          <View style={styles.offerContainer}>
            <HolidayImageHolder
              imageUrl={subType === MMT_BLACK_PERSUASION ? getDensityImageUrl(iconUrl) : iconUrl}
              style={styles.imageIconStyle}
            />
            <View style={{ flex: 1 }}>
              {!isEmpty(text) && (
                <View style={[styles.offerText, getHeight()]}>
                  {!!header && (
                    <HTMLView
                      textComponentProps={{
                        style: {
                          color: ftColor,
                          overflow: 'hidden',
                        },
                      }}
                      htmlInnerStylesClassName={'persuasionHtmlStyle'}
                      value={`${header.replace('\n', '')}`}
                      stylesheet={styles}
                    />
                  )}
                  {!!text && <HTMLView
                    textComponentProps={{ style: { color: ftColor } }}
                    value={`<span>${text.replace('\n', '')}</span>`}
                    htmlInnerStylesClassName={'persuasionHtmlStyle'}
                    stylesheet={styles}
                  />}
                  {(!!cta || !!tncUrl) && (
                    <View style={[styles.bottomCTA]}>
                      {!!cta && (
                        <TouchableOpacity onPress={openDeepLink} hitSlop={tapableArea}>
                          <Text style={styles.cta}>{cta?.toUpperCase()}</Text>
                        </TouchableOpacity>
                      )}

                      {!!tncUrl && (
                        <TouchableOpacity onPress={openUrl} hitSlop={tapableArea}>
                          <Text style={styles.tnc}>Terms And Conditions</Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  )}
                </View>
              )}
            </View>
            {!!deeplink && <Image source={arrowIcon} style={styles.arrowIcon} />}
          </View>
          <PersuasionTimer
            startTime={sectionCardTimeData?.startTime}
            endTime={sectionCardTimeData?.endTime}
            timerStartTime={sectionCardTimeData?.timerStartTime}
            type={TIMER_DESIGN_TYPES.BOTTOM_TRAPAZOID_DESIGN}
          />
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.offerHeading}>{header}</Text>
      <Text style={styles.offerSubHeading}>{subHeader}</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flexGrowOne}
      >
        {cards.map((item, index) => renderPersuasionCard({ item, index }))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
  },
  offerContainer: {
    width: 310,
    height: 100,
    ...paddingStyles.ph10,
    ...paddingStyles.pv10,
    backgroundColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius4,
    flexDirection: 'row',
    alignItems: 'flex-start',
    ...marginStyles.mr14,
    shadowColor: holidayColors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 7,
    zIndex: 2,
  },
  offerHeading: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
    ...marginStyles.mb2,
    ...paddingStyles.pl20,
  },
  offerSubHeading: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
    ...marginStyles.mb12,
    ...paddingStyles.pl20,
  },
  imageIconStyle: {
    width: 24,
    height: 24,
  },
  arrowIcon: {
    width: 15,
    height: 10,
    transform: [{ rotate: '270deg' }],
    ...marginStyles.mt4,
  },
  offerText: {
    ...marginStyles.mh10,
    flex: 1,
    ...fontStyles.labelSmallRegular,
  },
  flexGrowOne: {
    flexGrow: 1,
  },
  firstTab: {
    ...marginStyles.ml20,
  },
  lastTab: {
    ...marginStyles.mr4,
  },
  tab: {
    ...marginStyles.mb6,
  },
  p: {
    ...fontStyles.labelSmallRegular,
  },
  tnc: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
  },
  cta: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  bottomCTA: {
    marginTop: 5,
  },
});
export default OffersCard;
