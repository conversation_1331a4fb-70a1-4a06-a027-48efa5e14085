import React, {useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { getPaxDetails, rupeeFormatterUtils } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { isArray } from 'lodash';
import { PHOENIX_GROUPING_V2_SECTIONS } from '../../Contants';
import { getPageSectionVisitResponse, sectionTrackingPageNames, setPageSectionVisitResponse } from '../../../utils/SectionVisitTracking';
import HolidaySectionHeader from 'mobile-holidays-react-native/src/Common/Components/Phoenix/HolidaySectionHeader';

const RecentlyViewCards = (props) => {
  const { recentlyViewedSectionDetails = {}, onPackageClicked, index: sectionIndex, trackClickEvent } = props || {};
  const { recentlySeenPackages: recentPackages = [], noPackageFoundResetFilter } =
    recentlyViewedSectionDetails || {};
  let rvsSectionVisitResponse = {};
    if (recentPackages?.length === 0 || !isArray(recentPackages)) {
    return null;
  }

  useEffect(() => {
    rvsSectionVisitResponse = getPageSectionVisitResponse({
      pageName: sectionTrackingPageNames.PHOENIX_GROUPING_PAGE_V2_RVS,
    });
  }, []);
  const renderCard = ({ item, index }) => {
    const {
      imageDetail: { mainImage },
      rooms,
      destinationDetail,
      priceDetail: { price },
    } = item || {};
    const { adult, child } = getPaxDetails({ roomDetails: rooms });
    const { fullPath } = mainImage || {};
    const { duration } = destinationDetail || {};

    const adultText = `${adult} ${adult > 1 ? 'Adults' : 'Adult'}`;
    const childText = child ? `, ${child} Child ` : ' ';
    const isCustomized = item?.customizationDetail?.customized || false;

    const handleOpenRecentlyViewedClick = () => {
      onPackageClicked({
        item: { ...item, cardIndex: recentlyViewedSectionDetails?.cardIndex },
        userPackageMeta: item,
        sourceInfo: {
          name: PHOENIX_GROUPING_V2_SECTIONS.RECENTLY_VIEWED,
          key: `${index}`,
          noPackageFoundResetFilter,
        },
      });
    };

    return (
      <TouchableOpacity
        style={[
          index === 0 && styles.firstTab,
          index === recentPackages.length - 1 && styles.lastTab,
        ]}
        onPress={handleOpenRecentlyViewedClick}
      >
        <View style={styles.cardContainer}>
          <Image source={{ uri: fullPath }} style={styles.packageImageStyle} />
          <View style={styles.contentContainer}>
            {isCustomized ? (
              <View style={styles.tagContainer}>
                <Text numberOfLines={2} style={styles.packageTitle}>
                  {item.name}
                </Text>
                <View style={styles.customizedTag}>
                  <Text style={styles.tagText}>Customized</Text>
                </View>
              </View>
            ) : (
              <Text numberOfLines={1} style={styles.packageTitle}>
                {item.name}
              </Text>
            )}
            <Text style={styles.packageText}>
              {`${adultText}${childText}${duration}N/${duration + 1}D`}
            </Text>
            <Text style={styles.packageTitle}>{rupeeFormatterUtils(price)}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const _onViewableItemsChanged = React.useRef(({ viewableItems, changed }) => {
    viewableItems?.forEach((viewableItem) => {
      const { item = {}, isViewable, index } = viewableItem;
      const cardKey = `Card_${index}`
      const sectionKey = `Section_RVS_${sectionIndex}`
      const sectionName = `${sectionKey}|${cardKey}`;

      if (!rvsSectionVisitResponse[sectionName]) {
        rvsSectionVisitResponse[sectionName] = 1;
        setPageSectionVisitResponse({
          pageName: sectionTrackingPageNames.PHOENIX_GROUPING_PAGE_V2_RVS,
          value: rvsSectionVisitResponse,
        });
        const eventName = `Viewed_${sectionName}`;
        trackClickEvent({ eventName, prop1: sectionName });
      }
    });
  }, []);


  const viewConfigRef = React.useRef({ viewAreaCoveragePercentThreshold: 50 })

  return (
    <View style={styles.container}>
      <HolidaySectionHeader heading={recentPackages.length ? 'Recently Viewed' : ''} styles={styles} >
      <FlatList
        horizontal
        data={recentPackages}
        renderItem={renderCard}
        showsHorizontalScrollIndicator={false}
        viewabilityConfig={viewConfigRef.current}
        contentContainerStyle={styles.flexGrowOne}
        onViewableItemsChanged={_onViewableItemsChanged.current}
        keyExtractor={(item, index) => `${index}_${item?.packageId}`}
      />      
      </HolidaySectionHeader>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // ...marginStyles.mb20,
  },
  cardContainer: {
    backgroundColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pv8,
    ...paddingStyles.pr0,
    ...paddingStyles.pl12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: 272,
    ...marginStyles.mr12,
  },
  headerContainer: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
    ...marginStyles.mb12,
    ...paddingStyles.pl16,
  },
  packageImageStyle: {
    width: 64,
    height: 64,
    ...holidayBorderRadius.borderRadius16,
    ...marginStyles.mr8,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
    ...paddingStyles.pv2,
  },
  packageTitle: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
    lineHeight: 17,
    ...marginStyles.mr6,
    flex: 1,
  },
  packageText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 15,
  },
  flexGrowOne: {
    flexGrow: 1,
  },
  firstTab: {
    ...marginStyles.ml16,
  },
  lastTab: {
    ...marginStyles.mr4,
  },
  customizedTag: {
    ...paddingStyles.ph12,
    ...paddingStyles.pv4,
    borderTopLeftRadius: 16,
    borderBottomLeftRadius: 16,
    backgroundColor: holidayColors.fadedYellow,
    alignSelf: 'flex-start',
  },
  tagText: {
    fontSize: 10,
    fontWeight: '700',
    lineHeight: 14,
    color: holidayColors.yellow,
  },
  tagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
export default RecentlyViewCards;
