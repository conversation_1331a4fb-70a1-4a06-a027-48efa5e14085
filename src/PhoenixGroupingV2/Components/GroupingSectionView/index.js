import React, { useCallback, useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { connect } from 'react-redux';
import {
  checkIsListingRedirection,
  createFiltersIdMap,
  getSelectedGroup,
  openPhoenixPackagePageFromGroupingV2,
} from '../../Utils/PhoenixGroupingV2Utils';
import { PHOENIX_GROUPING_V2_POPUPS, PHOENIX_GROUPING_V2_SECTIONS } from '../../Contants';
import * as PhoenixSectionCardClickHandler from '../../Utils/PhoenixGroupingSectionCardClickHandler';
import { getDimensionsForAd } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import {
  getCardCategoryForPdt,
  getCardClickEventName,
  getCardProp1Value,
  getPhoenixGroupingV2PageName,
} from '../../Utils/PhoenixGroupingV2TrackingUtils';

/* Actions */
import {
  holidayLandingGroupDtoUpdate,
  // initialLoadAction,
  removeFilterAndReload,
  // resetPhoenixGroupingV2DataAfterEdit,
} from '../../Actions/groupingV2Actions';
import { resetListingState, listingPackagesLoading } from '../../Actions/listingPackagesActions';
import { groupMetaLoading } from '../../Actions/groupMetaActions';
import { showMMTBlack, showMMTPersonalization, showMMTPersonalizationV2, showNewRVSSection } from '../../../utils/HolidaysPokusUtils';

/* Components  */
import ListingCard from './ListingCard';
import ListingCardLoader from './ListingCard/Loader';
import AddonSection from './AddonSection';
import RecentlyViewCards from './RecentlyViewedCard';
import PersausionSection from './PersuasionSection';
import TripIdeaTravelStories from 'mobile-holidays-react-native/src/Grouping/Components/TripIdeaSections/TravelStories';
import HolidayDidYouKnowBanner from 'mobile-holidays-react-native/src/Common/Components/Phoenix/HolidayDidYouKnowBanner';
import HolidayCollectionBanner from 'mobile-holidays-react-native/src/Common/Components/Phoenix/HolidayCollectionBanner';
import HolidayAdCard from 'mobile-holidays-react-native/src/Common/Components/HolidayAdCard';
import TripIdeaDestinationGuide from 'mobile-holidays-react-native/src/Grouping/Components/TripIdeaSections/DestinationGuide';
import ErrorPage from '../Error';
import ContentCard from './ContentCard';
import NoPackageFound from './NoPackageFound';
import AppliedFiltersList from 'mobile-holidays-react-native/src/SearchWidget/Components/PhoenixSearchPage/AppliedFiltersList';
import RVSSectionV2 from 'mobile-holidays-react-native/src/Common/Components/Sections/RVSV2';
import { logHolidaysGroupingPDTEvents } from '../../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import MembershipCard from '../../../Common/Components/Membership/MembershipCard';
import { CONTENT_TYPES } from '../../../Common/Components/Membership/utils/constants';
import { TRACKING_EVENTS } from 'mobile-holidays-react-native/src/HolidayTrackingConstants';
import {showCommonOverlay, hideCommonOverlay} from "mobile-holidays-react-native/src/Common/Components/CommonOverlay/CommonOvelayAction";

const GroupingSectionView = ({
  // state to props
  fabCta,
  holidayLandingGroupDto,
  recentPackages,
  groupMetaState,
  metaDataState,
  // state to props

  // actions
  holidayLandingGroupDtoUpdate,
  removeFilterAndReload,
  listingPackagesLoading,
  groupMetaLoading,
  resetListingState,
  // actions

  item,
  index,
  variantTypeFromPokus,
  setPackageVariant,
  trackClickEvent = null,
  trackPageExit = null,
  closeIntervention,
  togglePopup,
  setMmtBlackBottomSheetDetails,
  trackViewedSectionClickEvent = () => {},
  setMmtBlackBucketDetail = () => {},
    hideCommonOverlay,
    showCommonOverlay,
}) => {
  const { isGroupMetaDataLoading, selectedGroupCollectionKey, groupMetaDetail } =
    groupMetaState || {};
  const currentSelectedGroupCollection = getSelectedGroup({
    selectedGroupKey: selectedGroupCollectionKey,
    groupMetaDetail: groupMetaDetail,
  });
  const { filters = [], masterListingFilters = [] } = holidayLandingGroupDto || {};
  const filtersIdMap = createFiltersIdMap(masterListingFilters);

  const trackHandleCardClickEvents = ({ eventName, prop1 = '' ,isPremium = false }) => {
    trackViewedSectionClickEvent();
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.contentClicked,
      value: eventName, 
      category: getCardCategoryForPdt({ isPremium }),  
    });
    trackClickEvent({ eventName, prop1 });
    trackPageExit();
  };

  const handleOpenPackageClick = ({ item, userPackageMeta = null, sourceInfo = {} } = {}) => {
    const eventName = getCardClickEventName({
      cardItem: item,
      cardIndex: `${item?.cardIndex}` || `${index}`,
      groupCollection: currentSelectedGroupCollection,
      sourceInfo,
    });
    const prop1 = getCardProp1Value({ cardItem: item });
    trackHandleCardClickEvents({ eventName, prop1,  isPremium: item?.isPremium });
    openPhoenixPackagePageFromGroupingV2({
      holidayLandingGroupDto,
      packageCard: item,
      userPackageMeta,
    });
  };

  const onPressGroupingSection = ({ card, data }) => {
    trackViewedSectionClickEvent();
    PhoenixSectionCardClickHandler.genericOnPressHandlingFromGrouping(card, data);
  };

  const trackAppliedFilterCloseEvents = ({ eventName }) => {
    trackClickEvent({ eventName });
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
  };
  const handleAppliedFilterClose = ({ id, value, data }) => {
    const { errorMessage = '' } = data || {};
    const eventName = `${errorMessage}_collection_${currentSelectedGroupCollection?.index}_${filtersIdMap[id]?.urlParam}_${value}`;
    trackAppliedFilterCloseEvents({ eventName });
    const updatedCriteria = holidayLandingGroupDto?.filters.filter((item) => item?.id !== id);
    holidayLandingGroupDtoUpdate({ ...holidayLandingGroupDto, filters: updatedCriteria });
    const isListingRedirection = checkIsListingRedirection({
      holidayLandingGroupDto,
      listingRedirection: metaDataState?.metaDataDetail?.listingRedirection,
    });
    if (!isListingRedirection) {
      groupMetaLoading(); // not to dispatch groupMetaLoading if listing page
    }
    listingPackagesLoading();
    resetListingState();
    removeFilterAndReload({
      holidayLandingGroupDto: { ...holidayLandingGroupDto, filters: updatedCriteria },
      selectedGroup: currentSelectedGroupCollection,
    });
  };

  const handleMmtBlackTrackEvent = ({ eventName = '', prop1 = '', mmtBlackBucketDetail = {} }) => {
    const {gcBucket = null, myCashBucket = null, effectivePriceBucket = null} = mmtBlackBucketDetail || {};
    const evar46 = `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`;
    trackClickEvent({eventName, prop1, omniData: {[TRACKING_EVENTS.M_V46]: evar46}});
  };

  const handleMemberShipOnKnowMorePress = useCallback((bottomSheet, mmtBlackBucketDetail, ctaText) => {
    const eventName = `GC_${ctaText.split(' ').join('_')}`;
    setMmtBlackBottomSheetDetails(bottomSheet);
    setMmtBlackBucketDetail(mmtBlackBucketDetail);
    togglePopup({ popup: PHOENIX_GROUPING_V2_POPUPS.MMT_BLACK_BOTTOMSHEET });
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    handleMmtBlackTrackEvent({eventName, mmtBlackBucketDetail});
  }, []);

  const handleMembershipOnLayout = ({sectionOrder = 1,isPersonlisation = false}) => {
    const eventName = isPersonlisation ? `Loaded_section_PZN` : `Loaded_section_MMTBlack`;
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.contentSeenUser,
      value: eventName + `|${sectionOrder}`,
      shouldTrackToAdobe:false
    });
    handleMmtBlackTrackEvent({
      eventName,
      prop1: sectionOrder,
    });
  }

  const getCardData = (type) => {
    switch (type) {
      case PHOENIX_GROUPING_V2_SECTIONS.MMT_BLACK:
        const {
          mmtBlackDetail: { section, bottomSheet, mmtBlackPdtData={}},
        } = item?.cards?.[0];
        return (
          showMMTBlack() && (
            <View onLayout={() => handleMembershipOnLayout({ sectionOrder: item?.order, isPersonlisation: false })}>
              <MembershipCard
                sectionDetails={section}
                containerStyles={styles.membershipCard}
                onKnowMorePress={() =>
                  handleMemberShipOnKnowMorePress(
                    bottomSheet,
                    mmtBlackPdtData?.mmtBlackBucketDetail,
                    section?.cta?.text,
                  )
                }
                mmtBlackPdtEvents={logHolidaysGroupingPDTEvents}
                trackMemberShipLoadEvent={handleMmtBlackTrackEvent}
                sectionOrder={item?.order}
                mmtBlackBucketDetail={mmtBlackPdtData?.mmtBlackBucketDetail}
                sendLoadEvents={false}
              />
            </View>
          )
        );
        case PHOENIX_GROUPING_V2_SECTIONS.PERSONALIZATION:
          //Taking data from 0th element as only one card will be present in card array 
          const { personalizationDetail }  = item?.cards?.[0]; 
          return (
            showMMTPersonalization() &&
            showMMTPersonalizationV2() && (
              <View
                onLayout={() =>
                  handleMembershipOnLayout({ sectionOrder: item?.order, isPersonlisation: true })
                }
              >
                <MembershipCard
                  sectionDetails={personalizationDetail?.section}
                  containerStyles={styles.membershipCard}
                  onKnowMorePress={() => {}}
                  mmtBlackPdtEvents={logHolidaysGroupingPDTEvents}
                  trackMemberShipLoadEvent={handleMmtBlackTrackEvent}
                  sectionOrder={item?.order}
                  sendLoadEvents={false}
                />
              </View>
            )
          );
      case PHOENIX_GROUPING_V2_SECTIONS.PERSUASIONS:
        return (
          <PersausionSection
            persuasionDetails={item}
            trackClickEvent={trackClickEvent}
            trackViewedSectionClickEvent={trackViewedSectionClickEvent}
          />
        );
      case PHOENIX_GROUPING_V2_SECTIONS.RECENTLY_VIEWED:
        if (showNewRVSSection()) {
          return [];
        }
        return (
          <RecentlyViewCards
            recentlyViewedSectionDetails={item}
            index={index}
            onPackageClicked={handleOpenPackageClick}
            trackClickEvent={trackClickEvent}
          />
        );
      case PHOENIX_GROUPING_V2_SECTIONS.PAYMENT_DROP_OFF:
        if (showNewRVSSection()) {
          return (
            <RVSSectionV2
              data={item}
              itemData={item?.cards || []}
              pageName={getPhoenixGroupingV2PageName({
                isListing: holidayLandingGroupDto?.isListing,
              })}
              trackLocalClickEvent={trackClickEvent}
              trackViewedSectionClickEvent={trackViewedSectionClickEvent}
            />
          );
        } else {
          return [];
        }
      case PHOENIX_GROUPING_V2_SECTIONS.LISTING_CARD:
        return (
          <ListingCard
            item={item}
            index={index}
            variantTypeFromPokus={variantTypeFromPokus}
            setPackageVariant={setPackageVariant}
            handleOpenPackageClick={handleOpenPackageClick}
            trackClickEvent={trackClickEvent} // For Image Carousal
          />
        );
      case PHOENIX_GROUPING_V2_SECTIONS.TRIP_IDEAS_STORIES:
        return (
          <TripIdeaTravelStories
            travelStoriesData={item}
            closeIntervention={closeIntervention}
            groupingData={{
              pageDataMap: holidayLandingGroupDto.pageDataMap,
              requestId: holidayLandingGroupDto.requestId,
              holidayLandingGroupDto,
            }}
            containerStyles={marginStyles.mb0}
            fabCta={fabCta}
            omniPageName={getPhoenixGroupingV2PageName({
              isListing: holidayLandingGroupDto?.isListing,
            })}
            trackViewedSectionClickEvent={trackViewedSectionClickEvent}
          />
        );
      case PHOENIX_GROUPING_V2_SECTIONS.TRIP_IDEAS_DESTINATION_GUIDE:
        return (
          <TripIdeaDestinationGuide
            destinationGuide={item}
            closeIntervention={closeIntervention}
            groupingData={{
              pageDataMap: holidayLandingGroupDto.pageDataMap,
              requestId: holidayLandingGroupDto.requestId,
              holidayLandingGroupDto,
            }}
            containerStyles={marginStyles.mb0}
            fabCta={fabCta}
            omniPageName={getPhoenixGroupingV2PageName({
              isListing: holidayLandingGroupDto?.isListing,
            })}
            trackViewedSectionClickEvent={trackViewedSectionClickEvent}
          />
        );
      case PHOENIX_GROUPING_V2_SECTIONS.DID_YOU_KNOW:
        return (
          <HolidayDidYouKnowBanner
            key={`HolidayDidYouKnowBanner_${item.id}`}
            data={item}
            onPress={onPressGroupingSection}
          />
        );
      case PHOENIX_GROUPING_V2_SECTIONS.COLLECTION_BANNER:
        return (
          <HolidayCollectionBanner
            key={`HolidayCollectionBanner_${item.id}`}
            data={item}
            onPressCard={onPressGroupingSection}
            onPressExplore={onPressGroupingSection}
            containerStyles={marginStyles.mb0}
          />
        );
      case PHOENIX_GROUPING_V2_SECTIONS.AD_BANNER:
        //  const  getuuidLists = this.getUuid(groupDataItem);    will be required for multi-ad
        const { height, width } = getDimensionsForAd();
        const resizeMode = 'cover';
        return (
          <HolidayAdCard
            card={item?.cards?.[0]}
            data={item}
            adStyles={{
              styles: { adCardContainer: styles.adCardContainer, adImage: styles.adImage },
              resizeMode,
            }}
            adDimensions={{ width, height }}
            onPress={() => {}}
          />
        );
      case PHOENIX_GROUPING_V2_SECTIONS.CONTENT_CARD:
        return (
          <View style={paddingStyles.ph16}>
            <ContentCard
              data={item}
              trackClickEvent={trackClickEvent}
              index={index}
              trackViewedSectionClickEvent={trackViewedSectionClickEvent}
            />
          </View>
        );
      case PHOENIX_GROUPING_V2_SECTIONS.NO_PACKAGE_FOUND_SHOW_SIMILAR:
        return <NoPackageFound item={item} />;
      case PHOENIX_GROUPING_V2_SECTIONS.ERROR_PAGE:
        return (
          <ErrorPage {...item.data} togglePopup={togglePopup} trackClickEvent={trackClickEvent}>
            <AppliedFiltersList
              criterias={filters || []}
              filtersIdMap={filtersIdMap}
              containerStyles={{ paddingVertical: 5 }}
              onRemove={(id, value) => handleAppliedFilterClose({ id, value, data: item?.data })}
            />
          </ErrorPage>
        );
      case PHOENIX_GROUPING_V2_SECTIONS.ADDON:
        return (
          <AddonSection
            {...item}
            trackClickEvent={trackClickEvent}
            showOverlay={showCommonOverlay}
            hideOverlay={hideCommonOverlay}
          />
        );
      case PHOENIX_GROUPING_V2_SECTIONS.LOADER_PAGE:
        return <ListingCardLoader />;
      default:
        return [];
    }
  };

  return (
    <View>
      {isGroupMetaDataLoading && <ListingCardLoader />}
      {Object.values(PHOENIX_GROUPING_V2_SECTIONS).includes(item?.sectionCode) && (
        <View style={styles.container}>{getCardData(item?.sectionCode)}</View>
      )}
    </View>
  );
};

const mapStateToProps = (state) => {
  return {
    metaDataState: state.holidaysPhoenixGroupingV2.metaDataDetail,
    fabCta: state.holidaysPhoenixGroupingV2.fabCtaDetail?.fabCtaDetail,
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
    recentPackages: state.holidaysPhoenixGroupingV2.listingPackagesData.recentPackages,
    groupMetaState: state.holidaysPhoenixGroupingV2.groupMetaDetail,
  };
};

const mapDispatchToProps = {
  holidayLandingGroupDtoUpdate,
  removeFilterAndReload,
  listingPackagesLoading,
  groupMetaLoading,
  resetListingState,
  showCommonOverlay,
  hideCommonOverlay,
};
const styles = StyleSheet.create({
  adCardContainer: {
    ...marginStyles.mh16,
    borderRadius: 8,
  },
  adImage: {
    height: getDimensionsForAd()?.height,
    borderRadius: 8,
  },
  container: {
    ...marginStyles.mb16,
  },
  membershipCard: {
    ...marginStyles.ml16,
    ...marginStyles.mr16,
    ...marginStyles.mt16,
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(GroupingSectionView);
