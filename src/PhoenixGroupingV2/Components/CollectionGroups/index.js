import React from 'react';
import { View, StyleSheet, ScrollView, Text } from 'react-native';
import { connect } from 'react-redux';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import CollectionGroupsLoader from './CollectionGroupLoader';
import CollectionGroupTabs from './CollectionTab';
import { getSelectedGroup } from '../../Utils/PhoenixGroupingV2Utils';
import { isLuxeFunnel } from 'mobile-holidays-react-native/src/utils/HolidayUtils';

const CollectionGroups = ({
  groupMetaState = {},
  metaDataState = {},
  holidayLandingGroupDto = {},
  isStickyHeader,
  handleScrollToTop,
  trackClickEvent = () => {},
  trackViewedSectionClickEvent = () => {},
}) => {
  const {
    isGroupMetaDataSuccess,
    isGroupMetaDataLoading,
    groupMetaDetail,
    selectedGroupCollectionKey,
  } = groupMetaState || {};

  // if Luxe funnel, do not show the tabs and this is done by backend itself but shimmer should also be not shown thats why returning null
  if(isLuxeFunnel()) {
    return null;
  }

  const tabContainer = isStickyHeader ? 'containerSM' : 'container';
  const selectedGroupDescription = getSelectedGroup({ selectedGroupKey: selectedGroupCollectionKey, groupMetaDetail })?.description || '';

  return isGroupMetaDataLoading ? (
    <View style={[styles[tabContainer], paddingStyles.pt10]}>
      <CollectionGroupsLoader isStickyHeader={isStickyHeader} />
    </View>
  ) : !isGroupMetaDataLoading && isGroupMetaDataSuccess ? (
    <View>
      <View style={styles[tabContainer]}>
        <CollectionGroupTabs
          isStickyHeader={isStickyHeader}
          handleScrollToTop={handleScrollToTop}
          trackViewedSectionClickEvent={trackViewedSectionClickEvent}
          trackClickEvent={trackClickEvent}
        />
      </View>
      {!isStickyHeader && !!selectedGroupDescription && (
        <Text style={styles.descriptionText}>{selectedGroupDescription?.trim()}</Text>
      )}
    </View>
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // ...paddingStyles.pv2,
    backgroundColor: holidayColors.lightGray2,
  },
  containerSM: {
    backgroundColor: holidayColors.lightGray2,
    shadowColor: holidayColors.black,
    shadowOffset: { width: -2, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    // ...paddingStyles.pv2,
  },
  tabCard: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  tab: {
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.disableGrayBg,
    backgroundColor: holidayColors.white,
    ...marginStyles.mr8,
    ...paddingStyles.ph16,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  selectedTab: {
    borderColor: holidayColors.primaryBlue,
    backgroundColor: holidayColors.lightBlueBg,
  },
  titleHead: {
    color: holidayColors.black,
    ...fontStyles.labelSmallBold,
    lineHeight: 16,
  },
  titleHeadNormal: {
    color: holidayColors.black,
    ...fontStyles.labelSmallRegular,
    lineHeight: 16,
  },
  titleText: {
    color: holidayColors.lightGray,
    ...fontStyles.labelSmallRegular,
    lineHeight: 16,
  },
  selectedText: {
    ...fontStyles.labelSmallBold,
  },
  firstTab: {
    ...marginStyles.ml12,
  },
  lastTab: {
    ...marginStyles.mr12,
  },
  flexGrowOne: {
    flexGrow: 1,
  },
  height48: {
    height: 48,
  },
  height30: {
    height: 30,
  },
  descriptionText: {
    lineHeight: 18,
    ...fontStyles.labelBaseRegular,
    color: holidayColors.darkGray,
    ...paddingStyles.ph16,
    ...paddingStyles.pb10,
  },
});

const mapStateToProps = (state) => {
  return {
    groupMetaState: state.holidaysPhoenixGroupingV2.groupMetaDetail,
    metaDataState: state.holidaysPhoenixGroupingV2.metaDataDetail,
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
  };
};

const mapDispatchToProps = {};

export default connect(mapStateToProps, mapDispatchToProps)(CollectionGroups);
