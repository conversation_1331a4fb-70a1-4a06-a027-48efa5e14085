import React, { useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { connect } from 'react-redux';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import {
  getAbortController,
  resetAbortController,
} from '../../../utils/NetworkUtils/AbortController';
import { shapeTypes } from '@mmt/legacy-commons/Common/Components/CoachMarks';

/* Actions */
import { setSelectedGroupKey } from '../../Actions/groupMetaActions';
import { loadListingAction } from '../../Actions/groupingV2Actions';

/* Components */
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import { logHolidaysGroupingPDTEvents } from '../../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

import HolidayPDTMetaIDHolder from '../../../utils/HolidayPDTMetaIDHolder';
const SHORT_NAME_TEXT_LIMIT = 25;

const getItemLayout = (data, index) => {
  return { length: 140, offset: 140 * index, index };
};
const CollectionGroupTab = ({
  groupMetaState = {},
  metaDataState = {},
  holidayLandingGroupDto = {},
  isStickyHeader,
  setSelectedGroupKey = null,
  loadListingAction,
  listingPackagesState,
  handleScrollToTop = null,
  trackClickEvent = () => {},
  trackViewedSectionClickEvent = () => {},
}) => {
  const flatListRef = useRef(null);
  const { groupMetaDetail = {}, selectedGroupCollectionKey = 0 } = groupMetaState || {};
  const groupDetailsList = groupMetaDetail?.groupDetailsList || [];
  const tabHeight = isStickyHeader ? 'height30' : 'height48';
  const tabNormal = isStickyHeader ? 'titleHeadNormal' : 'titleHead';
  const tabIndex = groupDetailsList?.findIndex(
    (item) => item.groupKey === selectedGroupCollectionKey,
  ) 
  const selectedTabIndex = tabIndex !== -1 ? tabIndex : 0;

  if (groupDetailsList?.length <= 1) {
    return [];
  }
  const renderTab = ({ item, index }) => {
    const { shortName = '', groupKey = '', totalPackageCount = 0 } = item || {};
    const isSelected = groupKey === selectedGroupCollectionKey;
    const isTabDisabled = totalPackageCount === 0 && !isSelected;
    const trackTabSelectEvents = async() => {
      const eventName = `collection_${index}`;
      logHolidaysGroupingPDTEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: eventName + '|' + groupKey,
      });
      await HolidayPDTMetaIDHolder.getInstance().setPdtId();
      trackViewedSectionClickEvent();
      trackClickEvent({ eventName, prop1: groupKey });
    };
    const handleTabSelect = () => {
      if (handleScrollToTop) {
        handleScrollToTop();
      }
      if (isSelected) {
        return; // do nothing if current tab is selected again
      } else {
        trackTabSelectEvents();
        const isDataForGroupPresent = Boolean(listingPackagesState[groupKey]);
        setSelectedGroupKey(groupKey);
        flatListRef.current.scrollToIndex({ animated: true, index: index });
        if (!isDataForGroupPresent) {
          resetAbortController();
          loadListingAction({
            holidayLandingGroupDto,
            metaDataResponse: metaDataState,
            selectedGroupCollection: item,
            abortController: getAbortController(),
          });
        }
      }
    };
    return (
      <TouchableOpacity
        style={[
          styles.tab,
          isSelected ? styles.selectedTab : isTabDisabled ? styles.disabledTab : styles.tabColor,
          index === 0 && styles.firstTab,
          index === groupDetailsList.length - 1 && styles.lastTab,
        ]}
        disabled={isTabDisabled}
        onPress={handleTabSelect}
        key={`group-${groupKey}`}
      >
        <View style={[styles.tabCard, styles[tabHeight]]}>
          <Text
            style={[
              styles[tabNormal],
              isSelected ? styles.selectedText : isTabDisabled ? styles.disabledText : {},
            ]}
          >
            {shortName.slice(0, SHORT_NAME_TEXT_LIMIT)}
            {shortName?.length > SHORT_NAME_TEXT_LIMIT ? '...' : ''}
          </Text>
          {!isStickyHeader && (
            <Text style={styles.titleText}>
              {totalPackageCount} Package{totalPackageCount > 1 ? 's' : ''}
            </Text>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderList = () => (
    <View style={[styles.container, isStickyHeader ? paddingStyles.pv8 : paddingStyles.pb10]}>
      <FlatList
        horizontal
        ref={flatListRef}
        data={groupDetailsList}
        renderItem={renderTab}
        showsHorizontalScrollIndicator={false}
        initialScrollIndex={selectedTabIndex}
        getItemLayout={getItemLayout}
        onScrollToIndexFailed={() => {}}
        contentContainerStyle={styles.tabContainer}
        keyExtractor={(_, index) => `item-${_.groupKey}`}
      />
    </View>
  );

  return isStickyHeader ? (
    renderList()
  ) : (
    <DynamicCoachMark
      isSetCustomShape
      containerStyle={{ flex: 1 }}
      cueStepKey={'collectionGroups'}
      offsetHeight={10}
      shapeObject={{
        type: shapeTypes.rect,
        height: 80,
        borderRadius: 16,
      }}
    >
      {renderList()}
    </DynamicCoachMark>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    backgroundColor: holidayColors.lightGray2,
  },
  tabContainer: {
    flex: 1,
  },
  tabCard: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  tab: {
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.mr8,
    ...paddingStyles.ph16,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  tabColor: {
    borderColor: holidayColors.disableGrayBg,
    backgroundColor: holidayColors.white,
  },
  selectedTab: {
    borderColor: holidayColors.primaryBlue,
    backgroundColor: holidayColors.lightBlueBg,
  },
  disabledTab: {
    borderColor: holidayColors.grayBorder,
    backgroundColor: holidayColors.lightGray2,
  },
  titleHead: {
    color: holidayColors.black,
    ...fontStyles.labelSmallBold,
    lineHeight: 16,
  },
  titleHeadNormal: {
    color: holidayColors.black,
    ...fontStyles.labelSmallRegular,
    lineHeight: 16,
  },
  titleText: {
    color: holidayColors.lightGray,
    ...fontStyles.labelSmallRegular,
    lineHeight: 16,
  },
  selectedText: {
    ...fontStyles.labelSmallBold,
  },
  disabledText: {
    color: holidayColors.lightGray,
  },
  firstTab: {
    ...marginStyles.ml12,
  },
  lastTab: {
    ...marginStyles.mr12,
  },
  flexGrowOne: {
    flexGrow: 1,
  },
  height48: {
    height: 48,
  },
  height30: {
    height: 30,
  },
  descriptionText: {
    lineHeight: 18,
    ...fontStyles.labelBaseRegular,
    color: holidayColors.darkGray,
    ...paddingStyles.ph16,
    ...paddingStyles.pb10,
    ...marginStyles.mt10,
  },
});

const mapStateToProps = (state) => {
  return {
    groupMetaState: state.holidaysPhoenixGroupingV2.groupMetaDetail,
    metaDataState: state.holidaysPhoenixGroupingV2.metaDataDetail,
    listingPackagesState: state.holidaysPhoenixGroupingV2.listingPackagesData,
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
    listingPackagesState: state.holidaysPhoenixGroupingV2.listingPackagesData,
  };
};

const mapDispatchToProps = {
  setSelectedGroupKey,
  loadListingAction,
};

export default connect(mapStateToProps, mapDispatchToProps)(CollectionGroupTab);
