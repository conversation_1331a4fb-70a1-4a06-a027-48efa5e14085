import React from 'react';
import { StyleSheet, View, ScrollView } from 'react-native';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import ShimmerCommon from '@Frontend_Ui_Lib_App/Shimmer';

const CollectionTab = ({ isStickyHeader }) => {
  return (
    <View style={styles.shimmerTab}>
      <View style={[styles.animatedOuter, styles.width70, styles.height12, styles.marginBottom6]}>
        <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
      </View>
      {!isStickyHeader && (
        <View style={[styles.animatedOuter, styles.width50, styles.height12]}>
          <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
        </View>
      )}
    </View>
  );
};
const CollectionGroupsLoader = ({ isStickyHeader }) => {
  const collectionArray = [0, 1, 2, 3, 4];
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={[styles.tabContainer, styles.flexGrowOne]}
    >
      {collectionArray.map((item) => (
        <CollectionTab key={`index-${item}`} isStickyHeader={isStickyHeader} />
      ))}
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  container: {
    ...paddingStyles.pv10,
  },
  tabContainer: {
    ...marginStyles.mb10,
    ...paddingStyles.pl14,
  },
  shimmerTab: {
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.disableGrayBg,
    backgroundColor: holidayColors.white,
    ...marginStyles.mr8,
    ...paddingStyles.ph16,
    ...paddingStyles.pv8,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    flex: 1,
  },

  width70: { width: 70 },
  width50: { width: 50 },
  height12: { height: 14 },
  animatedOuter: {
    backgroundColor: '#e6e6e6',
    position: 'relative',
    overflow: 'hidden',
  },
  marginBottom6: {
    ...marginStyles.mb6,
  },
});
export default CollectionGroupsLoader;
