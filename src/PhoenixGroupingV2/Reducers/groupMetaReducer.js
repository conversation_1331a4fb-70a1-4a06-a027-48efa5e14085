import {
  GROUP_META_ACTION_TYPES,
  PHOENIX_GROUPING_V2_ACTION_TYPES,
} from '../Actions/PhoenixGroupingV2ActionTypes';

const initialMetaDataState = {
  groupMetaDetail: {},
  selectedGroupCollectionKey: 0,
  isGroupMetaDataLoading: false,
  isGroupMetaDataError: false,
  isGroupMetaDataSuccess: false,
};
const groupMetaDetail = (state = initialMetaDataState, action) => {
  switch (action.type) {
    case GROUP_META_ACTION_TYPES.GROUP_META_LOADING:
      return {
        ...state,
        isGroupMetaDataLoading: true,
      };
    case GROUP_META_ACTION_TYPES.GROUP_META_SUCCESS:
      return {
        ...state,
        groupMetaDetail: action.groupMetaDetail,
        isGroupMetaDataLoading: false,
        isGroupMetaDataSuccess: true,
      };
    case GROUP_META_ACTION_TYPES.GROUP_META_ERROR:
      return {
        error: action.error,
        isGroupMetaDataError: true,
        isGroupMetaDataLoading: false,
      };
    case GROUP_META_ACTION_TYPES.SET_SELECTED_GROUP:
      return {
        ...state,
        selectedGroupCollectionKey: action.selectedGroupCollectionKey,
      };
    case PHOENIX_GROUPING_V2_ACTION_TYPES.RESET_STATE:
      return {
        ...initialMetaDataState,
      };
    case PHOENIX_GROUPING_V2_ACTION_TYPES.RESET_STATE_AFTER_EDIT:
      return {
        ...initialMetaDataState,
      };
    case GROUP_META_ACTION_TYPES.RESET_GROUP_META:
      return {
        ...initialMetaDataState,
      };
    default:
      return state;
  }
};

export default groupMetaDetail;
