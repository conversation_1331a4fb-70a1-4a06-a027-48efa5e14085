import {
  FAB_CTA_ACTION_TYPES,
  PHOENIX_GROUPING_V2_ACTION_TYPES,
} from '../Actions/PhoenixGroupingV2ActionTypes';

const initialFabCtaDataState = {
  fabCtaDetail: {},
};
const fabCtaDetail = (state = initialFabCtaDataState, action) => {
  switch (action.type) {
    case FAB_CTA_ACTION_TYPES.FAB_CTA_DATA_SUCCESS:
      return {
        ...state,
        fabCtaDetail: action.fabCtaDetail,
      };
    case PHOENIX_GROUPING_V2_ACTION_TYPES.RESET_STATE:
      return {
        ...initialFabCtaDataState,
      };
    default:
      return state;
  }
};

export default fabCtaDetail;
