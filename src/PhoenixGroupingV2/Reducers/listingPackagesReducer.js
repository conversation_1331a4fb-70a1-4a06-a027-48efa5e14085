import {
  LISTING_PACKAGES_ACTION_TYPE,
  PHOENIX_GROUPING_V2_ACTION_TYPES,
} from '../Actions/PhoenixGroupingV2ActionTypes';

const initialListingPackagesDataState = {
  listingPackagesData: {},
  isListingPackageDataLoading: false,
  isListingPackageDataError: false,
  isListingPackageDataSuccess: false,
  recentPackages: {},
};
const listingPackagesData = (state = initialListingPackagesDataState, action) => {
  switch (action.type) {
    case LISTING_PACKAGES_ACTION_TYPE.LISTING_PACKAGES_LOADING:
      return {
        ...state,
        isListingPackageDataLoading: true,
      };
    case LISTING_PACKAGES_ACTION_TYPE.LISTING_PACKAGES_SUCCESS:
      const collectionPackageData = {
        ...(state?.[action?.payload?.groupKey] || {}),
        ...(action?.payload?.listingResponseBody || {}),
        packageDetails: [
          ...(state?.[action?.payload?.groupKey]?.packageDetails || []),
          ...(action?.payload?.listingResponseBody?.packageDetails || []),
        ],
        ...(action?.payload?.noPackageFoundResetFilter && {
          noPackageFoundResetFilter: action?.payload?.noPackageFoundResetFilter,
        }),
      };

      return {
        ...state,
        [action?.payload?.groupKey]: collectionPackageData,
        isListingPackageDataLoading: false,
        isListingPackageDataSuccess: true,
        isListingPackageDataError: false,
      };
    case LISTING_PACKAGES_ACTION_TYPE.LISTING_PACKAGES_ERROR:
      return {
        error: action.error,
        isListingPackageDataError: true,
        isListingPackageDataLoading: false,
      };
    case LISTING_PACKAGES_ACTION_TYPE.RECENT_PACKAGES_SUCCESS:
      return {
        ...state,
        recentPackages: action.recentPackages,
      };
    case PHOENIX_GROUPING_V2_ACTION_TYPES.RESET_STATE:
    case LISTING_PACKAGES_ACTION_TYPE.RESET_LISTING_STATE:
    case PHOENIX_GROUPING_V2_ACTION_TYPES.RESET_STATE_AFTER_EDIT:
      return {
        ...initialListingPackagesDataState,
      };
    case LISTING_PACKAGES_ACTION_TYPE.RESET_LISTING_PACKAGES:
      return {
        ...state,
        listingPackagesData: {},
      };
    default:
      return state;
  }
};

export default listingPackagesData;
