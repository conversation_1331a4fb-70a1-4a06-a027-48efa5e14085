import { combineReducers } from 'redux';
import metaDataDetail from './metaDataReducer';
import fabCtaDetail from './fabCtaReducer';
import groupMetaDetail from './groupMetaReducer';
import listingPackagesData from './listingPackagesReducer';
import holidayLandingGroupReducer from './holidayLandingGroupReducer';
const holidaysPhoenixGroupingV2 = combineReducers({
  metaDataDetail,
  fabCtaDetail,
  groupMetaDetail,
  listingPackagesData,
  holidayLandingGroupReducer,
});

export default holidaysPhoenixGroupingV2;
