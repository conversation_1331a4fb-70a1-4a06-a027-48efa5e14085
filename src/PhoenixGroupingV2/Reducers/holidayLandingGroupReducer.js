import {
  HOLIDAY_LANDING_GROUP_DTO_ACTION_TYPE,
  PHOENIX_GROUPING_V2_ACTION_TYPES,
} from '../Actions/PhoenixGroupingV2ActionTypes';

const initialDataState = {
  holidayLandingGroupDto: {},
  groupSections: [],
};
const holidayLandingGroupReducer = (state = initialDataState, action) => {
  switch (action.type) {
    case HOLIDAY_LANDING_GROUP_DTO_ACTION_TYPE.HOLIDAY_LANDING_GROUP_DTO_UPDATE:
      return {
        ...state,
        holidayLandingGroupDto: {
          ...state.holidayLandingGroupDto,
          ...action.holidayLandingGroupDto,
        },
      };
    case HOLIDAY_LANDING_GROUP_DTO_ACTION_TYPE.GROUP_SECTION_FETCH_SUCCESS:
      return {
        ...state,
        groupSections: action.sections,
      };
    case PHOENIX_GROUPING_V2_ACTION_TYPES.RESET_STATE:
      return {
        ...initialDataState,
      };
    case PHOENIX_GROUPING_V2_ACTION_TYPES.RESET_STATE_AFTER_EDIT:
      return {
        ...state,
        groupSections: [],
      };
    default:
      return state;
  }
};

export default holidayLandingGroupReducer;
