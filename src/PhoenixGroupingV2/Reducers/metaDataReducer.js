import {
  FILTER_SEARCH_PAGE_ACTION_TYPES,
  META_DATA_ACTIONS_TYPES,
  PHOENIX_GROUPING_V2_ACTION_TYPES,
} from '../Actions/PhoenixGroupingV2ActionTypes';

const initialMetaDataState = {
  metaDataDetail: {},
  resetMetaIfNoPackage: false,
  isMetaDataLoading: false,
  isMetaDataError: false,
  isMetaDataSuccess: false,
};
const metaDataDetail = (state = initialMetaDataState, action) => {
  switch (action.type) {
    case META_DATA_ACTIONS_TYPES.META_DATA_LOADING:
      return {
        ...state,
        isMetaDataLoading: true,
      };
    case META_DATA_ACTIONS_TYPES.META_DATA_SUCCESS:
      return {
        ...state,
        metaDataDetail: action.metaDataDetail,
        isMetaDataLoading: false,
        isMetaDataSuccess: true,
      };
    case META_DATA_ACTIONS_TYPES.META_DATA_ERROR:
      return {
        error: action.error,
        isMetaDataError: true,
        isMetaDataLoading: false,
      };
    case META_DATA_ACTIONS_TYPES.RESET_META_NO_PKG_FOUND:
      return {
        ...state,
        resetMetaIfNoPackage: action.resetMetaIfNoPackage,
      };
    case FILTER_SEARCH_PAGE_ACTION_TYPES.LOAD_PHOENIX_SEARCH_PAGE:
      return {
        ...state,
        // isSWSuccess: true, // check how isSWSuccess is being used
      };
    case PHOENIX_GROUPING_V2_ACTION_TYPES.RESET_STATE:
      return {
        ...initialMetaDataState,
      };
    case PHOENIX_GROUPING_V2_ACTION_TYPES.RESET_STATE_AFTER_EDIT:
      return {
        ...initialMetaDataState,
      };
    default:
      return state;
  }
};

export default metaDataDetail;
