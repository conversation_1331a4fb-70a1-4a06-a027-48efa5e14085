import React from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import PropTypes from 'prop-types';
import {backIcon} from '@mmt/legacy-commons/Helpers/displayHelper';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

class SearchHeader extends React.Component {
  render() {
    return (
      <View
        style={styles.container}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
      >
        <TouchableOpacity onPress={this.props.backHandlerFunction}>
          <Image
            style={{width: 16, height: 16, marginTop: 5, marginRight: 10, padding: 5}}
            source={backIcon}
          />
        </TouchableOpacity>
        <View
          style={[AtomicCss.flex1, AtomicCss.marginLeft8]}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="on-drag"
        >
          <TextInput
            style={styles.txtInput}
            autoFocus
            defaultValue={this.props.searchText}
            onChangeText={this.props.handleTextChange}
            returnKeyLabel="search"
            returnKeyType="search"
            onSubmitEditing={this.props.onEnterPress}
            placeholder="Country Name or Code"
            placeholderTextColor="grey"
          />
        </View>
        <TouchableOpacity
          style={AtomicCss.marginLeft10}
          onPress={() => {
            this.props.clearSearch();
          }}
          hitSlop={AtomicCss.tapableArea}
        >
          <Text style={[AtomicCss.boldFont, AtomicCss.font10, this.props.searchText === '' ? AtomicCss.lightText : AtomicCss.lightBlueText]}>CLEAR</Text>
        </TouchableOpacity>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: 'white',
    paddingVertical: 10,
    paddingLeft: 16,
    paddingRight: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  txtInput: {
    height: 22,
    paddingVertical: 0,
    fontFamily: 'Lato-Bold',
    fontSize: 18,
    color: 'black',
  },
});

export default SearchHeader;

SearchHeader.defaultProps = {
  searchText: '',
};

SearchHeader.propTypes = {
  searchText: PropTypes.string,
  handleTextChange: PropTypes.func.isRequired,
  onEnterPress: PropTypes.func.isRequired,
  clearSearch: PropTypes.func.isRequired,
  backHandlerFunction: PropTypes.func.isRequired,
};

