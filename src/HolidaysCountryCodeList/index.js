import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { Text, View, FlatList, TouchableOpacity, BackHandler } from 'react-native';
import React from 'react';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import SearchHeader from './SearchHeader';
import { HARDWARE_BACK_PRESS } from '../SearchWidget/SearchWidgetConstants';
import withB<PERSON><PERSON>andler from '../hooks/withBackHandler';

class HolidaysCountryCodeList extends BasePage {

  constructor(props) {
    super(props);
    this.searchText = '';
    this.countriesData = props.countriesListData;
    this.state = {
      filterList: this.countriesData,
    };
  }

  componentDidMount() {
    super.componentDidMount();

  }
  onBackClick=()=>{
      this.backHandlerFunction();
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }


  renderItem = ({item, index}) => {
    const {name, emoji, phoneCode} = item;
    return (
      <TouchableOpacity onPress={() => this.countrySelected(item)}>
        <View style={{marginVertical: 10, marginHorizontal: 5, padding: 10, alignContent: 'center', flexDirection: 'row'}}>
          <View style={{display: 'flex', flex : 1, flexDirection: 'row'}}>
            <Text style={[AtomicCss.font22, {alignSelf: 'center'}]}>
              {emoji}
            </Text>
            <Text style={[AtomicCss.marginTop3, AtomicCss.font16, AtomicCss.defaultText, AtomicCss.regularFont, AtomicCss.marginLeft15, {flexWrap: 'wrap', marginRight: 20}]}>
              {name}
            </Text>
          </View>
          <View style={{marginLeft: 'auto', alignSelf: 'center'}}>
            <Text style={[AtomicCss.marginTop3, AtomicCss.font15, AtomicCss.defaultText, AtomicCss.boldFont, AtomicCss.marginLeft12]}>
              +{phoneCode}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  }

  render() {
    return (
      <View style={[{ flex: 1 }, this.props.containerStyle]}>
        <SearchHeader
          handleTextChange={this.handleTextChange}
          clearSearch={this.clearSearch}
          searchText={this.searchText}
          onEnterPress={this.onEnterPress}
          backHandlerFunction={this.backHandlerFunction}
        />
        <FlatList
          data={this.state.filterList}
          keyboardShouldPersistTaps="handled"
          renderItem={this.renderItem}
         ItemSeparatorComponent={this.renderSeparator}
      />
      </View>
    );
  }

  renderSeparator = () => {
      return (<View style={{
        paddingBottom:1,
        backgroundColor:'#e5e5e5'}} />);
  };

  countrySelected = (country) => {
    this.props.countryCodeSelected(country.phoneCode);
    this.props.onBackPress();
  }

  handleTextChange = (text) => {
    this.filterCountries(text);
  };

  clearSearch = () => {
    this.searchText = '';
    this.setState({filterList: this.countriesData});
  };

  onEnterPress = () => {
    this.filterCountries(this.searchText);
  };

  filterCountries = (text) => {
    this.searchText = text;
    const text2 = text.replace('+', '');
    const newfilterList = this.countriesData.filter(country => (
      country.name.toLowerCase().includes(text.toLowerCase()) ||
      country.phoneCode.includes(text2))
    );
    this.setState({
      filterList: newfilterList,
    });
  }

  backHandlerFunction = () => {
    this.props.onBackPress();
    return true;
  };
}
export default withBackHandler(HolidaysCountryCodeList);
