import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import {StyleSheet} from 'react-native';

export const ViewGallery = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginLeft: 10,
    borderColor: 'rgba(255, 255, 255, 0.33)',
    borderWidth: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.33)',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 2,
    paddingVertical: 0.5,
  },
  icon: {
    height: 16, width: 16,
    marginRight: 4,
  },
  text: {
    fontFamily: fonts.bold, fontSize: 10, color: '#fff',
  },
  rightArrow: {
    marginLeft: 3,
    width: 13,
    height: 10,
  },
});
