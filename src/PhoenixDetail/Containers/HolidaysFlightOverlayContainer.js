import {connect} from 'react-redux';
import HolidaysFlightOverlay from '../HolidaysFlightOverlay';
import {fetchFlightListingData, fetchFlightsPriceMapData} from '../Actions/HolidaysFlightActions';

const mapStateToProps = state => ({
  ...state.holidaysFlightOverlay,
});

const mapDispatchToProps = dispatch => ({
  fetchFlightListingData: (
    dynamicId, flightRequestObject
  ) => dispatch(
    fetchFlightListingData(dynamicId, flightRequestObject)),

  // fetchFlightsPriceMapData: (
  //   dynamicId, flightRequestObject
  // ) => dispatch(
  //   fetchFlightsPriceMapData(dynamicId, flightRequestObject)),
});

export default connect(mapStateToProps, mapDispatchToProps)(HolidaysFlightOverlay);
