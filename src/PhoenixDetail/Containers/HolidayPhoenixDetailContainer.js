import {connect} from 'react-redux';
import HolidaysDetail from '../HolidayDetailPageNew';
import {
  fetchDetailData,
  RemoveMmtBlackDetail,
  fetchPersuasionDataActions,
  fetchShortListedPackagesActions, updateShortListedPackage,
  fetchCta, fetchHotelDetail, togglePackageComponent, changeFlight,
  fetchSimilarPackagesActions, changeTransfer, changeTransferListing,
  addActivity, addActivityListing, viewActivityDetails, fetchCancellationData, changeHotel, changeMeal,
  modifyActivity,
  modifyCommute,
  setReviewComponentFailureData,
  applyOfferSection,
  getEmiOptions,
  fetchPaxGuidelines,
  clearHolidayDetailData,
} from '../Actions/HolidayDetailActions';
import {fetchStoryData} from '../../Story/Redux/HolidayStoryActions';
import {clearOverlays,showOverlay,hideOverlays} from '../Components/DetailOverlays/Redux/DetailOverlaysActions';
import { fetchReviewData } from '../../Review/Actions/HolidayReviewActions';

const mapStateToProps = state => ({
  ...state.holidaysDetail,
});

const mapDispatchToProps = dispatch => ({
  fetchDetailData:
    (
      holidayDetailData, packageDetailDTO, dynamicDispatchId, roomDetails, isChangingDate,
      isActionApiCalled = false, actionLoadingText = '', undo
    ) =>
      dispatch(fetchDetailData(
        holidayDetailData, packageDetailDTO,
        dynamicDispatchId, roomDetails, isChangingDate, isActionApiCalled, actionLoadingText,undo,true
      )),
  fetchPersuasionDataActions: () => dispatch(fetchPersuasionDataActions()),
  getEmiOptions: (dynamicId) => dispatch(getEmiOptions(dynamicId)),
  fetchShortListedPackages: () => dispatch(fetchShortListedPackagesActions()),
  fetchSimilarPackages: () => dispatch(fetchSimilarPackagesActions()),
  updateShortListedPackage: (id, name, isShortList) =>
    dispatch(updateShortListedPackage(id, name, isShortList)),
  fetchCta: (holidayDetailData, showFabFromDeeplink) =>
    dispatch(fetchCta(holidayDetailData, showFabFromDeeplink)),
  fetchHotelDetail: (packageDetailDTO, hotel, onApiError) =>
    dispatch(fetchHotelDetail(packageDetailDTO, hotel, onApiError)),
  togglePackageComponent: (actionData, onFlightToggled, onApiError, packageComponent) =>
    dispatch(togglePackageComponent(actionData, onFlightToggled, onApiError, packageComponent)),
  changeTransferListing: (packageDetailDTO, onTransferChange, transferObj, onApiError) =>
    dispatch(changeTransferListing(packageDetailDTO, onTransferChange, transferObj, onApiError)),
  changeFlight: (actionRequest, onFlightChanged, onApiError) =>
    dispatch(changeFlight(actionRequest, onFlightChanged, onApiError)),
  changeMeal: (actionRequest, onFlightChanged, onApiError) =>
    dispatch(changeMeal(actionRequest, onFlightChanged, onApiError)),
  changeHotel: (actionRequest, onHotelChanged, onApiError) =>
      dispatch(changeHotel(actionRequest, onHotelChanged, onApiError)),
  changeTransfer: (actionData, packageComponent, onFlightChanged, onApiError) =>
    dispatch(changeTransfer(actionData, packageComponent, onFlightChanged, onApiError)),
  addActivityListing: (packageDetailDTO, onActivityAdd, day, staySequence, onApiError) =>
    dispatch(addActivityListing(packageDetailDTO, onActivityAdd, day, staySequence, onApiError)),
  addActivity: (actionData, packageComponent, onFlightChanged, onApiError) =>
    dispatch(addActivity(actionData, packageComponent, onFlightChanged, onApiError)),
  modifyActivity: (actionData, onActivityUpdated, onApiError) =>
    dispatch(modifyActivity(actionData, onActivityUpdated, onApiError)),
  modifyCommute: (actionData, onActivityUpdated, onApiError) =>
      dispatch(modifyCommute(actionData, onActivityUpdated, onApiError)),
  viewActivityDetails: (packageDetailDTO, onActivityDetails, activityCode, day, staySequence, isFromActivityListing, onApiError) =>
    dispatch(viewActivityDetails(packageDetailDTO, onActivityDetails, activityCode, day, staySequence, isFromActivityListing, onApiError)),
  fetchStoryData: (holidayDetailData, roomDetails, trackLocalClickEvent) => dispatch(fetchStoryData(holidayDetailData, roomDetails, trackLocalClickEvent)),
  fetchCancellationData: (dynamicPackageId) => dispatch(fetchCancellationData(dynamicPackageId)),
  applyOfferSection: (action, coupon, dynamicPackageId) => dispatch(applyOfferSection(action, coupon, dynamicPackageId, false)),
  fetchReviewData: (holidayReviewData, roomDetails,isFphReview,criteria,callback,fromDetails,reviewSuccessCallBackOnDetails,reviewFailureCallBackOnDetails,fromPreSalesEdit,modificationAllowed,reviewRequestSource) =>
    dispatch(fetchReviewData(holidayReviewData, roomDetails,isFphReview,criteria,callback,fromDetails,reviewSuccessCallBackOnDetails,reviewFailureCallBackOnDetails,fromPreSalesEdit,modificationAllowed,reviewRequestSource)),
  setReviewComponentFailureData: (componentFailureData) => dispatch(setReviewComponentFailureData(componentFailureData)),
  clearHolidayDetailData: () => dispatch(clearHolidayDetailData()),
  RemoveMmtBlackDetail: () => dispatch(RemoveMmtBlackDetail()),

  fetchPaxGuidelines : () => dispatch(fetchPaxGuidelines()),
  showOverlay: (key, data) => dispatch(showOverlay(key, data)),
  hideOverlays: (keys) => dispatch(hideOverlays(keys)),
  clearOverlays: () => dispatch(clearOverlays()),
});
export default connect(mapStateToProps, mapDispatchToProps)(HolidaysDetail);

