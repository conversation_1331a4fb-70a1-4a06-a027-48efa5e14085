import {connect} from 'react-redux';
import PhoenixDetailOverlay from '../Components/PhoneixDetailOverlay';
import { hideOverlays, showOverlay } from '../Components/DetailOverlays/Redux/DetailOverlaysActions';
const mapStateToProps = state => ({
    ...state.holidaysDetail,
});
const mapDispatchToProps = dispatch => ({
    showOverlay: (key, data) => dispatch(showOverlay(key, data)),
    hideOverlays: (keys) => dispatch(hideOverlays(keys)),
});
export default connect(mapStateToProps, mapDispatchToProps)(PhoenixDetailOverlay);
