import { connect } from 'react-redux';
import PackageAddOnsContainer from '../Components/PackageAddOns/PackageAddOnsContainer';

const mapStateToProps = state => ({
  ...state.holidaysActivityOverlay,
});

const mapDispatchToProps = dispatch => ({});

export default connect(
  state => ({
    ...state.holidaysActivityOverlay,
    packageDetail: state.holidaysDetail?.detailData?.packageDetail,
  }),
  mapDispatchToProps
)(PackageAddOnsContainer);
