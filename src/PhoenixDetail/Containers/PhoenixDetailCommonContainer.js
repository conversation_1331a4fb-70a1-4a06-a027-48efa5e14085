import React from 'react';
import MimaPreSalesContainer from '../../MimaPreSales/Containers/MimaPreSalesContainer';
import MimaPreSalesEditDetailContainer from '../../MimaPreSales/Containers/MimaPreSalesEditDetailContainer';
import HolidayPhoenixDetailContainer from './HolidayPhoenixDetailContainer';
class HolidayDetailCommonContainer extends React.Component {
  DetailPageView;

  constructor(props) {
    super(props);
    if (props.openMimaPreSales) {
      this.DetailPageView = MimaPreSalesContainer;
    } else if (props.openMimaPreSalesEditDetail) {
      this.DetailPageView = MimaPreSalesEditDetailContainer;
    } else {
      // Open Phoenix Detail Page
      this.DetailPageView = HolidayPhoenixDetailContainer;
    }
  }

  render() {
    return <this.DetailPageView {...this.props} />;
  }
}

export default HolidayDetailCommonContainer;
