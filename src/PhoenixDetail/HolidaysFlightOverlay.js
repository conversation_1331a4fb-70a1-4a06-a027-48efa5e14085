import React from 'react';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import FlightDomRetListingComponent from './Components/FlightDetailPage/FlightDomRetListingComponent';
import {Platform, StatusBar, StyleSheet, Text, View} from 'react-native';
import {flightDetailTypes} from './DetailConstants';
import {statusBarHeightForIphone} from '@mmt/legacy-commons/Styles/globalStyles';
import FlightListingCombo from './Components/FlightDetailPage/FlightListing/FlightListingCombo';
import { isNumber } from 'lodash';
import { createTravellerObjForLoader } from './Utils/HolidayDetailUtils';
import HolidayDetailLoader from './Components/HolidayDetailLoader';
import FullPageError from './Components/ReviewRating/components/FullPageError';
import PhoenixHeader from './Components/PhoenixHeader';
import { HOLIDAYS_FLIGHT_OVERLAY, HOLIDAY_FLIGHT_OVERLAY_LISTING } from './Utils/PheonixDetailPageConstants';
import { PDT_PAGE_LOAD, SUB_PAGE_NAMES } from '../HolidayConstants';
import HolidayDataHolder from '../utils/HolidayDataHolder';
import { HolidayNavigation } from '../Navigation';
import {isMobileClient} from "../utils/HolidayUtils";
import { holidayNavigationClearScreens } from './Utils/DetailPageNavigationUtils';

class HolidaysFlightOverlay extends BasePage {
  constructor(props) {
    super(props);
    this.domRtFilterData = {
      filterData: null,
      sortByData: null,
    };
    const {listingFlightSequence} = props.flightRequestObject || {};
    this.currentTabOnLaunch = isNumber(listingFlightSequence) ? listingFlightSequence - 1 : 0;
  }

  componentDidMount() {
    super.componentDidMount();
    this.props.fetchFlightListingData(this.props.dynamicId, this.props.flightRequestObject);
    HolidayDataHolder.getInstance().setCurrentPage('holidaysFlightOverlay');
  }

  updateFiltersForDomRt = (depF, retF, sortByData) => {
    this.domRtFilterData = {
      filterData: [depF, retF],
      sortByData: sortByData,
    };
  }

  updateTabForDomRt = (index) => {
    this.currentTabOnLaunch = index;
  }

  getFlightView = () => {
    const {flightListingData} = this.props;
    const {listingDataType} = flightListingData || {};

    switch (listingDataType) {
      case flightDetailTypes.OBT:
      case flightDetailTypes.DOM_ONWARDS:
        return (
          <FlightListingCombo
            {...this.props}
          />
        );
      case flightDetailTypes.DOM_RETURN:
        return (
          <FlightDomRetListingComponent
            {...this.props}
            domRtFilterData={this.domRtFilterData}
            updateFiltersForDomRt={this.updateFiltersForDomRt}
            currentTabOnLaunch={this.currentTabOnLaunch}
            updateTabForDomRt={this.updateTabForDomRt}
          />
        );
      default: return [];
    }
  }

  close = () => {
    holidayNavigationClearScreens({ clearOverlays: this.props.clearOverlays });
  };

  render() {
    const {listingLoading, listingSuccess, listingError} = this.props;
    return (
      <View style={styles.pageContainer}>
        {listingError && this.renderError()}
        {listingLoading && this.renderLoader()}
        {listingSuccess && this.renderContent()}
      </View>
    );
  }

  renderError() {
    return (
      <View style={{flex: 1}}>
        <FullPageError
          title="Oops! Page not found"
          subTitle="We can’t seem to find the page you’re looking for"
          suggestion=""
          onRefreshPressed={null}
          renderStickyHeader={() => (
            <PhoenixHeader
              title={'Change Flight'}
              subtitleData={this.props.subtitleData}
              handleClose={this.close}
            />
          )}
          onBackPressed={this.close}
        />
      </View>
    );
  }

  renderLoader() {
    const {packageDetailDTO, roomDetails} = this.props;
    return (
      <HolidayDetailLoader
        departureCity={packageDetailDTO.depCityName}
        departureDate={packageDetailDTO.departureDate}
        duration={packageDetailDTO.duration}
        travellerObj={createTravellerObjForLoader(roomDetails)}
        changeAction={false}
      />
    );
  }

  renderContent() {
    return (
      <View style={styles.pageWrap}>
        {this.getFlightView()}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  pageContainer: {
    flex: 1,
  },
  pageWrap: {
    width: '100%',
    height: '100%',
  },

  iconBell: {
    width: 10,
    height: 14,
    resizeMode: 'cover',
  },
  baggageInfoTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: '#ffeae1',
    justifyContent: 'center',
    borderBottomColor: '#bababa',
    borderBottomWidth: 1,
  },
});


export default HolidaysFlightOverlay;
