import React, { Component } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>H<PERSON><PERSON>,
  DeviceEventEmitter,
  Dimensions,
  FlatList,
  KeyboardAvoidingView,
  LayoutAnimation,
  NativeModules,
  Platform,
  StyleSheet,
  View,
} from 'react-native';
import {
  componentImageTypes,
  deepLinkParams,
  DEFAULT_TRAVELLER_COUNT,
  DETAIL_LOCAL_NOTIFICATION_PAGE_NAME,
  DETAIL_QUERY_PAGE_NAME,
  DETAIL_TRACKING_PAGE_NAME,
  detailReviewFailure,
  extraInfoRefs,
  itineraryUnitTypes,
  overlays,
  packageActionComponent,
  packageActions,
  PDTConstants,
} from './DetailConstants';
import { cloneDeep, isEmpty } from 'lodash';
import ValidationSummary from '../Common/Components/ValidationSummary';
import {
  createChatID,
  createCuesSteps,
  createRandomString,
  doCall,
  doQuery,
  exitLocalNotification,
  getPaxConfig,
  getReturnUrl,
  hasOnBoardingCuesLastVisit,
  isEmptyString,
  isFlexiDateAvailable,
  isMobileClient,
  isNotNullAndEmptyCollection,
  isOnBoardingCuesDelayOver,
  isRawClient,
  isSummaryTabDefaultOpen,
  isZCAvailable,
  openSeoQueryDeepLink,
  removeCuesStepsShown,
  saveHolMeta,
  sharePackage,
  startReactChat,
  updateDeviceType,
  openGenericDeeplink,
  isdisplayPoweredbyMMT,
  isBacktoLanding,
} from '../utils/HolidayUtils';
import {
  DOM_BRANCH,
  FROM_LISTING_GROUPING_DEEPLINK,
  FUNNEL_ENTRY_TYPES,
  HLD_CUES_POKUS_KEYS,
  HLD_PAGE_NAME,
  PDT_PAGE_ENTRY_EVENT,
  PDT_PAGE_EXIT_EVENT,
  PDT_RAW_EVENT,
  WEEKEND_GETAWAY_PAGE_TYPE,
} from '../HolidayConstants';
import { EVENT_NAMES, PDT_EVENT_TYPES } from '../utils/HolidayPDTConstants';
import {
  addPersuasionToDetailData,
  calculateFlightsCount,
  calculateTravellersCount,
  createHolidayDetailData,
  createLoggingMap,
  createRoomDetailsFromApi,
  createRoomDetailsFromRoomDataForPhoenix,
  createTravellerObjForLoader,
  fetchErrorMessage,
  getActionData,
  getEventName,
  getVideoUrl,
  openChangeHotelFromPhoenixPage,
  toggleDayPlan,
  updateEMIPriceForPersuasion,
} from './Utils/HolidayDetailUtils';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { fetchQueueIdentifier } from '../utils/HolidayNetworkUtils';
import {
  calculateToDateTime,
  createChildAgeArrayFromApi,
  createChildAgeArrayFromRoomDataForPhoenix,
  createDestinationMap,
  createFlightRequestParams,
  createSightSeeingDayPlanDataMap,
  createStaticItineraryData,
  createSubtitleData,
  extractDataForPDTFromDetailResponse,
  getActivityExtraData,
  getDates,
  getFilteredPackageFeatures,
  getHotelObject,
  getOptimizedPackageDetail,
  getPackageFeatureByKey,
} from './Utils/PhoenixDetailUtils';
import { TABS } from './Components/DayPlan/Sorter';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { initAbConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { getAndUpdateStoryStatus } from '../Story/StoryUtils';
import {addGclId} from '../utils/googleAdUtils';

import {
  setTrackingData,
  trackHolidaysDetailClickEvent,
  trackHolidaysDetailLoadEvent,
} from './Utils/PhoenixDetailTracking';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../Navigation';
import {
  clearCuesStepPositions,
  getValidCuesSteps,
  updatedCuesSteps,
} from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCueStepsUtils';
import { viewTypes } from '../Listing/ListingConstants';
import {trackDetailGIClickEvent, trackDetailGILoadEvent} from '../utils/PhoenixDetailGITracking';
import {handleLocationPopUp, updateCityWithoutShowingPopup} from '../Common/GeoLoc/GeoLocationUtils';
import {GEO_LOC_AUTO_UPDATE_MESSAGE} from '../LandingNew/Components/HolidayLandingPage';
import {getGiTrackingDataFromHolidayDetailData} from '../utils/GiTrackingUtils';
import { getCuesConfig, getEnableGeoLoc, getEnableSummaryTabRemoveAccordian, getHolShowStoryMob, getHolShowSummaryTabFirst, getMaxUndoAllowed, getPokusForGalleryV2, getPokusForNewDetailContent, showMMTBlack,
  showFabAnimationExtended,
  showHolAgentOnDetailAndReviewPage,
} from '../utils/HolidaysPokusUtils';
import { TRACKING_EVENTS } from '../HolidayTrackingConstants';
import { PACKAGE_FEATURES } from './Utils/PheonixDetailPageConstants';
import { getPersuasionV2, getZCPersuasion, persuasions } from './Utils/PersuationUtils';
import { sectionHeaderSpacing } from '../Styles/holidaySpacing';
import { holidayColors } from '../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../Styles/Spacing';
import { fontStyles } from '../Styles/holidayFonts';
import { sectionTrackingPageNames, setPageSectionVisitResponse } from '../utils/SectionVisitTracking';
import { getEvar108ForFunnel, trackDeeplinkRececived } from '../utils/HolidayTrackingUtils';
import HolidayDeeplinkParser from '../utils/HolidayDeeplinkParser';
import PerformanceMonitorModule from '@mmt/legacy-commons/Native/PerformanceMonitorModule';
import BranchIOTracker from '../utils/HolidayBranchSDKEventTracker';
import ViewControllerModule from '@mmt/legacy-commons/Native/ViewControllerModule';
import HolidayDataHolder from '../utils/HolidayDataHolder';

/* Icons  */
import AlertIcon from '@mmt/legacy-assets/src/alert_cream.webp';

/* Components */
import HolidayExpertSection from './Components/PackageInfoSection/HolidayExpertSection';
// import ModifySearchContainer from './Components/PackageInfoSection/ModifySearchContainer';
import OfferOverlayNew from '../DetailMimaComponents/OffersOverlayComponent/OfferOverlayNew';
import SelectCityPopUp from '../SearchWidget/Components/SelectCityPopUp';
import HolidayCancellationOverlayV2 from './Components/HolidayCancellationOverlayV2';
import HolidayWpmPersuasion from '../Common/Components/HolidayWpmPersuasion';
import SummaryView from './Components/SummaryView';
import HolidaysMessageStrip from '../Common/Components/HolidaysMessageStrip';
import PackageUpdatedToast from './Components/PackageUpdatedToast';
import ReviewFailureHandlingPopup from './Components/ReviewFailureHandlingPopup';
import VisaContainer from '../Common/Components/visa';
import SafeBanner from './Components/SafeBanner';
import BasicDetailsSection from './Components/PackageInfoSection/BasicDetailsSection';
import ExtraInfoOverlay from './Components/ExtraInfoOverlay';
import FloatingWidget from './Components/FloatingWidget';
// import FeatureList from './Components/FDFeatureEdit/FeatureList';
import FDFeatureEditOverlayV2 from './Components/FDFeatureEditOverlayV2';
import CustomizationPopup from './CustomizationPopup';
import BlackFooter from './Components/BlackFooter';
import PricePersuasionOverlay from './Components/PricePersuasionOverlay';
import GridGallery from './Components/Gallery/GridGallery';
import withIntervention from '../Common/Components/Interventions/withIntervention';
import PackageInfoSection from './Components/PackageInfoSection';
import HolidayDetailLoader from './Components/HolidayDetailLoader';
import HolidayDetailError from './Components/Common/HolidayDetailError';
import EditOverlay from './Components/EditOverlay/editBasePage';
import Accordian from './Components/Accordian/Accordian';
import { ItineraryHeader } from './Components/ItenaryCard';
import DateSection from './Components/DayPlan/dateSection';
import DayPlan from './Components/DayPlan';
import DetailPageHeader from './Components/PageHeader';
import HolidayInstaStory from '../Common/Components/HolidayInstaStory';
import HolidayStoryPage from '../Story/Components/HolidayStoryPage';
import LightHeader from './Components/LightHeader';
import PackageHighlights from './Components/PackageHighlights';
import ModifySearchContainer from './Components/PackageInfoSection/ModifySearchContainer';
import { initDetailPDTObj, logPhoenixDetailPDTEvents } from '../utils/PhoenixDetailPDTTrackingUtils';
import { getDetailPDTObjWithCorrectPax } from '../utils/PhoenixDetailPDTDataHolder';
import DetailOverlays from './Components/DetailOverlays';
import { isIos } from '@mmt/core/helpers/platformHelper';
import HolidayFabAnimationContainer from '../Common/Components/Widgets/FabAnimation/FabAnimationContainer';
import { createDetailFabData } from '../Common/Components/Widgets/FabAnimation/FabAnimationUtils';
import BottomSheet from '../Common/Components/BottomSheet';
import MMTBlackBottomSheet from '../Common/Components/Membership/BottomSheet';
import { getDataFromStorage, DEVICE_TYPE } from '@mmt/legacy-commons/AppState/LocalStorage';
import SignatureBanner from './Components/PackageInfoSection/SignatureBannerV2';
import DownloadApp from '../Common/Components/DownloadApp';
import { sendMMTGtmEvent } from '../utils/ThirdPartyUtils';
import GenericModule from 'packages/legacy-commons/Native/GenericModule';
import { getMemberShipCardArray } from '../Common/Components/Membership/utils/MembershipUtils';
import { CONTENT_TYPES } from '../Common/Components/Membership/utils/constants';
import MembershipCarouselV2 from '../Common/Components/Membership/Carousel/MembershipCarouselV2';
import withBackHandler from '../hooks/withBackHandler';
import HolidayPDTMetaIDHolder from '../utils/HolidayPDTMetaIDHolder';

const SCREEN_WIDTH = Dimensions?.get('window').width || 32;
const FAB_BOTTOM_WITH_UNDO = 120;
const FAB_BOTTOM_DEFAULT = 100;

let loginEventDetailListener;
const MESSAGE_TYPES = {
  PKG_MESSAGE: 'PKG_MESSAGE',
  DFD_CHILD_BED: 'DFD_CHILD_BED',
  PACKAGE_DATES_UPDATED: 'PACKAGE_DATES_UPDATED',
  STARTING_CITY: 'STARTING_CITY',
};

const getChangeOrRemoveTopValue = ({ steps }) => {
  const changeOrRemoveStep = steps.find((step) => step?.key === 'changeOrRemove');
  return changeOrRemoveStep?.extraInfo?.from === 'transferRow' ? 125 : 180;
};

const LOGIN_EVENT_DETAIL = 'login_event_detail';

class HolidayDetailPageNew extends Component {
  flatListRef = React.createRef();
  activityFailedScrollRef = React.createRef();
  scrollNode = null;
  canScrollCalender = false;
  deepLinkSimilar = false;
  constructor(props) {
    super(props, 'detail');
    addGclId();
    PerformanceMonitorModule.start('PhoenixDetailPage');
    this.replaced = !!this.props.replaced;
    if (this.props[deepLinkParams.deepLink]) {
      this.holidayDetailData = HolidayDeeplinkParser.parseDetailPageDeeplink(
        this.props[deepLinkParams.query],
        true,
      );
      this.deepLinkSimilar = true;
      trackDeeplinkRececived({ [TRACKING_EVENTS.M_V81]: this.holidayDetailData.cmp });
    } else {
      this.holidayDetailData = cloneDeep(this.props.holidaysDetailData);
      this.deepLinkSimilar = !!this.props.deepLinkSimilar;
    }

    this.holidayDetailData.isWG = this.holidayDetailData.pt === WEEKEND_GETAWAY_PAGE_TYPE;
    this.holidayDetailData.trackingData = this.props.trackingData;
    this.isLoading = true;
    this.dynamicCuesSteps = [];
    this.dynamicCoachMarkTimeOut = null;
    this.showNewContentUI = false;
    this.showNewGallery = false;
    this.cuesConfig = {};
    this.detailPageSectionVisits = {};
    this.state = {
      showMmtBlackBottomsheet: false,
      showSearch: false,
      showPageHeader: false,
      fixed: false,
      fab: false,
      popupData: {},
      cityId: '',
      popup: overlays.NONE,
      sectionToShow: '',
      openHalf: false,
      videoPaused: false,
      showActivityDetailForDay: false,
      showActivityDetailForInclusion: false,
      instaRunning:
        this.storyCount > 0 &&
        this.holidayDetailData.isWG &&
        isNotNullAndEmptyCollection(this.holidayDetailData.images),
      showStoryPage: false,
      isItineraryVisible: true,
      currentActivePlanOnItinerary: isSummaryTabDefaultOpen({fromPreSales :false}) ? TABS.SUMMARY : TABS.DAY,
      openFDFeatureEditVisibility: false,
      undoStack: [],
      isUndoVisible: false,
      fabTextShrinked: false,
      showPackageUpdatedToast: false,
      showCoachMarks: false,
      showingQueryCoachMark: false,
      reviewError: {},
      allowChangeHub: true,
      activeFeature: -1,
      whatToexpectReadMore: false,
      showSelectCityPopUp: false,
      selectCityPopupData: {},
      isShowWarningMessage: true,
      forceHitTravelplex: true,
    };
    this.packageDetailDTO = {
      DFD: false,
      cmp: this.holidayDetailData.cmp ? this.holidayDetailData.cmp : 'detail_share',
      searchCriteria: this.holidayDetailData.searchCriteria,
      dynamicPackageId: this.holidayDetailData.dynamicPackageId,
      pageType: this.holidayDetailData.pt ? this.holidayDetailData.pt : '',
    };
    this.closeWarningMessages = {};
    if (isEmpty(this.holidayDetailData.cmp) && !isEmpty(this.props.campaign)) {
      this.holidayDetailData.cmp = this.props.campaign;
    }
    this.isShowWarningMessage = true;
    if (this.holidayDetailData?.banner) {
      HolidayDataHolder.getInstance().setBanner(this.holidayDetailData.banner);
    }
    HolidayDataHolder.getInstance().setSource(this.holidayDetailData.source);
    HolidayDataHolder.getInstance().setCmp(this.holidayDetailData.cmp);
    HolidayDataHolder.getInstance().setCampaign(this.props.campaign);
    this.cusCountLimit = 3;
    this.travelPlexAttr4 = null;
    if (isNotNullAndEmptyCollection(this.holidayDetailData.rooms)) {
      this.roomDetails = createRoomDetailsFromApi(this.holidayDetailData.rooms);
      this.childAgeArray = createChildAgeArrayFromApi(this.holidayDetailData.rooms);
    } else {
      this.roomDetails = [
        {
          noOfAdults: DEFAULT_TRAVELLER_COUNT,
          noOfChildrenWB: 0,
          noOfInfants: 0,
          listOfAgeOfChildrenWB: [],
          listOfAgeOfChildrenWOB: [],
          noOfChildrenWOB: 0,
        },
      ];
      this.childAgeArray = [];
    }
    const detailData = {
      requestId: createRandomString(),
      holidayDetailData: this.holidayDetailData,
      cmp: this.holidayDetailData.cmp,
      isWG: this.holidayDetailData.isWG,
    };
    detailData.pageDataMap = createLoggingMap(detailData, []);
    HolidayDataHolder.getInstance().setCurrentPage('holidaysDetail');
    trackHolidaysDetailLoadEvent({
      logOmni: false,
      pageName: DETAIL_TRACKING_PAGE_NAME,
      pdtData: {
        pageDataMap: detailData?.pageDataMap || {},
        eventType: PDT_RAW_EVENT,
        activity: PDT_PAGE_ENTRY_EVENT,
        requestID: createRandomString(),
        branch: detailData?.branch || DOM_BRANCH,
      },
    });
    sendMMTGtmEvent({
      eventName: PDT_PAGE_ENTRY_EVENT,
      data: { pageName: 'details',
        branch: detailData?.branch || DOM_BRANCH,
       },
    });
    if (isRawClient()){
      trackDetailGILoadEvent({giData: getGiTrackingDataFromHolidayDetailData({holidayDetailData: this.holidayDetailData, rooms: this.roomDetails})});
    }

   // onUpdateSearchWidgetPDTDetail({ holidayDetailData: this.holidayDetailData});
    saveHolMeta(
      this.holidayDetailData.isWG,
      this.holidayDetailData.aff,
      this.holidayDetailData.pt,
      this.holidayDetailData.cmp,
    );
    this.calenderRef = React.createRef();
  }
  static getDerivedStateFromProps(nextProps, state) {
    const { metadataDetail } = this?.props?.detailData?.packageDetail || {};
    const { metadataDetail: nextMetaDataDetail } = nextProps?.detailData?.packageDetail || {};
    if (metadataDetail !== nextMetaDataDetail) {
      return {
        ...state,
        allowChangeHub: !(nextMetaDataDetail?.hubRestricition || nextMetaDataDetail?.landOnly),
      };
    }
    return null;
  }

  updatePackageWithV2 = (packageId) => {
    this.refreshDetails(false, false, packageId, false);
  };

  updatePackage = (packageId) => {
    this.toggleFDFeatureBottomSheet(false);
    this.refreshDetails(false, false, packageId, false);
  };
  updateInPDTRequestIdV2 = () => {
     HolidayPDTMetaIDHolder.getInstance().setPdtId();
  }

  updateMeal = (mealCode) => {
    this.toggleFDFeatureBottomSheet(false);
    const actionData = {
      action: packageActions.CHANGE,
      dynamicPackageId: this.packageDetailDTO.dynamicPackageId,
      mealCode: mealCode,
    };
    this.updateInPDTRequestIdV2();
    this.props.changeMeal(actionData, this.onPackageComponentToggled, this.onApiError);
  };

  updateVisa = (visaIncluded) => {
    this.toggleFDFeatureBottomSheet(false);
    this.checkAndUpdateReviewError(packageActionComponent.VISA);
    this.checkAndUpdatePrePaymentError({}, packageActionComponent.VISA);
    this.actionLoadingText = '';
    const actionData = {
      action: packageActions.TOGGLE,
      dynamicPackageId: this.packageDetailDTO.dynamicPackageId,
      removed: !visaIncluded,
    };
    this.updateInPDTRequestIdV2();
    this.props
      .togglePackageComponent(
        actionData,
        this.onPackageComponentToggled,
        this.onApiError,
        packageActionComponent.VISA,
      )
      .then(() => {
        if (!visaIncluded) {
          this.setState({
            currentActivePlanOnItinerary: TABS.DAY,
          });
        }
      });
  };

  showItinerary = () => {
    if (!this.state.isItineraryVisible) {
      const eventName = 'navigation_';
      const suffix = 'itinerary';
      const value = eventName + suffix;
      this.captureClickEvents({ eventName, suffix, value });
    }
    this.setState({ isItineraryVisible: !this.state.isItineraryVisible });
  };

  toggleDayPlan = (plan) => {
    const eventName = 'inclusion_';
    const value = eventName + plan;
    this.captureClickEvents({ eventName, suffix: plan, sendGIData: isRawClient() , value });
    // if (plan === TABS.VISA) {
    //   this.togglePopup(overlays.VISA_OVERLAY);
    //   return;
    // }
    this.setState({ currentActivePlanOnItinerary: plan });
    if (this.flatListRef) {
      this.canScrollCalender = true;
      // setTimeout(() => {      //todo - it is issuing the flatlist incorrect scroll
        const obj = { animated: true, index: isRawClient() ? 7 : 6, viewOffset: (plan == TABS.SUMMARY ? 70 :  125) }
        if (isRawClient()){
          obj.viewOffset = plan == TABS.SUMMARY ? 70 :  125;
        }
      //   this.flatListRef.scrollToIndex(obj);
      // }, 200);
    }
  };

  handleMemberShipCardOnKnowMoreClick = (bottomSheetDetail, mmtbucketDetail, ctaText = '') => {
    const eventName = `GC_${ctaText.split(' ').join('_')}`;
    this.setState({ showMmtBlackBottomsheet: true });
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    this.trackMmtBlackClickEvent({eventName});
  };

  handleMmtBlackTogglePopup = () => {
    const eventName = 'GC_Popop_click_close';
    this.setState({ showMmtBlackBottomsheet: false });
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    this.trackMmtBlackClickEvent({eventName});
  };

  handleMmtBlackCtaButtonClick = () => {
    const eventName = 'GC_Popup_Click_got_it';
    this.setState({ showMmtBlackBottomsheet: false });
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    this.trackMmtBlackClickEvent({eventName});
  };

  handleTermConditionClick = (url) => {
    const eventName = 'GC_Popup_Click_t&c';
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    this.trackMmtBlackClickEvent({eventName});
    this.setState({ showMmtBlackBottomsheet: false });
    openGenericDeeplink({url});
  }

  trackMmtBlackClickEvent = ({eventName, prop1 = ''}) => {
    const {gcBucket = null, myCashBucket = null, effectivePriceBucket = null} = this.props.mmtBlackDetail?.mmtBlackPdtData?.mmtBlackBucketDetail || {};
    const evar46 = `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`;
    this.trackLocalClickEvent(eventName, '', {omniData: {[TRACKING_EVENTS.M_V46]: evar46,}, prop1});
  }

  trackMemberShipLoadEvent = ({eventName, prop1, isPersonalisation}) => {
    if(!isPersonalisation){
      this.trackMmtBlackClickEvent({eventName, prop1});
      return;
    }
    this.trackLocalClickEvent(eventName, '', {prop1});
  }


  // toggleDayPlan = plan => this.setState({'currentActivePlanOnItinerary': plan});

   async getPlatform(){
  return await getDataFromStorage(DEVICE_TYPE);
}
  onBackClick = ()=> {
    return this.onBackPressed();
  }
  async componentDidMount() {
    loginEventDetailListener =
      DeviceEventEmitter &&
      DeviceEventEmitter.addListener(LOGIN_EVENT_DETAIL, this.onLoginEventReceived);
      this.props.clearOverlays();
    this.props.setReviewComponentFailureData(null);
    this.props.clearHolidayDetailData();
    await this.initAb();
    await HolidayDataHolder.getInstance().setSubFunnel();
    HolidayDataHolder.getInstance().setFunnelEntry(FUNNEL_ENTRY_TYPES.ONLINE);
    initDetailPDTObj({ holidayDetailData: this.holidayDetailData });
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.pageEntry,
      value: EVENT_NAMES.PAGE_ENTRY,
      shouldTrackToAdobe:false
    });
    this.refreshDetails(
      false,
      !isEmptyString(this.holidayDetailData.dynamicPackageId),
      null,
      false,
    );
    this.lastPageName = HOLIDAY_ROUTE_KEYS.DETAIL;
    await this.getDeviceType(this.holidayDetailData?.deviceType)
    clearCuesStepPositions();
    /*    setFirebaseTrackingForListing({
      detailData: this.holidayDetailData,
      roomDetails: this.roomDetails,
      branch: this.props.detailData?.branch || DOM_BRANCH,
    });*/
    BranchIOTracker.trackPageView({
      pageName: BranchIOTracker.PAGE.DETAIL,
      [BranchIOTracker.KEYS.EXTRA_DATA]: {},
    });
    //this.handleLocationPermission();

    // Fetch attr4 data for TravelPlex
    try {
      this.travelPlexAttr4 = await fetchQueueIdentifier('detail');
    } catch (error) {
      console.log('Error fetching TravelPlex attr4 data:', error);
    }
  }
  componentDidUpdate(prevProps, prevState) {
    if (
      prevProps?.detailData?.packageDetail?.dynamicId !==
      this.props.detailData?.packageDetail?.dynamicId
    ) {
      const { tagDestinationName } = this.fetchTagDestAndBranch();
      this.props.updateInterventionData({
        packageId: this.props.detailData?.packageDetail?.id,
        branch: this.props.detailData?.branch || DOM_BRANCH,
        destinationCity: tagDestinationName,
        cmp: this.holidayDetailData?.cmp || '',
        departureDate: this.props.detailData?.packageDetail?.departureDetail?.departureDate,
        roomData: this.getRoomDetails(),
        campaign: this.holidayDetailData.campaign,
      });
    }
    if (
      (prevState.popup !== overlays.EDIT_OVERLAY && this.state.popup === overlays.EDIT_OVERLAY) ||
      (!prevState.showCoachMarks && this.state.showCoachMarks) ||
      (!prevState.whatToexpectReadMore && this.state.whatToexpectReadMore) ||
      (!prevState.openFDFeatureEditVisibility && this.state.openFDFeatureEditVisibility) ||
      (prevState.popup !== extraInfoRefs.TNC && this.state.popup === extraInfoRefs.TNC) ||
      (prevState.popup !== overlays.OFFER_OVERLAY && this.state.popup === overlays.OFFER_OVERLAY) ||
      (prevState.popup !== extraInfoRefs.CANCELLATION_POLICY &&
        this.state.popup === extraInfoRefs.CANCELLATION_POLICY) ||
      (!prevProps.isLoading && this.props.isLoading) ||
      (!prevProps.isError && this.props.isError)
    ) {
      this.props.pauseIntervention();
      this.interventionPaused = true;
    } else if (
      (prevState.popup === overlays.EDIT_OVERLAY && this.state.popup !== overlays.EDIT_OVERLAY) ||
      (prevState.showCoachMarks && !this.state.showCoachMarks) ||
      (prevState.whatToexpectReadMore && !this.state.whatToexpectReadMore) ||
      (prevState.openFDFeatureEditVisibility && !this.state.openFDFeatureEditVisibility) ||
      (prevState.popup === extraInfoRefs.TNC && this.state.popup !== extraInfoRefs.TNC) ||
      (prevState.popup === overlays.OFFER_OVERLAY && this.state.popup !== overlays.OFFER_OVERLAY) ||
      (prevState.popup === extraInfoRefs.CANCELLATION_POLICY &&
        this.state.popup !== extraInfoRefs.CANCELLATION_POLICY) ||
      (prevProps.isLoading && !this.props.isLoading) ||
      (prevProps.isError && !this.props.isError)
    ) {
      this.props.playIntervention();
      this.interventionPaused = false;
    }

      const activities = this?.newPackageDetail?.activityDetail?.cityActivities?.flatMap(cityActivity => cityActivity?.activities || []);
      const firstUnavailableActivity = activities?.find(activity => activity.unavailable === true);
      if (firstUnavailableActivity && this.activityFailedScrollRef.current) {
        const dayOfFirstUnavailableActivity = firstUnavailableActivity.day;
        this?.scrollToIndexFunc(dayOfFirstUnavailableActivity + 6, -this.activityIndexPerDay?.[dayOfFirstUnavailableActivity] * 150);
     }
  }

  async getDeviceType(device) {
    let deviceType = await updateDeviceType(device);
    this.setState({deviceType:deviceType});
  }

  async getDeviceType(device) {
    let deviceType = await updateDeviceType(device);
    this.setState({deviceType:deviceType});
  }

  componentWillUnmount() {
    //TODO - do we need to add this, since it was not present before
    // if(loginEventDetailListener.remove) {
    //   loginEventDetailListener.remove();
    // }
    this.props.RemoveMmtBlackDetail();
    this.trackPageExit()
    // BackHandler.removeEventListener(HARDWARE_BACK_PRESS, this.onBackPressed);

  }

  handleLocationPermission = async () => {
    const enableNewGeoLocation = getEnableGeoLoc();
    //Return if geo loc feature is not enabled.
    if (!enableNewGeoLocation) {
      return;
    }

    const popUpData = await handleLocationPopUp('DETAIL');
    const {showPopup, nearByCities, city, lat, lng} = popUpData || {};

    if (showPopup) {
      this.setState({
        selectCityPopupData: popUpData,
        showSelectCityPopUp: true,
      });
    } else {
      updateCityWithoutShowingPopup(city, nearByCities, this.onCitySelectedFromPopUp, this.trackUpdateCityWithoutShowingPopup);
    }
    if (city || nearByCities) {
      this.setState({citySelectionType: 'Auto-detect'});
    }
  }

  handleLocationPermission = async () => {
    const enableNewGeoLocation = getEnableGeoLoc();
    //Return if geo loc feature is not enabled.
    if (!enableNewGeoLocation) {
      return;
    }

    const popUpData = await handleLocationPopUp('DETAIL');
    const {showPopup, nearByCities, city, lat, lng} = popUpData || {};

    if (showPopup) {
      this.setState({
        selectCityPopupData: popUpData,
        showSelectCityPopUp: true,
      });
    } else {
      updateCityWithoutShowingPopup(city, nearByCities, this.onCitySelectedFromPopUp, this.trackUpdateCityWithoutShowingPopup);
    }
    if (city || nearByCities) {
      this.setState({citySelectionType: 'Auto-detect'});
    }
  }

  handleLocationPermission = async () => {
    const enableNewGeoLocation = getEnableGeoLoc();
    //Return if geo loc feature is not enabled.
    if (!enableNewGeoLocation) {
      return;
    }

    const popUpData = await handleLocationPopUp('DETAIL');
    const {showPopup, nearByCities, city, lat, lng} = popUpData || {};

    if (showPopup) {
      this.setState({
        selectCityPopupData: popUpData,
        showSelectCityPopUp: true,
      });
    } else {
      updateCityWithoutShowingPopup(city, nearByCities, this.onCitySelectedFromPopUp, this.trackUpdateCityWithoutShowingPopup);
    }
    if (city || nearByCities) {
      this.setState({citySelectionType: 'Auto-detect'});
    }
  }

  handleLocationPermission = async () => {
    const enableNewGeoLocation = getEnableGeoLoc();
    //Return if geo loc feature is not enabled.
    if (!enableNewGeoLocation) {
      return;
    }

    const popUpData = await handleLocationPopUp('DETAIL');
    const {showPopup, nearByCities, city, lat, lng} = popUpData || {};

    if (showPopup) {
      this.setState({
        selectCityPopupData: popUpData,
        showSelectCityPopUp: true,
      });
    } else {
      updateCityWithoutShowingPopup(city, nearByCities, this.onCitySelectedFromPopUp, this.trackUpdateCityWithoutShowingPopup);
    }
    if (city || nearByCities) {
      this.setState({citySelectionType: 'Auto-detect'});
    }
  }

  handleLocationPermission = async () => {
    const enableNewGeoLocation = getEnableGeoLoc();
    //Return if geo loc feature is not enabled.
    if (!enableNewGeoLocation) {
      return;
    }

    const popUpData = await handleLocationPopUp('DETAIL');
    const { showPopup, nearByCities, city, lat, lng } = popUpData || {};

    if (showPopup) {
      this.setState({
        selectCityPopupData: popUpData,
        showSelectCityPopUp: true,
      });
    } else {
      updateCityWithoutShowingPopup(
        city,
        nearByCities,
        this.onCitySelectedFromPopUp,
        this.trackUpdateCityWithoutShowingPopup,
      );
    }
    if (city || nearByCities) {
      this.setState({ citySelectionType: 'Auto-detect' });
    }
  };


  trackUpdateCityWithoutShowingPopup = (cityName) => {
    if (!isEmpty(cityName)) {
      showShortToast(cityName + GEO_LOC_AUTO_UPDATE_MESSAGE);
      this.trackClickEvent('From City_auto update');
    }
  };

  hideSelectCityPop = () => {
    this.setState({
      showSelectCityPopUp: false,
    });
  };

  onCitySelectedFromPopUp = ({ city }) => {
    const { cityName, locusId, airportCode } = city || {};
    const selectedCityType = isEmpty(airportCode) ? 'Non-Airport' : 'Airport';
    this.setState({ selectedCityType });
    this.holidayDetailData.departureDetail.departureCity = cityName;
    this.holidayDetailData.departureDetail.departureCityLocusCode = locusId;
    this.refreshDetails(false, false, null, false);
    this.trackLocalClickEvent(`select_hub_${cityName}`);
  };

  getRoomDetails = () => {
    return this.props.detailData?.roomDetails || this.roomDetails;
  };
  refreshDetails = (
    isChangingDate,
    isActionApiCalled = false,
    packageId,
    undo,
    { roomDetails = null } = {},
  ) => {
    if(!isActionApiCalled){
      this.updateInPDTRequestIdV2();
      }
    this.fetchDetailDataFunc(
      isChangingDate,
      isActionApiCalled,
      packageId,
      undo,
      roomDetails ? roomDetails : this.getRoomDetails(),
    );
    setTimeout(() => this.fetchStoryPageData(), 500);
    this.isLoading = false;
    this.isShowWarningMessage = true;
    this.setState({ fixed: false, isShowWarningMessage: true });
  };

  fetchStoryPageData = async () => {
    const storyPropAvailable = this.holidayDetailData ? this.holidayDetailData.storyEnabled : false;
    if (storyPropAvailable) {
      this.props.fetchStoryData(
        this.holidayDetailData,
        this.getRoomDetails(),
        this.trackLocalClickEvent,
      );
      const showStoryAgain = await getAndUpdateStoryStatus(
        parseInt(this.holidayDetailData.packageId),
      );
      if (showStoryAgain) {
        this.setState({
          showStoryPage: true,
        });
      }
    }
  };
  toggleStoryPage = () => {
    this.captureClickEvents({ eventName: 'story_open' });
    this.setState({
      showStoryPage: !this.state.showStoryPage,
    });
  };

  // Below function removes flight data present in this.props.componentFailureData;
  checkAndUpdatePrePaymentError = async (actionRequest, componentType) => {
    if (isEmpty(this.props.componentFailureData)) {
      return;
    }
    if (
      componentType == componentImageTypes.FLIGHT ||
      componentType == packageActionComponent.FLIGHT
    ) {
      const newReviewError = { ...this.props.componentFailureData };
      if (
        newReviewError &&
        newReviewError.componentErrors &&
        newReviewError.componentErrors.FLIGHT
      ) {
        delete newReviewError.componentErrors.FLIGHT;
        if (
          newReviewError.componentErrors.HOTEL &&
          newReviewError.componentErrors.HOTEL.length > 0
        ) {
          this.props.setReviewComponentFailureData(newReviewError);
        } else {
          this.props.setReviewComponentFailureData(null);
        }
      }
    } else if (componentType == componentImageTypes.HOTEL) {
      const newReviewError = { ...this.props.componentFailureData };
      const { componentErrors } = newReviewError;
      if (componentErrors && componentErrors.HOTEL && componentErrors.HOTEL.length) {
        componentErrors.HOTEL = componentErrors.HOTEL.filter((item) => {
          if (
            item.sellableId != actionRequest.sellableId &&
            item.sellableId == actionRequest.prevSellableId
          ) {
            return false;
          }
          return true;
        });
        if (componentErrors.HOTEL.length === 0) {
          delete componentErrors.HOTEL;
        }
        if (isEmpty(componentErrors.HOTEL) && isEmpty(componentErrors.FLIGHT)) {
          this.props.setReviewComponentFailureData(null);
        } else if (!isEmpty(this.props.componentFailureData))  {
          this.props.setReviewComponentFailureData(newReviewError);
        }
      }
    }
  };

  checkAndUpdateReviewError = async (componentType) => {
    if (isEmpty(this.state.reviewError)) {
      return;
    }
    const { error } = this.state.reviewError;
    const { errorType } = error || {};
    if (errorType === componentType) {
      this.setState({ reviewError: {} });
    }
  };

  /**
   * Common function to update components in the package.
   **/
  onComponentChange = (actionRequest, componentType, invalidActivitiesCount = 0) => {
    this.invalidActivitiesCount = invalidActivitiesCount;
    this.isLoading = true;
    this.setState({ showReviewPopUp: false,reviewError:{} });
    switch (componentType) {
      case componentImageTypes.FLIGHT:
        this.onFlightSelected(actionRequest);
        break;
      case componentImageTypes.HOTEL:
        this.onHotelSelected(actionRequest);
        break;
      case componentImageTypes.TRANSFERS:
        this.onTransferSelected(actionRequest);
        break;
      case componentImageTypes.ACTIVITY:
        this.onActivitySelected(actionRequest);
        break;
      case componentImageTypes.COMMUTE:
        this.onCommuteSelected(actionRequest);
        break;
      default:
        break;
    }
    this.checkAndUpdateReviewError(componentType);
    this.checkAndUpdatePrePaymentError(actionRequest, componentType);
  };

  /**
   * Common function to remove/toggle a component from the package.
   * Same function as used in old detail page
   * */
  onPackageComponentToggle = (add, packageComponent, transferObj) => {
    this.isLoading = true;
    this.setState({ showReviewPopUp: false });
    const action = {};
    action.action = packageActions.TOGGLE;
    if (packageComponent === packageActionComponent.CAR) {
      action.sellableId = transferObj.carItinerary.sellableId;
      action.startDay = transferObj.startDay;
    } else if (packageComponent === packageActionComponent.ACTIVITY) {
      action.removeAll = true;
      action.action = packageActions.REMOVE;
    } else if (packageComponent === packageActionComponent.FLIGHT) {
      this.checkAndUpdateReviewError(packageComponent);
      this.checkAndUpdatePrePaymentError({}, packageActionComponent.FLIGHT);
    }
    action.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
    this.updateInPDTRequestIdV2();
    this.props.togglePackageComponent(
      action,
      this.onPackageComponentToggled,
      this.onApiError,
      packageComponent,
    );
    const actionData = getActionData(add, packageComponent);
    // this.trackLocalClickEvent(actionData.eventName, '');
    this.actionLoadingText = actionData.actionLoadingText;
  };

  onFlightSelected = (actionRequest) => {
    actionRequest.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
    this.updateInPDTRequestIdV2();
    this.props.changeFlight(actionRequest, this.onPackageComponentToggled, this.onApiError);
    this.actionLoadingText = 'Updating selected flight';
  };

  onHotelSelected = (actionRequest) => {
    actionRequest.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
    this.updateInPDTRequestIdV2();
    this.props.changeHotel(actionRequest, this.onPackageComponentToggled, this.onApiError);
    this.actionLoadingText = 'Updating Hotel';
  };

  onTransferSelected = (actionRequest) => {
    actionRequest.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
    this.updateInPDTRequestIdV2();
    if (actionRequest.action === packageActions.TOGGLE) {
      this.props.togglePackageComponent(
        actionRequest,
        this.onPackageComponentToggled,
        this.onApiError,
        actionRequest.packageComponent,
      );
      this.actionLoadingText = 'Removing Transfers..';
    }

    if (actionRequest.action === packageActions.CHANGE) {
      this.props.changeTransfer(
        actionRequest,
        actionRequest.packageComponent,
        this.onPackageComponentToggled,
        this.onApiError,
      );
      this.actionLoadingText = 'Updating Transfers..';
    }
  };

  onActivitySelected = (actionRequest) => {
    this.updateInPDTRequestIdV2();
    this.props.modifyActivity(actionRequest, this.onPackageComponentToggled, this.onApiError);
    this.actionLoadingText = 'Updating Activities...';
  };

  onCommuteSelected = (actionRequest) => {
    this.updateInPDTRequestIdV2();
    this.props.modifyCommute(actionRequest, this.onPackageComponentToggled, this.onApiError);
    this.actionLoadingText = 'Updating Commute...';
  };

  onPackageComponentToggled = (response, lob, action) => {
    if (response) {
      //TODO push to undo stack
      if (lob === 'Activity') {
        const { actionResults } = response || {};
        let res = '';
        if (actionResults && actionResults.length > 0) {
          actionResults.forEach((item) => {
            res += item.errorMsg + '\n';
          });
          showShortToast(res);
        }
      }
      const priceChange =
        response.packageDetail.pricingDetail.categoryPrices[0].discountedPrice -
        this.packageDetailDTO.discountedPrice;

      if (lob === 'Hotel' && this.invalidActivitiesCount) {
        this.pushToStack(priceChange, lob, action, this.invalidActivitiesCount);
      } else {
        this.pushToStack(priceChange, lob, action);
      }
      this.togglePopup('');
      this.refreshDetails(false, true, null, false);
      //showLongToast((lob === 'CAR_ITINERARY' ? 'Transfer' : lob) +' has been ' + (action === 'TOGGLE' ? 'removed' : 'changed'));
      this.setPackageUpdatedToastState(true);
    }
  };

  undoPackage = () => {
    this.updateInPDTRequestIdV2();
    this.refreshDetails(false, true, null, true);
  };

  pushToStack = (priceChange, lob, action, activityCount) => {
    const tempStack = [...this.state.undoStack];
    const obj = { priceChange: priceChange, lob: lob, action: action };
    if (activityCount) {
      obj.activityCount = activityCount;
    }
    tempStack.push(obj);
    if (tempStack.length > this.cusCountLimit) {
      tempStack.shift();
    }
    this.setState({
      undoStack: tempStack,
      forceHitTravelplex: true,
    });
  };

  popFromStack = () => {
    const tempStack = [...this.state.undoStack];
    tempStack.pop();
    this.setState({
      undoStack: tempStack,
      isUndoVisible: false,
      forceHitTravelplex: true,
    });
    this.updateInPDTRequestIdV2()
    this.refreshDetails(false, false, null, true);
    this.setPackageUpdatedToastState(true);
  };

  // Callback to reset forceHitTravelplex flag after TravelPlex consumes the data
  resetForceHitTravelplex = () => this.setState({ forceHitTravelplex: false });

  onApiError = (msg, alert, reload) => {
    if (msg) {
      if (alert) {
        Alert.alert('', msg);
      } else {
        showShortToast(msg, 'bottom', 'dark');
      }
      this.isLoading = false;
      this.props.isLoading = false;
    }
    if (reload) {
      this.reloadOnError();
    } else {
      this.refreshDetails(false, false, null, false);
    }
  };

  reloadOnError = () => {
    this.packageDetailDTO.dynamicPackageId = '';
    this.refreshDetails(false, false, null, false);
  };

  fetchDetailDataFunc = async (
    isChangingDate,
    isActionApiCalled = false,
    packageId,
    undo,
    roomDetails = {},
  ) => {
    let dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
    if (packageId !== null) {
      // Only for Package Variant
      this.holidayDetailData.packageId = packageId; //to handle update package type in case of FD packages
      this.holidayDetailData.categoryId = null;
      dynamicPackageId = null;
    }
    const response = await this.props.fetchDetailData(
      this.holidayDetailData,
      this.packageDetailDTO,
      dynamicPackageId,
      roomDetails,
      isChangingDate,
      isActionApiCalled,
      this.actionLoadingText,
      undo,
    );

    /********* Handle onboarding cues **********/
    const hasPageVisitTime = await hasOnBoardingCuesLastVisit(HLD_CUES_POKUS_KEYS.DETAIL);
    if (hasPageVisitTime) {
      const delayOver = await isOnBoardingCuesDelayOver(HLD_CUES_POKUS_KEYS.DETAIL);
      if (delayOver) {
        //  clean page keys, since we need to show it again
        await removeCuesStepsShown(HLD_CUES_POKUS_KEYS.DETAIL);
        await this.showCoachMarksOverlay(response);
      }
    } else {
      await this.showCoachMarksOverlay(response);
    }

    PerformanceMonitorModule.stop();
  };

  onScroll = (event) => {
    //Fix for HLD-18708
    if (!isIos()) {
      LayoutAnimation.configureNext(LayoutAnimation.create(100, 'easeInEaseOut', 'opacity'));
    }
    const scrollIndex = event.nativeEvent.contentOffset.y;
    // Handle FAB button animation here
    if (scrollIndex > 50 && !this.state.fabTextShrinked) {
      this.setState({
        fabTextShrinked: true,
      });
    }
  };

  prepareDayPlanSections = (packageDetail, sightSeeingMap) => {
    const { currentActivePlanOnItinerary } = this.state;
    const summaryData = packageDetail?.packageSummary || [];
    if (currentActivePlanOnItinerary === TABS.SUMMARY && this.state.isItineraryVisible) {
      return [
        {
          id: 'Summary',
          sectionName: 'PackageSummaryTabSection',
          component: [
            <View style={styles.summaryContainer}>
              <SummaryView summaryData={summaryData} />
            </View>,
          ],
        },
      ];
    } else if (currentActivePlanOnItinerary === TABS.VISA && this.state.isItineraryVisible) {
      return [
        {
          id: 'visa',
          sectionName: 'PackageVisaSection',
          component: [
            <View style={styles.visaContainer}>
              <VisaContainer
                packageContent={this.props.packageContent}
                visaContentLoading={isEmpty(this.props.packageContent)}
                dynamicPackageId={this.packageDetailDTO.dynamicPackageId}
                updateVisa={this.updateVisa}
                visaPackageFeature={getPackageFeatureByKey({
                  key: PACKAGE_FEATURES.VISA,
                  packageFeatures: this.props.detailData.packageDetail.packageFeatures,
                })}
              />
              <View style={styles.seperator} />
            </View>,
          ],
        },
      ];
    }
    return this.getDayPlanComponents(packageDetail, sightSeeingMap);
  };

  getDayPlanComponents = (packageDetail, sightSeeingMap) => {
    const { itineraryDetail, metadataDetail, imageDetail, destinationDetail, departureDetail } =
      packageDetail || {};
    const { images = [] } = imageDetail || {};
    const { bundled = false } = metadataDetail || {};
    const { dynamicItinerary, staticItinerary } = itineraryDetail || {};
    const { dayItineraries = [] } = dynamicItinerary || {};
    const { duration, destinations } = destinationDetail || {};
    const destinationMap = createDestinationMap(duration, destinations);
    const staticData = createStaticItineraryData(staticItinerary, images);
    const flightReqParams = createFlightRequestParams(packageDetail);
    const sections = [];
    const {showOverlay, hideOverlays, clearOverlays} = this.props || {};
    if (dayItineraries && dayItineraries.length > 0) {
      dayItineraries.forEach((data, index) => {
        // Find the index of activity unit type for each day
        let activityIndex = data.itineraryUnits.findIndex((unit) => {
          const { itineraryUnitType } = unit;
          return itineraryUnitType === itineraryUnitTypes.ACTIVITY;
        });

        // Store activity index per day in an object
        if (!this.activityIndexPerDay) {
          this.activityIndexPerDay = {};
        }
        this.activityIndexPerDay[data.day] = activityIndex;
        let indexItem = data.itineraryUnits.findIndex((unit, index) => {
          const { itineraryUnitType } = unit;
          return (
            itineraryUnitType === itineraryUnitTypes.TRANSFERS ||
            itineraryUnitType === itineraryUnitTypes.CAR ||
            itineraryUnitType === itineraryUnitTypes.COMMUTE
          );
        });

        if (Object.keys(sightSeeingMap).length > 0 && indexItem === -1) {
          if (sightSeeingMap[data.day]) {
            data.itineraryUnits.push(sightSeeingMap[data.day]);
          }
        } else if (Object.keys(sightSeeingMap).length > 0 && indexItem >= 0) {
          if (sightSeeingMap[data.day]) {
            data.itineraryUnits.splice(indexItem + 1, 0, sightSeeingMap[data.day]);
          }
        }
        let failedHotels = [];
        let ifFlightGroupFailed = false;
        if (!isEmpty(this.state.reviewError)) {
          if (this.state.reviewError.error) {
            if (
              this.state.reviewError.error.errorType ===
              detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL
            ) {
              if (this.state.reviewError.error.errorData) {
                failedHotels = this.state.reviewError.error.errorData.failedHotels || [];
              }
            } else if (
              this.state.reviewError.error.errorType ===
              detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT
            ) {
              if (this.state.reviewError.error.code) {
                ifFlightGroupFailed = true;
              }
            }
          }
        } else if (!isEmpty(this.props.componentFailureData)) {
          const { componentErrors = {} } = this.props.componentFailureData || {};
          const { FLIGHT = [], HOTEL = [] } = componentErrors;
          if (FLIGHT && FLIGHT.length > 0) {
            ifFlightGroupFailed = true;
          }
          if (HOTEL && HOTEL.length > 0) {
            failedHotels = HOTEL;
          }
        }

        sections.push({
          id: `DayplanSection_${index}`,
          sectionName: `PackageDay_${index + 1}Section`,
          component: [
            <DayPlan
              failedHotels={failedHotels}
              ifFlightGroupFailed={ifFlightGroupFailed}
              data={data}
              roomDetails={this.getRoomDetails()}
              staticData={staticData}
              destinationMap={destinationMap}
              bundled={bundled}
              packageDetail={packageDetail}
              index={index + 1}
              totalDays={dayItineraries.length}
              isVisible={this.state.isItineraryVisible}
              currentActivePlanOnItinerary={this.state.currentActivePlanOnItinerary}
              togglePopup={this.togglePopup}
              onComponentChange={this.onComponentChange}
              onPackageComponentToggle={this.onPackageComponentToggle}
              packageDetailDTO={this.packageDetailDTO}
              flightReqParams={flightReqParams}
              branch={this.props.detailData.branch}
              trackLocalClickEvent={this.trackLocalClickEvent}
              trackLocalPageLoadEvent={this.trackLocalPageLoadEvent}
              lastPageName={this.lastPageName}
              showOverlay={showOverlay}
              hideOverlays={hideOverlays}
              clearOverlays={clearOverlays}
              packageContent={this.props.packageContent}
              isLoading={this.props.isLoading}
              detailData={this.props.detailData}
              hotelDetailLoading={this.props.hotelDetailLoading}
              updateMeal={this.updateMeal}
            />,
          ],
        });
      });
    }
    return sections;
  };

  scrollToIndexFunc = (index,viewOffset = 125) => {
    if (this.flatListRef) {
      this.canScrollCalender = true;
      setTimeout(() => {
        this?.flatListRef?.scrollToIndex?.({ animated: true, index, viewOffset });
        // this.canScrollCalender = false;
      }, 200);
    }
  };
  onScrollDragBegin = () => {
    this.canScrollCalender = false;
  };

  onViewableItemsChanged = ({ viewableItems, changed }) => {
    if (!this.canScrollCalender) {
      if (this.state.currentActivePlanOnItinerary === TABS.DAY) {
        const index = this.getDaySectionIndex(viewableItems);
        if (this.calenderRef && this.calenderRef.current && index != null) {
          this.calenderRef.current._updateSelectedDate(index);
        }
      }
    }
    viewableItems.forEach((viewableItem) => {
      const { item = {}, isViewable, index: sectionIndex } = viewableItem;
      const { sectionName = '' } = item;
      if (!sectionName || !isViewable) {
        return;
      }
      if (!this.detailPageSectionVisits[sectionName]) {
        this.detailPageSectionVisits[sectionName] = 1;
        setPageSectionVisitResponse({
          pageName: sectionTrackingPageNames.DETAIL_PAGE,
          value: this.detailPageSectionVisits,
        });
        const eventName = `Viewed_${sectionName}_${sectionIndex}`;
        this.captureClickEvents({
          eventName,
          prop1: sectionName,
          actionType: PDT_EVENT_TYPES.contentSeen,
        });
      }
    });
  };

  renderItem = ({ item }) => item.component;

  handlePDT = (eventName, prop1) => {
    this.captureClickEvents({ eventName,  sendGIData: isRawClient(), prop1  });
  };

  editTravelDetails = () => {
    this.setState({ showSearch: false });
    this.setState({ showPageHeader: false });
    this.captureClickEvents({ eventName: 'edit_intent' });
    this.trackLocalClickEvent(PDTConstants.EDIT_INTENT, '', {sendGIData: isRawClient()});
    this.togglePopup(overlays.EDIT_OVERLAY);
  };

  captureClickEvents = ({
    eventName = '',
    value = '',
    actionType = {},
    suffix = '',
    prop1 = '',
    prop66 = '',
    omniData = {},
    pdtExtraData = {},
    giData = {},
    sendGIData = false } = {}) => {
    logPhoenixDetailPDTEvents({
      actionType: !isEmpty(actionType) ? actionType : PDT_EVENT_TYPES.buttonClicked,
      value: value || eventName,
      shouldTrackToAdobe:isEmpty(actionType)
    });
    this.trackLocalClickEvent(eventName, suffix, { prop1, prop66, omniData, pdtExtraData, sendGIData, giData });
  };


  trackLocalClickEvent = (
    eventName,
    suffix = '',
    { prop1 = '', prop66 = '',  sendGIData = false, giData = {}, omniData = {}, pdtExtraData = {} } = {},
  ) => {
    sendGIData && trackDetailGIClickEvent({eventName: eventName + suffix, ...giData});
    const pdtData = {
      pageDataMap: this.props.detailData.pageDataMap,
      interventionDetails: this.props?.fabCta?.interventionLoggingDetails || {},
      eventType: PDT_RAW_EVENT,
      activity: eventName + suffix,
      requestId: createRandomString(),
      branch: this.props.detailData.branch,
      extraData: pdtExtraData,
    };

    trackHolidaysDetailClickEvent({
      omniEventName: eventName + suffix,
      omniData: {
        ...omniData,
        [TRACKING_EVENTS.M_C1]: prop1,
      },
      prop66,
      prop83: HolidayDataHolder.getInstance().getBanner(),
      pdtData,
    });
  };
  trackErrorLocalClickEvent = ({ eventName = '', suffix = '', evar22 = '' }) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName + suffix,
      errorDetails: [{
        code: this.props.error?.code,
        message: fetchErrorMessage(this.props?.error),
      }],
    });
    trackHolidaysDetailClickEvent({
      omniEventName: eventName + suffix,
      omniData: {
        [TRACKING_EVENTS.M_V22]: evar22 ? `HLD:${evar22}` : '',
      },
      pdtData: {
        pageDataMap: this.props.detailData.pageDataMap,
        interventionDetails: this.props?.fabCta?.interventionLoggingDetails || {},
        eventType: PDT_RAW_EVENT,
        activity: eventName + suffix,
        requestId: createRandomString(),
        branch: this.props.detailData.branch,
      },
    });
  };

  trackLocalChatClickEvent = ({ eventName = '', suffix = '', prop66 , sendGIData = false,omniData = {} }) => {
    trackHolidaysDetailClickEvent({
      omniEventName: eventName + suffix,
      prop66,
      pdtData: {
        pageDataMap: this.props.detailData.pageDataMap,
        interventionDetails: this.props?.fabCta?.interventionLoggingDetails || {},
        eventType: PDT_RAW_EVENT,
        activity: prop66,
        requestId: createRandomString(),
        branch: this.props.detailData.branch,
      },
      omniData
    });
    sendMMTGtmEvent({
      eventName: eventName + suffix,
      data: { pageName: this.pageName, branch: this.props.detailData.branch },
    });
    sendGIData && trackDetailGIClickEvent({eventName: eventName+suffix});
  };

  trackLocalPageLoadEvent = (event, logOmni = false, pageName = '', { prop1 = '' } = {}) => {
    const { trackingData = {} } = this.holidayDetailData || {};
    const { categoryTrackingEvent = {} } = trackingData || {};
    trackHolidaysDetailLoadEvent({
      logOmni,
      omniPageName: pageName,
      omniData: {
        [TRACKING_EVENTS.M_C1]: prop1,
      },
      pdtData: {
        pageDataMap: this.props?.detailData?.pageDataMap || {},
        interventionDetails: this.props?.fabCta?.interventionLoggingDetails || {},
        eventType: PDT_RAW_EVENT,
        activity: event,
        requestID: createRandomString(),
        branch: this.props?.detailData?.branch || '',
        categoryTrackingEvent,
      },
    });
  };

  togglePopup = (popupName, cityId, sectionToShow, openHalf = false) => {
    const eventName = getEventName(popupName, sectionToShow, this.placesName);
    this.setState({
      popup: popupName,
      popupData: {},
      cityId,
      sectionToShow,
      openHalf,
      videoPaused: true,
      showActivityDetailForInclusion: false,
      activityDetailData: {},
    });
    if (eventName && this.props.detailData && this.props.detailData.pageDataMap) {
      logPhoenixDetailPDTEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: eventName,
      });
      trackHolidaysDetailClickEvent({
        omniEventName: eventName,
        pdtData: {
          pageDataMap: this.props.detailData.pageDataMap,
          interventionDetails: this.props?.fabCta?.interventionLoggingDetails || {},
          eventType: PDT_RAW_EVENT,
          activity: eventName,
          requestId: createRandomString(),
          branch: this.props.detailData.branch,
          sendGIData: isRawClient(),
        },
      });
    }
  };

  packageReview = async (reviewRequestSource="DETAILS_PAGE_REVIEW") => {
    this.setState({ videoPaused: true });
    this.setState({ reviewError: {}, showReviewPopUp: false });
    if (!isEmpty(this.props.componentFailureData)) {
      this.props.setReviewComponentFailureData({});
    }
    const dynamicPackageId = this.props.detailData.packageDetail.dynamicId;
    const { trackingData = {} } = this.holidayDetailData || {};
    if (dynamicPackageId) {
      const holidayReviewData = {};
      holidayReviewData.showChat =
        this.props.fabCta && this.props.fabCta.showChat ? this.props.fabCta.showChat : false;
      holidayReviewData.dynamicPackageId = dynamicPackageId;
      holidayReviewData.searchCriteria = this.holidayDetailData.searchCriteria;
      holidayReviewData.cmp = this.holidayDetailData.cmp;
      holidayReviewData.roomDetails = this.getRoomDetails();
      holidayReviewData.packageName = this.props.detailData.packageDetail.name;
      holidayReviewData.packageId = this.props.detailData.packageDetail.id;
      holidayReviewData.packageType =
        this.props.detailData.packageDetail.metadataDetail.packageType;
      holidayReviewData.categoryId = this.holidayDetailData.categoryId;
      holidayReviewData.duration = this.props.detailData.packageDetail.destinationDetail.duration;
      holidayReviewData.branch = this.props.detailData.packageDetail.metadataDetail.branch;
      holidayReviewData.tagDestination = this.props.detailData.packageDetail.tagDestination.name;
      holidayReviewData.isWG = this.props.detailData.isWG;
      holidayReviewData.pt = this.holidayDetailData?.pt || '';
      holidayReviewData.aff = this.holidayDetailData.aff;
      holidayReviewData.trackingData = trackingData || {};
      holidayReviewData.source = this.holidayDetailData.source || '';
      PerformanceMonitorModule.start('PackageDetailReview');
      this.captureClickEvents({eventName : PDTConstants.BOOK, sendGIData: isRawClient(),  sendGIData: isRawClient(),
        giData: { itemSelected: `${this.holidayDetailData.packageId}_${this.holidayDetailData.name}` }});
      this.trackPageExit();
      this.props.fetchReviewData(
        holidayReviewData,
        holidayReviewData.roomDetails,
        false,
        {},
        null,
        true,
        (reviewData) => {
          this.goToReview(holidayReviewData, reviewData);
        },
        this.handleReviewFailure,
        false,
        false,
        reviewRequestSource
      );
    }
    const { detailData } = this.props;
    const { packageDetail = {} } = detailData || {};
    const {
      dynamicId = '',
      name = '',
      additionalDetail = {},
      pricingDetail = {},
      metadataDetail = {},
    } = packageDetail || {};
    const { aff = '', departureDetail = {}, destinationDetail = {} } = this.holidayDetailData || {};
    BranchIOTracker.trackCommerceEvent({
      [BranchIOTracker.KEYS.EVENT_NAME]: BranchIOTracker.EVENT.ADD_TO_CART,
      [BranchIOTracker.KEYS.PAGE_NAME]: BranchIOTracker.PAGE.DETAIL,
      [BranchIOTracker.KEYS.CANONICAL_IDENTIFIER]: dynamicId,
      [BranchIOTracker.KEYS.TITLE]: name,
      [BranchIOTracker.KEYS.DESCRIPTION]: additionalDetail.description,
      [BranchIOTracker.KEYS.AFFILIATE]: aff,
      [BranchIOTracker.KEYS.COUPON]:
        pricingDetail?.categoryPrices[0]?.couponDiscount?.couponCode || '',
      [BranchIOTracker.KEYS.CUSTOM_META_DATA]: {
        [BranchIOTracker.KEYS.PRODUCT_NAME]: name,
        [BranchIOTracker.KEYS.PRICE]: pricingDetail?.categoryPrices[0]?.price,
        [BranchIOTracker.KEYS.CURRENCY]: pricingDetail?.categoryPrices[0]?.currencyCode,
      },
      [BranchIOTracker.KEYS.CUSTOM_DATA]: {
        ...departureDetail,
        ...destinationDetail,
        ...metadataDetail,
      },
    });
  };

  pageName = () => {
    if (this.props.branch === DOM_BRANCH) {
      return 'mob:funnel:DOM holidays: details';
    }
    return 'mob:funnel:OBT holidays: details';
  };

  refreshFetchContent = (departureDate, departureCity, roomData, packagePaxDetail) => {
    this.roomDetails = createRoomDetailsFromRoomDataForPhoenix(roomData, packagePaxDetail);
    this.childAgeArray = createChildAgeArrayFromRoomDataForPhoenix(roomData);
    this.holidayDetailData.departureDetail.departureCity = departureCity;
    if (this.holidayDetailData.departureDetail.departureDate !== departureDate) {
      this.holidayDetailData.selectedDate = departureDate;
    }
    this.holidayDetailData.departureDetail.departureDate = departureDate;
    this.closeWarningMessages = {};
    this.togglePopup('');
    this.refreshDetails(false, false, null, false, { roomDetails: this.roomDetails });
    BranchIOTracker.trackContentEvent({
      [BranchIOTracker.KEYS.EVENT_NAME]: BranchIOTracker.EVENT.SEARCH,
      [BranchIOTracker.KEYS.PAGE_NAME]: BranchIOTracker.PAGE.DETAIL,
      [BranchIOTracker.KEYS.DESCRIPTION]: 'Search updated from details page',
      [BranchIOTracker.KEYS.SEARCH_QUERY]: departureCity,
      [BranchIOTracker.KEYS.CUSTOM_DATA]: {
        departureDate,
        departureCity,
        roomData,
        packagePaxDetail,
      },
    });
  };

  onBackPressed = () => {
    const { popup } = this.state || {};
    if (this?.props?.leaveIntent?.toUpperCase() == 'Y' && !this.interventionPaused) {
      this?.props?.close();
      {
        /* to handle intervention on back press */
      }
      return true;
    } else {
      if (popup !== '') {
        this.togglePopup('');
      } else if ((this.props[deepLinkParams.deepLink] || this.deepLinkSimilar) && !isRawClient()) {
        if (isIosClient()) {
          if (this.holidayDetailData.refreshLanding) {
            this.holidayDetailData.refreshLanding();
          }
          ViewControllerModule.popViewController(1);
        } else {
          BackHandler.exitApp();
        }
        this.props.setReviewComponentFailureData(null);
      }
      else if (this.holidayDetailData.fromDeepLink && isRawClient() && isBacktoLanding(this.holidayDetailData.aff, this.state.deviceType)) {
        window.location.href = '/holidays/international';
      }
      else {
        if (this.holidayDetailData.refreshLanding) {
          this.holidayDetailData.refreshLanding();
        }
        HolidayNavigation.pop();
        this.props.setReviewComponentFailureData(null);
      }
      exitLocalNotification(DETAIL_LOCAL_NOTIFICATION_PAGE_NAME);
      this.captureClickEvents({ eventName: PDTConstants.BACK, sendGIData : isRawClient()  });
      return true;
    }
  };

  getDaySectionIndex = (viewableItems) => {
    if (viewableItems && viewableItems.length > 0) {
      const { item } = viewableItems[0];
      const { id } = item || {};
      if (id && id.includes('DayplanSection_')) {
        const items = id.split('_');
        if (items.length > 1) {
          return parseInt(items[1]);
        }
      }
    }
    return null;
  };

  /**
   * This function returns persuasion at different indices.
   * Since persuasions position are dynamic on the detail page
   */

  getPersuasion = (persuasionData) => {
    const index = persuasionData?.findIndex((el) => el?.type == viewTypes.LISTING_WPM_PERSUASION);
    if (persuasionData && Array.isArray(persuasionData) && index > -1) {
      return (
        <HolidayWpmPersuasion
          key="WPM"
          wpmDetail={persuasionData[index]?.data?.persuasionsDetail?.wpmPersuasion?.wpmDetail}
          style={detailPersuasionStyle}
        />
      );
    }
    return [];
  };
  handlePricePersuasion = (departureDate) => {
    if (this.holidayDetailData.departureDetail.departureDate !== departureDate) {
      this.holidayDetailData.selectedDate = departureDate;
      this.holidayDetailData.departureDetail.departureDate = departureDate;
      this.refreshDetails(false, false, false, false);
    }
  };
  getPersuasionV2 = (persuasionData, order) => {
    return getPersuasionV2(
      persuasionData,
      order,
      this.handlePricePersuasion,
      this.trackLocalClickEvent,
      styles.persuasionContainer,
    );
  };

  getZCPersuasion = () => {
    return getZCPersuasion(
      this.props.cancellationPolicyData,
      this.openZCSection,
      extraInfoRefs.FREE_CANCELLATION_BANNER,
    );
  };

  getTopPersuasion = (persuasionData, index) => {
    const indexItem = persuasionData?.findIndex((el) => el?.data?.order === index);
    if (indexItem > -1) {
      return this.getPersuasionV2(persuasionData, 2);
    } else if (
      this.props.cancellationPolicyData &&
      this.props.cancellationPolicyData.success &&
      this.props.cancellationPolicyData.penaltyDetail &&
      this.props.cancellationPolicyData.penaltyDetail.zcOptions &&
      (isZCAvailable(this.props.cancellationPolicyData.penaltyDetail.zcOptions) ||
        isFlexiDateAvailable(this.props.cancellationPolicyData.penaltyDetail.zcOptions))
    ) {
      return this.getZCPersuasion();
    } else {
      return [];
    }
  };

  getBottomPersuasion = (persuasionData, index) => {
    const indexItem = persuasionData?.findIndex((el) => el?.data?.order == index);
    if (indexItem > -1) {
      if (
        this.props.cancellationPolicyData &&
        this.props.cancellationPolicyData.success &&
        this.props.cancellationPolicyData.penaltyDetail &&
        this.props.cancellationPolicyData.penaltyDetail.zcOptions &&
        (isZCAvailable(this.props.cancellationPolicyData.penaltyDetail.zcOptions) ||
          isFlexiDateAvailable(this.props.cancellationPolicyData.penaltyDetail.zcOptions))
      ) {
        return this.getZCPersuasion();
      }
    }
    return [];
  };

  openZCSection = () => {
    this.togglePopup('');
    this.setState({ popup: extraInfoRefs.CANCELLATION_POLICY });
  };

  updateDepDate = (newDate) => {
    this.holidayDetailData.departureDetail.departureDate = newDate;
    this.togglePopup('');
    this.refreshDetails(true, false, null, false);
  };

  instaStoryDone = () => {
    this.setState({ instaRunning: false });
    const eventName = `story_view_${DETAIL_TRACKING_PAGE_NAME}`;
    this.trackLocalClickEvent(eventName);
  };

  render() {
    return (
      <View style={{ flex: 1 }}>
        <View style={styles.pageWrap} />
        {(this.isLoading || this.props.isLoading) &&
          !this.state.instaRunning &&
          this.renderProgressView()}
        {this.props.isError && !this.state.instaRunning && this.renderError()}
        {!this.isLoading && this.props.isSuccess && !this.state.instaRunning && this.props.detailData?.packageDetail && this.renderContent()}
        {this.state.instaRunning && (
          <HolidayInstaStory
            swipeUpText="Swipe up to view details"
            count={this.storyCount}
            images={this.holidayDetailData.images}
            onInstaStoryDone={this.instaStoryDone}
          />
        )}
        {this.props.storySuccess && this.state.showStoryPage && (
          <HolidayStoryPage
            packageId={this.holidayDetailData.packageId}
            storyData={this.props.storyData}
            toggleStoryPage={this.toggleStoryPage}
            trackLocalClickEvent={this.trackLocalClickEvent}
            captureClickEvents={this.captureClickEvents}
          />
        )}
      </View>
    );
  }

  openCustomizationPopup = () => {
    if (this.state.undoStack && this.state.undoStack.length > 0) {
      this.captureClickEvents({
        eventName: 'expand_customizations',
        value: 'expand|customizations',
      });
      this.toggleUndoBottomSheet(true);
    }
  };

  checkAndUpdateCurrentActivePlan = () => {
    const { currentActivePlanOnItinerary } = this.state;
    const { detailData } = this.props;
    const { packageDetail } = detailData || {};
    const { packageInclusionsDetail, basePackageInclusionsDetail } = packageDetail || {};
    toggleDayPlan(
      currentActivePlanOnItinerary,
      packageInclusionsDetail,
      this.toggleDayPlan,
      basePackageInclusionsDetail,
    );
  };

  // Index should be size of flatListData length
  scrollToBottom = (viewOffset = 125, timeOutValue = 500) => {
    const index = this.flatListDataSize - 1;
    if (this.flatListRef) {
      setTimeout(() => {
        this.flatListRef.scrollToIndex({ animated: true, index, viewOffset });
      }, timeOutValue);
    }
  };
  toggleReadMore = (value) => {
    this.setState({
      whatToexpectReadMore: value,
    });
  };
  renderContent = () => {
    const { detailData, persuasionData, fabCta, similarPackages, cancellationPolicyData } =
      this.props;
    this.newPackageDetail = getOptimizedPackageDetail(
      detailData.packageDetail,
      this.props.packageContent,
    );
    const {
      name,
      itineraryDetail,
      departureDetail,
      additionalDetail,
      holidayExpert,
      destinationTips,
      metadataDetail,
      sightSeeingDetails,
      imageDetail,
    } = this.newPackageDetail || {};
    const { dynamicItinerary } = itineraryDetail || {};
    const { dayItineraries = [] } = dynamicItinerary || {};
    const { loggedIn, showToolTip, branch } = detailData || {};
    const isShortListed = this.isShortListedPackage();
    const travellerCount = calculateTravellersCount(this.getRoomDetails());
    const videoUrl = getVideoUrl(this.newPackageDetail);
    const showStory = this.getViewStoryEnabled(this.newPackageDetail);
    const tncAvailable =
      this.newPackageDetail.additionalDetail &&
      !isEmpty(this.newPackageDetail.additionalDetail.tnc);
    const isWG = this.holidayDetailData.pt === WEEKEND_GETAWAY_PAGE_TYPE;
    const { departureDate, packageDate } = departureDetail || {};
    const calenderDates = getDates(new Date(packageDate), dayItineraries, sightSeeingDetails);
    const { bundled } = metadataDetail || {};
    const { gallerySections = {} } = imageDetail || {};
    this.showNewGallery = this.showNewContentUI && !isEmpty(gallerySections);
    let disableBookNowButton = isEmpty(this.state.reviewError) ? false : true;

    if (!isEmpty(this.props.componentFailureData)) {
      const { componentErrors } = this.props.componentFailureData || {};
      const { FLIGHT = [], HOTEL = [] } = componentErrors || {};
      if ((HOTEL && HOTEL.length > 0) || (FLIGHT && FLIGHT.length > 0)) {
        disableBookNowButton = true;
      }
    }
    if (persuasionData) {
      addPersuasionToDetailData(detailData, persuasionData, cancellationPolicyData);
      updateEMIPriceForPersuasion(persuasionData, this.newPackageDetail);
    }
    this.checkAndUpdateCurrentActivePlan();
    setTrackingData(detailData.pageDataMap, branch, {
      categoryTrackingEvent: this.holidayDetailData.trackingData?.categoryTrackingEvent || '',
      source: this.holidayDetailData.source || '',
    });
    const sightSeeingMap = createSightSeeingDayPlanDataMap(
      this.props.packageContent,
      departureDetail,
    );
    const sightseeingCount = Object.keys(sightSeeingMap)?.length || 0;
    const memberShipCardData = {
      cards: getMemberShipCardArray({
        mmtBlackDetail: this.props.mmtBlackDetail,
        personalizationDetail: this.props.personalizationDetail,
      }),
    };
    const membershipCardWidth = SCREEN_WIDTH - 32;
    const flatListData = [
      { id: '0', component: [this.BasicDetails()], sectionName: 'PackageGallerySection' },
      {
        id: '1',
        sectionName: 'PackageDetailsSection',
        component: (
          <PackageInfoSection
            newPackageDetail={this.newPackageDetail}
            toggleFDFeatureBottomSheet={this.toggleFDFeatureBottomSheet}
            toggleReadMore={this.toggleReadMore}
            toggleStoryPage={this.toggleStoryPage}
            showStory={showStory}
            updateVisa={this.updateVisa}
            {...this.props}
          />
        ),
      },
      {
        id: '2',
        component: memberShipCardData && memberShipCardData?.cards?.length > 0 && (
          <View
            style={[
              styles.membershipCardContainer,
              !this.props.mmtBlackDetail?.section?.overlayImage ? { ...paddingStyles.pt10 } : {},
            ]}
          >
              <MembershipCarouselV2
                memberShipCardData={memberShipCardData}
                onKnowMorePress={this.handleMemberShipCardOnKnowMoreClick}
                mmtblackPdtEvents={logPhoenixDetailPDTEvents}
                trackMemberShipLoadEvent={this.trackMemberShipLoadEvent}
                contentType={CONTENT_TYPES.DETAIL}
                containerStyles={{ width: membershipCardWidth, ...marginStyles.mh16 }}
                showFullBorderGradient={true}
                cardItemStyle={styles.cardItemStyle}
                customCardsWprStyle = {styles.customCardsWprStyle}
              />
          </View>
        ),
      },
      {
        id: '3',
        sectionName: 'PackagePersuasionSection',
        component: [this.getTopPersuasion(persuasionData, persuasions.TOP_DETAIL)],
      },
      {
        id: '4',
        sectionName: 'PackageSafeBannerSection',
        component: <SafeBanner packageDetail={this.newPackageDetail} branch={branch} />,
      },
      {
        id: '5',
        sectionName: 'PackageItineraryHeaderSection',
        component: (
          <ItineraryHeader
            isVisible={this.state.isItineraryVisible}
            onToggle={this.showItinerary}
          />
        ),
      },
      {
        id: '6',
        sectionName: 'PackageItineraryCalendarSection',
        component: (
          <DateSection
            failedItineraryName={this.getNameOfFailedItinerary(
              this.state.reviewError,
              this.newPackageDetail,
            )}
            reviewError={this.state.reviewError}
            componentFailureData={this.props.componentFailureData}
            ref={this.calenderRef}
            isVisible={this.state.isItineraryVisible}
            onPressHandler={this.scrollToIndexFunc}
            calenderDates={calenderDates}
            index={6}
            toggleDayPlan={this.toggleDayPlan}
            currentActivePlanOnItinerary={this.state.currentActivePlanOnItinerary}
            detailData={detailData}
            fromPreSales={false}
            componentCount={this.props.componentCount}
            sightseeingCount={sightseeingCount}
          />
        ),
      },
      ...this.prepareDayPlanSections(this.newPackageDetail, sightSeeingMap),
      {
        id: '7',
        sectionName: 'HolidayExpertSection',
        component: (
          <HolidayExpertSection
            packageDetail={this.newPackageDetail}
            toggleReadMore={this.toggleReadMore}
            fromPresales={this.props.fromPresales}
          />
        ), // HE section not to be shown on PSM
      },
      {
        id: '8',
        sectionName: 'PackageTopPersuasionSection',
        component: [this.getBottomPersuasion(persuasionData, persuasions.TOP_DETAIL)],
      },
      {
        id: '9',
        sectionName: 'PackageBottomPersuasionSection',
        component: [this.getPersuasionV2(persuasionData, persuasions.BOTTOM_DETAIL)],
      },
      {
        id: '10',
        sectionName: 'PackagePersuasionSection',
        component: [this.getPersuasion(persuasionData)],
      },
      {
        id: '11',
        sectionName: 'PackageAccordianSection',
        component: (
          <Accordian
            openCustomizationPopup={this.openCustomizationPopup}
            onSimilarPackageClicked={this.onSimilarPackageClicked}
            onTermsAndConditionClicked={() =>
              this.setState({ popup: extraInfoRefs.TNC, sectionToShow: extraInfoRefs.TNC })
            }
            onPolicyClicked={() =>
              this.setState({
                popup: extraInfoRefs.CANCELLATION_POLICY,
                sectionToShow: extraInfoRefs.CANCELLATION_POLICY,
              })
            }
            showSimilarPackage={similarPackages && similarPackages.length > 0 && !bundled}
            showTermsAndCondition={tncAvailable}
            showPolicy={
              this.props.cancellationPolicyData && this.props.cancellationPolicyData.success
            }
            scrollToBottom={this.scrollToBottom}
            fromPreSales={false}
            summaryData={this.props.detailData?.packageDetail?.packageSummary}
          />
        ),
      },
    ];

    this.flatListDataSize = flatListData.length;
    return (
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <View style={{ flex: 1 }}>
          <Animated.View
            style={{
              zIndex: 99999999,
              elevation: 4,
            }}
          >
            <DetailPageHeader
              heading={name}
              showSearch={this.state.showSearch}
              showPageHeader={this.state.showPageHeader}
              onBackPress={this.onBackPressed}
              onSharePress={this.onSharePress}
              onFavPress={this.updateShortListedPackage}
              isShortListed={isShortListed}
              editTravelDetails={this.editTravelDetails}
            />
          </Animated.View>

          {/* NOT IN USE - KEPT FOR REFERENCE */}
          {/* {this.state.popup !== '' &&
          this.state.popup === overlays.EDIT_OVERLAY &&
          this.state.popup !== overlays.IMAGE_OVERLAY &&
          createGenericTextualBottomSheet(this.state.popup, this.togglePopup)} */}
          {/* NOT IN USE - KEPT FOR REFERENCE */}

          {this.state.popup !== '' && this.state.popup === overlays.IMAGE_OVERLAY && (
            <GridGallery onBackPressed={() => this.togglePopup(overlays.NONE)} />
          )}
          <DownloadApp />
          <FlatList
            data={flatListData}
            renderItem={this.renderItem}
            keyExtractor={(item) => item.id}
            ref={(ref) => (this.flatListRef = ref)}
            onScroll={this.onScroll}
            onScrollToIndexFailed={() => {}}
            stickyHeaderIndices={[6]}
            onScrollBeginDrag={this.onScrollDragBegin}
            onViewableItemsChanged={this.onViewableItemsChanged}
            viewabilityConfig={{
              viewAreaCoveragePercentThreshold: isMobileClient() ? 10 : 100,
              waitForInteraction: true,
            }}
            style={{ width: '100%' }}
          />
          {this.state.showMmtBlackBottomsheet && this.props.mmtBlackDetail?.bottomSheet && (
            <BottomSheet
              onBackPressed={this.handleMmtBlackTogglePopup}
              containerStyle={{ padding: 0 }}
            >
              <MMTBlackBottomSheet
                togglePopup={this.handleMmtBlackTogglePopup}
                ctaButtonClick={this.handleMmtBlackCtaButtonClick}
                bottomSheetDetail={this.props.mmtBlackDetail?.bottomSheet}
                mmtBlackPdtEvents={logPhoenixDetailPDTEvents}
                trackClickEvent={this.trackMmtBlackClickEvent}
                handleTermConditionClick={this.handleTermConditionClick}
              />
            </BottomSheet>
          )}
          {this.state.openFDFeatureEditVisibility &&
          detailData.packageDetail?.packageFeatures?.length > 0 ? (
            <FDFeatureEditOverlayV2
              packageFeatures={getFilteredPackageFeatures(detailData.packageDetail.packageFeatures)}
              toggleFDFeatureBottomSheet={this.toggleFDFeatureBottomSheet}
              isOverlay={true}
              selectedMealCode={detailData.packageDetail.mealDetail?.meal.mealCode}
              updateMeal={this.updateMeal}
              updateVisa={this.updateVisa}
              dynamicPackageId={this.packageDetailDTO.dynamicPackageId}
              activeIndex={this.state.activeFeature}
            />
          ) : null}
          {this.state.isUndoVisible && this.state.undoStack && this.state.undoStack.length > 0 && (
            <CustomizationPopup
              popFromStack={this.popFromStack}
              undoStack={this.state.undoStack}
              toggleUndoBottomSheet={this.toggleUndoBottomSheet}
            />
          )}

          {this.state.showSelectCityPopUp && (
            <SelectCityPopUp
              hideSelectCityPop={this.hideSelectCityPop}
              onCitySelectFromPopup={this.onCitySelectedFromPopUp}
              trackClickEvent={this.trackLocalClickEvent}
              data={this.state.selectCityPopupData}
            />
          )}

          <FloatingWidget
            handleClick={this.openCustomizationPopup}
            count={this.state.undoStack.length}
            addDynamicCuesStep={this.addDynamicCuesStep}
          />

          <PackageUpdatedToast
            showPackageUpdatedToast={this.state.showPackageUpdatedToast}
            setToastMessageState={this.setPackageUpdatedToastState}
          />

          {!this.state.showCoachMarks && !this.state.showDynamicCoachMarks && (
            <BlackFooter
              togglePopup={this.togglePopup}
              packageReview={this.packageReview}
              categoryPrice={this.newPackageDetail.pricingDetail.categoryPrices[0]}
              dealDetail={this.newPackageDetail.dealDetail}
              travellerCount={travellerCount}
              loggedIn={loggedIn}
              aff={this.holidayDetailData.aff}
              disableBookNowButton={disableBookNowButton}
              trackLocalClickEvent={this.trackLocalClickEvent}
            />
          )}

          {this.state.popup === overlays.OFFER_OVERLAY && (
            <OfferOverlayNew
              togglePopup={this.togglePopup}
              dealDetail={this.newPackageDetail.dealDetail}
              loggedIn={loggedIn}
              onLoginClicked={this.onLoginClicked}
              travellerCount={travellerCount}
              categoryPrice={this.newPackageDetail.pricingDetail.categoryPrices[0]}
              aff={this.holidayDetailData.aff}
              applyOfferSection={(offerCode, action) =>
                this.props.applyOfferSection(
                  action,
                  offerCode,
                  this.props.detailData?.packageDetail?.dynamicId,
                )
              }
              offerSection={this.props.offerSection}
              showOfferHorizontalLoader={this.props.showOfferHorizontalLoader}
              emiDetails={this.newPackageDetail?.emiDetail}
              getEmiOptions={() =>
                this.props.getEmiOptions(this.props.detailData?.packageDetail?.dynamicId)
              }
              emiOptions={this.props.emiOptions}
              trackLocalClickEvent={this.trackLocalClickEvent}
              persuasion={this.getPersuasionV2(persuasionData, persuasions.OFFER_SECTION)}
            />
          )}

          {this.state.popup === overlays.EDIT_OVERLAY && (
            <EditOverlay
              togglePopup={this.togglePopup}
              pageName={this.pageName()}
              departureDate={
                this.newPackageDetail?.metadataDetail?.pkgDate
                  ? this.newPackageDetail.metadataDetail.pkgDate
                  : this.newPackageDetail.departureDetail.departureDate
              }
              departureCity={this.newPackageDetail.departureDetail.cityName}
              destinationCity={this.fetchTagDestAndBranch().tagDestinationName}
              flightCount={calculateFlightsCount(this.newPackageDetail.flightDetail)}
              packageId={this.newPackageDetail.id ? this.newPackageDetail.id : -1}
              categoryId={this.holidayDetailData.categoryId}
              cityId={this.newPackageDetail.departureDetail.cityId}
              packagePaxDetail={this.newPackageDetail?.packageConfigDetail?.packagePaxDetail}
              roomDetails={this.getRoomDetails()}
              childAgeArray={createChildAgeArrayFromApi(this.getRoomDetails())}
              refreshFetchContent={this.refreshFetchContent}
              onBackPressed={this.onBackPressed}
              isWG={isWG}
              allowChangeHub={this.state.allowChangeHub}
              trackLocalClickEvent={this.trackLocalClickEvent}
            />
          )}

          {((!this.state.showCoachMarks && !this.state.showDynamicCoachMarks) ||
            this.state.showingQueryCoachMark) &&
            fabCta &&
            fabCta.showFab && (
                          <HolidayFabAnimationContainer
              ref={(ref) => {
                if (this.props.registerFabAnimationRef) {
                  this.props.registerFabAnimationRef(ref);
                }
              }}textShrinked={this.state.fabTextShrinked && !showFabAnimationExtended()}
              pageName={DETAIL_QUERY_PAGE_NAME}
          containerBottomValue={90}
              configId={HLD_PAGE_NAME.DETAILS}
              containerStyle={{ bottom: this.state?.undoStack?.length ? FAB_BOTTOM_WITH_UNDO : FAB_BOTTOM_DEFAULT }}
              fabCta={fabCta}
              invalidateChatViewData={this.state.forceHitTravelplex }
              onTravelPlexDataUsed={this.resetForceHitTravelplex} // Pass callback to reset flag after TravelPlex uses it
              fabData={createDetailFabData({
                detailsData: this.props.detailData,
                fetchTagDestAndBranchData: this.fetchTagDestAndBranch(),
              })}
              setLocatorState={this.setState()}
              unmountIntervention={this.props.unmountIntervention}
              trackLocalClickEvent={this.trackLocalChatClickEvent}
              trackPageExit={this.trackPageExit}
              trackPDTV3Event={logPhoenixDetailPDTEvents}
              travelPlexConfigData={{...getDetailPDTObjWithCorrectPax(this.getRoomDetails()),
                 dynamicPackageId: this.packageDetailDTO.dynamicPackageId,
                 to_date_time: calculateToDateTime(
                   this.props.detailData?.packageDetail?.departureDetail?.departureDate,
                   this.props.detailData?.packageDetail?.destinationDetail?.duration
                 ),
                 product: [{
                   id: this.packageDetailDTO.dynamicPackageId,
                   name: this.props.detailData.packageDetail.name,
                 }],
                 attr1:this.fetchTagDestAndBranch()?.branchName,
                 attr2: this.props.detailData.packageDetail.tagDestination.name,
                 attr4: this.travelPlexAttr4?.allocationDetail?.queueIdentifier,
                 hideInput: !showHolAgentOnDetailAndReviewPage(),
               }}
            />
            )}
          {this.state.popup === extraInfoRefs.TNC ? (
            <ExtraInfoOverlay
              tnc={this.newPackageDetail?.additionalDetail?.tnc}
              exclusions={this.newPackageDetail.additionalDetail.exclusions}
              togglePopup={this.togglePopup}
              sectionToShow={this.state.sectionToShow}
            />
          ) : (
            []
          )}
          {this.state.popup === extraInfoRefs.CANCELLATION_POLICY &&
            this.props?.cancellationPolicyData?.success && (
              <HolidayCancellationOverlayV2
                dynamicId={this.packageDetailDTO.dynamicPackageId}
                togglePopup={this.togglePopup}
                isPerPerson={true}
                travellerCount={travellerCount}
                trackClickEvent={(event, suffix) => this.trackLocalClickEvent(event, suffix)}
                cancellationPolicyData={this.props.cancellationPolicyData}
                pageName={DETAIL_TRACKING_PAGE_NAME}
                activeTab={0}
              />
            )}
          {this.state.popup === overlays.PRICE_OVERLAY && (
            <PricePersuasionOverlay
              togglePopup={this.togglePopup}
              persuasionData={persuasionData}
              departureDate={departureDate}
              updateDepDate={this.updateDepDate}
            />
          )}
          {/* {this.state.popup === overlays.VISA_OVERLAY && !isEmpty(this.props.packageContent) &&
        <VisaContainer
          togglePopup={this.togglePopup}
          packageContent={this.props.packageContent}
          tourManagerExist={isTourManagerExist(this.newPackageDetail.tourManagerDetail)}
        />
        } */}
        <DetailOverlays />
          {this.state.showCoachMarks && this.renderCoachMarks()}
          {this.state.showDynamicCoachMarks && this.renderDynamicCoachMarks()}
          {!this.isLoading &&
            !this.state.instaRunning &&
            this.showReviewErrorPopup(this.state.reviewError, this.newPackageDetail)}
        </View>
      </KeyboardAvoidingView>
    );
  };

  renderDynamicCoachMarks = () => {
    if (this.dynamicCuesSteps && this.dynamicCuesSteps.length > 0) {
      const CoachMarks = require('@mmt/legacy-commons/Common/Components/CoachMarks').default;
      const validCuesSteps = getValidCuesSteps(this.dynamicCuesSteps);
      if (validCuesSteps.length === 0) {
        return null;
      }
      return (
        <CoachMarks
          steps={validCuesSteps}
          onDone={() => this.hideCoachMarksOverlay(true)}
          onSkip={() => this.hideCoachMarksOverlay(true)}
          onStart={(step) =>
            setTimeout(() => {
              this.handleScrollForCoachMarks(step);
            }, 250)
          }
          onStepChange={(step) => this.handleScrollForCoachMarks(step)}
          pageName={HLD_CUES_POKUS_KEYS.DETAIL}
          trackEvent={this.handlePDT}
        />
      );
    }
    return null;
  };
  renderCoachMarks = () => {
    if (this.finalCuesSteps && this.finalCuesSteps.length) {
      const CoachMarks = require('@mmt/legacy-commons/Common/Components/CoachMarks').default;
      return (
        <CoachMarks
          steps={this.finalCuesSteps}
          onDone={this.hideCoachMarksOverlay}
          onSkip={this.hideCoachMarksOverlay}
          onStart={(step) =>
            setTimeout(() => {
              this.handleScrollForCoachMarks(step);
            }, 250)
          }
          onStepChange={(step) => this.handleScrollForCoachMarks(step)}
          pageName={HLD_CUES_POKUS_KEYS.DETAIL}
          trackEvent={this.handlePDT}
        />
      );
    }
    return null;
  };
  onLoginClicked = () => {
    const { HolidayModule } = NativeModules;
    if (isMobileClient()) {
      HolidayModule.onLoginUserDetail();
    } else {
      HolidayModule.onLoginUserDetail(getReturnUrl(this.packageDetailDTO.dynamicPackageId));
    }
    this.trackLocalClickEvent(PDTConstants.LOGIN, '');
  };

  onLoginEventReceived = (response) => {
    if (response && response.loggedIn) {
      this.refreshDetails(false, false, null, false);
    }
  };
  onSharePress = () => {
    this.captureClickEvents({ eventName: 'share', value: 'Click_sharewhatsapp' ,sendGIData: isRawClient()});
    sharePackage(this.props.detailData.packageDetail);
  };

  isShortListedPackage = () => {
    const { detailData } = this.props;
    const { packageDetail } = detailData || {};
    let isShortListed = false;
    if (packageDetail && this.props.shortListedPackages) {
      isShortListed = this.props.shortListedPackages.has(packageDetail?.id);
    }
    return isShortListed;
  };

  updateShortListedPackage = (isShortList) => {
    this.trackLocalClickEvent('shortlist', '');
    const { detailData } = this.props;
    const { packageDetail } = detailData || {};
    const { id, name } = packageDetail || {};
    if (!this.props.shortListedPackages) {
      this.props.shortListedPackages = new Set();
    }
    if (isShortList) {
      this.props.shortListedPackages.add(id);
    } else {
      this.props.shortListedPackages.delete(id);
    }
    this.props.updateShortListedPackage(id, name, isShortList);
  };

  getViewStoryEnabled = (packageDetail) => {
    if (packageDetail) {
      const { metadataDetail } = packageDetail || {};
      const { enableStory } = metadataDetail || {};
      return !!enableStory;
    }
    return false;
  };

  renderProgressView = () => {
    let openingSavedPackage = false;
    if (this.holidayDetailData.savePackageId && !this.packageDetailDTO.dynamicPackageId) {
      openingSavedPackage = true;
    }
    return (
      <HolidayDetailLoader
        departureCity={this.holidayDetailData?.departureDetail?.departureCity}
        departureDate={this.holidayDetailData?.departureDetail?.departureDate}
        duration={this.holidayDetailData?.destinationDetail?.duration}
        travellerObj={createTravellerObjForLoader(this.roomDetails)}
        openingSavedPackage={openingSavedPackage}
        showDateText={this.props.changingDate}
        changeAction={this.props.changeAction}
        loadingText={this.props.loadingText}
      />
    );
  };

  renderError = () => {
    const errorMessage = fetchErrorMessage(this.props.error);
    return (
      <HolidayDetailError
        fabCta={this.props.fabCta}
        startCall={this.startNoPkCall}
        startQuery={this.startNoPkQuery}
        startChat={this.startNoPkChat}
        duration={this.holidayDetailData.destinationDetail.duration}
        onBackPressed={this.onBackPressed}
        packageName={this.holidayDetailData.name}
        errorMessage={errorMessage}
        isWG={this.holidayDetailData.isWG}
        aff={this.holidayDetailData.aff}
      />
    );
  };

  BasicDetails = () => {
    const {  detailData, showOverlay  } = this.props;
    const { packageDetail } = detailData || {};
    const { imageDetail, genreDetail, packageConfigDetail, metadataDetail } = packageDetail || {};
    const { bundled, safe } = metadataDetail || {};
    const { packageType } = packageConfigDetail || {};
    const isShortListed = this.isShortListedPackage();
    const videoUrl = getVideoUrl(packageDetail);
    const showStory = this.getViewStoryEnabled(packageDetail);
    let isSafe = safe ? true : false;
    const isWG = this.holidayDetailData.pt === WEEKEND_GETAWAY_PAGE_TYPE;
    const { components: messages, count } = this.getMessageStrip(metadataDetail);
    const pkgMessage = metadataDetail?.pkgMessage || '';
    return (
      <View style={{ borderBottomColor: holidayColors.grayBorder, borderBottomWidth: 1 }}>
        <LightHeader
          onBackPress={this.onBackPressed}
          onSharePress={this.onSharePress}
          onFavPress={this.updateShortListedPackage}
          isShortListed={isShortListed}
        />
        {!this.showNewGallery && (
          <PackageHighlights
            togglePopup={this.togglePopup}
            imageDetail={imageDetail}
            videoUrl={videoUrl}
            videoPaused={this.state.videoPaused}
            trackLocalClickEvent={this.trackLocalClickEvent}
          />
        )}
        <BasicDetailsSection
          detailData={detailData}
          dynamicPackageId={this.packageDetailDTO.dynamicPackageId}
          updatePackage={this.updatePackageWithV2}
          showOverlay={showOverlay}
        />
        <ModifySearchContainer
          editTravelDetails={this.editTravelDetails}
          roomDetails={this.getRoomDetails()}
          showNewGallery={this.showNewGallery}
          count={count}
        />
        {count > 0 && messages}
        <ValidationSummary
          validationSummary={metadataDetail?.validationSummary}
          containerStyles={styles.messageStripContainer}
        />
      </View>
    );
  };

  getMessageStrip = (metadataDetail) => {
    const components = [];
    let count = 0;
    const { PKG_MESSAGE, DFD_CHILD_BED, PACKAGE_DATES_UPDATED, STARTING_CITY } =
      MESSAGE_TYPES || {};
    const { pkgMessage, pkgMessageMap } = metadataDetail || {};
    const {
      DFD_CHILD_BED: dfdChildBed,
      PACKAGE_DATES_UPDATED: packageDatesUpdate,
      STARTING_CITY: startingCityMessage,
    } = pkgMessageMap || {};
    const showCrossIcon = true; // (!isEmpty(pkgMessage) && (!isEmpty(dfdChildBed) || !isEmpty(packageDatesUpdate)) ||  !isEmpty(dfdChildBed) && !isEmpty(packageDatesUpdate))
    const trackEvent = (type) => {
      this.captureClickEvents({
        eventName: `Message_shown_${type}`,
        actionType: PDT_EVENT_TYPES.contentSeen,
      });
    };

    if (!isEmpty(pkgMessage)) {
      if (!this.closeWarningMessages[PKG_MESSAGE]) {
        this.closeWarningMessages[PKG_MESSAGE] = false;
        count = count + 1;
      }
      components.push(
        <HolidaysMessageStrip
          shouldShow={!this.closeWarningMessages[PKG_MESSAGE]}
          message={pkgMessage}
          containerStyles={styles.messageStripContainer}
          icon={AlertIcon}
          showCross={showCrossIcon}
          trackEvent={() => {
            trackEvent(PKG_MESSAGE);
          }}
          onClose={() => this.onClose(PKG_MESSAGE)}
        />,
      );
    }

    if (!isEmpty(dfdChildBed)) {
      if (!this.closeWarningMessages[DFD_CHILD_BED]) {
        this.closeWarningMessages[DFD_CHILD_BED] = false;
        count = count + 1;
      }
      components.push(
        <HolidaysMessageStrip
          shouldShow={!this.closeWarningMessages[DFD_CHILD_BED]}
          message={dfdChildBed}
          containerStyles={styles.messageStripContainer}
          icon={AlertIcon}
          showCross={showCrossIcon}
          trackEvent={() => {
            trackEvent(DFD_CHILD_BED);
          }}
          onClose={() => this.onClose(DFD_CHILD_BED)}
        />,
      );
    }

    if (!isEmpty(packageDatesUpdate)) {
      if (!this.closeWarningMessages[PACKAGE_DATES_UPDATED]) {
        this.closeWarningMessages[PACKAGE_DATES_UPDATED] = false;
        count = count + 1;
      }
      components.push(
        <HolidaysMessageStrip
          shouldShow={!this.closeWarningMessages[PACKAGE_DATES_UPDATED]}
          message={packageDatesUpdate}
          containerStyles={styles.messageStripContainer}
          icon={AlertIcon}
          showCross={showCrossIcon}
          trackEvent={() => {
            trackEvent(PACKAGE_DATES_UPDATED);
          }}
          onClose={() => this.onClose(PACKAGE_DATES_UPDATED)}
        />,
      );
    }
    if (!isEmpty(startingCityMessage)) {
      if (!this.closeWarningMessages[STARTING_CITY]) {
        this.closeWarningMessages[STARTING_CITY] = false;
        count = count + 1;
      }
      components.push(
        <HolidaysMessageStrip
          shouldShow={!this.closeWarningMessages[STARTING_CITY]}
          message={startingCityMessage}
          containerStyles={styles.messageStripContainer}
          icon={AlertIcon}
          showCross={showCrossIcon}
          trackEvent={() => {
            trackEvent(STARTING_CITY);
          }}
          onClose={() => this.onClose(STARTING_CITY)}
        />,
      );
    }

    return { components, count };
  };

  onClose = (type) => {
    this.isShowWarningMessage = false;
    this.closeWarningMessages[type] = true;
    this.captureClickEvents({ eventName: `Message_close_${type}` });
    this.setState({ isShowWarningMessage: false });
  };

  onSimilarPackageClicked = (packageDetails, storyImageSize, index) => {
    const holidaysDetailData = createHolidayDetailData(
      packageDetails,
      this.holidayDetailData,
      this.getRoomDetails(),
      storyImageSize,
      this.holidayDetailData.departureDetail.departureCity,
    );
    if (isRawClient()) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL, {
        holidaysDetailData,
      });
    } else if (this.replaced) {
      HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.DETAIL, {
        replaced: false,
        [deepLinkParams.deepLink]: false,
        deepLinkParams: null,
        holidaysDetailData,
        deepLinkSimilar: this.deepLinkSimilar,
      });
    } else {
      HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.DETAIL, {
        replaced: true,
        [deepLinkParams.deepLink]: false,
        deepLinkParams: null,
        holidaysDetailData,
        deepLinkSimilar: this.deepLinkSimilar,
      });
    }
    this.trackLocalClickEvent(
      PDTConstants.SIMILAR_PACKAGE,
      `_${index}_${packageDetails.packageId}`,
    );
  };

  toggleFDFeatureBottomSheet = (visibility, index) => {
    if (!this.showNewContentUI) {
      this.trackLocalClickEvent('edit_fd', '');
    }
    this.setState({
      openFDFeatureEditVisibility: visibility,
      activeFeature: index,
    });
  };

  toggleUndoBottomSheet = (visibility) => {
    this.setState({
      isUndoVisible: visibility,
    });
  };

  async initAb() {
    await initAbConfig();
    this.cusCountLimit = getMaxUndoAllowed();
    this.showNewContentUI = getPokusForNewDetailContent();
    this.cuesConfig = getCuesConfig();
    const showStoryMob = getHolShowStoryMob();
    if (this.props[deepLinkParams.deepLink]) {
      await setTimeout(() => {
        if (showStoryMob) {
          this.setState({
            showStoryAB: true,
          });
        }
      }, 500);
    } else {
      if (showStoryMob) {
        this.setState({
          showStoryAB: true,
        });
      }
    }
  }

  addDynamicCuesStep = async ({ cueStepKey }) => {
    const localSteps = require('./HolidayDetailPageCoachMarks.json');
    const cuesSteps = await createCuesSteps({
      pokusSteps: this.cuesConfig[HLD_CUES_POKUS_KEYS.DETAIL],
      localSteps,
      isDynamic: true,
      pageName: HLD_CUES_POKUS_KEYS.DETAIL,
    });
    this.finalDynamicCuesStep =
      cuesSteps.filter((cueStep) => cueStep.key === cueStepKey)?.[0] || null;

    if (this.finalDynamicCuesStep) {
      this.dynamicCuesSteps.push(this.finalDynamicCuesStep);
    }
    // Check if there are more one cue steps to be shown if yes then add delay to show coach mark
    if (this.dynamicCuesSteps.length > 0) {
      clearTimeout(this.dynamicCoachMarkTimeOut);
      this.dynamicCoachMarkTimeOut = setTimeout(this.showDynamicCoachMarksOverlay, 200);
    } else {
      this.dynamicCoachMarkTimeOut = setTimeout(this.showDynamicCoachMarksOverlay, 200);
    }
  };

  showDynamicCoachMarksOverlay = () => {
    this.setState({
      showDynamicCoachMarks: this.dynamicCuesSteps && this.dynamicCuesSteps.length > 0,
    });
  };

  showCoachMarksOverlay = async (response) => {
    const localSteps = require('./HolidayDetailPageCoachMarks.json');
    this.finalCuesSteps = await createCuesSteps({
      pokusSteps: this.cuesConfig[HLD_CUES_POKUS_KEYS.DETAIL],
      localSteps,
      pageName: HLD_CUES_POKUS_KEYS.DETAIL,
    });
    this.finalCuesSteps = getValidCuesSteps(this.finalCuesSteps);
    this.finalCuesSteps = updatedCuesSteps({
      updatedCuesSteps: {
        changeOrRemove: { shape: { top: isRawClient() ? getChangeOrRemoveTopValue({steps: this.finalCuesSteps}) : 125, left: '-2%' } },
        ...(this.showNewGallery && {
          editSearch: {
            arrow: { top: '55%' },
            label: { top: '60%' },
          },
        }),
        ...(this.enableSummaryTabRemoveAccordion && {
          summaryTab: {
            shape: { top: -50, left: getHolShowSummaryTabFirst() ? 10 : 55, bottom: null },
            arrow: { type: 'up-left', bottom: null, top: 150, left: 100 },
            label: {top: isRawClient() ? 200 : 0}
            },
        }),
        ...(!this.enableSummaryTabRemoveAccordion && {
          summaryTab: {
            shape: {
              bottom: this.props.detailData?.packageDetail?.packageFeatures?.length > 0 ? 200 : 300,
            },
          },
        }),
      },
      steps: this.finalCuesSteps,
    });

    //filter cues according to response data fetched discuss it with Naina
    this.setState({ showCoachMarks: this.finalCuesSteps && this.finalCuesSteps.length > 0 });
  };

  hideCoachMarksOverlay = (isDynamic = false) => {
    this.setState(
      {
        ...(isDynamic ? { showDynamicCoachMarks: false } : { showCoachMarks: false }),
        currentActivePlanOnItinerary: TABS.DAY,
        showingQueryCoachMark: false,
        isItineraryVisible: true,
      },
      () => {
        setTimeout(() => {
          if (this.flatListRef) {
            this.flatListRef.scrollToIndex({ animated: true, index: 0 });
          }
        }, 500);
        if (isDynamic) {
          this.dynamicCuesSteps = [];
          clearTimeout(this.dynamicCoachMarkTimeOut);
        }
      },
    );
  };
  handleScrollForCoachMarks = (step) => {
    const { persuasionData } = this.props;
    this.setState({ isItineraryVisible: true });
    if (step.key === 'imageGallery') {
      this.setState({ showingQueryCoachMark: false }, () => {
        this.flatListRef.scrollToIndex({ animated: true, index: 0 });
      });
    } else if (step.key === 'summaryTab') {
      this.setState({ showingQueryCoachMark: false, isItineraryVisible: false }, () => {
        const viewOffset = -125;
        const timeOutValue = 1000;
        this.scrollToBottom(viewOffset, timeOutValue);
      });
    } else if (step.key === 'summaryTab' && this.enableSummaryTabRemoveAccordion) {
      this.setState({ showingQueryCoachMark: false }, () => {
        if (isRawClient()) {
          if(this.props.detailData.packageDetail.packageFeatures){
            this.flatListRef.scrollToIndex({ animated: true, index: 4 })
          } else {
            this.scrollToBottom();
          }
        } else {
          this.flatListRef.scrollToIndex({ animated: true, index: 6 });
        }
      });
    } else if (step.key === 'query') {
      this.setState({ showingQueryCoachMark: true }, () => {
        this.flatListRef.scrollToIndex({ animated: true, index: 1 });
      });
    } else if (step.key === 'editSearch') {
      this.setState({ showingQueryCoachMark: false }, () => {
        this.flatListRef.scrollToIndex({ animated: true, index: 0 });
      });
    } else if (step.key === 'changeOrRemove') {
      this.setState({ showingQueryCoachMark: false }, () => {
        const extraHeight = this.getTopPersuasionHeight(persuasionData, persuasions.TOP_DETAIL);
        this.flatListRef.scrollToIndex({
          animated: true,
          index: 0,
          viewOffset: -(
              step.extraInfo?.fy
              + extraHeight
              - (isRawClient() ? 220 : 170 - statusBarHeightForIphone)
          ),
        });
      });
    } else if (step.key === 'customizationWidget') {
      this.setState({ showingQueryCoachMark: false }, () => {
        this.flatListRef.scrollToIndex({ animated: true, index: 0 });
      });
    }
  };

  getTopPersuasionHeight = (persuasionData, index) => {
    const { cancellationPolicyData } = this.props;
    const indexItem = persuasionData?.findIndex((el) => el?.data?.order == index);
    if (indexItem > -1 && persuasionData && Array.isArray(persuasionData)) {
      return 150;
    } else if (
      cancellationPolicyData?.success &&
      cancellationPolicyData?.penaltyDetail?.zcOptions &&
      (isZCAvailable(cancellationPolicyData.penaltyDetail.zcOptions) ||
        isFlexiDateAvailable(cancellationPolicyData.penaltyDetail.zcOptions))
    ) {
      return 50;
    } else {
      return 0;
    }
  };

  // getFDFeatureList = (detailData) => {
  //   if (detailData.packageDetail.packageFeatures) {
  //     return (
  //       <FeatureList
  //         packageFeatures={detailData.packageDetail.packageFeatures}
  //         isOverlay={false}
  //         toggleFDFeatureBottomSheet={(show, index) => this.toggleFDFeatureBottomSheet(show, index)}
  //       />
  //     );
  //   }
  //   return [];
  // };

  setPackageUpdatedToastState = (status) => {
    this.isShowWarningMessage = true;
    this.setState({
      showPackageUpdatedToast: status,
      isShowWarningMessage: true,
    });
  };

  trackPageExit = () => {
     // This will handle omniture and data to old pdt
    trackHolidaysDetailLoadEvent({
      logOmni: false,
      pdtData: {
        pageDataMap: this.props.detailData?.pageDataMap || {},
        interventionDetails: this.props?.fabCta?.interventionLoggingDetails || {},
        eventType: PDT_RAW_EVENT,
        activity: PDT_PAGE_ENTRY_EVENT,
        requestID: createRandomString(),
        branch: this.props.detailData?.branch || DOM_BRANCH,
      },
    });
    const couponListData= this.props?.offerSection?.couponList || [];
    // Implement the new PDT
    const { pricingData } = extractDataForPDTFromDetailResponse(this.props.detailData, couponListData, this.holidayDetailData);

    // Log page exit event with pricing data
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.pageExit,
      value: PDT_PAGE_EXIT_EVENT,
      pricingData, // Include pricing data for page exit event
    });
  };

  fabHandle = () => {
    const { fabCta } = this.props;
    if (fabCta.showQuery && !fabCta.showCall && !fabCta.showChat) {
      this.startQuery(true);
    } else if (!fabCta.showQuery && fabCta.showCall && !fabCta.showChat) {
      this.startCall(true);
    } else if (!fabCta.showQuery && !fabCta.showCall && fabCta.showChat) {
      this.startChat(true);
    } else {
      this.handleDefaultFabClick();
    }
  };

  startCall = (fromIcon) => {
    if (!fromIcon) {
      this.setState({ fab: !this.state.fab });
    }
    const { branchName } = this.fetchTagDestAndBranch();
    doCall(branchName);
    this.handleDefaultFabClick();
    const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
    this.trackLocalClickEvent(eventName, PDTConstants.CALL_SUFFIX, {sendGIData: isRawClient()});
    this.trackPageExit();
    this.setState({ videoPaused: true });
  };

  getFabData = (fromIcon) => {
    const { tagDestinationName, branchName, packageId, packageName, pkgType, dynamicPackageId } =
      this.fetchTagDestAndBranch();
    const {
      trackingData = {},
      aff = '',
      isWG = '',
      cmp = '',
      source = '',
      initId = '',
    } = this.holidayDetailData || {};
    const fabData = {
      fromIcon,
      destinationCity: tagDestinationName,
      branch: branchName,
      packageId,
      packageName,
      pkgType,
      dynamicPackageId,
      pageName: DETAIL_QUERY_PAGE_NAME,
      cmp: cmp === '' ? initId : cmp,
      aff,
      trackingData,
      source,
      isWG,
    };
    return fabData;
  };

  startQuery = (fromIcon) => {
    if (!fromIcon) {
      this.setState({ fab: !this.state.fab });
    }
    const { tagDestinationName, branchName, packageId, packageName, pkgType, dynamicPackageId } =
      this.fetchTagDestAndBranch();
    const { trackingData = {} } = this.holidayDetailData || {};
    if (this.holidayDetailData.fromSeo) {
      openSeoQueryDeepLink(tagDestinationName, branchName);
    } else {
      const queryDto = {};
      queryDto.destinationCity = tagDestinationName;
      queryDto.branch = branchName;
      queryDto.packageId = packageId;
      queryDto.packageName = packageName;
      queryDto.pageName = DETAIL_QUERY_PAGE_NAME;
      queryDto.omniPageName = DETAIL_QUERY_PAGE_NAME;
      queryDto.funnelStep = DETAIL_QUERY_PAGE_NAME;
      queryDto.pkgType = pkgType;
      queryDto.dynamicPackageId = dynamicPackageId;
      queryDto.isWG = this.holidayDetailData.isWG;
      queryDto.aff = this.holidayDetailData.aff;
      queryDto.cmp = this.holidayDetailData.cmp ? this.holidayDetailData.cmp : '';
      queryDto.source = this.holidayDetailData.source || '';
      queryDto.trackingData = trackingData || {};
      if (queryDto.cmp === '') {
        queryDto.cmp = this.holidayDetailData.initId ? this.holidayDetailData.initId : '';
      }
      if (this.props.fabCta.formId) {
        queryDto.formId = this.props.fabCta.formId;
      }
      doQuery(queryDto);
    }
    const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
    const querySuffix = PDTConstants.QUERY_SUFFIX;
    const formQuerySuffix = this.props?.fabCta?.formId ? `form_no:${this.props.fabCta.formId}` : '';
    this.trackLocalClickEvent(eventName, querySuffix, { prop1: formQuerySuffix,prop66: formQuerySuffix, sendGIData: isRawClient() });
    this.trackPageExit();
    this.setState({ videoPaused: true });
  };

  showLocator = (fromIcon) => {
    this.setState({
      popup: overlays.BRANCH_OVERLAY,
      openHalf: false,
      videoPaused: true,
    });
    const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
    this.trackLocalClickEvent(eventName, PDTConstants.BRANCH_LOCATOR_SUFFIX);
    this.trackPageExit();
  };

  startChat = (fromIcon) => {
    this.props.unmountIntervention();
    if (!fromIcon) {
      this.setState({ fab: !this.state.fab });
    }
    const { tagDestinationName, branchName } = this.fetchTagDestAndBranch();
    const { detailData = {} } = this.props || {};
    const { cmp = '', initId = '', packageDetail = {}, pageDataMap = {} } = detailData || {};
    const { id = '', name = '', dynamicId = '' } = packageDetail || {};
    let cmpValue = cmp ? cmp : '';
    if (cmpValue === '') {
      cmpValue = initId ? initId : '';
    }

    const chatIdentifier = createChatID();
    const { trackingData = {}, source } = this.holidayDetailData || {};
    const { categoryTrackingEvent = {} } = trackingData || {};
    const chatDto = {
      destinationCity: tagDestinationName,
      branch: branchName,
      travelDate: pageDataMap.otherDetails.travel_start_date,
      packageId: `${id}`,
      packageName: name,
      dynamicPackageId: dynamicId,
      paxConfig: getPaxConfig(pageDataMap),
      cmp: cmpValue,
      chatId: chatIdentifier,
      pageName: DETAIL_QUERY_PAGE_NAME,
      categoryTrackingEvent,
      eventData: {
        [TRACKING_EVENTS.M_V108]: getEvar108ForFunnel({
          source,
          trackingPageName: DETAIL_QUERY_PAGE_NAME,
        }),
      },
    };
    startReactChat(chatDto);
    const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
    this.trackLocalClickEvent(eventName, PDTConstants.CHAT_SUFFIX, { prop66: chatIdentifier });

    this.trackPageExit();
    this.setState({ videoPaused: true });
  };

  startNoPkCall = () => {
    const { branchName } = this.fetchTagDestAndBranch();
    const errorMessage = this.props?.error?.code
      ? `${this.props.error.code}:${fetchErrorMessage(this.props.error)}`
      : '';
    doCall(branchName);
    this.trackErrorLocalClickEvent({
      eventName: PDTConstants.CONTACT_ICON,
      suffix: PDTConstants.CALL_SUFFIX,
      evar22: errorMessage,
    });
  };

  startNoPkQuery = () => {
    const { tagDestinationName, branchName } = this.fetchTagDestAndBranch();
    const errorMessage = this.props?.error?.code
      ? `${this.props.error.code}:${fetchErrorMessage(this.props.error)}`
      : '';
    if (this.holidayDetailData.fromSeo) {
      openSeoQueryDeepLink(tagDestinationName, branchName);
    } else {
      const queryDto = {};
      queryDto.destinationCity = tagDestinationName;
      queryDto.branch = branchName;
      queryDto.pageName = 'details:error';
      queryDto.funnelStep = 'details_nopk';
      queryDto.omniPageName = DETAIL_QUERY_PAGE_NAME;
      queryDto.isWG = this.holidayDetailData.isWG;
      queryDto.aff = this.holidayDetailData.aff;
      queryDto.errorMessage = errorMessage;
      if (this.props.fabCta.formId) {
        queryDto.formId = this.props.fabCta.formId;
      }
      doQuery(queryDto, true);
    }
    this.trackErrorLocalClickEvent({
      eventName: PDTConstants.CONTACT_ICON,
      suffix: PDTConstants.QUERY_SUFFIX,
      evar22: errorMessage,
    });
  };

  startNoPkChat = () => {
    const { tagDestinationName, branchName } = this.fetchTagDestAndBranch();
    const errorMessage = this.props?.error?.code
      ? `${this.props.error.code}:${fetchErrorMessage(this.props.error)}`
      : '';
    const { trackingData = {}, source } = this.holidayDetailData || {};
    const { categoryTrackingEvent = {} } = trackingData || {};
    const chatDto = {
      destinationCity: tagDestinationName,
      branch: branchName,
      pageName: 'details:error',
      categoryTrackingEvent,
      eventData: {
        [TRACKING_EVENTS.M_V108]: getEvar108ForFunnel({
          source,
          trackingPageName: DETAIL_QUERY_PAGE_NAME,
        }),
      },
    };
    startReactChat(chatDto);
    this.trackErrorLocalClickEvent({
      eventName: PDTConstants.CONTACT_ICON,
      suffix: PDTConstants.CHAT_SUFFIX,
      evar22: errorMessage,
    });
  };

  handleDefaultFabClick = () => {
    this.setState({ fab: !this.state.fab });
    const totalCtasToBeShown =
      (this.props.fabCta.showCall ? 1 : 0) +
      (this.props.fabCta.showQuery ? 1 : 0) +
      (this.props.fabCta.showChat ? 1 : 0);
    if (!this.state.fab && totalCtasToBeShown > 1) {
      this.trackLocalClickEvent(
        'fab',
        `_${this.props.fabCta.showCall ? 'C' : ''}${this.props.fabCta.showQuery ? 'Q' : ''}${
          this.props.fabCta.showChat ? 'Ch' : ''
        }${this.props.fabCta.branchLocator ? 'B' : ''}`,
        { sendGIData: isRawClient() },
      );
    }
  };

  fetchTagDestAndBranch() {
    let tagDestinationName = '';
    let branchName = DOM_BRANCH;
    let packageId = '';
    let packageName = '';
    let pkgType = '';
    let dynamicPackageId = '';
    if (this.props.detailData && this.props.detailData.packageDetail) {
      tagDestinationName = this.props.detailData.packageDetail.tagDestination.name;
      branchName = this.props.detailData.packageDetail.metadataDetail.branch;
      packageId = this.props.detailData.packageDetail.id;
      packageName = this.props.detailData.packageDetail.name;
      dynamicPackageId = this.props.detailData.packageDetail.dynamicId;
      if (this.props.detailData.packageDetail.metadataDetail) {
        pkgType = this.props.detailData.packageDetail.metadataDetail.packageType;
      }
    } else if (this.holidayDetailData) {
      if (
        this.holidayDetailData.destinationDetail &&
        this.holidayDetailData.destinationDetail.tagDestination
      ) {
        tagDestinationName = this.holidayDetailData.destinationDetail.tagDestination;
      }
      if (this.holidayDetailData.branch) {
        branchName = this.holidayDetailData.branch;
      }
      if (this.holidayDetailData.packageId) {
        packageId = this.holidayDetailData.packageId;
      }
      if (this.holidayDetailData.name) {
        packageName = this.holidayDetailData.name;
      }
    }
    return {
      tagDestinationName,
      branchName,
      packageId,
      packageName,
      pkgType,
      dynamicPackageId,
    };
  }

  goToReview = (holidayReviewData, reviewData) => {
    const { packageConfigDetail } = this.newPackageDetail;
    const { componentAccessRestriction = {} } = packageConfigDetail;
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.REVIEW, {
      holidayReviewData,
      reviewData,
      fromDetails: true,
      openForwardFlowFromReviewPage: isRawClient() ? undefined : this.openForwardFlowFromReviewPage,
      componentAccessRestriction,
      reviewDataFromDetail: {
        campaign: this.holidayDetailData.campaign,
      } /* add extra review data(from detail) */,
    });
  };
  showReviewErrorPopup(reviewError, packageDetail) {
    if (reviewError && reviewError.error && this.state.showReviewPopUp) {
      return (
        <ReviewFailureHandlingPopup
          type={reviewError.error.errorType}
          reviewError={reviewError}
          packageReview={this.packageReview}
          name={this.getNameOfFailedItinerary(reviewError, packageDetail)}
          componentAccessRestriction={
            packageDetail?.packageConfigDetail?.componentAccessRestriction
          }
          openCorrespondingListingPage={() =>
            this.openCorrespondingListingPage(reviewError, packageDetail)
          }
          onReviewFailurePopupClosed={() => this.onReviewFailurePopupClosed(reviewError)}
          openListingPage={this.openListingPage}
          trackLocalClickEvent={this.trackLocalClickEvent}
        />
      );
    }
    return [];
  }

  onReviewFailurePopupClosed = (reviewError) => {
    switch (reviewError.error.errorType) {
      case detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT: {
        this.scrollToIndexFunc(6);
        break;
      }
      case detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL: {
        this.scrollToIndexFunc(6);
        break;
      }
      case detailReviewFailure.REVIEW_FAILED_TYPE_VISA: {
        this.scrollToIndexFunc(6);
        break;
      }
    }
  };

  openListingPage = () => {
    const { notFromDeeplink } = this.props;
    const { holidayDetailData, packageDetailDTO } = this;
    const { departureDetail = {}, departureCity, destinationDetail = {} } = holidayDetailData || {};
    const { pt, aff } = holidayDetailData || {};

    const listingData = {
      dest: destinationDetail.tagDestination,
      destinationCity: destinationDetail.tagDestination,
      departureCity: departureCity,
      packageDate: departureDetail.departureDate,
      cmp: packageDetailDTO.cmp,
      pt,
      aff,
      fromDeepLink: true,
      [FROM_LISTING_GROUPING_DEEPLINK]: true,
    };
    if (notFromDeeplink) {
      this.onBackPressed();
    } else {
      // try {
      //   HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.LISTING, { holidaysListingData: listingData });
      // } catch (e) {
      //   HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.LISTING, { holidaysListingData: listingData });
      // }
      try {
        // HolidayNavigation.pop();
        HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.GROUPING, listingData);
      } catch (e) {
        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GROUPING, listingData);
      }
    }
  };

  // Callback function to open forward flow overlays
  openForwardFlowFromReviewPage = () => {
    if (!isEmpty(this.props.componentFailureData)) {
      const { componentErrors } = this.props.componentFailureData || {};
      const { FLIGHT = [], HOTEL = [] } = componentErrors || {};
      if (FLIGHT && FLIGHT.length > 0) {
        setTimeout(() => this.forwardFlowOpenFlightOverlay(), 1000);
      } else if (HOTEL && HOTEL.length > 0) {
        setTimeout(() => this.forwardFlowOpenHotelOverlay(HOTEL[0].sellableId), 1000);
      }
    }
  };

  forwardFlowOpenHotelOverlay = (hotelSellableId) => {
    const hotel = getHotelObject(this.newPackageDetail.hotelDetail, hotelSellableId);
    const {showOverlay, hideOverlays, detailData} = this.props || {};
    openChangeHotelFromPhoenixPage(
      hotel,
      this.packageDetailDTO,
      this.getRoomDetails(),
      this.onComponentChange,
      this.lastPageName,
      '',
      hotelSellableId,
       showOverlay, hideOverlays, detailData,
    );
  };

  forwardFlowOpenFlightOverlay = () => {
    const { packageConfigDetail, destinationDetail } = this.newPackageDetail;
    const { componentAccessRestriction = {} } = packageConfigDetail;
    const subtitleData = createSubtitleData(
      this.props.detailData.packageDetail.departureDetail,
      this.getRoomDetails(),
    );
    const { duration, destinations } = destinationDetail || {};
    const destinationMap = createDestinationMap(duration, destinations);
    const flightReqParams = createFlightRequestParams(this.newPackageDetail);
    const { flightSelections, overnightDelays } = flightReqParams;
    const requestParams = {};
    requestParams.listingFlightSequence = 1;
    if (flightSelections && flightSelections.length > 0) {
      requestParams.flightSelections = flightSelections;
    }
    if (overnightDelays) {
      requestParams.overnightDelays = overnightDelays;
    }
    //trackLocalClickEvent('change_', `${itineraryUnitTypes.FLIGHT}_${destinationName}_${day}`);
    const {showOverlay, hideOverlays, clearOverlays} = this.props || {};
    const flightOverlayProps={
      flightRequestObject: requestParams,
      dynamicId: this.packageDetailDTO.dynamicPackageId,
      pricingDetail: this.props.detailData.packageDetail.pricingDetail,
      onComponentChange: this.onComponentChange,
      onPackageComponentToggle: this.onPackageComponentToggle,
      subtitleData: subtitleData,
      accessRestriction: componentAccessRestriction,
      lastPage: this.lastPageName,
      packageDetailDTO: this.packageDetailDTO,
      roomDetails: this.getRoomDetails(),
      trackLocalClickEvent: this.trackLocalClickEvent,
      trackLocalPageLoadEvent: this.trackLocalPageLoadEvent,
      isFlightFailed: true,
      showOverlay,
      hideOverlays,
      clearOverlays,
    };
    // holidayNavigationPush({
    //   props: flightOverlayProps,
    //   pageKey: HOLIDAY_ROUTE_KEYS.FLIGHT_LISTING,
    //   overlayKey: Overlay.FLIGHT_OVERLAY,
    //   hideOverlays,
    //   showOverlay,
    // })
    isMobileClient()
        ? HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.FLIGHT_LISTING, flightOverlayProps)
        : showOverlay(Overlay.FLIGHT_OVERLAY, flightOverlayProps);
  };

  openCorrespondingListingPage = (reviewError, optimizedPackageDetail) => {
    const { packageConfigDetail } = this.newPackageDetail;
    const { componentAccessRestriction = {} } = packageConfigDetail;
    switch (reviewError.error.errorType) {
      case detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT: {
        if (!componentAccessRestriction.changeFlightRestricted) {
          this.forwardFlowOpenFlightOverlay();
        } else {
          HolidayNavigation.pop();
        }
        break;
      }
      case detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL: {
        if (!componentAccessRestriction.changeHotelRestricted) {
          const { failedHotels } = reviewError.error.errorData || [];
          this.forwardFlowOpenHotelOverlay(failedHotels[0].sellableId);
        } else {
          HolidayNavigation.pop();
        }
        break;
      }
      case detailReviewFailure.REVIEW_FAILED_TYPE_VISA: {
        this.updateVisa(false);
        break;
      }
      case detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY:
      case detailReviewFailure.REVIEW_FAILED_TYPE_ADDON_INCLUSION:
      case detailReviewFailure.REVIEW_FAILED_TYPE_TRANSFER: {
        const { pricingDetail, departureDetail } = this.props.detailData.packageDetail;
        const day = reviewError?.error?.errorData?.dialogResponse?.content?.soldOutItems?.[0]?.day;
        const roomDetails = this.getRoomDetails();
        const activityReqParams = getActivityExtraData(this.props.detailData.packageDetail, day);
        const subtitleData = createSubtitleData(departureDetail, roomDetails, {});
        this.activityFailedScrollRef.current = true;

        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.TRAVEL_TIDBITS, {
          departureDetail,
          activityReqParams,
          day: day,
          dynamicId: this.packageDetailDTO.dynamicPackageId,
          pricingDetail: pricingDetail,
          onComponentChange: this.onComponentChange,
          subtitleData: subtitleData,
          lastPage: HOLIDAY_ROUTE_KEYS.DETAIL,
          branch: this.props.detailData.branch,
          packageDetailDTO: this.packageDetailDTO,
          roomDetails: roomDetails,
          trackLocalClickEvent: this.trackLocalClickEvent,
          trackLocalPageLoadEvent: this.trackLocalPageLoadEvent,
          activityProductType: reviewError?.error?.errorData?.dialogResponse?.content?.soldOutItems?.[0]?.productType,
          currentActivePlanOnItinerary: this.state.currentActivePlanOnItinerary,
        });
        break;
      }
      default: {
        HolidayNavigation.pop();
      }
    }
  };

  getNameOfFailedItinerary = (reviewError, packageDetail) => {
    if (reviewError && reviewError.error && reviewError.error.errorType) {
      switch (reviewError.error.errorType) {
        case detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL: {
          let namesOfHotels = '';
          if (packageDetail) {
            if (packageDetail.hotelDetail) {
              if (packageDetail.hotelDetail) {
                const { failedHotels } = reviewError.error.errorData || [];
                if (failedHotels) {
                  for (let i = 0; i < failedHotels.length; i++) {
                    if (packageDetail.hotelDetail[failedHotels[i].sellableId]) {
                      namesOfHotels = packageDetail.hotelDetail[failedHotels[i].sellableId].name;
                      break;
                    }
                  }
                }
              }
            }
          }
          return namesOfHotels;
        }
        case detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT: {
          return 'Flights';
        }
        case detailReviewFailure.REVIEW_FAILED_TYPE_VISA: {
          return 'VISA';
        }
        case detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY: {
          return 'Activity';
        }

        default:
          return '';
      }
    } else if (!isEmpty(this.props.componentFailureData)) {
      /*
       * Below commented code enable highlighting error component in red color
       * and also shows a toast message in day plan
       */
      const { componentErrors } = this.props.componentFailureData || {};
      const { FLIGHT = [], HOTEL = [] } = componentErrors || {};
      if (HOTEL && HOTEL.length > 0) {
        return HOTEL[0].hotelName;
      }
      if (FLIGHT && FLIGHT.length > 0) {
        return 'Flights';
      }
    }
    return '';
  };

  handleReviewFailure = (reviewError = {}) => {
    const {error} = reviewError || {}
    const { errorType = '' } = reviewError?.error || {};
    if (errorType === detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT) {
      this.trackLocalClickEvent(PDTConstants.SEEN, PDTConstants.FLIGHT_SOLD_OUT);
    } else if (errorType === detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL) {
      this.trackLocalClickEvent(PDTConstants.SEEN, PDTConstants.HOTEL_SOLD_OUT);
    } else if (errorType === detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY || errorType === detailReviewFailure.REVIEW_FAILED_TYPE_ADDON_INCLUSION || errorType === detailReviewFailure.REVIEW_FAILED_TYPE_TRANSFER) {
      logPhoenixDetailPDTEvents({actionType:PDT_EVENT_TYPES.contentSeen,value:PDTConstants.ACTIVITY_SOLD_OUT,shouldTrackToAdobe:false});

      this.fetchDetailDataFunc(
        false,
        true,
        null,
        false,
        this.getRoomDetails(),
      );
    } else {
      this.trackLocalClickEvent(PDTConstants.SEEN, PDTConstants.GENERIC_FORWARD_FLOW_ERROR);
    }
    this.setState({
      reviewError,
      showReviewPopUp: true,
    });
  };
}

const styles = StyleSheet.create({
  pageWrap: {
    ...Platform.select({
      ios: {
        marginTop: -statusBarHeightForIphone,
        paddingTop: statusBarHeightForIphone,
      },
    }),
    backgroundColor: holidayColors.white,
  },
  affBanner:{
    alignItems:'center',
    justifyContent:"center",
    backgroundColor: "#EEF0F2",
    paddingVertical:10,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    ...sectionHeaderSpacing,
  },
  leftRightSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    marginTop: 14,
  },
  pkgName: {
    ...marginStyles.mt20,
    ...paddingStyles.ph16,
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  messageStripContainer: {
    marginTop: 0,
    paddingTop: 5,
  },
  seperator: {
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
    paddingLeft: 10,
  },
  visaContainer: {
    paddingHorizontal: 0,
  },
  summaryContainer: {
    paddingHorizontal: 16,
  },
  persuasionContainer: {
    borderBottomColor: holidayColors.grayBorder,
    borderBottomWidth: 5,
    marginBottom: 0,
  },
  fabAnimationBottom: {

  },
  fabAnimationBottomTravelPlex: {
    bottom: 90,  // Positions the element 90 pixels from the bottom of its containing element when using position: 'absolute'
    marginBottom: 70,  // Adds 70 pixels of margin space below the element, creating additional space between this element and the next element below it
  },
  membershipCard: {
    ...marginStyles.ml16,
    ...marginStyles.mr16,
  },
  membershipCardContainer: {
    borderTopColor: holidayColors.grayBorder,
    borderTopWidth: 4,
    ...paddingStyles.pb10,
    ...paddingStyles.pt10,
  },
  cardItemStyle: {
    paddingTop: 14,
  },
  customCardsWprStyle: {
    ...paddingStyles.pb6,
  },
  membershipCardCarousel: {
    ...paddingStyles.ph16,
    ...paddingStyles.pv4,
  },
});
const detailPersuasionStyle = {
  paddingTop: 14,
  paddingBottom: 14,
  backgroundColor: '#fff',
  marginBottom: 0,
};
export default withIntervention(
  withBackHandler(HolidayDetailPageNew),
  DETAIL_TRACKING_PAGE_NAME
);
