import {isEmpty,has} from 'lodash';
import fecha from 'fecha';
import cloneDeep from 'lodash/cloneDeep';
import {
  ADD_ACTIVITY_DETAILS_ERROR_MSG,
  COUPON_CODE_FAILED,
  COUPON_CODE_SUCCESS,
  DEFAULT_TRAVELLER_COUNT,
  DETAIL_REVIEW_ERROR_MSG,
  DETAIL_TRACKING_PAGE_NAME,
  DETAILS_FLIGHT_CHANGE_ERROR_MSG,
  DFIT_PKG_TYPE,
  errorCodesPrefix,
  errorMessages,
  flightDetailTypes,
  HOL_TYPE_TAG_NAME,
  itineraryUnitSubTypes,
  itineraryUnitTypes,
  MIN_CHILD_AGE,
  OBT_BRANCH,
  OVERLAY_ANIMATE_DELAY,
  OVERLAY_ANIMATE_DURATION,
  overlays,
  packageActionComponent,
  packageActions,
  packageErrorCodes,
  PDTConstants,
  REMOVE_ACTIVITY_DETAILS_ERROR_MSG,
  REMOVE_FLIGHT_CHANGE_ERROR_MSG,
} from '../DetailConstants';
import {viewTypes} from '../../Listing/ListingConstants';
import {
  createRandomString,
  createSearchCriteriaAppliedMap,
  getFilterNameValuesMap,
  getItineraryComponents,
  getItineraryThemeTags,
  isIosClient,
  isMobileClient,
  isNonMMTAffiliate,
  isNotNullAndEmptyCollection,
  isNullOrEmptyCollection,
  isZCAvailable,
} from '../../utils/HolidayUtils';
import {
  MONTH_ARR_CAMEL,
  NO_DEPARTURE_CITY,
  PACKAGE_TYPE_FIT,
  PDT_LOB,
  REQUEST_LOB,
  STORY_IMAGE_SIZE_ATTR,
} from '../../HolidayConstants';
import {createBaseRequest} from '../../utils/HolidayNetworkUtils';
import {convertToFullDateFormat, createPersuasionLoggingMap} from '../../Listing/Utils/HolidayListingUtils';
import {DATE_API_FORMAT} from '../../Query/QueryConstants';
import {isValidURL} from '@mmt/legacy-commons/Helpers/validationHelpers';
import {
  CANCELLATION,
  DATE_CHANGE,
  isFlexiDateAvailable,
} from '../../ZeroCancellation/CancellationDateChange/InnerContent';
import {HOLIDAY_REVIEW_PAGE} from '../../Review/HolidayReviewConstants';
import {LocationDetail} from '../../Grouping/Utils/HolidayGroupingUtils';
import {HOLIDAY_ROUTE_KEYS, HolidayNavigation} from '../../Navigation';
import { getRootTag } from '@mmt/legacy-commons/AppState/RootTagHolder';
import {TABS} from '../Components/DayPlan/Sorter';
import { CANCELLATION_POLICY, DATE_CHANGE_POLICY } from '../../PhoenixReview/Utils/HolidayReviewConstants';
import {Overlay} from "../Components/DetailOverlays/OverlayConstants";


export const calculateFlightsCount = (flightDetail) => {
  let flightsCount = 0;
  if (flightDetail && !flightDetail.removed) {
    switch (flightDetail.flightDetailType) {
      case flightDetailTypes.DOM_ONWARDS:
        flightsCount += flightDetail.onwardFlights.length;
        break;
      case flightDetailTypes.OBT:
        for (let i = 0; i < flightDetail.obtFlightGroup.flights.length; i += 1) {
          flightsCount += flightDetail.obtFlightGroup.flights[i].flightLegs.length;
        }
        break;
      case flightDetailTypes.DOM_RETURN:
        flightsCount += 2;
        break;
      default:
    }
  }
  return flightsCount;
};

export const isTourManagerExist = (tourManagerDetail) => {
  return !!tourManagerDetail?.available;
};

export const createDateFromString = dateStr => {
  if (!dateStr) return null;
  return fecha.parse(dateStr, DATE_API_FORMAT);
};

export const fetchHotelUsingSellableId = (sellableId, hotels) => {
  return hotels.find(hotel => hotel.sellableId === sellableId) || null;
};


export const fetchImagesFromImageDataList = (imageDataList) => {
  const imageList = [];
  if (imageDataList && imageDataList.length > 0) {
    const imageData = imageDataList[0];
    if (imageData.mainImage && imageData.mainImage.path) {
      imageList.push({fullPath: imageData.mainImage.path});
    }
    if (imageData.images) {
      for (let i = 0; i < imageData.images; i += 1) {
        const image = imageData.images[i];
        if (image && image.path) {
          imageList.push({fullPath: image.path});
        }
      }
    }
  }
  return imageList;
};

const getCouponList = offerSection => {
  if (!offerSection) {
    return [];
  }

  const { couponList = [], couponData = {} } = offerSection || {};
  const { error } = couponData || {};

  if (error) {
    return [];
  }

  const now = Math.floor(Date.now() / 1000); // Get the current Unix timestamp in seconds
  return couponList
    .filter(coupon => coupon?.couponExpiryDate > now && coupon?.baseCouponCode)
    .filter(Boolean); // removes falsy values from the array
};

const isValidCategoryDiscount = dealDetail => {
  return dealDetail
    && isNotNullAndEmptyCollection(dealDetail.categoryDiscountDetails)
    && dealDetail.categoryDiscountDetails[0].walletAmount > 0;
};

export const calculateOffersCount = (couponDiscount, dealDetail, loggedIn, aff, offerSection) => {
  let offersCount = 0;
  const hasValidCategoryDiscount = isValidCategoryDiscount(dealDetail);
  const hasValidCouponList = getCouponList(offerSection).length > 0;

  if (!isEmpty(couponDiscount)) {
    offersCount++;
  }
  if (hasValidCategoryDiscount || hasValidCouponList) {
    offersCount++;
  }
  if (!loggedIn && !isNonMMTAffiliate(aff)) {
    offersCount++;
  }
  return offersCount;
};

export const calculateTotalDiscountPercentage =
  (dealDetail, price, discountedPrice, travellerCount) => {
    const totalDiscount =
      calculateTotalDiscount(price, discountedPrice, dealDetail, travellerCount);
    return Math.round((totalDiscount * 100) / price);
  };

export const calculateTotalDiscount = (price, discountedPrice, dealDetail, travellerCount) => (
  (price - discountedPrice) +
  (dealDetail && isNotNullAndEmptyCollection(dealDetail.categoryDiscountDetails) && dealDetail.categoryDiscountDetails[0].walletAmount > 0
    ? Math.round(dealDetail.categoryDiscountDetails[0].walletAmount / travellerCount) : 0)
);

export const getWalletAmount = (dealDetail, travellerCount) => {
  if (dealDetail
    && isNotNullAndEmptyCollection(dealDetail.categoryDiscountDetails)
    && dealDetail.categoryDiscountDetails[0].walletAmount > 0) {
    return Math.round(dealDetail.categoryDiscountDetails[0].walletAmount / travellerCount);
  }
  return 0;
};

export const getDiscountAmount = (discountItem, travellerCount) => {
  if (discountItem?.discount){
    return Math.round(discountItem.discount / travellerCount);
  }
  return 0;
};

export const getDiscountedPriceAfterWallet = (discountedPrice, dealDetail, travellerCount) => {
  return discountedPrice - getWalletAmount(dealDetail, travellerCount);
};

export const findPricePersuasion = (persuasionData) => {
  for (const persuasion of persuasionData) {
    if (persuasion.type === viewTypes.LISTING_PRICE_PERSUASION) {
      return persuasion.item;
    }
  }
  return null; // If no matching persuasion is found, return null or some other default value
};

export const fetchPricePersDateText = (departureDateStr) => {
  const departureDate = createDateFromString(departureDateStr);
  return `${departureDate.getDate()}${fetchDayPostFix(departureDate.getDate())} ${MONTH_ARR_CAMEL[departureDate.getMonth()]}`;
};

export const fetchPricePersDate = departureDate => `${departureDate.getDate()}${fetchDayPostFix(departureDate.getDate())} ${MONTH_ARR_CAMEL[departureDate.getMonth()]}`;

export const fetchDayPostFix = (day) => {
  let dayPostFix = '';
  switch (day) {
    case 31:
    case 21:
    case 1:
      dayPostFix = 'st';
      break;
    case 22:
    case 2:
      dayPostFix = 'nd';
      break;
    case 23:
    case 3:
      dayPostFix = 'rd';
      break;
    default:
      dayPostFix = 'th';
  }
  return dayPostFix;
};

export const createLoaderDate = (departureDateStr) => {
  const departureDate = createDateFromString(departureDateStr);
  return `${departureDate.getDate()} ${MONTH_ARR_CAMEL[departureDate.getMonth()]} ${departureDate.getFullYear()}`;
};

export const createTravellersText = (travellerObj) => {
  let travellersText = `${travellerObj.adult} Adult${travellerObj.adult > 1 ? 's' : ''} `;
  if (travellerObj.child && travellerObj.child > 0) {
    travellersText += `${travellerObj.child} Child${travellerObj.child > 1 ? 'ren' : ''} `;
  }
  if (travellerObj.infant && travellerObj.infant > 0) {
    travellersText += `${travellerObj.infant} Infant${travellerObj.infant > 1 ? 's' : ''}`;
  }
  return travellersText;
};

export const openChangeHotel = (hotel, packageDetailDTO) => {
  HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_LISTING_OLD, {
    packageDetailDTO,
    searchCriteria: packageDetailDTO.searchCriteria,
    hotel,
    hotelCode: hotel.hotelCode,
    cityName: hotel.cityName,
    price: packageDetailDTO.price,
    sequences: hotel.hotelSequence,
    reactTag: getRootTag(),
  });
};

export const openChangeHotelFromPhoenixPage = (hotel, packageDetailDTO, roomDetails, onComponentChange, lastPageName, fromPage = '', failedHotelSellableId, showOverlay, hideOverlays, detailData) => {
  const data={
    packageDetailDTO,
    searchCriteria: packageDetailDTO.searchCriteria,
    hotel,
    hotelCode: hotel.hotelCode,
    cityName: hotel.cityName,
    price: packageDetailDTO.price,
    sequences: hotel.hotelSequence,
    reactTag: getRootTag(),
    roomDetails,
    onComponentChange,
    lastPageName,
    fromPage,
    showOverlay,
    hideOverlays,
    back: () => hideOverlays([Overlay.PHOENIX_HOTELS_LISTING, Overlay.VIEW_DETAILS]),
    failedHotelSellableId,
    detailData,
  };

  isMobileClient()
      ? HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_LISTING, data)
      : showOverlay(Overlay.PHOENIX_HOTELS_LISTING, data);
};

export const addPersuasionToDetailData = (detailData, persuasionData, cancellationPolicyData = null) => {
  if (detailData.pageDataMap) {
    detailData.pageDataMap.persuasionDetails = createPersuasionLoggingMap(persuasionData, cancellationPolicyData);
  }
};


export const updateEMIPriceForPersuasion = (persuasionData, details) => {
  if (persuasionData && persuasionData.length && details && details.emiDetail && details.emiDetail.emiAmount) {
    const data = persuasionData.filter(persuasion => persuasion.type === viewTypes.LISTING_EMI_PERSUASION);
    if (data && data.length > 0 && data[0].item && data[0].item.emiDetail) {
      data[0].item.emiDetail.emiAmount = Math.ceil(details.emiDetail.emiAmount);
    }
  }
};

const getPackageCities = (packageDetailData) => {
  const citiesSet = new Set();
  const {destinations} = packageDetailData.destinationDetail;
  for (let index = 0; index < destinations.length; index += 1) {
    citiesSet.add(destinations[index].name);
  }
  return [...citiesSet];
};

const getHolidayDuration = packageDetailData => packageDetailData.destinationDetail.duration;
const getPackageType = packageDetailData => packageDetailData.metadataDetail.packageType;

export const getPaxCount = (roomDetails, paxType) => {
  let noOfPax = 0;
  for (let index = 0; index < roomDetails.length; index += 1) {
    let count = roomDetails[index][paxType];
    if (count > 0) {
      noOfPax += roomDetails[index][paxType];
    }
  }
  return noOfPax;
};

const getItineraryDetails = (packageDetailData) => {
  const itineraryDetails = {};
  const hotelCheckinDays = [];
  const hotelCheckoutDays = [];
  const flightDays = [];
  if (packageDetailData) {
    const {dynamicItinerary} = packageDetailData.itineraryDetail;
    if (dynamicItinerary) {
      const {dayItineraries} = dynamicItinerary;
      for (let dayIndex = 0; dayIndex < dayItineraries.length; dayIndex += 1) {
        const {itineraryUnits} = dayItineraries[dayIndex];
        for (let itineraryIndex = 0; itineraryIndex < itineraryUnits.length; itineraryIndex += 1) {
          if (itineraryUnits[itineraryIndex].itineraryUnitType === itineraryUnitTypes.FLIGHT) {
            flightDays.push(dayItineraries[dayIndex].day);
          } else if (itineraryUnits[itineraryIndex].itineraryUnitType === itineraryUnitTypes.HOTEL
            && itineraryUnits[itineraryIndex].itineraryUnitSubType ===
            itineraryUnitSubTypes.CHECKIN) {
            hotelCheckinDays.push(dayItineraries[dayIndex].day);
          } else if (itineraryUnits[itineraryIndex].itineraryUnitType ===
            itineraryUnitTypes.HOTEL && itineraryUnits[itineraryIndex].itineraryUnitSubType ===
            itineraryUnitSubTypes.CHECKOUT) {
            hotelCheckoutDays.push(dayItineraries[dayIndex].day);
          }
        }
      }
    }
    itineraryDetails.hotelCheckinDays = hotelCheckinDays;
    itineraryDetails.hotelCheckoutDays = hotelCheckoutDays;
    itineraryDetails.flightDays = flightDays;
  }
  return itineraryDetails;
};

const getImagesCount = (packageDetailData) => {
  const {imageDetail} = packageDetailData || {};
  const {images} = imageDetail || {};
  if (images) {
    return images.length;
  }
  return 0;
};

const getPackageCategory = (packageDetailData) => {
  const {categoryDetail} = packageDetailData;
  if (categoryDetail) {
    const categoryId = categoryDetail.defaultCategoryId;
    const {packageCategories} = categoryDetail;
    for (let index = 0; index < packageCategories.length; index += 1) {
      if (categoryId === packageCategories[index].id) {
        return packageCategories[index].name;
      }
    }
  }
  return null;
};

const getPricingDetails = (packageDetailData) => {
  const pricingDetails = {};
  let discountedPriceParam;
  let preDiscountedPrice;
  let gstAmount;
  let cdfDiscount;
  let walletDiscount;
  if (packageDetailData) {
    const {pricingDetail} = packageDetailData;
    if (pricingDetail.categoryPrices[0]) {
      const categoryPrice = pricingDetail.categoryPrices[0];
      preDiscountedPrice = categoryPrice.price;
      discountedPriceParam = categoryPrice.discountedPrice;
      cdfDiscount = preDiscountedPrice - discountedPriceParam;
      gstAmount = categoryPrice.serviceTax;
      if (categoryPrice.couponDiscount) {
        pricingDetails.couponType = categoryPrice.couponDiscount.type;
        pricingDetails.couponCode = categoryPrice.couponDiscount.couponCode;
        const isInstantDiscount = categoryPrice.couponDiscount.discountAppliedType === 'INSTANT';
        pricingDetails.instantDiscount = isInstantDiscount;
        pricingDetails.cashbackDiscount = !isInstantDiscount;
      }
    }
    if (packageDetailData.dealDetail && isNotNullAndEmptyCollection(packageDetailData.dealDetail.categoryDiscountDetails)) {
      walletDiscount = packageDetailData.dealDetail.categoryDiscountDetails[0].walletAmount;
    }

    pricingDetails.discountedPrice = discountedPriceParam;
    pricingDetails.preDiscountedPrice = preDiscountedPrice;
    pricingDetails.gstAmount = gstAmount;
    pricingDetails.cdfDiscount = cdfDiscount;
    pricingDetails.walletDiscount = walletDiscount;
  }
  return pricingDetails;
};

const getHolidayType = (detailData) => {
  const {tagDetail} = detailData || {};
  if (!isEmpty(tagDetail) && has(tagDetail, 'tags')) {
    const {tags = []} = tagDetail || {};
    for (let index = 0; index < tags?.length; index += 1) {
      if (tags[index].name === HOL_TYPE_TAG_NAME && tags[index].values &&
          tags[index].values.length > 0) {
        return tags[index].values.join('_');
      }
    }
  }
  return null;
};

const isPackageCustomized = (detailData) => {
  if (detailData.metadataDetail) {
    return detailData.metadataDetail.customized;
  }
  return false;
};

export const isFlightChangeable = (detailData) => {
  if (detailData.metadataDetail) {
    return (detailData.metadataDetail.packageType === PACKAGE_TYPE_FIT &&
      detailData.metadataDetail.branch === OBT_BRANCH);
  }
  return false;
};

export const isFlightIncluded = detailData => !((!detailData.flightDetail) || detailData.flightDetail.removed);

const getVisaDetailsList = (detailData) => {
  const visaDetailTypes = [];
  if (detailData.visaDetail) {
    const {visas} = detailData.visaDetail;
    for (let index = 0; index < visas.length; index += 1) {
      visaDetailTypes.push(visas[index].visaType);
    }
  }
  return visaDetailTypes;
};

const getPkgItinry = (pkgDetails) => {
  let itinary = '';
  if (pkgDetails.destinationDetail && isNotNullAndEmptyCollection(pkgDetails.destinationDetail.destinations)) {
    pkgDetails.destinationDetail.destinations.forEach((destination) => {
      itinary = `${itinary}${destination.name}${destination.duration},`;
    });
  }
  if (itinary.length > 1) {
    itinary = itinary.substring(0, itinary.length - 1);
  }
  return itinary;
};

export const createLoggingMap = (detailData, roomDetails) => {
  const { packageDetail = {} } = detailData || {};
  const { metadataDetail = {} } = packageDetail || {};
  const { trackingInfo = {} } = metadataDetail || {};
  const loggingMap = initializeLoggingMap();
  const departureDate = detailData.holidayDetailData.departureDetail ?
    detailData.holidayDetailData.departureDetail.departureDate : null;
  const departureCity = detailData.holidayDetailData.departureDetail ?
    detailData.holidayDetailData.departureDetail.departureCity : null;
  loggingMap.otherDetails = {
    travel_start_date: departureDate ? convertToFullDateFormat(departureDate) : null,
    last_page_name: detailData.holidayDetailData.lastPageName,
    departureCity,
  };
  const pricingDetails = getPricingDetails(detailData.packageDetail);
  const paxCount = {
    adultCount: getPaxCount(roomDetails, 'noOfAdults'),
    childCount: getPaxCount(roomDetails, 'noOfChildrenWB') +  getPaxCount(roomDetails, 'noOfChildrenWOB'),
    infantCount: getPaxCount(roomDetails, 'noOfInfants'),
    roomCount: roomDetails.length,
  };
  loggingMap.searchCriteriaAppliedMap = createSearchCriteriaAppliedMap(detailData.holidayDetailData);
  loggingMap.packageDetails = createPackageDetailsLoggingMap(detailData.packageDetail, paxCount);
  loggingMap.pricingDetails =
    createPricingDetailsLoggingMap(detailData.packageDetail, pricingDetails, paxCount);
  loggingMap.discountDetails =
    createDiscountDetailsLoggingMap(detailData.packageDetail, pricingDetails);
  loggingMap.requestDetails = createRequestDetailsLoggingMap(detailData.cmp, detailData.isWG);
  loggingMap.locusDetails = createLocusDetailsLoggingMap(detailData.packageDetail);
  loggingMap.trackingDetails = {
    source: detailData?.holidayDetailData?.source || '',
    ticketSource: trackingInfo?.ticketSource || '',
  };
  return loggingMap;
};

const createLocusDetailsLoggingMap = (packageDetailResponse) => {
  const data = {
    package_tag_destination: {},
    search_from_city: {},
    packages_cities: [],
    search_package_dest: [],
    search_select_destination_list: [],
  };
  if (packageDetailResponse) {
    const {tagDestination, departureDetail, destinationDetail} = packageDetailResponse;
    const {destinations = []} = destinationDetail;
    const destLocusInfo = [];
    destinations.forEach((destObj) => {
      const {locusDetails} = destObj;
      const {locusCode = '', locusType = ''} = locusDetails || {};
      destLocusInfo.push(LocationDetail(locusCode, locusType));
    });
    data.package_tag_destination = LocationDetail(tagDestination.locusDetails?.locusCode, tagDestination.locusDetails?.locusType);
    data.search_from_city = LocationDetail(departureDetail.locusDetails?.locusCode, departureDetail.locusDetails?.locusType);
    data.packages_cities = destLocusInfo;
    data.search_package_dest = destLocusInfo;
    data.search_select_destination_list = destLocusInfo;
  }
  return data;
};

const createPackageDetailsLoggingMap = (packageDetailData, paxCount) => {
  const { packageInclusionsDetail = {}, metadataDetail = {} } = packageDetailData || {};
  const packageDetailsMap = {};
  if (packageDetailData) {
    const itineraryDetails = getItineraryDetails(packageDetailData);
    packageDetailsMap.pkg_nm = packageDetailData.name;
    packageDetailsMap.pkg_hld_id = packageDetailData.id;
    packageDetailsMap.pkg_tag_dest = packageDetailData.tagDestination.name;
    packageDetailsMap.pd_px_ad = paxCount.adultCount;
    packageDetailsMap.pd_px_ch = paxCount.childCount;
    packageDetailsMap.pd_px_inf = paxCount.infantCount;
    packageDetailsMap.roomCount = paxCount.roomCount;
    packageDetailsMap.pkg_cities = getPackageCities(packageDetailData);
    packageDetailsMap.pkg_hld_durn = getHolidayDuration(packageDetailData);
    packageDetailsMap.pkg_hld_type = getPackageType(packageDetailData);
    packageDetailsMap.pkg_hol_typ = getHolidayType(packageDetailData);
    packageDetailsMap.pkg_hol_star_rating = getHotelStarRating(packageDetailData);
    packageDetailsMap.pkg_htl_chkin_day_list = itineraryDetails.hotelCheckinDays;
    packageDetailsMap.pkg_htl_chkout_day_list = itineraryDetails.hotelCheckoutDays;
    packageDetailsMap.pkg_flt_days = itineraryDetails.flightDays;
    packageDetailsMap.pkg_visa_list = getVisaDetailsList(packageDetailData);
    packageDetailsMap.pkg_img_cnt = getImagesCount(packageDetailData);
    packageDetailsMap.pkg_flt_chng_stat = isFlightChangeable(packageDetailData);
    packageDetailsMap.pkg_htl_chng_stat = true;
    packageDetailsMap.pkg_catgry = getPackageCategory(packageDetailData);
    packageDetailsMap.pkg_custm_stat = isPackageCustomized(packageDetailData);
    packageDetailsMap.pkg_flight_included = isFlightIncluded(packageDetailData);
    packageDetailsMap.pkg_itinry = getPkgItinry(packageDetailData);
    packageDetailsMap.pkg_itinry_components = getItineraryComponents(packageInclusionsDetail);
    packageDetailsMap.pkg_themeTags = getItineraryThemeTags(packageDetailData);
    if (packageDetailData.metadataDetail && packageDetailData.metadataDetail.premium) {
      packageDetailsMap.premium = packageDetailData.metadataDetail.premium;
    }
    if (packageDetailData.metadataDetail && packageDetailData.metadataDetail.safe) {
      packageDetailsMap.safe = true;
    }
    if (metadataDetail?.bundled) {
      packageDetailsMap.bundled = metadataDetail?.bundled;
    }
  }
  return packageDetailsMap;
};


const createPricingDetailsLoggingMap = (detailData, pricingDetails, paxCount) => {
  const pricingDetailsMap = {};
  if (pricingDetails) {
    const {
      preDiscountedPrice, discountedPrice, gstAmount, cdfDiscount, walletDiscount,
    } = pricingDetails;
    pricingDetailsMap.prc_pre_disc = preDiscountedPrice;
    pricingDetailsMap.prc_post_disc = discountedPrice;
    pricingDetailsMap.prc_gst_amt = gstAmount;
    pricingDetailsMap.prc_cdf_disc = cdfDiscount;
    pricingDetailsMap.prc_wal_disc = walletDiscount;
    pricingDetailsMap.prc_tot_payable_ad = discountedPrice + gstAmount;
    pricingDetailsMap.prc_tot_bkg_amt = pricingDetailsMap.prc_tot_payable_ad *
      (paxCount.adultCount +
        paxCount.childCount +
        paxCount.infantCount);
  }
  return pricingDetailsMap;
};

const createDiscountDetailsLoggingMap = (detailData, pricingDetails) => {
  const discountDetailsMap = {};
  if (pricingDetails) {
    const {
      couponCode, couponType, instantDiscount, cashbackDiscount,
    } = pricingDetails;
    discountDetailsMap.cpn_code = couponCode;
    discountDetailsMap.cpn_cd_type = couponType;
    discountDetailsMap.cpn_status = couponCode ?
      COUPON_CODE_SUCCESS : COUPON_CODE_FAILED;
    discountDetailsMap.cpn_inst_disc_stat = instantDiscount;
    discountDetailsMap.cpn_cashbk_disc_stat = cashbackDiscount;
  }
  return discountDetailsMap;
};

export const createRequestDetailsLoggingMap = (cmp, isWG) => {
  const requestDetailsLoggingMap = {};
  requestDetailsLoggingMap.lob = PDT_LOB;
  requestDetailsLoggingMap.page = DETAIL_TRACKING_PAGE_NAME;
  requestDetailsLoggingMap.funnel_step = DETAIL_TRACKING_PAGE_NAME;
  requestDetailsLoggingMap.cmp_channel = cmp;
  requestDetailsLoggingMap.isWG = isWG;
  return requestDetailsLoggingMap;
};

export const initializeLoggingMap = () => {
  const loggingMap = {};
  loggingMap.filterDetails = {};
  loggingMap.packageDetails = {};
  loggingMap.pricingDetails = {};
  loggingMap.discountDetails = {};
  loggingMap.sorterDetails = {};
  loggingMap.otherDetails = {};
  loggingMap.interventionDetails = {};
  loggingMap.persuasionDetails = {};
  loggingMap.requestDetails = {};
  loggingMap.errorDetails = {};
  loggingMap.searchCriteriaAppliedMap = {};
  return loggingMap;
};

export const getEventName = (popupName, sectionToShow, placesName = '') => {
  let eventName;
  switch (popupName) {
    case overlays.ACTIVITY_OVERLAY:
      eventName = PDTConstants.INCLUSION_PREFIX + PDTConstants.ACTIVITIES_SUFFIX;
      break;
    case overlays.SIGHTSEEING_OVERLAY:
      eventName = PDTConstants.INCLUSION_PREFIX + PDTConstants.SIGHTSEEING_SUFFIX;
      break;
    case overlays.TRANSFER_OVERLAY:
      eventName = PDTConstants.INCLUSION_PREFIX + PDTConstants.TRANSFERS_SUFFIX;
      break;
    case overlays.IMAGE_OVERLAY:
      eventName = PDTConstants.IMAGE_CAROUSAL;
      break;
    case overlays.EXTRA_INFO_OVERLAY:
      eventName = `${PDTConstants.POLICY}_${sectionToShow}`;
      break;
    case overlays.PRICE_OVERLAY:
      eventName = PDTConstants.CHEAPER_DATE_CHECK;
      break;
    case overlays.HOTEL_OVERLAY:
      eventName = PDTConstants.INCLUSION_PREFIX + PDTConstants.HOTELS_SUFFIX;
      break;
    case overlays.FLIGHTS_OVERLAY:
      eventName = PDTConstants.INCLUSION_PREFIX + PDTConstants.FLIGHTS_SUFFIX;
      break;
    case overlays.PLACES_OVERLAY:
      eventName = `${PDTConstants.VIEW_PLACES}_${placesName}`;
      break;
    case overlays.COST_OVERLAY:
      eventName = PDTConstants.COST_PERS;
      break;
    case overlays.EDIT_OVERLAY:
    eventName = undefined; // fire no event
    break;
    default:
      eventName = 'back';
  }
  return eventName;
};

export const animateOverlay = (thisObj, bottom) => {
  thisObj.startAnimate(bottom, OVERLAY_ANIMATE_DURATION, OVERLAY_ANIMATE_DELAY);
};

export const fetchErrorMessage = (error) => {
  let errorMessage = errorMessages.DEFAULT;
  if (error && error.code) {
    if (error.code.indexOf(errorCodesPrefix.FLIGHT) > -1) {
      errorMessage = errorMessages.FLIGHT;
    } else if (error.code.indexOf(errorCodesPrefix.HOTEL) > -1) {
      errorMessage = errorMessages.HOTEL;
    } else if (error.code.indexOf(errorCodesPrefix.PACKAGE) > -1) {
      switch (error.code) {
        case packageErrorCodes.PACKAGE1:
        case packageErrorCodes.PACKAGE2:
          errorMessage = errorMessages.PACKAGE_MAIN;
          break;
        case packageErrorCodes.PACKAGE3:
          errorMessage = errorMessages.PACKAGE_CITY;
          break;
        case packageErrorCodes.PACKAGE4:
          errorMessage = errorMessages.PACKAGE_DATE;
          break;
        default:
          errorMessage = errorMessages.PACKAGE_DEFAULT;
      }
    } else if (error.code.indexOf(errorCodesPrefix.CAR) > -1 ||
      error.code.indexOf(errorCodesPrefix.ROUTE) > -1) {
      errorMessage = errorMessages.CAR_AND_ROUTE;
    }
  }
  return errorMessage;
};

export const calculateTravellersCount = (roomData) => {
  if (!roomData || roomData.length === 0) {
    return DEFAULT_TRAVELLER_COUNT;
  }
  let travellersCount = 0;
  for (let i = 0; i < roomData.length; i += 1) {
    const room = roomData[i];
    travellersCount += room.noOfAdults;
    travellersCount += room.noOfChildrenWB;
    travellersCount += room.noOfInfants;
  }
  return travellersCount;
};

export const getVideoUrl = (packageDetail) => {
  if (packageDetail && packageDetail.videoDetail && packageDetail.videoDetail.packageVideo) {
    if (isIosClient()) {
      if (packageDetail.videoDetail.packageVideo.hlsUrl
        && isValidHlsUrl(packageDetail.videoDetail.packageVideo.hlsUrl)) {
        return packageDetail.videoDetail.packageVideo.hlsUrl;
      }
    } else if (packageDetail.videoDetail.packageVideo.mpdUrl
      && isValidMpdUrl(packageDetail.videoDetail.packageVideo.mpdUrl)) {
      return packageDetail.videoDetail.packageVideo.mpdUrl;
    }
  }
  return '';
};

export const isValidMpdUrl = (url) => {
  return isValidURL(url) && url.endsWith('.mpd');
};

export const isValidHlsUrl = (url) => {
  return isValidURL(url) && url.endsWith('.m3u8');
};

export const createTravellerObjForLoader = (roomData) => {
  if (!roomData || roomData.length === 0) {
    return {
      adult: DEFAULT_TRAVELLER_COUNT,
      child: 0,
      infant: 0,
      totalChild: 0,
      totalPax: DEFAULT_TRAVELLER_COUNT,
      roomCount: 1,
    };
  }
  const travellersObj = {
    adult: 0,
    child: 0,
    infant: 0,
    totalChild:0,
    totalPax:0,
    roomCount:0,
  };
  for (let i = 0; i < roomData.length; i += 1) {
    const room = roomData[i];
    travellersObj.adult += room.noOfAdults;
    travellersObj.child += (room.noOfChildrenWB + room.noOfChildrenWOB);
    travellersObj.infant += room.noOfInfants;
  }
  travellersObj.totalChild = travellersObj.child + travellersObj.infant;
  travellersObj.totalPax = travellersObj.adult + travellersObj.child + travellersObj.infant;
  travellersObj.roomCount = roomData.length;
  return travellersObj;
};

export const convertDeepLinkDateToRequestDate = (deepLinkDate) => {
  if (deepLinkDate && deepLinkDate.includes('-')) {
    const dateParams = deepLinkDate.split('-');
    if (dateParams.length === 3) {
      return `${dateParams[0]}-${dateParams[1]}-${dateParams[2]}`;
    }
    return null;
  }
  return null;
};

export const sanetizeRequestQuoteIdValue = (quoteRequestId) => {
  if (quoteRequestId) {
    return quoteRequestId.replaceAll(' ', '+');
  }
  return null;
};

export const createRoomDataFromRoomDetails = (roomDetails) => {
  const roomData = [];
  for (let i = 0; i < roomDetails.length; i += 1) {
    const room = roomDetails[i];
    roomData.push({
      adultCount: room.noOfAdults,
      childCount: room.noOfChildrenWB + room.noOfInfants,
      infantCount: room.noOfInfants,
      childAgeArray: createChildAgeArray(room.noOfChildrenWB + room.noOfInfants, room.listOfAgeOfChildrenWB),
    });
  }
  return roomData;
};

export const createRoomDataFromRoomDetailsPhoenix = (
  roomDetails,
  childAgeArray,
  withoutChildBed,
) => {
  const roomData = [];
  for (let i = 0; i < roomDetails.length; i += 1) {
    const room = roomDetails[i];
    let ageArray;
    // Check if childAgeArray is available in room.
    if (has(room, 'childAgeArray')) {
      ageArray = room.childAgeArray;
    } else {
      // when childAgeArray is not available in the room, check the childAgeArray passed in the argument.
      // The childAgeArray passed in the argument of the method is the list of child array. It's length should be equal to the length of the total rooms.
      ageArray = childAgeArray && childAgeArray.length >= i + 1 ? childAgeArray[i] : [];
    }

    roomData.push({
      adultCount: room.noOfAdults,
      childCount: !withoutChildBed
        ? room.noOfChildrenWB + room.noOfChildrenWOB + room.noOfInfants
        : (room?.noOfChildren || 0) + (room?.noOfInfants || 0),
      infantCount: room.noOfInfants,
      childAgeArray: ageArray,
    });
  }
  return roomData;
};

const createChildAgeArray = (childCount, ageArray) => {
  const childAgeArray = [];
  for (let i = 0; i < childCount; i += 1) {
    childAgeArray.push(ageArray.length > i ? ageArray[i] : 1);
  }
  return childAgeArray;
};

export const createRoomDetailsFromRoomData = (roomData) => {
  const roomDetails = [];
  for (let i = 0; i < roomData.length; i += 1) {
    const room = roomData[i];
    roomDetails.push({
      noOfAdults: room.adultCount,
      noOfChildrenWB: room.childCount - room.infantCount,
      noOfInfants: room.infantCount,
      listOfAgeOfChildrenWB: room.childAgeArray,
      noOfChildrenWOB: 0,
    });
  }
  return roomDetails;
};

function updateBedRequiredForMaxAge(data) {
  if (data.length > 1) {
    let maxAge = Math.max(...data.map(obj => obj.age));
    let countMaxAge = 0;

    data.forEach(obj => {
      obj.bedRequired = obj.age === maxAge && countMaxAge++ === 0;
    });
  }

  return data;
}

/**
 * This method has to be used in new Phoenix detail page.
 * @param roomData
 * @param packagePaxDetail
 * @returns {[]}
 */
export const createRoomDetailsFromRoomDataForPhoenix = (roomData, packagePaxDetail) => {
  const roomDetails = [];
  const {minChildAge, childWithBed = false} = packagePaxDetail || {};
  // MIN_CHILD_AGE is the default child age.
  // minChildAge is the age we received through backend.
  const minimumChildAge = childWithBed ? minChildAge : MIN_CHILD_AGE;
  for (let i = 0; i < roomData.length; i += 1) {
    const room = roomData[i];

    // Initialize default data;
    let noOfInfants = 0;
    let noOfChildrenWB = 0;
    let noOfChildrenWOB = 0;
    let listOfAgeOfChildrenWB = [];
    let listOfAgeOfChildrenWOB = [];
    let listOfInfAge = [];
    let {childAgeArray} = room || {};
    // we want only one child with bed required
    childAgeArray = childWithBed ? updateBedRequiredForMaxAge(childAgeArray) : childAgeArray;
    childAgeArray.forEach(item => {
      const {age, bedRequired} = item || {};
      if (age) {
        if (age >= minimumChildAge) {
          if (bedRequired) {
            ++noOfChildrenWB;
            listOfAgeOfChildrenWB.push(age);
          } else {
            ++noOfChildrenWOB;
            listOfAgeOfChildrenWOB.push(age);
          }
        } else {
          ++noOfInfants;
          listOfInfAge.push(age);
        }
      }
    });

    if (childWithBed && childAgeArray?.length > 1 && noOfChildrenWB === 0 && noOfInfants > 1) {
      const age = listOfInfAge.pop();
      --noOfInfants;
      ++noOfChildrenWB;
      listOfAgeOfChildrenWB.push(age);
    }

    const data = {
      noOfAdults: room.adultCount,
      noOfInfants,
      listOfInfAge,
      noOfChildrenWB,
      noOfChildrenWOB,
      ...(!childWithBed && {noOfChildren: noOfChildrenWB + noOfChildrenWOB}),
      listOfAgeOfChildrenWB,
      listOfAgeOfChildrenWOB,
    };
    roomDetails.push(data);
  }
  return roomDetails;
};

export const getRoomDataForPdt = data => {
  if (data === undefined) {
    return '';
  }
  let retString = '';
  data?.forEach((item, index) => {
        const {
          noOfAdults,
          noOfInfants,
          noOfChildrenWB,
          noOfChildrenWOB,
        } = item || {};
        // Format : --> Room 1 - A:2, C:2, I:1; Room 2 -
        retString = retString + `Room ${index + 1} - A:${noOfAdults}, C:${noOfChildrenWB + noOfChildrenWOB}, I:${noOfInfants} ;`;
      },
  );
  return retString;
};

export const getPaxCountInfoForTotalRooms = rooms => {
  if(rooms)
  {
  const adult = getPaxCount(rooms, 'noOfAdults');
  const child = getPaxCount(rooms, 'noOfChildrenWOB') + getPaxCount(rooms, 'noOfChildrenWB');
  const noOfRooms = rooms.length;
  const infantCount = getPaxCount(rooms, 'noOfInfants');
  //FromCity: Name, Dest: Name, Pax: Adult|Child|Infant|Total, Date: abcd, Filters: a,b,c,d
  return `${adult}|${child}|${infantCount}|${adult + child + infantCount}`;
  }
  return '2|0|0|2';
};

export const createRoomDetailsFromApi = (roomDetails) => {
  const roomData = [];
  for (let i = 0; i < roomDetails.length; i += 1) {
    const room = roomDetails[i];
    roomData.push({
      noOfAdults: room.noOfAdults,
      noOfChildrenWB: room.noOfChildrenWB,
      noOfChildrenWOB: room.noOfChildrenWOB,
      noOfInfants: room.noOfInfants,
      listOfAgeOfChildrenWB: isNullOrEmptyCollection(room.listOfAgeOfChildrenWB) ? [] : room.listOfAgeOfChildrenWB,
      listOfAgeOfChildrenWOB: isNullOrEmptyCollection(room.listOfAgeOfChildrenWOB) ? [] : room.listOfAgeOfChildrenWOB,
      listOfInfAge: isNullOrEmptyCollection(room.listOfInfAge) ? [] : room.listOfInfAge,
    });
  }
  return roomData;
};

export const createPackageDetailDto = (packageDetail = {}, roomDetails, fabCta) => {
  const roomsList = cloneDeep(roomDetails);
  for (let i = 0; i < roomsList.length; i += 1) {
    roomsList[i].paxCount = 0;
    roomsList[i].noOfPaxBooked = 0;
    roomsList[i].roomFilled = false;
  }
  const showChat = fabCta && fabCta.showChat;
  const packageDetailDto = {
    packageName: packageDetail.name,
    branch: packageDetail.metadataDetail.branch,
    destList: '',
    pkgType: DFIT_PKG_TYPE,
    creationTimestamp: new Date().getTime(),
    tagDest: packageDetail.tagDestination.name,
    pkgWithoutFlight: !packageDetail.packageInclusionsDetail.flights,
    depCityName: packageDetail.departureDetail.cityName,
    depCityId: packageDetail.departureDetail.cityId,
    duration: packageDetail.destinationDetail.duration,
    dynamicPkg: true,
    packageCategoryId: getDefaultCategoryId(packageDetail),
    selectedRateId: getDefaultCategoryId(packageDetail),
    packageId: packageDetail.id,
    dynamicPackageId: packageDetail.dynamicId,
    dFD: false,
    onlineOnly: false,
    chatButton: showChat,
    roomsList,
    depDate: (createDateFromString(packageDetail.departureDetail.departureDate)).getTime(),
    departureDateStr: packageDetail.departureDetail.departureDate,
  };
  return packageDetailDto;
};

export const createReviewCategoryModel = packageDetail => (
  {
    catName: fetchDefaultCategoryName(packageDetail),
    maxHtlStar: packageDetail.hotelDetail.hotelCategoryDetails[0].maxStarRating,
    minHtlStar: fetchMinHtlStarRating(packageDetail),
    displayPrice: packageDetail.pricingDetail.categoryPrices[0].price,
  }
);

const fetchDefaultCategoryName = (packageDetail) => {
  const defaultCategoryId = getDefaultCategoryId(packageDetail);
  let defaultCategoryName = '';
  const {packageCategories} = packageDetail.categoryDetail;
  for (let i = 0; i < packageCategories.length; i += 1) {
    if (packageCategories[i].id === defaultCategoryId) {
      defaultCategoryName = packageCategories[i].name;
      break;
    }
  }
  return defaultCategoryName;
};

const getDefaultCategoryId = packageDetail => (
  packageDetail.pricingDetail.categoryPrices[0].categoryId
);

const fetchMinHtlStarRating = (packageDetail) => {
  let minHotelStarRating = 6;
  const {hotels} = packageDetail.hotelDetail.hotelCategoryDetails[0];
  for (let i = 0; i < hotels.length; i += 1) {
    if (hotels[i].starRating < minHotelStarRating) {
      minHotelStarRating = hotels[i].starRating;
    }
  }
  return minHotelStarRating;
};


export const createReviewRequest = async (mPackageDetailDTO, searchKey, email) => {
  const mHolidayReviewRequest = await createBaseRequest();
  mHolidayReviewRequest.lob = REQUEST_LOB;
  mHolidayReviewRequest.packageClassId = `${mPackageDetailDTO.packageCategoryId}`;
  mHolidayReviewRequest.packageId = `${mPackageDetailDTO.packageId}`;
  mHolidayReviewRequest.searchKey = searchKey;
  mHolidayReviewRequest.bookingInfo = makeBookingInfo(mPackageDetailDTO);
  mHolidayReviewRequest.requestId = createRandomString();
  mHolidayReviewRequest.customisationSessionId = createRandomString();
  mHolidayReviewRequest.creationTimestamp = mPackageDetailDTO.creationTimestamp;
  mHolidayReviewRequest.dynamicPackageId = mPackageDetailDTO.dynamicPackageId;
  mHolidayReviewRequest.baseRate = {id: 0};
  if (email !== '') {
    mHolidayReviewRequest.emailId = email;
  }
  return mHolidayReviewRequest;
};

export const makeBookingInfo = (mPackageDetailDTO) => {
  const bookingInfo = {
    departureDate: `${mPackageDetailDTO.departureDateStr}T00:00:00+05:30`,
    dynamicBooking: true,
    roomCount: mPackageDetailDTO.roomsList.length,
    rooms: mPackageDetailDTO.roomsList,
    baseRate: {id: 0},
  };
  if (NO_DEPARTURE_CITY !== mPackageDetailDTO.depCityName) {
    bookingInfo.depCity = {
      name: mPackageDetailDTO.depCityName,
      id: mPackageDetailDTO.depCityId,
      valid: true,
    };
  }
  return bookingInfo;
};

export const calculateInfants = (tempChildAgeArray) => {
  let infants = 0;
  for (let j = 0; j < tempChildAgeArray.length; j += 1) {
    if (tempChildAgeArray[j] < MIN_CHILD_AGE && tempChildAgeArray[j] > 0) {
      infants += 1;
    }
  }
  return infants;
};

export const calculateInfantsForPhoenix = (tempChildAgeArray) => {
  let infants = 0;
  for (let j = 0; j < tempChildAgeArray.length; j += 1) {
    if (tempChildAgeArray[j].age < MIN_CHILD_AGE && tempChildAgeArray[j].age > 0) {
      infants += 1;
    }
  }
  return infants;
};

export const createErrorMap = (errorDetails, holidayDetailData, roomDetails) => {
  const loggingMap = initializeLoggingMap();
  loggingMap.otherDetails = {
    travel_start_date: holidayDetailData.departureDetail && holidayDetailData.departureDetail.departureDate ? holidayDetailData.departureDetail.departureDate : '',
    last_page_name: holidayDetailData.last_page_name,
  };
  const paxCount = {
    adultCount: getPaxCount(roomDetails, 'noOfAdults'),
    childCount: getPaxCount(roomDetails, 'noOfChildrenWB'),
    infantCount: getPaxCount(roomDetails, 'noOfInfants'),
    roomCount: roomDetails.length,
  };
  loggingMap.packageDetails = getPackageDetailErrorLogMap(holidayDetailData, paxCount);
  loggingMap.searchCriteriaAppliedMap = createSearchCriteriaAppliedMap(holidayDetailData);
  loggingMap.requestDetails =
    createRequestDetailsLoggingMap(holidayDetailData.cmp, holidayDetailData.isWG);
  loggingMap.errorDetails = errorDetails;
  const packageDetailsError = {};
  packageDetailsError.packageId = holidayDetailData.packageId;
  packageDetailsError.name = holidayDetailData.name;
  loggingMap.packageDetailsError = packageDetailsError;
  return loggingMap;
};

const getPackageDetailErrorLogMap = (holidayDetailData, paxCount) => {
  const packageDetailsMap = {};
  if (holidayDetailData) {
    packageDetailsMap.pkg_nm = holidayDetailData.name;
    packageDetailsMap.pkg_hld_id = holidayDetailData.packageId;
    packageDetailsMap.pkg_tag_dest = holidayDetailData.destinationDetail.tagDestination;
    packageDetailsMap.pd_px_ad = paxCount.adultCount;
    packageDetailsMap.pd_px_ch = paxCount.childCount;
    packageDetailsMap.pd_px_inf = paxCount.infantCount;
    packageDetailsMap.roomCount = paxCount.roomCount;
    packageDetailsMap.pkg_hld_durn = holidayDetailData.destinationDetail.duration;
    packageDetailsMap.pkg_hld_type = holidayDetailData.packageType;
    packageDetailsMap.pkg_catgry = holidayDetailData.categoryId;
  }
  return packageDetailsMap;
};

export const getStoryImages = (imageDetails, storyImageSize) => {
  const images = [];
  if (imageDetails && isNotNullAndEmptyCollection(imageDetails.componentImages)) {
    imageDetails.componentImages.forEach((image) => {
      const imgPath = `${image.path.split('?')[0]}?${storyImageSize ?
          storyImageSize : STORY_IMAGE_SIZE_ATTR}`;
      images.push({
        title: image.title,
        path: imgPath,
      });
    });
  }
  return images;
};

export const createHolidayDetailData =
  (similarPackageDetail, currentHolidayDetailData, roomDetails, storyImageSize, departureCity) => {
    const holidayDetailData = cloneDeep(currentHolidayDetailData);
    holidayDetailData.packageId = similarPackageDetail.packageId;
    holidayDetailData.branch = similarPackageDetail.branch;
    holidayDetailData.name = similarPackageDetail.packageName;
    holidayDetailData.inclusionsDetail = {
      flights: similarPackageDetail.inclusionDetails && similarPackageDetail.inclusionDetails.flights,
    };
    holidayDetailData.categoryId = similarPackageDetail.defaultCategory;
    holidayDetailData.departureDetail.departureCity = departureCity;

    let depDate = '';
    if (similarPackageDetail && similarPackageDetail.categoryDetails
      && isNotNullAndEmptyCollection(similarPackageDetail.categoryDetails)) {
      /*depDate = similarPackageDetail.categoryDetails.filter(categoryPrice =>
        categoryPrice.categoryId ===
        similarPackageDetail.defaultCategory)[0].departureDate;*/
    }

    if (depDate) {
      if (depDate.includes('T')) {
        const dateParams = depDate.split('T');
        if (dateParams.length === 2) {
          holidayDetailData.departureDetail.departureDate = dateParams[0];
        }
      } else {
        holidayDetailData.departureDetail.departureDate = depDate;
      }
    }
    holidayDetailData.destinationDetail = {
      tagDestination: similarPackageDetail.tagDestination,
      duration: similarPackageDetail.nights,
    };
    holidayDetailData.images = getStoryImages(similarPackageDetail.imageDetails, storyImageSize);
    holidayDetailData.rooms = roomDetails;
    holidayDetailData.savePackageId = null;
    holidayDetailData.dynamicPackageId = null;
    return holidayDetailData;
  };

export const getPlacesTitle = (hotels) => {
  let title = '';
  if (isNotNullAndEmptyCollection(hotels) && hotels.length > 1) {
    title = 'Around ';
    for (let index = 0; index < hotels.length; index += 1) {
      if (hotels[index].cityName) {
        if (index === hotels.length - 1) {
          title += ' and ';
          title += `${hotels[index].cityName}`;
        } else if (index === 0) {
          title += `${hotels[index].cityName}`;
        } else {
          title += ' , ';
          title += `${hotels[index].cityName}`;
        }
      }
    }
  } else if (isNotNullAndEmptyCollection(hotels) && hotels.length === 1
      && hotels[0].cityName) {
    title = `Around ${hotels[0].cityName}`;
  } else {
    title = 'Explore Sightseeing Spots';
  }
  return title;
};

export const getActionData = (add, packageComponent) => {
  let eventName = '';
  let actionLoadingText = '';
  if (packageComponent === packageActionComponent.FLIGHT) {
    if (add) {
      eventName = PDTConstants.ADD_FLIGHT;
      actionLoadingText = 'Adding flight to the package';
    } else {
      eventName = PDTConstants.REMOVE_FLIGHT;
      actionLoadingText = 'Removing flight from the package';
    }
  } else if (packageComponent === packageActionComponent.TRANSFER || packageComponent === packageActionComponent.CAR) {
    if (add) {
      eventName = PDTConstants.ADD_TRANSFER;
      actionLoadingText = 'Adding transfer to the package';
    } else {
      eventName = PDTConstants.REMOVE_TRANSFER;
      actionLoadingText = 'Removing transfer from the package';
    }
  } else if (packageComponent === packageActionComponent.ACTIVITY) {
    if (add) {
      eventName = PDTConstants.ADDED_ACTIVITY;
      actionLoadingText = 'Adding activity to the package';
    } else {
      eventName = PDTConstants.REMOVE_ACTIVITY_ALL;
      actionLoadingText = 'Removing activity from the package';
    }
  }

  return {eventName, actionLoadingText};
};

export const getActionErrorMessage = (action, packageComponent) => {
  let actionMessage = DETAIL_REVIEW_ERROR_MSG;
  if (packageComponent === packageActionComponent.FLIGHT && action === packageActions.CHANGE) {
    actionMessage = DETAILS_FLIGHT_CHANGE_ERROR_MSG;
  } if (packageComponent === packageActionComponent.FLIGHT && action === packageActions.REMOVE) {
    actionMessage = REMOVE_FLIGHT_CHANGE_ERROR_MSG;
  } else if (packageComponent === packageActionComponent.ACTIVITY && action === packageActions.REMOVE) {
    actionMessage = REMOVE_ACTIVITY_DETAILS_ERROR_MSG;
  } else if (packageComponent === packageActionComponent.ACTIVITY && action === packageActions.CHANGE) {
    actionMessage = REMOVE_ACTIVITY_DETAILS_ERROR_MSG;
  } else if (packageComponent === packageActionComponent.ACTIVITY && action === packageActions.ADD) {
    actionMessage = ADD_ACTIVITY_DETAILS_ERROR_MSG;
  }
  return actionMessage;
};

export const getFreeCancellationBannerText = (zcOptions) => {
  const isFlexiDate = isFlexiDateAvailable(zcOptions);
  const isZC = isZCAvailable(zcOptions);
  if (isFlexiDate && isZC) {
    return 'Worried about Changes & Cancellations? ';
  } else if (isFlexiDate) {
    return 'Worried about Change in the Plan?';
  } else if (isZC) {
    return 'Worried about Cancellations?';
  } else {
    return '';
  }
};

export const getFreeCancellationBannerSubText = (zcOptions) => {
  const isFlexiDate = isFlexiDateAvailable(zcOptions);
  const isZC = isZCAvailable(zcOptions);
  if (isFlexiDate && isZC) {
    return 'Opt for Zero Cancellation or Flexi Date Protection plan and enjoy flexibility with your package in case you change your mind! ';
  } else if (isFlexiDate) {
    return 'Opt for Flexi Date Protection plan and enjoy flexibility with your package in case you change your mind!';
  } else if (isZC) {
    return 'Opt for Zero Cancellation and enjoy flexibility with your package in case you change your mind!  ';
  } else {
    return '';
  }
};

export const getStarConditionText = (cancellationPolicyData, activeTabIndex, pageName) => {
  // Hide star condition view for review pages.
  if (pageName === HOLIDAY_REVIEW_PAGE) {
    return '';
  }
  const {penaltyDetail} = cancellationPolicyData;
  const {zcOptions} = penaltyDetail;

  const isFlexiDate = isFlexiDateAvailable(zcOptions);
  const isZC = isZCAvailable(zcOptions);

  if (activeTabIndex === CANCELLATION && isZC) {
    return '* You can opt for Zero Cancellation Plan on Review Page ';
  } else if (activeTabIndex === DATE_CHANGE && isFlexiDate) {
    return '* You can opt for FlexiDate Plan on Review Page ';
  } else {
    return '';
  }
};

export const getHotelRating = (ratingType, taInfo, mmtRatingInfo) => {
  if (ratingType && ratingType === 'MMT' && mmtRatingInfo && has(mmtRatingInfo, 'userRating')) {
    return mmtRatingInfo.userRating;
  } else if (ratingType && taInfo && has(taInfo, 'userRating')) {
    return taInfo.userRating;
  } else {
    return '';
  }
};
export const createPersuasionDataFromResponseV2 = (persuasionDataResponse) => {
  let persuasionData = [];
  persuasionDataResponse?.map((persuasion)=>{
    if (persuasion?.type == 'PERSUASION_V2'){
        persuasionData = persuasionData?.concat({
          type: viewTypes.LISTING_PRS_PERSUSAION,
          data: persuasion,
        });
    }
    else if (persuasion?.type == 'PERSUASION'){
      persuasionData = persuasionData?.concat({
        type: viewTypes.LISTING_WPM_PERSUASION,
        data: persuasion,
      });
    }
  });
  const compareFunc = function (o1, o2) {
    return o1?.data?.order - o2?.data?.order;
  };
  persuasionData?.sort(compareFunc);
  return persuasionData;
};

export const getHotelStarRating = (packageDetailData) => {
  return packageDetailData?.hotelDetail?.hotelCategoryDetails?.[0]?.hotels?.[0]?.starRating || '';
};

export const toggleDayPlan = (currentActivePlanOnItinerary, packageInclusionsDetail, toggleDayPlan, basePackageInclusionsDetail = {}) => {
  const {flights, hotels, carItinerary, cabItinerary, cityDrops, activities, airportTransfers, visa} = packageInclusionsDetail || {};
  const { visa : isPkgVisa } = basePackageInclusionsDetail || {};

  if (currentActivePlanOnItinerary !== TABS.DAY) {
    switch (currentActivePlanOnItinerary) {
      case TABS.FLIGHT:
        if (!flights) {
          toggleDayPlan(TABS.DAY);
        }
        break;
      case TABS.HOTEL:
        if (!hotels) {
          toggleDayPlan(TABS.DAY);
        }
        break;
      case TABS.TRANSFER:
        if (!(carItinerary || cabItinerary || cityDrops || airportTransfers)) {
          toggleDayPlan(TABS.DAY);
        }
        break;
      case TABS.ACTIVITIES:
        if (!(activities || carItinerary)) {
          toggleDayPlan(TABS.DAY);
        }
        break;
      case TABS.VISA:
        if (!(isPkgVisa || visa)) {
          toggleDayPlan(TABS.DAY);
        }
        break;
      case TABS.COMMUTE:
        if (!(carItinerary || cabItinerary || cityDrops || airportTransfers || flights)) {
          toggleDayPlan(TABS.DAY);
        }
        break;
      default:
        break;
    }
  }
};
export const constructPolicyData = ({ penaltyDetails = {} }) => {
  const tabList = [];
  let penaltyList = {};
  let zcOptions = [];
  const { cancellationPenalty = {}, dateChangePenalty = {}, zcOptions: zcOptionsObj = {}} = penaltyDetails || {};
  if (cancellationPenalty) {
    penaltyList[CANCELLATION_POLICY] = cancellationPenalty;
    tabList.push(CANCELLATION_POLICY);
  }
  if (dateChangePenalty) {
    penaltyList[DATE_CHANGE_POLICY] = dateChangePenalty;
    tabList.push(DATE_CHANGE_POLICY);
  }

  if (zcOptionsObj) {
    zcOptions = zcOptionsObj;
  }

  return { tabList, penaltyList, zcOptions };
};
