// To-do @vatsala.mittal - will update code after getting response for selectable meal options

import React, {useState} from 'react';
import {FlatList, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import RadioGroupButton from '../../../Common/Components/RadioButtonGroup';
import {rupeeFormatterUtils} from '../../../utils/HolidayUtils';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';

const SelectableMealOption = ({
  options,
  updateMeal,
  packagePriceMap,
  discountedFactor,
  selectedMealCode,
}) => {
  const initialSelection = options.findIndex((item) => item.mealCode === selectedMealCode);
  const [showSelection, setShowSelection] = useState(initialSelection);

  const toggleSelection = (index, mealCode) => {
    setShowSelection(showSelection === index ? null : index);
    updateMeal(mealCode);
  };

  const getPriceDiff = (mealCode, selectedMealCode) => {
    const selectedMealPrice = packagePriceMap[selectedMealCode];
    const mealPrice = packagePriceMap[mealCode];
    return Math.ceil((mealPrice - selectedMealPrice) * discountedFactor);
  };

  const renderMealItem = ({ item, index }) => {
    const priceDiffValue = getPriceDiff(item.mealCode, selectedMealCode);

    const handleSelectBtn = () => {
      toggleSelection(index, item.mealCode);
    };
    return (
      <View style={styles.mealItem}>
        <RadioGroupButton
          optionText={item.mealName}
          selected={index === showSelection}
          optionTextStyle={styles.mealText}
          handleClick={handleSelectBtn}
        >
          {index === showSelection ? (
            <Text style={[styles.optionText, styles.optionSelected]}>Included</Text>
          ) : (
            <Text
              style={[
                styles.optionText,
                {
                  color: priceDiffValue >= 0 ? '#eb2026' : '#1a7971',
                },
              ]}
            >
              {`${priceDiffValue >= 0 ? '+' : ''} ${rupeeFormatterUtils(priceDiffValue)}/P`}
            </Text>
          )}
        </RadioGroupButton>
      </View>
    );
  };
  return (
    <View style={styles.container}>
      <FlatList data={options} renderItem={renderMealItem} />
    </View>
  );
};

const styles = StyleSheet.create({
  description: {
    marginVertical: 11,
    fontFamily: 'Lato',
    fontSize: 12,
    color: holidayColors.gray,
    marginHorizontal: 50,
  },
  tick: {
    width: 14,
    height: 14,
    marginRight: 8,
  },
  selectedText: {
    color: holidayColors.primaryBlue,
    fontFamily: 'Lato-Bold',
    fontSize: 10,
  },
  price: {
    fontSize: 12,
    fontFamily: 'Lato',
  },
  mealText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
  },
  optionSelected: {
    color: holidayColors.green,
  },
  optionText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
  },
  optionContainer: {
    width: '100%',
    backgroundColor: holidayColors.white,
    paddingVertical: 13,
    paddingLeft: 37,
    paddingRight: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 4,
    shadowColor: 'rgba(0, 0, 0, 0.4)',
    shadowOpacity: 0.4,
    shadowRadius: 2,
    elevation: 1,
  },
  activeOptionContainer: {
    borderColor: holidayColors.primaryBlue,
    borderWidth: 1,
    paddingLeft: 15,
  },
  container: {
    paddingHorizontal: 16,
  },
  heading: {
    marginTop: 30,
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  subHeading: {
    marginTop: 4,
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    color: holidayColors.lightGray,
  },
  mb10: { marginBottom: 10 },
  noteCategory: {
    backgroundColor: holidayColors.lightGray2,
    borderRadius: 2,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  noteText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 11 * 1.2,
  },
  leftCategoryContent: {
    width: '60%',
  },
  selectBtn: {
    backgroundColor: holidayColors.primaryBlue,
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  selectBtnText: {
    color: holidayColors.white,
    ...fontStyles.labelBaseBold,
  },
  mealItem: {
    paddingHorizontal: 10,
    flexDirection: 'column',
    paddingVertical: 5,
  },
});

export default SelectableMealOption;
