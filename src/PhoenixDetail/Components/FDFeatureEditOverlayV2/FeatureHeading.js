import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import TickIcon from '@mmt/legacy-assets/src/holidays/tick_icon.png';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { isEmpty } from 'lodash';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';

const FeatureHeading = ({ heading = '', description = '', children = null }) => {
  if (isEmpty(heading)) {
    return null;
  }
  return (
    <View style={styles.container}>
      <HolidayImageHolder defaultImage={TickIcon} style={styles.tickIcon} />
      <View style={styles.headingContainer}>
        {!!heading && <Text style={styles.heading}>{heading}</Text>}
        {!!description && (
          <View style={styles.descriptionContainer}>
            <Text style={styles.description}>{description}</Text>
          </View>
        )}
      </View>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    width: '100%',
  },
  headingContainer: {
    width: '100%',
    flex: 1,
  },
  tickIcon: {
    tintColor: holidayColors.black,
    width: 20,
    height: 20,
    marginRight: 10,
  },
  heading: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  descriptionContainer: {
    marginTop: 6,
  },
  description: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    lineHeight: 19,
  },
});

export default FeatureHeading;
