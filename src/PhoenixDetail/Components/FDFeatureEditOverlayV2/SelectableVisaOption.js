import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, FlatList } from 'react-native';
import { RadioButton } from '@mmt/legacy-commons/Common/Components/Radio';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import RadioGroupButton from '../../../Common/Components/RadioButtonGroup';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';

const SelectableVisaOption = (props) => {
  const { packagePriceMap, updateVisa, discountedFactor, options, containerStyles } = props;
  if (options.length <= 1) {
    return null;
  }
  const [showSelection, setShowSelection] = useState(options.findIndex((item) => item.selected));
  const [selectedVisaPrice, setSelectedVisaPrice] = useState(
    packagePriceMap[options.find((item) => item.selected)?.sellableId],
  );

  const toggleSelection = (index, sellableId, toggleAction) => {
    if (index !== showSelection) {
      setShowSelection(index);
      setSelectedVisaPrice(packagePriceMap[sellableId]);
      updateVisa(sellableId, toggleAction);
    }
  };

  const getPriceDiff = (sellableId) => {
    const visaPrice = packagePriceMap[sellableId];
    return Math.ceil((visaPrice - selectedVisaPrice) * discountedFactor);
  };

  const renderVisaOptions = ({ item: option, index }) => {
    const { sellableId, toggleAction, name = '' } = option;
    const priceDiffVal = getPriceDiff(sellableId);
    const isPriceDiffMore = priceDiffVal >= 0;
    const handleClick = () => {
      toggleSelection(index, sellableId, toggleAction);
    };
    return (
      <View style={styles.visaItem} key={index}>
        <RadioGroupButton
          optionText={name}
          selected={index === showSelection}
          optionTextStyle={styles.optionText}
          handleClick={handleClick}
        >
          <View style={styles.justifyCenter}>
            {index === showSelection ? (
              <View>
                <Text style={[styles.selectedText]}>SELECTED</Text>
              </View>
            ) : (
              <View>
                <Text
                  style={[
                    styles.price,
                    {
                      color: isPriceDiffMore ? '#eb2026' : '#1a7971',
                    },
                  ]}
                >
                  {isPriceDiffMore ? '+ ' : '- '}
                  {'\u20B9'}
                  {rupeeFormatter(Math.abs(priceDiffVal))}/p
                </Text>
              </View>
            )}
          </View>
        </RadioGroupButton>
      </View>
    );
  };
  return (
    <View style={[styles.container, containerStyles]}>
      <FlatList
        data={options}
        renderItem={renderVisaOptions}
        listKey={(item, index) => `_key${index.toString()}`}
        keyExtractor={(item, index) => `_key${index.toString()}`}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 15,
  },
  selectedText: {
    color: holidayColors.primaryBlue,
    fontFamily: 'Lato-Bold',
    fontSize: 10,
  },
  price: {
    ...fontStyles.labelSmallRegular,
  },
  optionText: {
    color: holidayColors.black,
    ...fontStyles.labelBaseRegular,
  },
  optionContainer: {
    width: '100%',
    backgroundColor: holidayColors.white,
    paddingHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  btnContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activeOptionContainer: {
    paddingLeft: 15,
  },
  heading: {
    marginTop: 30,
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  subHeading: {
    marginTop: 4,
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  mb10: { marginBottom: 10 },
  visaItem: {
    paddingHorizontal: 5,
    flexDirection: 'column',
    paddingVertical: 5,
    marginLeft: 8,
  },
  justifyCenter :{justifyContent:'center'},
});

export default SelectableVisaOption;
