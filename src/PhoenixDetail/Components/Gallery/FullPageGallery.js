import React, { useEffect, useRef, useState } from 'react';
import { FlatList, Image, StatusBar, StyleSheet, Text, TouchableOpacity, View ,Dimensions} from 'react-native';
import { HtmlHeading } from '../DayPlan/HtmlHeading';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import { HolidayNavigation } from '../../../Navigation';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import {hideOverlays} from '../DetailOverlays/Redux/DetailOverlaysActions';
import {isAndroidClient, isRawClient} from '../../../utils/HolidayUtils';
import {Overlay} from '../DetailOverlays/OverlayConstants';
import {connect} from "react-redux";
import HolidayDataHolder from '../../../../src/utils/HolidayDataHolder';
import { SUB_PAGE_NAMES } from '../../../../src/HolidayConstants';

const screenWidth = Dimensions.get('screen').width;
const viewAreaThreshold = 51;
const startGradient = { x: 0.0, y: 0.0 };
const endGradient = { x: 0.0, y: 1.0 };
const firstElementIndex = 0;
const FullPageGallery = ({
  imageList,
  destCity,
  usp,
  name = '',
  index = 0,
  RenderDescription = null,
  showDescription = false,
  hideOverlays,
  isGalleryV2 = false,
  back = ()=>{},
}) => {
  const imagelistNew = imageList.map((image,index)=>{
    return {index,image};
  });
  const [pageNumber,setPageNumber] = useState(index);
  const descriptionRef = useRef(null);
  const onViewableItemsChanged =  ({ viewableItems, changed }) => {
    const firstElement = viewableItems?.[firstElementIndex];
    if (firstElement?.index >= 0) {setPageNumber(firstElement?.item.index);}
};
const viewabilityConfig = { viewAreaCoveragePercentThreshold:viewAreaThreshold };
  const viewabilityConfigCallbackPairs = useRef([{ viewabilityConfig, onViewableItemsChanged }]);
  const flatListRef = useRef();
  const DescriptionView = () => {
    if (destCity && destCity.name && usp && usp.length > 0) {
      return (<View style={[styles.descriptionBox]}>
        <Text style={styles.infoTitle}>Explore {destCity.name}</Text>
        <HtmlHeading style={{heading: {...styles.infoDesc}}} htmlText={usp}/>
      </View>);
    }
    else if (isGalleryV2 && usp && usp.length > 0){
      return (<View style={styles.descriptionBox}>
       {!!usp?.[pageNumber] &&  <HtmlHeading style={{heading: {...styles.decriptionText}}} htmlText={usp?.[pageNumber]}/>}
      </View>);
    }
    else {
      return [];
    }
  };

  const onBackPressed = () => {
    if (isRawClient()) {
      hideOverlays([Overlay.FULL_PAGE_GALLERY]);
    } else {
      HolidayNavigation.pop();
    }
    back();
  };
  useEffect(() => {
    HolidayDataHolder.getInstance().setSubPageName(SUB_PAGE_NAMES.HOTEL_GALLERY)
    return () => {
      HolidayDataHolder.getInstance().clearSubPageName()
    }
  }, [])

  const renderItem = (item, index) => {
    return (
      <View style={styles.imageWrapper}>
        <PlaceholderImageView style={styles.carousalImage} url={item?.image} />
      </View>
    );
  };
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onBackPressed} style={{ flex: 1 }}>
          <Image style={styles.close} source={require('../images/close.png')} />
        </TouchableOpacity>
        <View style={[AtomicCss.marginRight10, { flex: 7 }]}>
          <Text style={styles.heading}>{name}</Text>
        </View>
        <View style={AtomicCss.flex1}>
          <Text style={styles.pageNumberActive}>
            <Text style={styles.activePage}>{pageNumber + 1}</Text>/{imageList.length}
          </Text>
        </View>
      </View>
      <View style={styles.carouselWrapper}>
        <FlatList
          data={imagelistNew}
          initialScrollIndex={pageNumber}
          horizontal
          renderItem={({ item, index }) => renderItem(item, index)}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          onScrollToIndexFailed={() => {}}
          snapToAlignment={'center'}
          ref={flatListRef}
          contentContainerStyle={{ alignItems: 'center' }}
          pagingEnabled
          snapToAlignment="start"
          decelerationRate={'fast'}
          snapToInterval={screenWidth}
          disableIntervalMomentum
          getItemLayout={(data, index) => ({
            length: screenWidth,
            offset: screenWidth * index,
            index,
          })}
          onViewableItemsChanged={
            viewabilityConfigCallbackPairs?.current?.[firstElementIndex].onViewableItemsChanged
          }
          viewabilityConfig={viewabilityConfigCallbackPairs?.current?.[firstElementIndex].viewabilityConfig}
        />
      </View>

      <DescriptionView />
      {RenderDescription && showDescription ? <RenderDescription ref={descriptionRef} /> : null}
    </View>
  );
};

const styles = StyleSheet.create({
  carousalCounterContainer: {
    top: 240,
    alignItems:'flex-end',
  },
  carousalCounterText: {
    fontSize: 11,
    lineHeight: 19,
    fontFamily: 'lato',
    fontWeight: '500',
    color: '#ccc',
  },
  imgDescContainer: {
    paddingHorizontal: 15,
    paddingVertical: 7,
    position: 'absolute',
    bottom: 0,
    zIndex: 1,
    width: '100%',
  },
  imgDesc: {
    fontSize: 11,
    lineHeight: 19,
    fontFamily: 'lato',
    fontWeight: '500',
    color: holidayColors.white,
  },
  infoBox: {
    backgroundColor: '#2e2e2e',
    borderRadius: 4,
  },
  descriptionBox: {
    width:'100%',
    paddingHorizontal: 15,
    paddingBottom:15,
    justifyContent:'flex-start',
    alignItems:'center',
    paddingTop:50,
    bottom:10,
    left:'auto',
    position:'absolute',
  },
  decriptionText: {
    ...fontStyles.labelSmallRegular,
    lineHeight: 19,
    color: holidayColors.white,
    justifyContent:'center',
  },
  infoTitle: {
    ...fontStyles.labelBaseBold,
    lineHeight: 19,
    color: holidayColors.white,
    marginBottom: 0,
  },
  infoDesc: {
    fontSize: 12,
    lineHeight: 16,
    fontFamily: 'lato',
    color: '#d0d0d0',
  },
  container: {
    flex: 1,
    backgroundColor: holidayColors.gray,
  },
  header: {
    flexDirection: 'row',
    marginTop: 32,
    alignItems: 'center',
  },
  close: {
    width: 24,
    height: 24,
    marginLeft: 15,
  },
  heading: {
    marginLeft: 22,
    marginRight: 22,
    lineHeight: 19,
    ...fontStyles.headingBase,
    color: holidayColors.white,
  },
  carousalContainer: {width: '100%', height: 230},
  imgContainer: {flex: 1},
  carousalImage: {
    height: '100%',
    width: '100%',
  },
  activeBullet: {
    transform: [{scale: 0.6}],
    backgroundColor: holidayColors.primaryBlue,
  },
  bullet: {
    transform: [{scale: 0.5}],
    backgroundColor: holidayColors.white,
    marginHorizontal: 2,
  },
  bulletsContainer: {
    bottom: -30,
  },
  pageNumberActive: {
    color: holidayColors.white,
    ...fontStyles.labelSmallRegular,
  },
  activePage: {
    ...fontStyles.labelSmallBlack,
  },
  carouselWrapper: {
    height: '80%',
    width: screenWidth,
    display: 'flex',
  },
  image: {
    ...StyleSheet.absoluteFillObject,
    resizeMode: 'cover',
  },
  imageWrapper:{width:screenWidth,height:230 },
});

const mapDispatchToProps = dispatch => ({
  hideOverlays: (keys) => dispatch(hideOverlays(keys)),
});

export default connect(null, mapDispatchToProps)(FullPageGallery);
