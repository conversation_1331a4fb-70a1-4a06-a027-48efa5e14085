import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
  FlatList,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { getImagesForDay, subTypes } from './GalleryUtils';
import vector from '../images/Vector.png';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const iconArrowDown = require('@mmt/legacy-assets/src/iconArrowDown.webp');
const options = ['Official Media', 'Traveller Media'];
const MAX_ROWS_TO_SHOW = 3;

const PropertyPhotos = ({
  name,
  checkIn,
  checkOut,
  roomType,
  components,
  GalleryImage,
  GalleryVideo,
  scrollTop,
  index,
  sectionIndex,
  city,
}) => {
  const [openDropDown, setOpenDropDown] = useState(false);
  const [selectedTravellerMedia, setSelectedMedia] = useState(false);
  const [openDropDownIndex, setOpenDropDownIndex] = useState(-1);
  const [showMore, setShowMore] = useState({
    isViewMore: false,
    showCompleteList: false,
  });
  const toggle = (index) => {
    setOpenDropDownIndex(index);
    setOpenDropDown(!openDropDown);
  };
  const renderListItem = ({ item, index: dataIndex, imageList, usps }) => {
    const { showCompleteList, isViewMore } = showMore || {};
    if (dataIndex > MAX_ROWS_TO_SHOW && !showCompleteList) {
      if (!isViewMore)
        {setShowMore((prevState) => ({
          ...prevState,
          isViewMore: true,
        }));}
      return;
    }
    const { videoUrl, imageDescription, fullPath, index, type } = item[0];
    switch (item?.length) {
      case 1:
        /* full width media to be displayed */
        if (type === subTypes.VIDEO && videoUrl) {
          return GalleryVideo(videoUrl);
        } else if (type === subTypes.IMAGE && fullPath) {
          return GalleryImage({
            imageList,
            usps,
            url: fullPath,
            index,
            imageDescription,
            imageStyle: { ...genericStyles.imageStyle, width: Dimensions.get('screen').width - 30 },
            city,
          });
        }
        break;
      case 2:
        /* 2 images to be displayed in a row */
        const { fullPath: url1, imageDescription: imageDescription1, index: index1 } = item[1];
        return getMultipleInlineImages({
          media: imageList,
          index,
          index1,
          usps,
          url: fullPath,
          imageDescription,
          url1,
          imageDescription1,
        });
    }
  };
  const getMultipleInlineImages = ({
    media,
    index,
    usps,
    url,
    imageDescription,
    index1,
    url1,
    imageDescription1,
  }) => {
    return (
      <View style={styles.imageWrapper}>
        <View style={styles.image}>
          {GalleryImage({
            imageList: media,
            index,
            usps,
            imageStyle: { ...genericStyles.imageStyle, width: (Dimensions.get('screen').width / 2) - 20 },
            url,
            imageDescription,
            city,
          })}
        </View>
        <View style={styles.image}>
          {GalleryImage({
            index: index1,
            imageList: media,
            usps,
            imageStyle: {
              ...genericStyles.imageStyle,
              width: (Dimensions.get('screen').width / 2) - 20,
              marginLeft: 5,
            },
            url: url1,
            imageDescription: imageDescription1,
            city,
          })}
        </View>
      </View>
    );
  };
  const getMediaList = () => {
    const list = [];
    let count = 0;
    let rowCount = 0;
    const filteredMediaList = components?.filter((el) => {
      return el.travellerMedia === selectedTravellerMedia;
    });
    const { mediaList: imageList, usps } = getImagesForDay(filteredMediaList);
    for (let index = 0; index < filteredMediaList?.length; index++) {
      const media = { ...filteredMediaList[index], index };
      const media2 = { ...filteredMediaList[index + 1], index: index + 1 };
      const isMultipleInlineImages =
        (index + 1) % 3 !== 0 /* every 3rd media ,needs to be displayed full width */ &&
        media?.type !== subTypes.VIDEO &&
        index + 1 < filteredMediaList?.length &&
        media2?.type !==
          subTypes.VIDEO /* check for media type,both media ,media 2 should be image to display in row */ &&
        media.travellerMedia ===
          selectedTravellerMedia /*type check wether official or traveller media */ &&
        media2.travellerMedia === selectedTravellerMedia;
      if (isMultipleInlineImages) {
        list.push([media, media2]);
        if (rowCount <= MAX_ROWS_TO_SHOW) {
          count = count + 2;
        }
        index++;
      } else {
        if (media.travellerMedia === selectedTravellerMedia) {
          list.push([media]);
        }
        if (rowCount <= MAX_ROWS_TO_SHOW) {
          count++;
        }
      }
      rowCount++;
    }
    return { list, imageList, usps ,count ,totalCount :filteredMediaList?.length};
  };
  const selected = (val) => {
    setSelectedMedia(val === options[0] ? false : true);
    setShowMore((prevState) => ({
      ...prevState,
      isViewMore: false,
    }));
    toggle();
  };
  const captureClickEvents = ({eventName, suffix = ''}) => {
    trackPhoenixDetailLocalClickEvent({eventName, suffix});
    logPhoenixDetailPDTEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value :  suffix ? `${eventName}${suffix}`: eventName,
    })

  }
  const showMoreButton = () => {
    setShowMore({
      showCompleteList: true,
      isViewMore: false,
    });
    captureClickEvents({
      eventName: 'Property_photos_Show_more',
    });
  };
  const showLessButton = () => {
    scrollTop({ index, sectionIndex });
    setTimeout(() => {
      setShowMore({
        showCompleteList: false,
        isViewMore: true,
      });
      captureClickEvents({
        eventName: 'Property_photos_Show_less',
      });
    }, 500);
  };
  const optionsClicked = (index)=>{
    captureClickEvents({
      eventName: 'Media_Type_click ',
    });
    toggle(index);
  };
  const getNoMedia = (mediaList) => {
    const [FIRST_OPTION, SECOND_OPTION] = options;
    const AVAILABLE_TEXT = 'available for this hotel';
    const OPTION_TEXT = selectedTravellerMedia ? SECOND_OPTION : FIRST_OPTION;
    return (
      mediaList?.length === 0 && (
        <View style={styles.noMediaWrapper}>
          <Image source={vector} style={styles.noMediaImage} />
          <Text style={styles.noMediaText}>
            No {OPTION_TEXT} {AVAILABLE_TEXT}
          </Text>
        </View>
      )
    );
  };
  const { list: mediaList, usps, imageList ,count,totalCount} = getMediaList();
  const { isViewMore, showCompleteList } = showMore || {};

  return (
    <View style={styles.propertyWrapper}>
      <View style={styles.hotelInfo}>
        <View style={styles.hotelDescription}>
          <View style={styles.displayRow}>
            {!!name && <Text style={styles.hotelNameText}>{name}</Text>}
          </View>
          <View style={[styles.displayRow]}>
            {!!city && <Text style={styles.city}>{city}</Text>}
            {!!checkIn && <Text style={styles.checkIn}>Check-In: {checkIn}</Text>}
            {!!checkOut && <Text style={styles.checkOut}>Check-Out: {checkOut}</Text>}
          </View>
        </View>

        <View style={[{ height:30 }]}>
        <TouchableOpacity onPress={() => optionsClicked(index)} style={[styles.inputWrapperSelect]}>
          <Text style={styles.input}>{selectedTravellerMedia ? options[1] : options[0]}</Text>
          <Image style={styles.iconArrowDown} source={iconArrowDown} />
        </TouchableOpacity>
        {openDropDown && openDropDownIndex === index && (
            <View style={styles.dropDown}>
                           <View>
                {options?.map((option, index) => (
                  <TouchableOpacity
                    key={option + index}
                    onPress={() => {
                      captureClickEvents({
                        eventName: `${option}_`,
                        suffix: index,
                      });
                      selected(option);
                    }}
                  >
                    <Text style={styles.dropdownText}>{option} </Text>
                    {index < options.length - 1 && <View style={styles.separator} />}
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}
        </View>
      </View>
      <View>
        {getNoMedia(mediaList)}
        <FlatList
          data={mediaList}
          renderItem={({ item, index }) => renderListItem({ item, index, imageList, usps })}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          onScrollToIndexFailed={() => {}}
          snapToAlignment={'center'}
          ListFooterComponent={() => {
            return (
              <>
                {isViewMore && (
                  <TouchableOpacity onPress={showMoreButton} style={styles.showMore}>
                    <Text style={styles.showMoreText}>{`Show ${totalCount - count} more Photos`.toUpperCase()}</Text>
                  </TouchableOpacity>
                )}
                {showCompleteList && (
                  <TouchableOpacity onPress={showLessButton} style={styles.showLess}>
                    <Text style={styles.showLessText}>SHOW LESS</Text>
                  </TouchableOpacity>
                )}
              </>
            );
          }}
        />
      </View>
    </View>
  );
};

const genericStyles = {
  imageStyle: {
    marginTop: 4,
    marginBottom: 5,
    borderRadius: 5,
  },
};
const styles = StyleSheet.create({
  inputWrapperSelect: {
    borderWidth: 1.5,
    borderColor: holidayColors.grayBorder,
    width: '100%',
    paddingHorizontal: 2,
    ...Platform.select({
      ios: {
        height: 30,
      },
      android: {
        height: 30,
      },
      web: {
        height: 30,
      },
    }),
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 7,
    borderRadius: 4,
  },
  input: {
    color: holidayColors.black,
    ...fontStyles.labelBaseBold,
    height: 22,
    width: 130,
    ...paddingStyles.pa2,
  },
  iconArrowDown: {
    width: 12,
    height: 7,
    position: 'absolute',
    right: 15,
    top: 12,
  },
  dropDown: {
    position: 'absolute',
    width: '100%',
    backgroundColor: holidayColors.white,
    elevation: 10,
    zIndex: 15,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    maxHeight: 150,
    top: 30,
    paddingBottom: 10,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  dropdownText: {
    fontSize: 14,
    textAlign: 'center',
    paddingTop: 10,
    fontWeight: '600',
    color: holidayColors.black,
  },
  imageStyle: {
    marginTop: 4,
    marginBottom: 5,
    borderRadius: 5,
  },
  imageWrapper: { display: 'flex', flexDirection: 'row', width: '100%' },
  image: { width: '100%', flex: 1 },
  hotelInfo: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    zIndex: 100,
    marginTop: 5,
  },
  propertyWrapper: { marginBottom: 25 },
  hotelDescription: { display: 'flex', flexDirection: 'column' },
  displayRow: { display: 'flex', flexDirection: 'column', maxWidth: 200 },
  hotelNameText: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  checkIn: { ...fontStyles.labelSmallRegular, color: holidayColors.lightGray },
  checkOut: { ...fontStyles.labelSmallRegular, color: holidayColors.lightGray },
  city: { ...fontStyles.labelSmallRegular, color: holidayColors.black },
  showMore: {
    backgroundColor: holidayColors.lightBlueBg,
    height: 40,
    justifyContent: 'center',
    display: 'flex',
    alignContent: 'center',
    borderColor: holidayColors.primaryBlue,
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius4,
    marginTop: 5,
  },
  showMoreText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelBaseBold,
    alignContent: 'center',
    alignSelf: 'center',
  },
  showLess: {
    backgroundColor: '#EDF7FF',
    height: 40,
    justifyContent: 'center',
    display: 'flex',
    alignContent: 'center',
  },
  showLessText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelBaseBold,
    alignContent: 'center',
    alignSelf: 'center',
  },
  separator: {
    height: 1,
    width: '85%',
    backgroundColor: holidayColors.grayBorder,
    marginTop: 5,
    marginHorizontal: 10,
  },
  nameContainer: {
    display: 'flex',
    flexDirection: 'row',
    flex: 10,
  },
  noMediaWrapper: {
    backgroundColor: holidayColors.lightGray2,
    height: 125,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 5,
    marginBottom: 5,
  },
  noMediaImage: {
    width: 38,
    height: 28,
  },
  noMediaText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.lightGray,
    marginTop: 10,
  },
});
export default PropertyPhotos;
