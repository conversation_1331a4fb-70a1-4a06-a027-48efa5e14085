import React from 'react';
import {connect} from 'react-redux';
import {getScreenWidth} from '../../../../utils/HolidayUtils';
import {ScrollView, StyleSheet, View} from 'react-native';
import SimilarPackage from './SimilarPackage';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const SimilarPackagesForNewDetailPage = (props) => {
  const {onSimilarPackageClicked, similarPackages, storyImageSize} = props || {};

  if (similarPackages === undefined || similarPackages.length === 0) {
    return null;
  }

  const {width} = getScreenWidth();
  return (
    <View style={{...styles.parent, width}}>
      <ScrollView
        alwaysBounceHorizontal={false}
        bounces={false}
        horizontal
        showsHorizontalScrollIndicator={false}>
        {
          similarPackages.map((item, index) => (
            <SimilarPackage
              key={index}
              index={index}
              item={item}
              storyImageSize={storyImageSize}
              onPackageClicked={onSimilarPackageClicked}
            />
          ))
        }
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  parent: {
    height: 255,
    backgroundColor: holidayColors.white,
  },
});

const mapStateToProps = (state) => {
  const {similarPackages, storyImageSize} = state.holidaysDetail || {};
  return {similarPackages, storyImageSize};
};

export default connect(mapStateToProps, null)(SimilarPackagesForNewDetailPage);
