import React from 'react';
import { StyleSheet, Text,  View } from 'react-native';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';
const FlightTopCard = (props) => {
  const { showHeader = true, containerStyles, onRemovePress, onChangePress} = props || {};

  return (
      <View
        style={[
          styles.container,
          containerStyles,
          showHeader ? marginStyles.mb10 : {},
        ]}
      >
        {showHeader && <FlightSeq flights={props.flights} roundTrip={props.roundTrip} />}
        {props.showOptions && (
          <View style={styles.actionContainer}>
            {!props.removeFlightRestricted && (
              <TextButton
                  buttonText="Remove"
                  handleClick={() => onRemovePress()}
                  btnTextStyle={styles.action}
              />
            )}
            {!props.changeFlightRestricted && !props.removeFlightRestricted && (
              <View style={styles.separator} />
            )}
            {!props.changeFlightRestricted && (
              <TextButton
                  buttonText="Change"
                  handleClick={() => onChangePress()}
                  btnTextStyle={styles.action}
              />
            )}
          </View>
        )}
      </View>
  );
};

const FlightSeq = ({flights, roundTrip}) => {
  if (flights.length < 3) {
    const { flightMetadataDetail = {}, fromAirport, toAirport } = flights?.[0] || {};
    const { isDummy = false } = flightMetadataDetail || {};
    return (
      <View style={styles.flightSeqContainer}>
        <Text style={styles.flightSeqHeading}>{`${isDummy ? 'Tentative ' : ''}Flight from `}</Text>
        <View style={styles.flightSeq}>
          <Text style={styles.airportCity}>{fromAirport.airportCity} </Text>
          <Text style={styles.airportCity}> - </Text>
          <Text style={styles.airportCity}> {toAirport.airportCity}</Text>
        </View>
      </View>
    );
  } else {
    let dest = [];
    flights.forEach((item, index) => {
      const {fromAirport, toAirport} = item || {};
      const from = fromAirport?.airportCity;
      const to = toAirport?.airportCity;
      dest.push(`${from} - ${to}`);
    });
    dest = dest.join(' | ');
    return (
      <View style={styles.destContainer}>
        <Text style={styles.dest}>{dest}</Text>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  separator: {
    marginLeft: 5,
    marginRight: 5,
    backgroundColor: holidayColors.primaryBlue,
    width: 1.5,
    height: 10,
  },
  flightSeqContainer: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
    flexGrow: 1,
    maxWidth: '60%',
  },
  flightSeq: {
    flexDirection: 'row',
    ...marginStyles.mt6,
  },
  flightSeqHeading: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.lightGray,
  },
  airportCity: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  action: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
  },
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 3,
    marginLeft: 'auto',
  },
  destContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...marginStyles.mr4,
    maxWidth: '60%',
  },
  dest: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
});

export default FlightTopCard;
