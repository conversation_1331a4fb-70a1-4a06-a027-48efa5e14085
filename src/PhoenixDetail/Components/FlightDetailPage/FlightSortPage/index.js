import React from 'react';
import { Image, Modal, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

import FilterHeader from '../FilterHeader';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { sortByX } from '../FlightFilterPage/FilterUtils';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { statusBarBootomHeightForIphone, statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { smallHeightSeperator } from '../../../../Styles/holidaySpacing';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';

class FlightSortPage extends BasePage {
  constructor(props) {
    super(props);
    this.state = {
      sortingOptions: props.sortingOptionsData,
    };
  }

  clearSortingOptions = () => {
    this.setState({
      sortingOptions: this.props.defaultSortionOptions(),
    });
  }

  // common function to handle all sort options state
  onSortingOptionPress = (sortIndex, optionIndex) => {
    let newState = sortByX(
      {...this.state.sortingOptions},
      sortIndex,
      optionIndex
    );
    this.setState({
      sortingOptions: newState,
    });
  }

  onApplyPress = () => {
    this.props.applySorting(this.state.sortingOptions);
  }

  render() {
    const {sortingDataIndex, optionIndex, sortingData} = this.state.sortingOptions;
    return (
      <View style={styles.modalView}>
        <FilterHeader
          headerTxt="Sort"
          subHeaderTxt="Showing 23 out of 210 Results"
          handleClose={this.props.handleClose}
          clearFilterData={this.clearSortingOptions}
        />
        <ScrollView>
          <View style={paddingStyles.pa16}>
            <View style={styles.sortTitle}>
              <Text style={styles.sortTitleText}>Sort By</Text>
            </View>
            {sortingData.map((item, i) =>
              <View style={styles.sortRow} key={i}>
                <View style={[cStyles.flexRow, cStyles.alignCenter, cStyles.flex1]}>
                  <View style={[cStyles.marginLeft10]}>
                    <Text style={styles.title}>{item.name}</Text>
                  </View>
                </View>
                <View style={[cStyles.flex1, cStyles.marginLeft15]}>
                  {item.options.map((option, j) => (
                    <FilterOption
                      name={option}
                      type={item.type}
                      onSortingOptionPress={this.onSortingOptionPress}
                      sortIndex={i}
                      optionIndex={j}
                      selected={sortingDataIndex === i && optionIndex === j}
                    />
                  ))}
                </View>
              </View>,
            )}
          </View>

        </ScrollView>
        <TouchableOpacity onPress={() => this.onApplyPress()}>
          <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={['#53b2fe', '#065af3']}
                          style={styles.filterBtn}>
            <Text style={[cStyles.font18, cStyles.blackFont, cStyles.whiteText]}>APPLY</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    );
  }
}

const FilterOption = ({ name, onSortingOptionPress, type, sortIndex, optionIndex, selected }) => {
  return (
    <TouchableOpacity onPress={() => onSortingOptionPress(sortIndex, optionIndex)}>
      <View style={styles.valueContainer}>
        <Text style={[styles.value, selected && styles.selectedValue]}>{name}</Text>
        {selected && (
          <Image
            source={require('@mmt/legacy-assets/src/tick.webp')}
            style={styles.tickIcon}
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  gallaryView: {
    width: '50%',
  },
  modalView: {
    marginTop:statusBarHeightForIphone,
    marginBottom:statusBarBootomHeightForIphone,
    backgroundColor: 'white',
    width: '100%',
    height: '100%',
    flex: 1,
  },

  filterBtn: {
    borderRadius: 4,
    padding: 15,
    margin: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sortRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    ...smallHeightSeperator,
  },
  sortTitle: {
    paddingBottom: 0,
  },
  sortTitleText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
  },
  title: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.black,
  },
  valueContainer: {
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'space-between',
  },
  value: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  selectedValue: {
    color: holidayColors.primaryBlue,
  },
  tickIcon: {
    height: 10,
    width: 10,
    ...marginStyles.mr16,
  },
});

export default FlightSortPage;
