import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { smallHeightSeperator } from '../../../../Styles/holidaySpacing';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
const FilterItemSection = ({ heading, children }) => {
  return (
    <View style={styles.filterBlk}>
      <View style={marginStyles.mb6}>
        <Text style={styles.filterSectionHeading}>{heading}</Text>
      </View>
      <View style={[styles.filterTabsWrap]}>{children}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  filterBlk: {
    ...paddingStyles.pa16,
    ...smallHeightSeperator,
  },
  filterSectionHeading: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
  },
  filterTabsWrap: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
});

export default FilterItemSection;
