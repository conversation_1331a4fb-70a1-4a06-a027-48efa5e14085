import React from 'react';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { Platform, StyleSheet, Text, View } from 'react-native';
import { getScreenWidth } from '../../../../utils/HolidayUtils';
import { isEqual } from 'lodash';
import { getPriceText } from '../FlightListing/FlightsUtils';
import MultiSlider from "../../../../Common/Components/MultiSlider";

export default class PriceRange extends BasePage {

  constructor(props) {
    super(props);
    this.state = {
      priceRange: props.priceRange ? props.priceRange : props.defaultPriceRange,
    };
  }

  componentWillReceiveProps (nextProps) {
    const {defaultPriceRange, priceRange} = nextProps;
    if ((priceRange || defaultPriceRange) && !isEqual(this.state.priceRange, priceRange)) {
      this.setState({
        priceRange: priceRange ? priceRange : defaultPriceRange,
      });
    }
  }

  togglePriceRange = (value) => {
    const priceRange = { min: value[0], max: value[1] };
    this.props.setPriceRange(priceRange);
    this.setState({
      priceRange: priceRange,
    });
  }

  render() {
    return <View style={cStyles.marginBottom15}>
      <Text style={[cStyles.font14, cStyles.defaultText, cStyles.blackFont]}>Price Range</Text>
      <View style={[cStyles.flexRow, cStyles.alignCenter, cStyles.marginTop5]}>
        <Text style={[cStyles.font14, cStyles.boldFont, cStyles.defaultText]}>{getPriceText(this.state.priceRange.min)}  -  {getPriceText(this.state.priceRange.max)}</Text>
      </View>
      <MultiSlider
        values={[
          this.props.priceRange ? this.props.priceRange.min : this.props.defaultPriceRange.min,
          this.props.priceRange ? this.props.priceRange.max : this.props.defaultPriceRange.max,
        ]}
        sliderLength={getScreenWidth() - 50}
        trackStyle={{
          height: 6, backgroundColor: '#e6e6e6',
        }}
        selectedStyle={{
          backgroundColor: '#008cff',
        }}
        onValuesChange={this.togglePriceRange}
        min={this.props.defaultPriceRange.min}
        max={this.props.defaultPriceRange.max}
        step={1}
        allowOverlap={false}
        snapped
        markerStyle={styles.markerStyle}
        pressedMarkerStyle={styles.markerStyle}
      />
    </View>;
  }
}

const styles = StyleSheet.create({
  galleryView: {
    width: '50%',
  },
  modalView: {
    backgroundColor: 'white',
    width: '100%',
    height: '100%',
    flex: 1,
  },
  filterTabContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterTabsWrap: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterBtn: {
    borderRadius: 4,
    padding: 15,
    margin: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterBlk: {
    padding: 15,
    borderBottomWidth: 1,
    borderColor: '#e0e0e0',
  },
  markerStyle: {
    height: 30,
    width: 30,
    borderRadius: 30,
    backgroundColor: '#fff',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#ccc',
    ...Platform.select({
      ios: {
        marginTop: 0,
      },
      android: {
        marginTop: 5,
      },
    }),
  },
});
