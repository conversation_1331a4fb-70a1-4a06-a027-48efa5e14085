import React from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import iconFlightBlue from '../../images/ic_flight-blue.png';
import { getFlightDate, getFlightTime, parseFlightDate } from '../../../Utils/FlightUtils';
import { flightDuration } from '../../../../ChangeFlight/HolidayFlightUtils';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import { getAirlineIconUrl, getBaggageInfoForFlight } from '../FlightListing/FlightsUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { HTML_CODES } from 'mobile-holidays-react-native/src/HolidayConstants';
import { smallHeightSeperator } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { borderRadiusValues, holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';


const FlightDetailCards = (props) => {
  const {flightLeg, baggageInfoMap = {}} = props;
  const {airlineCode, flightId, departure, fromAirport, toAirport, arrival, via, viaList,layoverDuration} = flightLeg;
  const {stops} = props;
  const baggageInfo = getBaggageInfoForFlight(baggageInfoMap, flightId);
  return (
    <View>
      {stops > 0 && !via && layoverDuration > 0 &&
      <View style={styles.flLayoverTag}>
        <View style={[cStyles.marginBottom3]}><Text style={[cStyles.font11, cStyles.defaultText]}>
          <Text style={{ color: '#000000' }}>
                Technical Stop | {flightDuration(layoverDuration)} |
              </Text>{' '}
              Layover in {fromAirport.airportCity}{' '}
            </Text>
        </View>
      </View>}
      <View style={styles.cardContent}>
        <View style={styles.flightName}>
          <View style={[cStyles.marginRight10,styles.iconAirlineLogo]}>
            <HolidayImageHolder
              imageUrl={getAirlineIconUrl(airlineCode)}
              style={styles.iconAirlineLogo}
            />
          </View>
          <View>
            <View style={cStyles.marginRight5}>
              <Text style={styles.airlineName}>{flightLeg.airlineName}</Text>
            </View>
            <Text style={styles.airlineCode}>{flightLeg.flightId}</Text>
          </View>
        </View>
          <View style={styles.flightScheduleWrap}>
            <View style={styles.flightLoc}>
              <View style={cStyles.marginBottom10}>
                <Text style={styles.flightTime}>{getFlightTime(parseFlightDate(departure))}</Text>
                <Text style={[styles.flightDateText, cStyles.alignLeft]}>
                  {getFlightDate(departure)}
                </Text>
                <Text style={styles.airportCity}>{fromAirport.airportCity}</Text>
              </View>
              <View>
                <Text style={styles.airportCode}>{fromAirport.airportCode}</Text>
              </View>
            </View>
            <View style={styles.layoverDuration}>
              <View style={styles.layoverDivider}>
                <Image source={iconFlightBlue} style={styles.iconFlightBlue} />
              </View>
              <View style={cStyles.marginTop15}>
                <Text style={styles.duration}>
                  {flightDuration(flightLeg.duration)}
                </Text>
              </View>
            </View>
            <View style={[styles.flightLoc, styles.flDestination]}>
              <View style={[cStyles.marginBottom10, { width: '100%' }]}>
                <Text style={[styles.flightTime, cStyles.alignRight]}>
                  {getFlightTime(parseFlightDate(arrival))}
                </Text>
                <Text style={[styles.flightDateText, cStyles.alignRight]}>
                  {getFlightDate(arrival)}
                </Text>
                <Text style={[styles.airportCity, cStyles.alignRight]}>{toAirport.airportCity}</Text>
              </View>
              <View>
                <Text style={styles.airportCode}>{toAirport.airportCode}</Text>
              </View>
            </View>
          </View>
      </View>
      <View style={styles.baggageContent}>
        {baggageInfo && baggageInfo.length > 0 && (
          <View>
            <View style={cStyles.marginBottom5}>
              <Text style={styles.flightBaggageHeading}>Flight Baggage</Text>
            </View>
            {baggageInfo.map((item, index) => {
              const { cabin, cabinColor, text, checkIn, checkInColor } = item || {};
              return (
                <View key={`BaggageInfo-${index}`}>
                  <Text style={styles.flightBaggagePerson}>{text}</Text>
                  <View>
                    <View style={styles.baggageItem}>
                      <Text style={styles.flightBaggageItemHeading}>Cabin : </Text>
                      <Text style={[styles.flightBaggageItemValue, { color: cabinColor }]}>
                        {cabin}
                      </Text>
                      <Text style={styles.bullet}>{HTML_CODES.BULLET}</Text>
                      <Text style={styles.flightBaggageItemHeading}>Check-in : </Text>
                      <Text style={[styles.flightBaggageItemValue, { color: checkInColor }]}>
                        {checkIn}
                      </Text>
                    </View>
                  </View>
                </View>
              );
            })}
          </View>
        )}
      </View>
      {stops > 0 && via && viaList.map((item) => {
        return (
          <View style={styles.flLayoverTag}>
            <View style={[cStyles.marginBottom3]}>
              <Text style={[cStyles.font11, cStyles.defaultText]}>
                <Text style={{color: '#000000'}}>
                  Technical Stop | {flightDuration(item.duration)} |
                </Text>
                {item.flightChange ? ` Change flight in ${item.airport}` : ` Layover in ${item.airport}`}
            </Text>
            </View>
          </View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  cardContent: {
    ...smallHeightSeperator,
  },
  flightName: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  flightLoc: {
    flexWrap: 'wrap',
    width: '35%',
  },
  airlineName: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  airlineCode: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  flightScheduleWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  flDestination: {
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    width: '35%',
  },
  flightTime: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  layoverDuration: {
    width: '30%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  layoverDivider: {
    borderTopWidth: 1,
    borderColor: '#cfcece',
    width: 70,
    position: 'relative',
  },
  duration : {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  iconFlightBlue: {
    width: 15,
    height: 15,
    position: 'absolute',
    left: '45%',
    top: -7.5,
  },
  iconAirlineLogo: {
    width: 25,
    height: 25,
    resizeMode: 'cover',
  },
  flLayoverTag: {
    backgroundColor: holidayColors.lightBlueBg,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 5,
    width: '100%',
    marginVertical: 10,
    ...holidayBorderRadius.borderRadius8,
  },
  flightDateText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  airportCity: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  airportCode: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  baggageContent: {
    ...paddingStyles.pt16,
  },
  flightBaggageHeading: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  baggageItem: {
    flexDirection: 'row',
  },
  flightBaggagePerson : {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  flightBaggageItemHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  flightBaggageItemValue: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    flex: 1,
  },
  bullet: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
    marginHorizontal: 4,
  },
});

export default FlightDetailCards;
