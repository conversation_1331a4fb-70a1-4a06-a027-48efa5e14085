import React from 'react';
import {
  BackHandler,
  Dimensions,
  FlatList,
  Modal,
  StyleSheet, Switch, Text,
  View,
} from 'react-native';
import FlightListingCard from './FlightListing/FlightListingCard';
import FlightListingTabs from './FlightListing/FlightListingTabs';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import Carousel from '../../../Common/Components/Carousel';
import {parseFlightDate } from '../../Utils/FlightUtils';

const screenWidth = Dimensions.get('screen').width;
import FlightShortListCard from './FlightListing/FlightShortListCard';
import FlightFilterPage from './FlightFilterPage';
import FlightSortPage from './FlightSortPage';
import FilterTag from './FlightListing/FlightFilterTag';
import {
  filter,
  getDefaultFilterState,
  getInitialSortingOptionsData,
  isSortingApplied, log_sortBy, logSelectedFilters,
  sortingX,
} from './FlightFilterPage/FilterUtils';
import {getDiscountedPrice, isMobileClient, isRawClient} from '../../../utils/HolidayUtils';
import {
  componentImageTypes,
  flightDetailTypes,
  packageActionComponent,
  packageActions,
} from '../../DetailConstants';
import {
  createBaggageDetailRequestBody,
  createFlightMap,
  roundPrice,
  persuasionText,
  persuasionTypes,
  maxNoOfFlightIdsForUnavailabilityPersuasion,
  TripName,
  createFlightLogText, getFlightOvernightDelay,
  applyDefaultFilters,
} from './FlightListing/FlightsUtils';
import PhoenixHeader from '../PhoenixHeader';
import ConfirmationPopup from '../ConfirmationPopup/ConfirmationPopup';
import {cloneDeep, isEqual} from 'lodash';
import { MONTH_ARR_CAMEL, SUB_PAGE_NAMES } from '../../../HolidayConstants';
import { HOLIDAYS_FLIGHT_OVERLAY_LISTING } from '../../Utils/PheonixDetailPageConstants';
import { checkAndChangePriceToZero } from '../../Utils/PhoenixDetailUtils';
import NotificationMessage from '../NotificationMessage';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { getShowOvernightsFilter } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import HolidayPriceChangeMessage from '../Common/HolidayPriceChangeMessage';
import {Overlay} from "../DetailOverlays/OverlayConstants";
import { holidayNavigationPop, holidayNavigationPush } from '../../Utils/DetailPageNavigationUtils';

const screenHeight = Dimensions.get('window').height;

import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import withBackHandler from '../../../hooks/withBackHandler';
class FlightDomRetListingComponent extends BasePage {
  constructor(props) {
    super(props);
    this._carousel = null;
    this._flatListRet = null;
    this._flatListDep = null;
    this.priceDepFlights = [];
    this.priceReturnFlight = [];
    const {flightListingData, pricingDetail, domRtFilterData} = props;
    const {filterData, sortByData} = domRtFilterData || {};
    const {
      flightSelections,
      overnightAvailabilityMap,
      departureFlights = [],
      returnFlights = [],
    } = flightListingData;
    this.discountedFactor = flightListingData.discountedFactor;
    const {categoryPrices} = pricingDetail || {};
    this.price = categoryPrices && categoryPrices.length > 0 ? categoryPrices[0].price : null;
    this.flightMap = createFlightMap(departureFlights, returnFlights);
    this.depFlight = null;
    this.retFlight = null;
    this.uniqueDepAirlines = [];
    this.uniqueRetAirlines = [];
    const uniqueDepAirlineMap = {};
    const uniqueRetAirlineMap = {};
    // this.depPriceRange = null;
    // this.retPriceRange = null;
    if (flightSelections && flightSelections.length > 0) {
      flightSelections.forEach((item) => {
        if (item.flightSequence === 1) {
          // let depPriceList = [];
          departureFlights.forEach((flight, index) => {
            flight.price = this._getFlightPrice(flight.sellableId, 0);
            flight.index = index;
            flight.overnightDelay = getFlightOvernightDelay(flight);
            // depPriceList.push(flight.price);
            if (flight.sellableId === item.sellableId) {
              this.depFlight = flight;
            }
            this.priceDepFlights.push(flight);
            for (let i = 0; i < flight.flightLegs.length; i++) {
              uniqueDepAirlineMap[flight.flightLegs[i].airlineName] = {
                'title': flight.flightLegs[i].airlineName,
                'code': flight.flightLegs[i].airlineCode,
                selected: false,
              };
            }
          });
          // depPriceList = depPriceList.sort((a, b) => a - b);
          // this.depPriceRange = {min: depPriceList[0], max: depPriceList[depPriceList.length - 1]};
          this.uniqueDepAirlines = Object.values(uniqueDepAirlineMap);
        }
        else {
          // let retPriceList = [];
          returnFlights.forEach((flight, index) => {
            flight.price = this._getFlightPrice(flight.sellableId, 1);
            flight.index = index;
            flight.overnightDelay = getFlightOvernightDelay(flight);
            // retPriceList.push(flight.price);
            if (flight.sellableId === item.sellableId) {
              this.retFlight = flight;
            }
            this.priceReturnFlight.push(flight);
            for (let i = 0; i < flight.flightLegs.length; i++) {
              uniqueRetAirlineMap[flight.flightLegs[i].airlineName] = {
                'title': flight.flightLegs[i].airlineName,
                'code': flight.flightLegs[i].airlineCode,
                selected: false,
              };
            }
          });
          // retPriceList = retPriceList.sort((a, b) => a - b);
          // this.retPriceRange = {min: retPriceList[0], max: retPriceList[retPriceList.length - 1]};
          this.uniqueRetAirlines = Object.values(uniqueRetAirlineMap);
        }
      });
    }
    this.currentTabOnLaunch = props.currentTabOnLaunch;
    this.dep_overnight = this.getOvernightFlightStatus(overnightAvailabilityMap, 1);
    this.ret_overnight = this.getOvernightFlightStatus(overnightAvailabilityMap, 2);
    const dFilter = this.getFilters(this.uniqueDepAirlines, null, false, filterData ? filterData[0] : null);
    const rFilter = this.getFilters(this.uniqueRetAirlines, null, false, filterData ? filterData[1] : null);
    const filteredFlights = this.applyPreviousFilters([dFilter, rFilter], sortByData);

    this.state = {
      fdf: filteredFlights[0],
      frf: filteredFlights[1],
      dFilterData: dFilter,
      rFilterData: rFilter,
      openFilter: false,
      openSorter: false,
      sortingOptionsData: sortByData ? sortByData : [getInitialSortingOptionsData(), getInitialSortingOptionsData()],
      showConfirmationDialog: false,
      showPriceUpdatedMessage :true
    };
    const {updatedFlightRequestObject} = this.props;
    const {overnightDelays} = updatedFlightRequestObject || {};
    this.overnightdFlightsSelected = this.getOvernightFilterApplied(overnightDelays, true);
    this.overnightrFlightsSelected = this.getOvernightFilterApplied(overnightDelays, false);
    this.currentTabIndex = props.currentTabOnLaunch;
  }

  closePriceMessage = () => {
    this.setState({
      showPriceUpdatedMessage: false,
    });
  };

  onCloseScreen = () => {
    holidayNavigationPop({
      overlayKeys: [this.props.overlayKey],
      hideOverlays: this.props.hideOverlays,
      navigationFunction: HolidayNavigation.navigate,
      navigationFunctionProps: this.props.lastPage,
    });
  };

  componentDidMount() {
    super.componentDidMount();
    /* Pre-apply filters if upgrade available */
    const { dFilterData = {}, rFilterData = {} } = this.state || {};
    const { flightUpgradeDetails = {}, flightListingData = {}, currentTabOnLaunch = {}, showUpgradeableFlights = false } =
      this.props || {};
    if (showUpgradeableFlights) {
      applyDefaultFilters({
        flightUpgradeDetails: flightUpgradeDetails,
        filterData: [cloneDeep(dFilterData), cloneDeep(rFilterData)],
        filterIndexs: [currentTabOnLaunch],
        flightType: currentTabOnLaunch === 0 ? 'DEPARTURE' : 'RETURN',
        from: flightListingData.listingDataType,
        applyFilter: this.applyFilter,
      });
    }
  }
  onBackClick = ()=> {
    this.onBackPress();
}

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  getOvernightFilterApplied = (overnightDelays, isOnward) => {
    if (overnightDelays){
      if (isOnward && overnightDelays.includes('1_')) {
        return true;
      } else if (overnightDelays.includes('_1')) {
        return true;
      }
    }
    return false;
  }
  getOvernightFlightStatus = (overnightAvailabilityMap, seq) => {
    const showOvernightFilter = getShowOvernightsFilter();
    if (overnightAvailabilityMap && showOvernightFilter) {
      const data = overnightAvailabilityMap[seq];
      if (data && data.length > 0) {
        for (let i = 0; i < data.length; i++) {
          if (data[i] > 0) {
            return true;
          }
        }
      }
    }
    return false;
  }

  getFilters = (airlines, priceRange, showOvernightFlightsFilter, oldFilters) => {
    let filters = null;
    if (oldFilters) {
      const newAirlines = cloneDeep(airlines);
      oldFilters.airlines.forEach((item) => {
        if (item.selected) {
          const index = newAirlines.findIndex(x => x.title === item.title);
          if (index !== -1) {
            newAirlines[index].selected = true;
          }
        }
      });
      filters = getDefaultFilterState(newAirlines, priceRange, showOvernightFlightsFilter);
      filters.noOfStops = oldFilters.noOfStops;
      filters.departureTime = oldFilters.departureTime;
      filters.arrivalTime = oldFilters.arrivalTime;
      if (showOvernightFlightsFilter) {
        filters.overnightFlights = oldFilters.overnightFlights;
      }
    } else {
      filters = getDefaultFilterState(airlines, priceRange, showOvernightFlightsFilter);
    }
    return filters;
  }

  applyPreviousFilters = (filterData, sortingData) => {
    if (filterData && sortingData) {
      const filteredLists = filter(filterData,[cloneDeep(this.priceDepFlights), cloneDeep(this.priceReturnFlight)]);

      for (let i = 0; i < sortingData.length; i++) {
        if (isSortingApplied(sortingData[i])) {
          filteredLists[i] = sortingX(filteredLists[i], sortingData[i]);
        }
      }
      return filteredLists;
    } else {
      return [cloneDeep(this.priceDepFlights), cloneDeep(this.priceReturnFlight)];
    }
  }

  onBackPress = () => {
    const selected = this.showingPreSelectedFlights();
    if (!selected) {
      this.setState({
        showConfirmationDialog: true,
      });
    } else {
      this.onCloseScreen();
    }
    return true;
  }
  captureClickEvents = ({ eventName = '', suffix = '', value = '', prop1 = ''}) =>{
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value : value || eventName,
      subPageName: prop1 || SUB_PAGE_NAMES.FLIGHT_LISTING
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1 : prop1 || HOLIDAYS_FLIGHT_OVERLAY_LISTING,
    })
  }
  onNotNowClicked = () => {
    this.captureClickEvents({ eventName: 'confirm_NOT NOW' });
    this.onCloseScreen();
  }

  onShowMeClicked = () => {
    this.setState({
      showConfirmationDialog: false,
    });
    this.captureClickEvents({ eventName: 'confirm_SHOW SELECTION' });
  }

  showingPreSelectedFlights = () => {
    const {flightListingData, flightRequestObject} = this.props;

    const {flightSelections} = flightListingData;
    let depFlightSellableId = null;
    let retFlightSellableId = null;
    if (flightSelections && flightSelections.length > 0) {
      flightSelections.forEach((item) => {
        if (item.flightSequence === 1) {
          depFlightSellableId = item.sellableId;
        } else {
          retFlightSellableId = item.sellableId;
        }
      });
    }
    let selected = true;
    const {flightSelections: flightSelection2} = flightRequestObject || {};
    if (flightSelection2 && flightSelection2.length > 0) {
      for (let i = 0; i < flightSelection2.length; i++) {
        if (flightSelection2[i].flightSequence === 1) {
          selected = flightSelection2[i].sellableId === depFlightSellableId;
        } else {
          selected = flightSelection2[i].sellableId === retFlightSellableId;
        }
        if (!selected) {
          break;
        }
      }
    }
    return selected;
  }

  createShortListCardDataJson = () => {
    const {flightListingData} = this.props;
    const {listingDataType, currPackagePrice} = flightListingData;
    // let newPackagePrice = getDiscountedPrice(currPackagePrice, this.discountedFactor);
    const selected = this.showingPreSelectedFlights();
    return {
      listingDataType: listingDataType,
      packagePrice: currPackagePrice,
      data: [this.depFlight, this.retFlight],
      selected: selected,
    };
  }

  resetFilter = () => {
    this.setState(
      {
        dFilterData: getDefaultFilterState(this.uniqueDepAirlines, null, this.dep_overnight),
        rFilterData: getDefaultFilterState(this.uniqueRetAirlines, null, this.ret_overnight),
        fdf: cloneDeep(this.priceDepFlights),
        frf: cloneDeep(this.priceReturnFlight),
      }
    );
  }

  onUpdatePress = ({pageName = HOLIDAYS_FLIGHT_OVERLAY_LISTING} = {}) => {
    const {updatedFlightRequestObject, flightListingData} = this.props;
    const {listingDataType, currPackagePrice} = flightListingData;
    const flightSelections = [{
        sellableId: this.depFlight?.sellableId,
        flightSequence: 1,
      }, {
        sellableId: this.retFlight?.sellableId,
        flightSequence: 2,
      }];
    const actionData = {
      action: packageActions.CHANGE,
      overnightDelays: updatedFlightRequestObject.overnightDelays,
      flightSelections: flightSelections,
    };
    const priceDiff = currPackagePrice - getDiscountedPrice(this.price, this.discountedFactor);
    const flightLog = createFlightLogText(flightSelections, listingDataType, priceDiff);
    this.captureClickEvents({ eventName: 'update_', suffix: flightLog,  prop1: pageName, sendGIData:isRawClient(), value : `update|${flightLog}` });
    this.props.onComponentChange(actionData, componentImageTypes.FLIGHT);
    this.onCloseScreen();
  }

  confirmBoxUpdatePress = () => {
    this.captureClickEvents({ eventName: 'confirm_UPDATE', sendGIData: isRawClient() });
    this.onUpdatePress({pageName: HOLIDAYS_FLIGHT_OVERLAY_LISTING});
  }


  applyFilter = (filterData = []) => {
    const filteredLists = filter(filterData,[cloneDeep(this.priceDepFlights), cloneDeep(this.priceReturnFlight)]);
    for (let i = 0; i < filteredLists.length; i++) {
      if (filteredLists[i].length === 0) {
        return false;
      }
    }

    for (let i = 0; i < this.state.sortingOptionsData.length; i++) {
      if (isSortingApplied(this.state.sortingOptionsData[i])) {
        filteredLists[i] = sortingX(filteredLists[i], this.state.sortingOptionsData[i]);
      }
    }

    this.setState({
      dFilterData: filterData[0],
      rFilterData: filterData[1],
      fdf: filteredLists[0],
      frf: filteredLists[1],
      openFilter: false,
    });

    logSelectedFilters({ filterData, listingDataType: flightDetailTypes.DOM_RETURN, prop1: HOLIDAYS_FLIGHT_OVERLAY_LISTING });
    this.props.updateFiltersForDomRt(filterData[0], filterData[1], this.state.sortingOptionsData);

    const originalFilterData = [cloneDeep(this.state.dFilterData), cloneDeep(this.state.rFilterData)];
    if (isEqual(originalFilterData, filterData)) {
      this.captureClickEvents({eventName: 'filter_applied' });
    }

    if (this._flatListDep) {
      this._flatListDep.scrollToIndex({ index: 0 });
    }
    if (this._flatListRet) {
      this._flatListRet.scrollToIndex({ index: 0 });
    }
    return true;
  }

  clearSortingOptions = () => {
    const filteredLists = filter(
      [this.state.dFilterData, this.state.rFilterData],
      [cloneDeep(this.priceDepFlights), cloneDeep(this.priceReturnFlight)]
    );

    if (this.currentTabIndex === 0) {
      this.setState({
        sortingOptionsData: [
          getInitialSortingOptionsData(),
          this.state.sortingOptionsData[1],
        ],
        fdf: filteredLists[0],
        openSorter: false,
      });
      this.props.updateFiltersForDomRt(this.state.dFilterData, this.state.rFilterData, [
        getInitialSortingOptionsData(),
        this.state.sortingOptionsData[1],
      ]);
    } else if (this.currentTabIndex === 1) {
      this.setState({
        sortingOptionsData: [
          this.state.sortingOptionsData[0],
          getInitialSortingOptionsData(),
        ],
        frf: filteredLists[1],
        openSorter: false,
      });
      this.props.updateFiltersForDomRt(this.state.dFilterData, this.state.rFilterData, [
        this.state.sortingOptionsData[0],
        getInitialSortingOptionsData(),
      ]);
    }
  }

  applySorting = (sortingOptions) => {
    if (!isSortingApplied(sortingOptions)) {
      this.clearSortingOptions();
    }
    else if (this.currentTabIndex === 0) {
      const sortedFlights = sortingX(cloneDeep(this.state.fdf), sortingOptions);
      this.setState({
        fdf: sortedFlights,
        sortingOptionsData: [
          sortingOptions,
          this.state.sortingOptionsData[1],
        ],
        openSorter: false,
      });
      this.props.updateFiltersForDomRt(this.state.dFilterData, this.state.rFilterData, [
        sortingOptions,
        this.state.sortingOptionsData[1],
      ]);
      log_sortBy({
        data: sortingOptions,
        prefix: 'sort_',
        listingType: flightDetailTypes.DOM_RETURN,
        index: this.currentTabIndex,
        prop1: HOLIDAYS_FLIGHT_OVERLAY_LISTING,
      });
      this._flatListDep.scrollToIndex({ index: 0});

    } else if (this.currentTabIndex === 1) {
      const sortedFlights = sortingX(cloneDeep(this.state.frf), sortingOptions);
      this.setState({
        frf: sortedFlights,
        sortingOptionsData: [
          this.state.sortingOptionsData[0],
          sortingOptions,
        ],
        openSorter: false,
      });
      this.props.updateFiltersForDomRt(this.state.dFilterData, this.state.rFilterData, [
        this.state.sortingOptionsData[0],
        sortingOptions,
      ]);
      log_sortBy({
        data: sortingOptions,
        prefix: 'sort_',
        listingType: flightDetailTypes.DOM_RETURN,
        index: this.currentTabIndex,
        prop1: HOLIDAYS_FLIGHT_OVERLAY_LISTING,
      });
      this._flatListRet.scrollToIndex({ index: 0 });
    }
  }

  handleSort = () => {
    this.setState({ openSorter: true });
    this.captureClickEvents({ eventName: 'sort_clicked' });
  }

  handleFilter = () => {
    this.setState({openFilter: true});
    this.captureClickEvents({ eventName: 'filter_clicked' });
  }

  render () {
    const departureDate = parseFlightDate(this.priceDepFlights[0].departure);
    const departureMonth = MONTH_ARR_CAMEL[departureDate.getMonth()];
    const departureDayNumber = departureDate.getDate();
    const returnDate = parseFlightDate(this.priceReturnFlight[0].departure);
    const returnMonth = MONTH_ARR_CAMEL[returnDate.getMonth()];
    const returnDayNumber = returnDate.getDate();
    const tabMenuData = [
      {
        source: 'Departure',
        loc: `${this.priceDepFlights[0].fromAirport.airportCode} - ${this.priceDepFlights[0].toAirport.airportCode}`,
        date: `${departureMonth} ${departureDayNumber}`,
      },
      {
        source: 'Return',
        loc: `${this.priceReturnFlight[0].fromAirport.airportCode}-${this.priceReturnFlight[0].toAirport.airportCode}`,
        date: `${returnMonth} ${returnDayNumber}`,
      },
    ];
    const shortListCardData = this.createShortListCardDataJson();
    const {AIRPORT_TRANSFER_MESSAGE : airPortTransferMessage } = this.props.flightListingData?.displayMessages || {};
    const {showPriceUpdatedMessage} = this.state || {};
    return (
      <View style={styles.pageWrap}>
        <PhoenixHeader title={'Change Flight'} subtitleData={this.props.subtitleData} handleClose={() => this.onBackPress()}/>
        <FlightShortListCard {...shortListCardData} onUpdatePress={this.onUpdatePress} pageName = {HOLIDAYS_FLIGHT_OVERLAY_LISTING}/>
        {this.props.isFlightFailed && <NotificationMessage
          type="error"
          message="Oops! It seems the included flight(s) are sold out, please look for another option"
        />}
        <FlightListingTabs currentTab={this.currentTabOnLaunch} data={tabMenuData} switchTab={this.switchTab} />
        {showPriceUpdatedMessage && <HolidayPriceChangeMessage  closePriceMessage={this.closePriceMessage}
          airPortTransferMessage = {airPortTransferMessage}/>}
        <Carousel
          ref={(c) => {
            this._carousel = c;
          }}
          data={[this.state.fdf, this.state.frf]}
          firstItem={this.currentTabOnLaunch}
          initialNumToRender={2}
          renderItem={this.getView}
          itemWidth={screenWidth}
          keyExtractor={(item, index) => `Tab-${index}`}
          scrollEnabled = {false}
          sliderWidth={screenWidth}
          autoplay={false}
          disableEdgeSwiping={true}
        />
        {this.priceDepFlights && this.priceDepFlights.length > 0 && this.priceReturnFlight && this.priceReturnFlight.length > 0 &&
          <FilterTag
            handleFilter={this.handleFilter}
            handleSort={this.handleSort}
          />
        }
        <Modal
          onRequestClose={()=> this.setState({'openFilter': false, 'openSorter': false})}
          animationType="slide"
          transparent={true}
          visible={this.state.openFilter || this.state.openSorter}
          propagateSwipe={true}
        >
          {this.state.openFilter &&
            <FlightFilterPage
              currentTabIndex = {this.currentTabIndex}
              tabData ={['Departure','Return']}
              filterData = {[cloneDeep(this.state.dFilterData), cloneDeep(this.state.rFilterData)]}
              handleFilterClose={this.handleFilterClose}
            />}
          {this.state.openSorter &&
            <FlightSortPage
              sortingOptionsData={{...this.state.sortingOptionsData[this.currentTabIndex]}}
              handleClose={() => {
                this.setState({
                  openSorter: false,
                });
              }}
              applySorting={this.applySorting}
              defaultSortionOptions={() => getInitialSortingOptionsData()}
            />}
        </Modal>
        <ConfirmationPopup
          confirmDialogVisibility={this.state.showConfirmationDialog}
          onUpdatePackageClickFromPopup={this.confirmBoxUpdatePress}
          onNotNowClicked={this.onNotNowClicked}
          onShowMeClicked={this.onShowMeClicked}
        />
      </View>
    );
  }

  handleFilterClose = (applyClicked,filterData = []) => {
    if (applyClicked && filterData) {
      return this.applyFilter(filterData);
    } else {
      this.setState({openFilter: false});
      return true;
    }
  }

   switchTab = (index) => {
    if (this._carousel) {
      this._carousel.snapToItem(index);
    }
     this.currentTabIndex = index;
    this.props.updateTabForDomRt(index);
  }

  getContentContainerStyle = () => {
    const contentContainerStyle = { paddingBottom: 80 };

    if (isRawClient()) {
      contentContainerStyle.overflow = 'scroll';
      contentContainerStyle.height = screenHeight - 124;  /* 124 is height for header section */
    }

    return contentContainerStyle;
  };


  getView = (item,pageIndex) => {
    const data = isMobileClient() ? item.item : item;
    return (
      <FlatList
        ref={(c) => {
          if (pageIndex === 0) {
            this._flatListDep = c;
          } else {
            this._flatListRet = c;
          }
        }}
        data={data}
        renderItem={(data)=>this._renderFlightLegs(data,pageIndex)}
        keyExtractor={this._keyExtractor}
        showsVerticalScrollIndicator
        ListHeaderComponent={this.FlatListHeader(pageIndex)}
        contentContainerStyle={this.getContentContainerStyle()}
      />
    );
  }
  setOvernightFlights = (state, isOnward) => {
    if (isOnward) {
    this.overnightdFlightsSelected = state;
  } else {
      this.overnightrFlightsSelected = state;
    }
    const newFlightRequestObject = {
      ...this.props.flightRequestObject,
      overnightDelays : `${(this.overnightdFlightsSelected ? '1' : '0')}_${(this.overnightrFlightsSelected ? '1' : '0')}`,
    };

   // this.setState({overnightFlightsSelected:state});
    const name = TripName[this.currentTabIndex];
    //  trackPhoenixDetailLocalClickEvent({ eventName: `select_${name}_${flightId}_${index}`, prop1: HOLIDAYS_FLIGHT_OVERLAY_LISTING });
    this.props.fetchFlightListingData(this.props.dynamicId, newFlightRequestObject);

  }

  FlatListHeader = (pageIndex) => {
    if ((pageIndex === 0 && this.dep_overnight) || (pageIndex === 1 && this.ret_overnight)) {
      return (
        <View style={{ flexDirection: 'row', alignItems: 'center', padding: 10, backgroundColor: 'white' }}>
          <Text style={{
            fontFamily: 'Lato-Black',
            fontSize: 14,
            flex: 1,
            lineHeight: 18,
            letterSpacing: 0,
            color: '#4a4a4a',
          }}>{'Overnight Flights'}</Text>
          <Switch
            trackColor={{ false: '#cccccc', true: '#d7edff' }}
            thumbColor={[this.getIsOvernightFlight(pageIndex) ? '#008cff' : '#f5f5f5']}
            onValueChange={(state) => this.setOvernightFlights(state, (pageIndex === 0) ? true : false)}
            value={this.getIsOvernightFlight(pageIndex)}
          />
        </View>
      );
    }
    return null;
  }

  getIsOvernightFlight = (pageIndex) => {
    if (pageIndex === 0 && this.overnightdFlightsSelected) {
      return true;
    } else if (pageIndex === 1 && this.overnightrFlightsSelected) {
      return true;
    }
    return false;
  }

  _renderFlightLegs = (item,pageIndex) => {
    if (item) {
      const flight = item.item;
          return this._renderItem(flight,pageIndex);
    }
  }

  _keyExtractor = item => item.sellableId.toString();

  setSelection = (flight,pageIndex) => {
    const newSelection = [this.depFlight, this.retFlight];
    newSelection[pageIndex] = flight;
    this._updatePriceMap(newSelection, flight);
  }

  _updatePriceMap = (newSelection, flight) => {
    const newFlightRequestObject = {
      ...this.props.flightRequestObject,
      overnightDelays: this.props.updatedFlightRequestObject.overnightDelays,
      listingFlightSequence: this.currentTabIndex + 1,
      flightSelections: [{
        sellableId: newSelection[0].sellableId,
        flightSequence: 1,
      }, {
        sellableId: newSelection[1].sellableId,
        flightSequence: 2,
      }],
    };
    const name = TripName[this.currentTabIndex];
    const {flightId, index} = flight;
    this.captureClickEvents({ eventName: `select_${name}_${flightId}_${index}`, value: `select|${name}|${flightId}|${index}` ,sendGIData:isRawClient()});
    this.props.fetchFlightListingData(this.props.dynamicId, newFlightRequestObject);
  }

  _getFlightPrice = (sellableId, pageIndex) => {
    const {departurePriceMap, returnPriceMap} = this.props.flightListingData;
    if (!departurePriceMap || !returnPriceMap) {
      return null;
    }
    switch (pageIndex) {
      case 0:
        return checkAndChangePriceToZero(roundPrice(departurePriceMap[sellableId]));
      case 1:
        return checkAndChangePriceToZero(roundPrice(returnPriceMap[sellableId]));
      default: return null;
    }
  }

  onRemovePress = () => {
    this.props.onPackageComponentToggle(false, packageActionComponent.FLIGHT);
    this.onCloseScreen();
  }

  openFlightDetail = (flight,pageIndex) => {
    const data = {initialScrollIndex: 0, flights: [], roundTrip: false};
    let flightInSeq = [];
    const tempFlight = pageIndex === 0 ? this.depFlight : this.retFlight;
    if (flight.sellableId === tempFlight.sellableId) {
      data.flights.push({heading: 'Departure Flights', flight: this.depFlight});
      data.flights.push({heading: 'Return Flights',flight: this.retFlight});
      if (flight.sellableId === this.retFlight.sellableId) {
          data.initialScrollIndex = 1;
      }
      data.roundTrip =  true;
      data.flights.forEach((item, index) => {
        flightInSeq.push({
          sellableId: item.flight.sellableId,
          flightSequence: index + 1,
        });
      });
    } else {
      if (pageIndex === 0) {
        data.flights.push({heading: 'Departure Flights',flight: flight});
        flightInSeq.push({
          sellableId: flight.sellableId,
          flightSequence: 1,
        });
      } else {
        data.flights.push({heading: 'Return Flights',flight: flight});
        flightInSeq.push({
          sellableId: flight.sellableId,
          flightSequence: 2,
        });
      }
    }
    let indexes = data.flights.map((item) => {
      const {flight} = item || {};
      const {index} = flight;
      return index;
    });
    indexes = indexes.join(',');
    this.captureClickEvents({ eventName: 'view_details_', suffix: indexes, sendGIData: isRawClient() });

    const {updatedFlightRequestObject, back} = this.props;
    const {overnightDelays} = updatedFlightRequestObject || {};
    const requestObject = createBaggageDetailRequestBody(flightInSeq, this.props.dynamicId, overnightDelays);
    const FlightDetailProps={
      data: data,
      onUpdatePress: this.onUpdatePress,
      shortListCardData: this.createShortListCardDataJson(),
      onRemovePress: this.onRemovePress,
      requestObject: requestObject,
      subtitleData: this.props.subtitleData,
      accessRestriction: this.props.accessRestriction,
      showOptions: data.flights.length > 1,
      trackLocalClickEvent: this.props.trackLocalClickEvent,
      onChangePress:this.onChangePress,
      back,
    };
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.FLIGHT_DETAIL,
      overlayKey: Overlay.FLIGHT_DETAIL,
      props: FlightDetailProps,
      showOverlay: this.props.showOverlay,
      hideOverlays: this.props.hideOverlays,
    });
  }

  onChangePress=()=>{
    this.props.hideOverlays([Overlay.FLIGHT_DETAIL]);
  }

  createPersuasionData = (persuasions, pageIndex) => {
    const data = [];
    if (persuasions) {
      const flightsText = [];
      persuasions.forEach((item, index) => {
        const { persuasionType, dataMap } = item;
        if (persuasionType === persuasionTypes.UNAVAILABLE) {
          let temp = {};
          temp.persuasionType = persuasionTypes.UNAVAILABLE;
          temp.heading = persuasionText[pageIndex];
          temp.items = [];
          const flightIds = dataMap['#FLIGHTS'];
          flightIds.forEach((id, i) => {
            const flight = this.flightMap.get(id);
            if (flight && i < maxNoOfFlightIdsForUnavailabilityPersuasion) {
              const flightInfo = `${flight.airlineName} ${flight.flightKey}`;
              temp.items.push(flightInfo);
            }
          });
          data.push(temp);
        } else if (persuasionType === persuasionTypes.SPECIALFARE) {
          const flightIds = dataMap['#FLIGHTS'];
          if (flightIds && flightIds.length > 0) {
            const flight = this.flightMap.get(flightIds[0]);
            if (flight && flightsText.length < 2) {
              flightsText.push(`${flight.flightTag}`);
            }
          }
        }
      });
      if (flightsText.length > 0) {
        const text = flightsText.join(' | ');
        data.push({
          heading1: 'Special Fare available with ',
          flight: text,
          persuasionType: persuasionTypes.SPECIALFARE,
        });
      }
    }
    return data;
  }

  _renderItem = (flight,pageIndex) => {
    const tempFlight = pageIndex === 0 ? this.depFlight : this.retFlight;
    const selectedFlightSellableId = tempFlight.sellableId;
    flight.price = this._getFlightPrice(flight.sellableId, pageIndex);
    const {flightPersuasionMap = {}} = this.props.flightListingData || {};
    const persuasions = flightPersuasionMap[flight.sellableId];
    const persuasionData = this.createPersuasionData(persuasions, pageIndex);
    return (
      <FlightListingCard
        flight = {flight}
        selectedFlightSellableId = {selectedFlightSellableId}
        pageIndex = {pageIndex}
        persuasionData={persuasionData}
        setSelection = {this.setSelection}
        openFlightDetail = {this.openFlightDetail}
      />
    );
  }
}

const styles = StyleSheet.create({
  pageWrap: {
    flex: 1,
    backgroundColor: holidayColors.lightGray2,
  },
  iconBell: {
    width: 10,
    height: 14,
    resizeMode: 'cover',
  },
  baggageInfoTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: '#ffeae1',
    justifyContent: 'center',
    borderBottomColor: '#bababa',
    borderBottomWidth: 1,
  },
  carousalContainer: {width: '100%', height: 100},
  carousalContainer1: {width: '50%', height: '100%',backgroundColor: '#ffffff'},
  carousalContainer2: {width: '25%', height: '100%',backgroundColor: '#ffffff'},
  carousalContainer3: {width: '25%', height: '100%',backgroundColor: '#ffffff'},
  carousalContainer4: {width: '25%', height: '100%',backgroundColor: '#ffffff'},

});

export default withBackHandler(FlightDomRetListingComponent);
