import { cloneDeep, isEmpty, isNumber } from 'lodash';
import { flightDetailTypes } from '../../../DetailConstants';
import { findDaysBetweenDates } from '@mmt/legacy-commons/Common/utils/DateUtils';
import { checkAndChangePriceToZero } from '../../../Utils/PhoenixDetailUtils';
import { isMobileClient, rupeeFormatterUtils } from '../../../../utils/HolidayUtils';
import { getScreenDensityName } from '@mmt/legacy-commons/Helpers/displayHelper';

export const AIRLINE_IMAGE_BASEURL = `https://imgak.mmtcdn.com/flights/assets/media/mobile/common/${isMobileClient() ? getScreenDensityName().toUpperCase() : getScreenDensityName()}/{CODE}.png?v=2`;



/**
 * This function creates a UID for each flightGroup and also creates a map
 * of flight groups for O(1) access using it's UID
 * */
export const preProcessFlightComboData = (flightGroupRows = [], price, discountedFactor, listingDataType) => {
  const { getSegmentFlights, getSegments } = require('../FlightFilterPage/FilterUtils');
  const newFlightGroupRows = cloneDeep(flightGroupRows);
  let selectedIndex = null;
  newFlightGroupRows.forEach((group, index1) => {
    const temp_segment = getSegments(group, listingDataType);
    group.id = index1;
    temp_segment.forEach((item, index2) => {
      const temp_flight_segment = getSegmentFlights(item, listingDataType);
      temp_flight_segment.forEach((flight, index3) => {
        flight.id = `${index1}_${index2}_${index3}`;
      });
    });
    // set Unique id of a group
    group.uid = index1.toString();
    group.currPackagePriceDiff = checkAndChangePriceToZero(roundPrice(group.currPackagePriceDiff));
    if (group.selected) {
      selectedIndex = group.uid;
    }
  });

  const flightGroupMap = new Map();
  newFlightGroupRows.forEach((group, index) => {
    flightGroupMap.set(group.uid, group);
  });
  return {
    newFlightGroupRows: newFlightGroupRows,
    selectedGroupId: selectedIndex,
    flightGroupMap: flightGroupMap,
  };
};

export const TripName = ['Departure', 'Return'];
export const LoggingTripName = ['Dep', 'Ret'];
export const BAGGAGE_TYPE = {
  CHECK_IN : 'checkin',
  CABIN: 'cabin',
};

/**
 * Returns Trip Text for flight combos based on flight listing data type
 * */
export const getComboTripName = (listingDataType, index, totalSegments) => {
  if (listingDataType === flightDetailTypes.OBT) {
    if (index !== totalSegments - 1) {
      return TripName[0];
    } else {
      return TripName[1];
    }
  }
  return `Trip ${index + 1}`;
};

export const getBaggageInfoForFlight = (baggageInfoMap, flightId) => {
  const arr = [];
  if (baggageInfoMap && flightId) {
    const baggageInfo = baggageInfoMap[flightId];
    const { paxTypeBaggageInfoMap = {} } = baggageInfo || {};
    const { ADULT, CHILD, INFANT } = paxTypeBaggageInfoMap || {};
    if (ADULT) {
      const {cabinBaggage = {}, checkInBaggage = {}} = ADULT;
      arr.push({
        checkIn: getBaggageLimitTextNew({
          travellerBaggageInfo: ADULT,
          type: BAGGAGE_TYPE.CHECK_IN,
        }),
        checkInColor: getBaggageLimitTextColor(checkInBaggage),
        cabin: getBaggageLimitTextNew({
          travellerBaggageInfo: ADULT,
          type: BAGGAGE_TYPE.CABIN,
        }),
        cabinColor: getBaggageLimitTextColor(cabinBaggage),
        text: 'Adult',
      });
    }
    if (CHILD) {
      const {cabinBaggage = {}, checkInBaggage = {}} = CHILD;
      arr.push({
        checkIn: getBaggageLimitTextNew({
          travellerBaggageInfo: CHILD,
          type: BAGGAGE_TYPE.CHECK_IN,
        }),
        checkInColor: getBaggageLimitTextColor(checkInBaggage),
        cabin: getBaggageLimitTextNew({
          travellerBaggageInfo: CHILD,
          type: BAGGAGE_TYPE.CABIN,
        }),
        cabinColor: getBaggageLimitTextColor(cabinBaggage),
        text: 'Child',
      });
    }

    if (INFANT) {
      const {cabinBaggage = {}, checkInBaggage = {}} = INFANT;
      arr.push({
        checkIn: getBaggageLimitTextNew({
          travellerBaggageInfo: INFANT,
          type: BAGGAGE_TYPE.CHECK_IN,
        }),
        checkInColor: getBaggageLimitTextColor(checkInBaggage),
        cabin: getBaggageLimitTextNew({
          travellerBaggageInfo: INFANT,
          type: BAGGAGE_TYPE.CABIN,
        }),
        cabinColor: getBaggageLimitTextColor(cabinBaggage),
        text: 'Infant',
      });
    }
  }
  return arr;
};

// const getBaggageLimitText = (baggageLimit) => {
//   if (baggageLimit) {
//     const {limit, unit} = baggageLimit;
//     if (limit && unit) {
//       return `${limit} ${unit}`;
//     } else {
//       return 'Info. not available'
//     }
//   }
//   return 'Info. not available';
// }

const getBaggageLimitTextNew = ({ travellerBaggageInfo = {}, type }) => {
  const { cabinBaggage = {}, checkInBaggage = {} } = travellerBaggageInfo;
  const baggageLimit = type === BAGGAGE_TYPE.CHECK_IN ? checkInBaggage : cabinBaggage;
  if (isEmpty(checkInBaggage) && isEmpty(cabinBaggage)) {
    return 'Info. not available';
  }
  if (
    (isEmpty(checkInBaggage) && type === BAGGAGE_TYPE.CHECK_IN) ||
    (isEmpty(cabinBaggage) && type === BAGGAGE_TYPE.CABIN)
  ) {
    return 'Not Included';
  }
  if (baggageLimit) {
    const { limit, unit } = baggageLimit;
    if (limit && unit) {
      return `${limit} ${unit}`;
    } else {
      return 'Info. not available';
    }
  }
  return 'Info. not available';
};

const getBaggageLimitTextColor = (baggageLimit) => {
  if (baggageLimit) {
    const {limit, unit} = baggageLimit;
    if (limit && unit) {
      return '#9b9b9b';
    } else {
      return '#ff0000';
    }
  }
  return '#ff0000';
};
export const createOvernightDelay = (flights) => {
  const arr = [];
  flights.forEach((item, index) => {
    const dayDiff = findDaysBetweenDates(item.departure, item.arrival);
    arr.push(dayDiff);
  });
  return arr.join('_');
};

export const createOvernightDelayDomRt = (flights, flightInSeq = null) => {
  const arr = [0, 0];
  flights.forEach((item, index) => {
    const dayDiff = findDaysBetweenDates(item.departure, item.arrival);
    if (flightInSeq) {
      arr[flightInSeq[index].flightSequence - 1] = dayDiff;
    } else {
      arr.push(dayDiff);
    }
  });
  return arr.join('_');
};

export const createFlightSeq = (flights) => {
  const flightSelections = [];
  flights.forEach((item, index) => {
    flightSelections.push({
      sellableId: item.sellableId,
      flightSequence: index + 1,
    });
  });
  return flightSelections;
};

export const createBaggageDetailRequestBody = (flightsInSeq, dynamicId, overnightDelays) => {
  const reqObject = {};
  reqObject.flightSelections = flightsInSeq;
  reqObject.dynamicPackageId = dynamicId;
  reqObject.overnightDelay = overnightDelays;
  return reqObject;
};

export const getPriceText = (price) => {
  if (price === 0) {
    return '\u20B9 0 ';
  }
  else if (price > 0 || price < 0) {
    return price > 0 ? `+ ${rupeeFormatterUtils(price)}` : `- ${rupeeFormatterUtils(Math.abs(price))}`;
  }
  return ' ';
};

export const getFlightLegDetails = (flightLegs) => {
  const flightIds = [];
  const airlineCodes = new Set();
  if (flightLegs && flightLegs.length > 0) {
    flightLegs.forEach((leg, index) => {
      flightIds.push(leg.flightId);
      airlineCodes.add(leg.airlineCode);
    });
  }
  return {
    flightIds: flightIds,
    airlineCodes: Array.from(airlineCodes),
  };
};

export const persuasionTypes = {
  SPECIALFARE: 'SPECIALFARE',
  UNAVAILABLE: 'UNAVAILABLE',
};

export const createFlightMap = (departureFlights, returnFlights) => {
  const map = new Map();
  if (departureFlights && departureFlights.length > 0) {
    departureFlights.forEach((dep, i) => {
      dep.index = i;
      map.set(dep.sellableId, dep);
    });
  }
  if (returnFlights && returnFlights.length > 0) {
    returnFlights.forEach((dep, i) => {
      dep.index = i;
      map.set(dep.sellableId, dep);
    });
  }
  return map;
};

export const roundPrice = (price) => {
  if (isNumber(price)) {
    return Math.round(price);
  }
  return price;
};

export const persuasionText = {
  1: 'Flight unavailable with selected Departure Flight. Pair flight with -',
  0: 'Flight unavailable with selected Return Flight. Pair flight with -',
};

export const maxNoOfFlightIdsForUnavailabilityPersuasion = 2;

export const getTripName = (index, maxTrips, listingDataType) => {
  let tripName = '';
  if (listingDataType === flightDetailTypes.DOM_RETURN) {
    tripName = LoggingTripName[index];
  } else {
    tripName = `Trip ${index}`;
  }
  return tripName;
};

export const createFlightLogText = (flightSelections, listingDataType, priceDiff) => {
  const textArr = [];
  flightSelections.forEach((item, index) => {
    const tripName = getTripName(index, flightSelections.length - 1, listingDataType);
    textArr.push(`${tripName}_${item.sellableId}`);
  });
  if (textArr.length > 0) {
    let ret = textArr.join('_');
    if (isNumber(priceDiff)) {
      ret += `_${priceDiff}`;
    }
    return ret;
  }
  return '';
};

export const getAirlineIconUrl = (airlineCode) => {
  if (isEmpty(airlineCode)) {
    return '';
  }
  let code = airlineCode;
  if (airlineCode === 'Multiple Flight Codes') {
    code = 'MultipleFlightCodes';
  }
  return AIRLINE_IMAGE_BASEURL.replace('{CODE}', code);
};

export const getFlightOvernightDelay = (flight) => {
  const dayDiff = findDaysBetweenDates(flight.departure, flight.arrival);
  return dayDiff;
};
export const isOBT = (flightDetailType) => flightDetailType === flightDetailTypes.OBT;
export const isDOM_ONWARDS = (flightDetailType) =>
  flightDetailType === flightDetailTypes.DOM_ONWARDS;
export const isDOM_RETURN = (flightDetailType) =>
  flightDetailType === flightDetailTypes.DOM_RETURN;

export const applyDefaultFilters = ({
  flightUpgradeDetails = {},
  filterData = {},
  applyFilter = () => {},
  filterIndexs = [],
  flightType = '',
  from = '',
}) => {
  const OBT_DOM_ONWARDS_UPGRADE_INFO_INDEX = 0;
  const { flightUpgradeInfos = {} } = flightUpgradeDetails;
  filterIndexs?.map((fIndex) => {
    const upgradeInfoIndex =
      flightUpgradeInfos.length === 1 && flightUpgradeInfos?.[0].flightUpgradeDetailType === 'GROUP'
        ? OBT_DOM_ONWARDS_UPGRADE_INFO_INDEX
        : isDOM_RETURN(from)
        ? flightUpgradeInfos.findIndex((upgradeInfo)=> upgradeInfo.flightUpgradeDetailType === flightType)
        : OBT_DOM_ONWARDS_UPGRADE_INFO_INDEX;
    const upgradeInfo = flightUpgradeInfos?.[upgradeInfoIndex] || {};
    const { premiumAirlines = [] } = upgradeInfo;
    premiumAirlines.forEach((premiumAirline) => {
      filterData?.[fIndex].airlines.forEach((airline) => {
        if (airline.code === premiumAirline.code) {
          airline.selected = true;
        }
      });
    });
  });

  applyFilter(filterData);
};
