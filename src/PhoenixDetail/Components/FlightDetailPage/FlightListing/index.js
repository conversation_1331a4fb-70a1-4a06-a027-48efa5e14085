import React, {useState} from 'react';
import {Image, ScrollView, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import FlightListingCard from './FlightListingCard';
import PhoenixHeader from '../../PhoenixHeader';
import FlightShortListCard from './FlightShortListCard';
import FlightListingTabs from './FlightListingTabs';
import FlightListingCombo from './FlightListingCombo';
import FilterTag from './FlightFilterTag';
import iconBell from '../../images/ic_bell.png';
import iconAirlineLogo1 from '../../images/ic_airlineLogo1.png';
import iconAirlineLogo2 from '../../images/ic_airlineLogo2.png';

import AvailabilityTooltip from './FlightListingCard/AvailabilityTooltip';
import FlightFilterPage from '../FlightFilterPage';
import FlightSortPage from '../FlightSortPage';

const flightListingData = [
  {
    flName: 'IndiGo',
    flNumber: '6E 505',
    departureTime: '05:10',
    departureLoc: 'New Delhi',
    duration: '2h 40m',
    stops: '2 stops',
    arrivalTime: '07:50',
    arrivalLoc: 'Kochi',
    price: '200',
    unavailable: false,
  },
  {
    flName: 'IndiGo',
    flNumber: '6E 505',
    departureTime: '05:10',
    departureLoc: 'New Delhi',
    duration: '2h 40m',
    stops: '1 stops',
    arrivalTime: '07:50',
    arrivalLoc: 'Kochi',
    price: '200',
    unavailable: false,
  },
  {
    flName: 'IndiGo',
    flNumber: '6E 505',
    departureTime: '05:10',
    departureLoc: 'New Delhi',
    duration: '2h 40m',
    stops: '1 stops',
    arrivalTime: '07:50',
    arrivalLoc: 'Kochi',
    price: '200',
    unavailable: true,
  },
  {
    flName: 'IndiGo',
    flNumber: '6E 505',
    departureTime: '05:10',
    departureLoc: 'New Delhi',
    duration: '2h 40m',
    stops: '2 stops',
    arrivalTime: '07:50',
    arrivalLoc: 'Kochi',
    price: '200',
    unavailable: false,
  },
  {
    flName: 'IndiGo',
    flNumber: '6E 505',
    departureTime: '05:10',
    departureLoc: 'New Delhi',
    duration: '2h 40m',
    stops: '1 stops',
    arrivalTime: '07:50',
    arrivalLoc: 'Kochi',
    price: '200',
    unavailable: false,
  },
  {
    flName: 'IndiGo',
    flNumber: '6E 505',
    departureTime: '05:10',
    departureLoc: 'New Delhi',
    duration: '2h 40m',
    stops: '1 stops',
    arrivalTime: '07:50',
    arrivalLoc: 'Kochi',
    price: '200',
    unavailable: false,
  },

];

const tabMenuData = [
  {
    source: 'Departure',
    loc: 'BLR-COK',
    date: 'Jan 3',
  },
  {
    source: 'Return',
    loc: 'BLR-COK',
    date: 'Jan 6',
  },
];

const flightListingCombo = [
  {
    title: 'Flights Combo',
    price: '2000',
    tripData: [
      {
        trip: 1,
        flName: 'IndiGo',
        flNumber: '6E 505',
        departureTime: '05:10',
        departureLoc: 'New Delhi',
        duration: '2h 40m',
        stops: 'Non stops',
        arrivalTime: '07:50',
        arrivalLoc: 'Kochi',
        price: '200',
        airlineLogo: iconAirlineLogo1,
        slots: true,
      },
      {
        trip: 2,
        flName: 'IndiGo',
        flNumber: '6E 505',
        departureTime: '05:10',
        departureLoc: 'New Delhi',
        duration: '2h 40m',
        stops: 'Non stops',
        arrivalTime: '07:50',
        arrivalLoc: 'Kochi',
        price: '200',
        airlineLogo: iconAirlineLogo2,
        slots: false,
      },
      {
        trip: 3,
        flName: 'IndiGo',
        flNumber: '6E 505',
        departureTime: '05:10',
        departureLoc: 'New Delhi',
        duration: '2h 40m',
        stops: 'Non stops',
        arrivalTime: '07:50',
        arrivalLoc: 'Kochi',
        price: '200',
        airlineLogo: iconAirlineLogo1,
        slots: false,
      },
    ],

  },
  {
    title: 'Flights Combo',
    price: '2000',
    tripData: [
      {
        trip: 1,
        flName: 'IndiGo',
        flNumber: '6E 505',
        departureTime: '05:10',
        departureLoc: 'New Delhi',
        duration: '2h 40m',
        stops: 'Non stops',
        arrivalTime: '07:50',
        arrivalLoc: 'Kochi',
        price: '200',
        airlineLogo: iconAirlineLogo1,
        slots: false,
      },
      {
        trip: 2,
        flName: 'IndiGo',
        flNumber: '6E 505',
        departureTime: '05:10',
        departureLoc: 'New Delhi',
        duration: '2h 40m',
        stops: 'Non stops',
        arrivalTime: '07:50',
        arrivalLoc: 'Kochi',
        price: '200',
        airlineLogo: iconAirlineLogo2,
        slots: false,
      },
      {
        trip: 3,
        flName: 'IndiGo',
        flNumber: '6E 505',
        departureTime: '05:10',
        departureLoc: 'New Delhi',
        duration: '2h 40m',
        stops: 'Non stops',
        arrivalTime: '07:50',
        arrivalLoc: 'Kochi',
        price: '200',
        airlineLogo: iconAirlineLogo1,
        slots: false,
      },
    ],

  },
];

const FlightListing = () => {
  const [cardActive, setCardActive] = useState(0);
  const [tooltipActive, setTooltipActive] = useState(false);
  const [activeFilterModal, setActiveFilterModal] = useState(false);
  const [activeSortModal, setActiveSortModal] = useState(false);

  const cardToggleActive = (index) => {
    setCardActive(index);
  };
  const toggleFilterModal = () => {
    setActiveFilterModal(true);
  };
  const toggleSortModal = () => {
    setActiveSortModal(true);
  };
  const handleFilterModalClose = () => {
    setActiveFilterModal(false);
  };
  const handleSortModalClose = () => {
    setActiveSortModal(false);
  };
  const toggleTooltip = () => {
    setTooltipActive(!tooltipActive);
  };
  return (
    <View style={styles.pageWrap}>
      <PhoenixHeader handleClose={() => handleModalClose()}/>
      <FlightShortListCard/>
      <FlightListingTabs data={tabMenuData}/>
      <ScrollView>
        {flightListingData.map((item, index) =>
          <View>
            <TouchableOpacity
              onPress={() => cardToggleActive(index)}
              style={(index === cardActive) ? {backgroundColor: '#eaf5ff'} : {backgroundColor: 'white'}}
            >
              <FlightListingCard
                key={index}
                index={index}
                flName={item.flName}
                flNumber={item.flNumber}
                departureTime={item.departureTime}
                departureLoc={item.departureLoc}
                duration={item.duration}
                stopPointer={item.stopPointer}
                stops={item.stops}
                arrivalTime={item.arrivalTime}
                arrivalLoc={item.arrivalLoc}
                price={item.price}
                unavailable={item.unavailable}
                handleTooltip={toggleTooltip}
              />

            </TouchableOpacity>
            {item.unavailable && tooltipActive ?
              <AvailabilityTooltip/> : null}
          </View>,
        )}

        {/* combo Listing */}
        <FlightListingCombo data={flightListingCombo}/>
        <View style={styles.baggageInfoTag}>
          <View style={cStyles.marginRight10}><Image source={iconBell} style={styles.iconBell}/></View>
          <Text style={[cStyles.font11, cStyles.boldFont, cStyles.blackText]}>Re-check in of baggage required</Text>
        </View>
      </ScrollView>
      <FilterTag
        handleFilter={() => toggleFilterModal()}
        handleSort={() => toggleSortModal()}
      />
      <FlightFilterPage activeFilterModal={activeFilterModal} handleClose={() => handleFilterModalClose()}/>
      <FlightSortPage activeSortModal={activeSortModal} handleClose={() => handleSortModalClose()}/>
    </View>
  );
};

const styles = StyleSheet.create({
  pageWrap: {
    flex: 1,
  },

  iconBell: {
    width: 10,
    height: 14,
    resizeMode: 'cover',
  },
  baggageInfoTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: '#ffeae1',
    justifyContent: 'center',
    borderBottomColor: '#bababa',
    borderBottomWidth: 1,
  },
});

export default FlightListing;
