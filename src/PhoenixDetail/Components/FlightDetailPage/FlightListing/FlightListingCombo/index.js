import React, { useEffect, useMemo, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, FlatList, Modal, StyleSheet, View } from 'react-native';
import FlightComboCard from './FlightComboCard';
import FlightShortListCard from '../FlightShortListCard';
import {
  componentImageTypes,
  flightDetailTypes,
  packageActionComponent,
  packageActions,
} from '../../../../DetailConstants';
import {getDiscountedPrice, isNotNullAndEmptyCollection} from '../../../../../utils/HolidayUtils';
import {isEqual, cloneDeep} from 'lodash';
import {
  clearFlightGroups,
  filterFlightsOnAirlines,
  filterFlightsOnStops,
  filterFlightsOnTime,
  getDefaultFilterStateV2,
  getInitialSortingOptionsDataV2,
  getSegmentFlights,
  getSegments,
  isFilterApplied,
  isPriceRangeApplied,
  isSortingApplied,
  isSpecificFilterApplied, log_sortBy,
  logSelectedFilters,
  setSegmentFlights,
  sortingX,
} from '../../FlightFilterPage/FilterUtils';
import {
  applyDefaultFilters,
  createBaggageDetailRequestBody, createFlightLogText,
  createFlightSeq,
  getComboTripName,
  preProcessFlightComboData,
} from '../FlightsUtils';
import { HOLIDAYS_FLIGHT_OVERLAY, HOLIDAYS_FLIGHT_OVERLAY_LISTING } from '../../../../Utils/PheonixDetailPageConstants';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { paddingStyles } from '../../../../../Styles/Spacing';
import {
  holidayNavigationPop,
  holidayNavigationPush,
} from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/DetailPageNavigationUtils';
import { Overlay } from '../../../DetailOverlays/OverlayConstants';

/* Components */
import PhoenixHeader from '../../../PhoenixHeader';
import ConfirmationPopup from '../../../ConfirmationPopup/ConfirmationPopup';
import NotificationMessage from '../../../NotificationMessage';
import FilterTag from '../FlightFilterTag';
import FlightFilterPage from '../../FlightFilterPage';
import FlightSortPage from '../../FlightSortPage';
import HolidayPriceChangeMessage from '../../../Common/HolidayPriceChangeMessage';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import useBackHandler from '../../../../../hooks/useBackHandler';


const FlightListingCombo = (props) => {
  const {
    flightListingData,
    flightRequestObject,
    pricingDetail,
    onComponentChange,
    lastPage,
    onPackageComponentToggle,
    dynamicId,
    subtitleData,
    accessRestriction,
    trackLocalClickEvent,
  } = props;
  const {categoryPrices} = pricingDetail || {};
  const price = categoryPrices && categoryPrices.length > 0 ? categoryPrices[0].price : null;
  const {flightGroupRows = [], listingDataType, discountedFactor} = flightListingData || {};
  const flatListRef = useRef(null);
  const flightComboData = useMemo( () => {
   return preProcessFlightComboData(flightGroupRows, price, discountedFactor, listingDataType);
  }, [flightGroupRows]);

  const defaultFilterData = useMemo(() => {
    return getDefaultFilterStateV2(
      flightComboData.newFlightGroupRows,
      listingDataType,
      discountedFactor,
      price);
  }, [flightGroupRows]);

  const [state, setState] = useState({
    selectedGroupId: flightComboData.selectedGroupId,
    flightRequestObject: flightRequestObject,
    flightGroupRows: cloneDeep(flightComboData.newFlightGroupRows),
    filterData: defaultFilterData.filterData,
    sortingData: getInitialSortingOptionsDataV2(),
  });

  const [showFilters, setShowFilters] = useState(false);
  const [showSort, setShowSort] = useState(false);
  const [showConfirmationDialog, setShowConfirmationDialog] = useState(false);
  const [showPriceUpdatedMessage ,setshowPriceUpdatedMessage] = useState(true);
  const validCombo =  useMemo(() => {
      return state.flightRequestObject.flightSelections.map((item) => item.sellableId).join('#');
    }, [state.flightRequestObject]);

  const onCloseScreen = () => {
    holidayNavigationPop({
      overlayKeys: [props.overlayKey],
      hideOverlays: props.hideOverlays,
      navigationFunction: HolidayNavigation.navigate,
      navigationFunctionProps: lastPage,
    });
  };

  const onBackPress = () => {
    if (showConfirmationDialog) {
      setShowConfirmationDialog(false);
      onCloseScreen();
    } else if (!isComboActive()) {
      setShowConfirmationDialog(true);
    } else {
      onCloseScreen();
    }
    return true;
  };



  useBackHandler(onBackPress);

  useEffect(() => {
    if (props.showUpgradeableFlights) {
      applyDefaultFilters({
        flightUpgradeDetails: props.flightUpgradeDetails,
        filterData: defaultFilterData.filterData,
        filterIndexs: defaultFilterData.filterData.map((_, i) => i),
        from:listingDataType,
        flightType: 'GROUP',
        applyFilter,
      });
    }
  }, []);
  const captureClickEvents = ({ eventName = '', prop1 = '', suffix = '', value = ''}) => {
    logPhoenixDetailPDTEvents({
      value: value || eventName,
      actionType : PDT_EVENT_TYPES.buttonClicked,
      subPageName: prop1 || HOLIDAYS_FLIGHT_OVERLAY_LISTING
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: prop1 || HOLIDAYS_FLIGHT_OVERLAY_LISTING,
    });
  }
  const onSelectPress = (index, flightSelections, overnightDelays) => {
    setState({
      ...state,
      selectedGroupId: index,
      flightRequestObject: {
        ...state.flightRequestObject,
        overnightDelays: overnightDelays,
        flightSelections: flightSelections,
      },
    });
    const sIds = flightSelections.map((item, i) => {
      const {sellableId} = item;
      return sellableId;
    });
    const flightNumberText = sIds.join(',');
    captureClickEvents({
      eventName: 'flight_select_combo_',
      suffix: flightNumberText || '',
      value: `flight|select|combo|${flightNumberText}`,
    });
  };

  const isComboActive = () => {
    if (flightComboData.selectedGroupId === state.selectedGroupId) {
      return isEqual(state.flightRequestObject, flightRequestObject);
    }
    return false;
  };

  const createShortListDataJson = (flights, comboPrice) => {
    const packagePrice = getDiscountedPrice(price, discountedFactor);
    // const groupPrice = getPriceDiff(price, comboPrice, discountedFactor);
    return {
      listingDataType: listingDataType,
      data: flights,
      packagePrice: packagePrice + comboPrice,
      comboPrice: comboPrice,
      selected: isComboActive(),
      addonPrice:props?.pricingDetail?.categoryPrices[0]?.addonsPrice
    };
  };

  const shortListCardData = useMemo(() => {
    const {flightRequestObject: {flightSelections}, selectedGroupId, flightGroupRows} = state;
    if (flightGroupRows && flightGroupRows.length > 0 && selectedGroupId) {
      const flightGroup = flightComboData.flightGroupMap.get(selectedGroupId);
      const segments = getSegments(flightGroup, listingDataType);
      const flights = [];
      if (flightSelections && segments) {
        for (let i = 0; i < flightSelections.length; i++) {
          const {sellableId} = flightSelections[i];
          for (let j = 0; j < segments.length; j++) {
            const segmentFlights = getSegmentFlights(segments[j], listingDataType);
            const flightObj = segmentFlights.find(row => row.sellableId === sellableId);
            if (flightObj) {
              flights.push(flightObj);
              break;
            }
          }
        }
      }
      return createShortListDataJson(flights, flightGroup.currPackagePriceDiff);
    }
    return {};
  }, [state]);

  const filterFlightGroupRows = (filterData, flightGroups) => {
    for (let tripIndex = 0; tripIndex < filterData.length; tripIndex++) {
      if (!isFilterApplied(filterData[tripIndex])) {
        continue;
      }

      flightGroups = flightGroups.filter((group) => {
        let add = true;
        if (isPriceRangeApplied(filterData[tripIndex])) {
          const dp = group.currPackagePriceDiff;
          if (!(filterData[tripIndex].priceRange.min <= dp && dp <= filterData[tripIndex].priceRange.max)) {
            add = false;
          }
        }

        if (filterData[tripIndex].overnightFlights) {
          if (filterData[tripIndex].overnightFlights.selected) {
            const overnightFiltersArray = group.overnightDelays.split('_');
            if (overnightFiltersArray.length > tripIndex) {
              if (overnightFiltersArray[tripIndex] === '0') {
                add = false;
              }
            }
          }
        }
        return add;
      });

      flightGroups.forEach((group) => {
        const segments = getSegments(group, listingDataType);
        let segment = segments[tripIndex];

        if (isSpecificFilterApplied(filterData[tripIndex].noOfStops)) {
          const segmentFlights = getSegmentFlights(segment, listingDataType);
          const filteredFlights = filterFlightsOnStops(segmentFlights, filterData[tripIndex].noOfStops);
          setSegmentFlights(segment, filteredFlights, listingDataType);
        }
        if (isSpecificFilterApplied(filterData[tripIndex].departureTime)) {
          const segmentFlights = getSegmentFlights(segment, listingDataType);
          const filteredFlights = filterFlightsOnTime(segmentFlights, filterData[tripIndex].departureTime, 'DEP');
          setSegmentFlights(segment, filteredFlights, listingDataType);
        }
        if (isSpecificFilterApplied(filterData[tripIndex].arrivalTime)) {
          const segmentFlights = getSegmentFlights(segment, listingDataType);
          const filteredFlights = filterFlightsOnTime(segmentFlights, filterData[tripIndex].arrivalTime, 'ARL');
          setSegmentFlights(segment, filteredFlights, listingDataType);
        }
        if (isSpecificFilterApplied(filterData[tripIndex].airlines)) {
          const segmentFlights = getSegmentFlights(segment, listingDataType);
          const filteredFlights = filterFlightsOnAirlines(segmentFlights, filterData[tripIndex].airlines);
          setSegmentFlights(segment, filteredFlights, listingDataType);
        }
      });
    }
    flightGroups = clearFlightGroups(flightGroups, listingDataType);
    return flightGroups;
  };

  const applyFilter = (filterData = []) => {
    let newFlightGroupRows = filterFlightGroupRows(filterData, cloneDeep(flightComboData.newFlightGroupRows));
    if (newFlightGroupRows.length > 0) {
      /**
       * Since filters can only be applied on initial flight combos,
       * we need to apply sorting if sortBy filter was previously selected
       */
      if (isSortingApplied(state.sortingData)) {
        newFlightGroupRows = sortingX(
          newFlightGroupRows,
          state.sortingData,
          true
        );
      }

      setState({
        ...state,
        flightGroupRows: newFlightGroupRows,
        filterData: filterData,
      });
      setShowFilters(false);
      logSelectedFilters({ filterData, listingDataType });

      if (isEqual(state.filterData, filterData)) {
        captureClickEvents({eventName: 'filter_applied'});
      }
      flatListRef.current.scrollToIndex({ index: 0 });
      return true;
    }
    return false;
  };

  const handleFilterClose = (applyClicked,filterData = []) => {
    if (applyClicked && filterData) {
      return applyFilter(filterData);
    } else {
      setShowFilters(false);
      return true;
    }
  };

  const clearSortingOptions = () => {
    let newFlightGroupRows = filterFlightGroupRows(state.filterData, cloneDeep(flightComboData.newFlightGroupRows));
    setState({
      ...state,
      sortingData: getInitialSortingOptionsDataV2(),
      flightGroupRows: cloneDeep(newFlightGroupRows),
    });
  };

  const applySorting = (sortingOptions) => {
    if (!sortingOptions.isSortingApplied) {
      clearSortingOptions();
    } else {
      const sortedGroups = sortingX(
        [...state.flightGroupRows],
        sortingOptions,
        true
      );
      setState({
        ...state,
        sortingData: sortingOptions,
        flightGroupRows: sortedGroups,
      });
    }
    log_sortBy({data: sortingOptions, prefix: 'sort_', listingType: listingDataType, prop1 : HOLIDAYS_FLIGHT_OVERLAY_LISTING});
    setShowSort(false);
    flatListRef.current.scrollToIndex({ index: 0 });
  };
  const onUpdatePress = (params = {}) => {
    const { pageName = HOLIDAYS_FLIGHT_OVERLAY_LISTING } = params;
    const actionData = {
      action: packageActions.CHANGE,
      overnightDelays: state.flightRequestObject.overnightDelays,
      flightSelections: state.flightRequestObject.flightSelections,
    };
    onComponentChange(actionData, componentImageTypes.FLIGHT);
    const flightGroup = state.flightGroupRows[state.selectedGroupId];
    const price = flightGroup ? flightGroup.currPackagePriceDiff : null;
    const flightLog = createFlightLogText(actionData.flightSelections, listingDataType, price);
    captureClickEvents({
      eventName: 'update_',
      suffix: flightLog,
      value : `update|${flightLog}`,
      prop1: pageName
    });
    onCloseScreen();
  };

  const confirmBoxUpdatePress = () => {
    captureClickEvents({
      eventName: 'confirm_UPDATE',
    });
    onUpdatePress();
  };

  const onRemovePress = () => {
    onPackageComponentToggle(false, packageActionComponent.FLIGHT);
    onCloseScreen();
  };

  const openFlightDetailsPage = (flights, overnightDelays, active) => {
    const data = {initialScrollIndex: 0, flights: [], roundTrip: true};
    if (flights && flights.length > 0) {
      flights.forEach((item, index) => {
        data.flights.push({
          flight: item,
          heading: getComboTripName(listingDataType, index, flights.length),
        });
      });
    }

    let indexes = data.flights.map((item) => {
      const { flight = {} } = item || {};
      const { id = '' } = flight || {};
      return id;
    });
    indexes = indexes.join(',');
    captureClickEvents({
      eventName: 'view_details',
      suffix: indexes,
      value: `view_detail|${indexes}`
    });

    if (isNotNullAndEmptyCollection(indexes)) {
      const flightsInSeq = createFlightSeq(flights);
      const requestObject = createBaggageDetailRequestBody(flightsInSeq, dynamicId, overnightDelays);
      holidayNavigationPush({
        pageKey: HOLIDAY_ROUTE_KEYS.FLIGHT_DETAIL,
        overlayKey: Overlay.FLIGHT_DETAIL,
        showOverlay: props.showOverlay,
        hideOverlays: props.hideOverlays,
        props: {
          data,
          onUpdatePress: onUpdatePress,
          shortListCardData: shortListCardData,
          onRemovePress: onRemovePress,
          requestObject: requestObject,
          subtitleData: subtitleData,
          accessRestriction: accessRestriction,
          showOptions: active,
          trackLocalClickEvent: trackLocalClickEvent,
        },
      });
    }
  };

  const onNotNowClicked = () => {
    captureClickEvents({
      eventName: 'confirm_NOT NOW',
    });
    onCloseScreen();
  };

  const onShowMeClicked = () => {
    captureClickEvents({
      eventName: 'confirm_SHOW SELECTION',
    });
    setShowConfirmationDialog(false);
  };

  const handleFilter = () => {
    setShowFilters(true);
    captureClickEvents({
      eventName: 'filter_clicked',
    });
  };

  const handleSort = () => {
    setShowSort(true);
    captureClickEvents({
      eventName: 'sort_clicked',
    });
  };
  const closePriceMessage = ()=> {
    setshowPriceUpdatedMessage(false)
  };
  const renderItem = ({item}) => {
    return (
      <FlightComboCard
        item={item}
        validCombo={validCombo}
        active={state.selectedGroupId === item.uid}
        selectedGroupId={item.uid}
        key={item.uid}
        oldPrice={price}
        onSelectPress={onSelectPress}
        listingDataType={listingDataType}
        discountedFactor={discountedFactor}
        onViewDetailPress={openFlightDetailsPage}
        trackLocalClickEvent={trackLocalClickEvent}
      />
    );
  };
  const { displayMessages } = flightGroupRows[state.selectedGroupId] || {};
  const { AIRPORT_TRANSFER_MESSAGE :airPortTransferMessage } = displayMessages || {}
  return state.flightGroupRows && state.flightGroupRows.length ? (
    <View style={styles.pageWrap}>
      <PhoenixHeader title={'Change Flight'} subtitleData={subtitleData} handleClose={onBackPress}/>
      <FlightShortListCard {...shortListCardData} onUpdatePress={onUpdatePress} pageName = {HOLIDAYS_FLIGHT_OVERLAY_LISTING}/>
      {showPriceUpdatedMessage && <HolidayPriceChangeMessage closePriceMessage={ closePriceMessage }
      airPortTransferMessage = { airPortTransferMessage }/>
      }
      {props.isFlightFailed && <NotificationMessage
        type="error"
        message="Oops! It seems the included flight(s) are sold out, please look for another option"
      />}
      <FlatList
        ref={flatListRef}
        data={state.flightGroupRows}
        showsVerticalScrollIndicator={false}
        renderItem={renderItem}
        keyExtractor={(item, index) => index}
        contentContainerStyle={styles.flightCardListContainer}
        />
      <FilterTag
        handleFilter={handleFilter}
        handleSort={handleSort}
      />
      <Modal
        onRequestClose={()=>  {setShowFilters(false); setShowSort(false);}}
        animationType="slide"
        transparent={true}
        visible={showFilters || showSort}
        propagateSwipe={true}
      >
        {showFilters &&
          <FlightFilterPage
            currentTabIndex={0}
            tabData={defaultFilterData.tabName}
            filterData={state.filterData}
            handleFilterClose={handleFilterClose}
            listingDataType={listingDataType}
          />}
        {showSort &&
          <FlightSortPage
            sortingOptionsData={{...state.sortingData}}
            handleClose={() => {
              setShowSort(false);
            }}
            applySorting={applySorting}
            defaultSortionOptions={() => getInitialSortingOptionsDataV2()}
          />}
      </Modal>
      <ConfirmationPopup
        confirmDialogVisibility={showConfirmationDialog}
        onUpdatePackageClickFromPopup={confirmBoxUpdatePress}
        onNotNowClicked={onNotNowClicked}
        onShowMeClicked={onShowMeClicked}
      />
    </View>
  ) : [];
};

const styles = StyleSheet.create({
  pageWrap: {
    flex: 1,
    backgroundColor: holidayColors.lightGray2,
  },
  flightCardListContainer: {
    ...paddingStyles.pa16,
    paddingBottom: 80,
  },
});
export default FlightListingCombo;
