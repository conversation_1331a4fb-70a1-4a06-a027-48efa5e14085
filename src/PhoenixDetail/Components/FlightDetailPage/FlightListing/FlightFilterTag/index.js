import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';

import iconFilter from '../../../images/ic_filter.png';
import iconSort from '../../../images/ic_sort.png';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import BottomTabs from '@Frontend_Ui_Lib_App/BottomTabs'; 
import {holidayBorderRadius} from '../../../../../Styles/holidayBorderRadius'
import {paddingStyles} from '../../../../../Styles/Spacing'


const FilterTag = (props) => {
  const {handleSort, handleFilter} = props;
  const data=[
    {
      icon: iconSort,
      label: 'Sort',
      onPress: handleSort,
    },
    {
      icon: iconFilter,
      label: 'Filter',
      onPress: handleFilter,
    }
  ]
  return (
    <View style={styles.filterTagWrap}>
      <View>
      <BottomTabs
        customStyles={styles.customStyles}
        data={data}
      />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  filterTagWrap: {
    width: '100%',
    position: 'absolute',
    bottom: 0,
    alignItems: 'center',
    zIndex: 4,
  },
  customStyles: {
    wrapperStyle: {
      width: '100%',
      ...paddingStyles.pv10,
      ...holidayBorderRadius.borderRadius4,
    }
  },
  filterTag: {
    backgroundColor: holidayColors.darkGray,
    borderRadius: 25,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    paddingHorizontal: 15,
    width: 320,
  },
  filterTab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingVertical: 10,
  },
  iconFilter: {
    width: 24,
    height: 24,
    resizeMode: 'cover',
  },
  filterText: {
    color: '#dbdbdb',
    ...fontStyles.labelBaseRegular,
  },
  divider: {
    borderRightWidth: 1,
    borderColor: holidayColors.lightGray,

  },
});

export default FilterTag;
