import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  Animated,
  Easing,
  ScrollView,
  TouchableHighlight,
} from 'react-native';
import ReactNative from 'react-native';
import PropTypes from 'prop-types';
import ExtraInfoSection from './ExtraInfoSection';
import {extraInfoHeadings, extraInfoRefs, OVERLAY_FULL_BOTTOM} from '../DetailConstants';
import {TNC_SEPERATOR} from '../../HolidayConstants';
import {animateOverlay} from '../Utils/HolidayDetailUtils';
import PageHeader from '../../Common/Components/PageHeader';
import { paddingStyles } from '../../Styles/Spacing';

class ExtraInfoOverlay extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      overlayPosition: new Animated.Value(0),
    };
  }

  static navigationOptions = {header: null};

  componentDidMount() {
    animateOverlay(this, OVERLAY_FULL_BOTTOM);
    // setTimeout(() =>
    //   this.scrollToItem(this.fetchRefAsPerSectionToShow(this.props.sectionToShow)), 0);
  }

  fetchRefAsPerSectionToShow = (sectionToShow) => {
    let refToReturn = '';
    switch (sectionToShow) {
      case extraInfoRefs.EXCLUSIONS:
        refToReturn = this.exclusionRef;
        break;
      case extraInfoRefs.TNC:
        refToReturn = this.tncRef;
        break;
      case extraInfoRefs.CANCELLATION_POLICY:
        refToReturn = this.cancellationRef;
        break;
      default:
    }
    return refToReturn;
  };


  headingSectionToShow = (sectionToShow) => {
    let heading = extraInfoHeadings.MAIN_HEADING;
    switch (sectionToShow) {
      case extraInfoRefs.EXCLUSIONS:
      case extraInfoRefs.TNC:
        heading = 'Terms and Conditions';
        break;
      case extraInfoRefs.CANCELLATION_POLICY:
        heading = 'Cancellation and Policies';
        break;
      default:
    }
    return heading;
  };

  startAnimate(bottom, duration, delay) {
    Animated.timing(this.state.overlayPosition, {
      toValue: bottom,
      easing: Easing.easeInOut,
      duration,
      delay,
    })
      .start();
  }

  scrollToItem = (refToShow) => {
    if (refToShow) {
      refToShow.measureLayout(
        ReactNative.findNodeHandle(this.scrollViewRef),
        (x, y) => {
          this.scrollViewRef.scrollTo({
            x: 0,
            y,
            animated: true,
          });
        }
      );
    }
  };

  setScrollViewRef = (element) => {
    this.scrollViewRef = element;
  };
  setExclusionRef = (element) => {
    this.exclusionRef = element;
  };
  setTncRef = (element) => {
    this.tncRef = element;
  };
  setCancellationRef = (element) => {
    this.cancellationRef = element;
  };

  render() {
    const {exclusions, tnc, cancellationPolicies} = this.props;
    let exclusionsArr = [];
    let tncArr = [];
    if (exclusions && exclusions !== '') {
      exclusionsArr = exclusions.split(TNC_SEPERATOR);
    }
    if (tnc && tnc !== '') {
      tncArr = tnc.split(TNC_SEPERATOR);
    }
    return (
      <View style={styles.overlayContainer}>
        <TouchableHighlight style={styles.overlayBg}><Text>.</Text></TouchableHighlight>
        <Animated.View style={[styles.overlayContent, {bottom: this.state.overlayPosition}]}>
          <PageHeader
            showBackBtn
            showShadow
            onBackPressed={() => this.props.togglePopup('')}
            title={this.headingSectionToShow(this.props.sectionToShow)}
          />
          <ScrollView style={styles.cardList} ref={this.setScrollViewRef}>
            <View style={[styles.tnCWrapper]}>
              {
                exclusionsArr.length > 0 &&
                <View ref={this.setExclusionRef}>
                  <ExtraInfoSection items={exclusionsArr} title={extraInfoHeadings.EXCLUSIONS}/>
                </View>
              }
              {
                tncArr.length > 0 &&
                <View ref={this.setTncRef}>
                  <ExtraInfoSection items={tncArr} title={extraInfoHeadings.TNC}/>
                </View>
              }
              {
                cancellationPolicies && cancellationPolicies.length > 0 &&
                <View ref={this.setCancellationRef}>
                  <ExtraInfoSection
                    items={cancellationPolicies}
                    title={extraInfoHeadings.CANCELLATION_POLICY}
                  />
                </View>
              }
            </View>
          </ScrollView>
        </Animated.View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  overlayContainer: {
    justifyContent: 'flex-end',
    position: 'absolute',
    bottom: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 12,
    elevation: 12,
  },
  overlayBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 3,
    elevation: 3,
  },
  overlayContent: {
    backgroundColor: '#fff',
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 4,
    zIndex: 4,
    position: 'absolute',
    bottom: 0,
    marginBottom: -700,
    width: '100%',
    shadowOffset: {
      width: 1,
      height: 0,
    },
    height: '100%',
  },
  tnCWrapper: {
    padding: 16,
  },
});

ExtraInfoOverlay.propTypes = {
  cancellationPolicies: PropTypes.array.isRequired,
  togglePopup: PropTypes.func.isRequired,
  sectionToShow: PropTypes.string.isRequired,
  exclusions: PropTypes.string,
  tnc: PropTypes.string,
};

ExtraInfoOverlay.defaultProps = {
  exclusions: '',
  tnc: '',
};
export default ExtraInfoOverlay;
