import React from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import PropTypes from 'prop-types';

const backArrowAndroid = require('@mmt/legacy-assets/src/backArrowAndroid.webp');

const OverlayHeader = ({title, handleBack, showBackBtn}) => (
  <View style={styles.header}>
    {showBackBtn ?
      <TouchableOpacity onPress={handleBack} style={styles.backWrapper}>
        <Image style={styles.iconBack} source={backArrowAndroid}/>
      </TouchableOpacity> : <View style={styles.backWrapper}/>
    }
    <Text
      numberOfLines={1}
      ellipsizeMode="tail"
      style={styles.title}>{title}</Text>
  </View>
);
const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingVertical: 20,
    height: 60,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    elevation: 4,
    zIndex: 4,
    paddingRight: 15,
  },
  backWrapper: {
    paddingRight: 10,
    paddingLeft: 10,
    paddingVertical: 15,

  },
  title: {
    flex: 5,
    color: '#000000',
    fontFamily: 'Lato-Bold',
    letterSpacing: 0.3,
    fontSize: 18,

  },
  iconBack: {
    height: 24,
    width: 24,

  },
});

OverlayHeader.propTypes = {
  handleBack: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  showBackBtn: PropTypes.bool.isRequired,
};
export default OverlayHeader;
