import React from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import SnackBar from '@Frontend_Ui_Lib_App/SnackBar';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../Styles/holidayColors';

const PackageUpdatedToast = ({showPackageUpdatedToast, setToastMessageState}) => {

  const hideToast = () => {
    if (showPackageUpdatedToast) {
      setToastMessageState(false);
    }
  };

  return showPackageUpdatedToast ? (
       <SnackBar
      isVisible={true}
      bgColor={[holidayColors.gray]}
      content="Your package has been updated."
      onDismiss={hideToast}
      barPosition={80}
      customStyle={styles.snackBarStyle}
      /> 
  ) : [];
};

const styles = StyleSheet.create({
  snackBarStyle: {
    
    wrapperStyle: {
      alignItems:'center',
      paddingHorizontal:15,
      paddingVertical:15,
      borderRadius:10
     
    },
    
  }
});

export default PackageUpdatedToast;
