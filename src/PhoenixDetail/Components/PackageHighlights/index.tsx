import React from 'react';
import isEmpty from 'lodash/isEmpty';
import {ImageDetail2} from '../../Types/PackageDetailApiTypes';
import { isRawClient } from '../../../utils/HolidayUtils';
import PhoenixVideoSection from '../HolidaysVideoPlayer/PhoenixVideoSection';
import ImageCarousel from '../Gallery/ImageCarousel';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { getPokusForGalleryV2, getPokusForNewDetailContent } from '../../../utils/HolidaysPokusUtils';
import { showOverlay } from '../DetailOverlays/Redux/DetailOverlaysActions';
import { connect } from 'react-redux';
import { Overlay } from '../DetailOverlays/OverlayConstants';

interface PackageHighlightsProps {
  imageDetail: ImageDetail2;
  videoUrl: string;
  videoPaused: boolean;
  onBackPressed: any;
  showOverlay: (key: string, data: object) => {};
}

const PackageHighlights = ({imageDetail, videoUrl, videoPaused, trackLocalClickEvent,showOverlay, fromPresales = false}: PackageHighlightsProps) => {
  const {images}: ImageDetail2 = imageDetail || {};
  const showGallery: boolean = images && images.length > 0 || false;

  const openGallery = () => {
    trackLocalClickEvent('gallery_open', '', { sendGIData: isRawClient() });
    const isOpenGalleryV2 =
      (getPokusForGalleryV2() || getPokusForNewDetailContent(fromPresales)) &&
      imageDetail?.gallery?.length > 0;
    if (isRawClient()) {
      showOverlay(isOpenGalleryV2 ? Overlay.GRID_GALLERY_V2 : Overlay.GRID_GALLERY, {});
    } else {
      HolidayNavigation.push(
        isOpenGalleryV2
          ? HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY_V2
          : HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY,
      );
    }
  };

  if (!isEmpty(videoUrl) && !isRawClient()) {
    return (
      <PhoenixVideoSection
        togglePopup={() => {
        }}
        videoUrl={videoUrl}
        openGallery={() => openGallery()}
        videoPaused={videoPaused}
        showGallery={showGallery}
        repeat={true}
      />
    );
  } else if ((isEmpty(videoUrl) || isRawClient()) && showGallery) {
    return (
      <ImageCarousel
        images={images}
        onImagePress={() => openGallery()}
      />
    );
  }
  return [];
};

const mapDispatchToProps = dispatch => ({
  showOverlay: (key, data) => dispatch(showOverlay(key, data)),
});

export default connect(null, mapDispatchToProps)(PackageHighlights);
