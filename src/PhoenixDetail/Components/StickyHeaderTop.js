import React from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import PropTypes from 'prop-types';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {isRawClient} from '../../utils/HolidayUtils';
import { PageHeaderBackButton, PageHeaderTitle } from '../../Common/Components/PageHeader';
import { holidayColors } from '../../Styles/holidayColors';
import { paddingStyles } from '../../Styles/Spacing';

const iconShareBlack = require('@mmt/legacy-assets/src/iconShareBlack.webp');
const iconLikeBlack = require('@mmt/legacy-assets/src/iconLikeBlack.webp');
const iconLikeFilled = require('@mmt/legacy-assets/src/gray_heart.webp');

export default class StickyHeaderTop extends React.Component {
  render() {
    const activeBtnOpacity = 0.7;
    const {
      duration, onBackPressed, showActions,
      packageName, sharePackage, isShortListed, updateShortListedPackage,
    } = this.props;
    return (
      <View style={styles.headerWrapper}>
        <PageHeaderBackButton onBackPressed={onBackPressed}/>
        <View style={styles.headerText}>
          <PageHeaderTitle title={packageName} titleStyles={styles.packageName}/>
          {!!duration && (
            <PageHeaderTitle
              title={`| ${duration}N. ${duration + 1}D`?.toUpperCase()}
            />
          )}
        </View>
        {showActions &&
        <View style={[AtomicCss.pushRight, AtomicCss.flexRow]}>
          {!isRawClient() &&
            <TouchableOpacity
              activeOpacity={activeBtnOpacity}
              style={styles.shareWrapper}
              onPress={() => sharePackage()}
            >
              <Image style={styles.iconShare} source={iconShareBlack}/>
            </TouchableOpacity>
          }
          {!isRawClient() &&
            <TouchableOpacity
              activeOpacity={activeBtnOpacity}
              style={styles.likeWrapper}
              onPress={() => updateShortListedPackage(!isShortListed)}
            >
              <Image
                style={styles.iconLike}
                source={isShortListed ? iconLikeFilled : iconLikeBlack}
                />
            </TouchableOpacity>
          }
        </View>
        }
      </View>
    );
  }
}
const styles = StyleSheet.create({
  iconShare: {
    width: 15,
    height: 16,
  },
  iconLike: {
    width: 16,
    height: 16,
  },
  shareWrapper: {
    paddingVertical: 15,
    paddingHorizontal: 5,
    paddingTop: 17,
    marginRight: 10,
  },
  likeWrapper: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    paddingTop: 17,
  },
  headerWrapper: {
    flexDirection: 'row',
    backgroundColor: holidayColors.white,
    borderBottomWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...paddingStyles.pa16,
    alignItems: 'center',
  },
  packageName: {
    width: 150,
  },
  headerText: {
    flexDirection: 'row',
    flex: 1,
  },
});

StickyHeaderTop.propTypes = {
  packageName: PropTypes.string,
  duration: PropTypes.number,
  isShortListed: PropTypes.bool,
  updateShortListedPackage: PropTypes.func,
  sharePackage: PropTypes.func,
  onBackPressed: PropTypes.func.isRequired,
  showActions: PropTypes.bool.isRequired,
};

StickyHeaderTop.defaultProps = {
  isShortListed: false,
  updateShortListedPackage: null,
  sharePackage: null,
  duration: null,
  packageName: null,
};
