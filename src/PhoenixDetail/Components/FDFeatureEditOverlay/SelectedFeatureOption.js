import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';

const SelectedFeatureOption = ({
  heading,
  description,
}) => {
  const containerStyle = [styles.optionContainer,styles.activeOptionContainer];
  return (
    <View style={styles.container}>
      <View style={styles.mb10}>
        {!!heading && <Text style={styles.heading}>{heading}</Text>}
      </View>
          <TouchableOpacity
            style={containerStyle}
            activeOpacity={0.8}
            onPress={() => {}}
          >
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Image
                  style={styles.tick}
                  source={require('../images/tick-blue-circular.png')}
                />
              <Text style={styles.optionText}>Included</Text>
            </View>
              <Text style={[styles.selectedText]}>SELECTED</Text>
          </TouchableOpacity>
      {!!description && <Text style={styles.description}>{description}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  description: {
    marginVertical: 11,
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  tick: {
    width: 14,
    height: 14,
    marginRight: 8,
  },
  selectedText: {
    color: '#008cff',
    fontFamily: 'Lato-Bold',
    fontSize: 10,
  },
  price: {
    fontSize: 12,
    fontFamily: 'Lato',
  },
  optionText: {
    color: '#000',
    fontSize: 14,
    fontFamily: 'Lato-Bold',
  },
  optionContainer: {
    backgroundColor: '#fff',
    marginBottom: 10,
    paddingVertical: 13,
    paddingLeft: 37,
    paddingRight: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 4,
    shadowColor: 'rgba(0, 0, 0, 0.4)',
    shadowOpacity: 0.4,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    elevation: 2,
  },
  activeOptionContainer: {
    borderColor: '#008cff',
    borderWidth: 1,
    paddingLeft: 15,
  },
  container: {
    paddingHorizontal: 15,
  },
  heading: {
    marginTop: 30,
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  subHeading: {
    marginTop: 4,
    ...fontStyles.labelBaseRegular,
    color: holidayColors.lightGray,
  },
  mb10: { marginBottom: 10 },
});

export default SelectedFeatureOption;
