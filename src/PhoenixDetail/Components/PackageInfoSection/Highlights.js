import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { isEmpty } from 'lodash';
import BottomSheetOverlay from '../../../Common/Components/BottomSheetOverlay';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import GreenTick from '@mmt/legacy-assets/src/green_tick.webp';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
const HighlightContainer = ({ detailData }) => {
  const { packageDetail } = detailData || {};
  const { destinationDetail, packageConfigDetail, packageAddOnTags = [] } = packageDetail || {};
  const { packageType } = packageConfigDetail || {};
  const { duration } = destinationDetail || {};

  const highlights = [
    {
      title: `${duration}N / ${duration + 1}D`,
    },
    {
      title: packageType?.title || '',
      subTitle: `The package you've selected is a ${
        packageType?.title || ''
      }, here are some highlights.`,
      description: packageType?.highlights?.length > 0 ? packageType.highlights : null,
    },
    ...packageAddOnTags,
  ];

  const renderHighlights = ({ item, index }) => {
    const { title = '' } = item || {};
    if (isEmpty(title)) {
      return null;
    }
    return <HighlightTextContainer {...item} />;
  };
  return (
    <View style={styles.highlightContainer}>
      <FlatList
        data={highlights}
        renderItem={renderHighlights}
        horizontal
        contentContainerStyle={styles.highlightItems}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};
const HighlightTextContainer = ({ title = '', subTitle = '', description = [] }) => {
  const [modalToggle, setModalToggle] = useState(false);

  const toggleModal = () => {
    setModalToggle(!modalToggle);
  };

  const handleOpenModal = () => {
    if (isEmpty(description)) {
      return;
    }
    setModalToggle(true);
  };
  return (
    <View>
      <TouchableOpacity activeOpacity={1} onPress={handleOpenModal}>
        <View style={styles.highlightTextContainer}>
          <Text style={styles.highlightText}>{title}</Text>
        </View>
      </TouchableOpacity>

      {description && modalToggle ? (
        <BottomSheetOverlay
          title={title}
          toggleModal={toggleModal}
          containerStyles={styles.modalContainer}
          visible={(description && modalToggle)}
          headingContainerStyles={styles.headingContainerStyles}
        >
          {!!subTitle && <Text style={styles.subTitle}>{subTitle}</Text>}
          {description.length === 1 ? (
            <View style={styles.descriptionItem}>
              <Text style={styles.hightlightDesc}>{description?.[0] || ''}</Text>
            </View>
          ) : (
            description.map((desc) => {
              return (
                <View style={styles.descriptionItem}>
                  <HolidayImageHolder
                    defaultImage={GreenTick}
                    style={{ width: 8, height: 8, marginRight: 6, padding: 8 }}
                  />
                  <Text style={styles.hightlightDesc}>{desc}</Text>
                </View>
              );
            })
          )}
        </BottomSheetOverlay>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  highlightContainer: {
    flexDirection: 'row',
  },
  highlightItems: {
    paddingBottom: 5,
  },
  highlightTextContainer: {
    borderWidth: 1,
    borderColor: holidayColors.lightGray,
    padding: 4,
    borderRadius: 5,
    marginRight: 5,
    paddingHorizontal:7,
  },
  highlightText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  subTitle: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    lineHeight: 19,
    marginBottom: 16,
    paddingTop:10
  },
  descriptionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  highlightItem: {
    width: 5,
    height: 5,
    backgroundColor: holidayColors.gray,
    borderRadius: 2.5,
    marginRight: 5,
  },
  hightlightDesc: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
    paddingVertical: 2,
  },
  modalContainer: {
    padding: 16,
  },
  headingContainerStyles:{
    paddingBottom: 10
  }
});
export default HighlightContainer;
