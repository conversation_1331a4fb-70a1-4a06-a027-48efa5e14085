import React, { useEffect, useState } from 'react';
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { isEmpty } from 'lodash';
import { packageVariantsPromise } from '../../../utils/HolidayNetworkUtils';
import BottomSheetOverlay from '../../../Common/Components/BottomSheetOverlay';
import { rupeeFormatterUtils } from '../../../utils/HolidayUtils';
import RadioButtonGroup from '../../../Common/Components/RadioButtonGroup';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import PrimaryButton from '../../../Common/Components/Buttons/PrimaryButton';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

const getInitialSelectedIndex = (data) => {
  return data?.variants?.findIndex((item) => item.selected);
};

const PackageVariantModal = ({
  dynamicPackageId,
  description = '',
  updatePackage = null,
  closeModal,
}) => {
  const [loading, setLoading] = useState(true);
  const [variantData, setVariantData] = useState({});
  const [showSelection, setShowSelection] = useState(getInitialSelectedIndex(variantData) || 0);
  const fetchPackageVariantData = async () => {
    try {
      const resp = await packageVariantsPromise(dynamicPackageId);
      const variantData = await resp.json();
      handlePackageVariantResponse({ loading: false, variantData });
    } catch (e) {
      handlePackageVariantResponse({ loading: true, variantData: {} });
    }
  };

  const handlePackageVariantResponse = ({ loading = true, variantData = {} }) => {
    setLoading(loading);
    setVariantData(variantData);
  };

  useEffect(() => {
    trackPhoenixDetailLocalClickEvent({ eventName: 'package_variant_view' });
    fetchPackageVariantData();
  }, []);

  const renderProgressView = () => {
    return (
      <View style={styles.progressView}>
        <Spinner
          size={36}
          strokeWidth={4}
          progressPercent={85}
          speed={1.5}
          color={holidayColors.primaryBlue}
          />
      </View>
    );
  };

  const renderPackageVariant = ({ item, index }) => {
    const priceDiffVal = item.priceDelta;
    const isPriceDiffMore = priceDiffVal >= 0;
    const initialSelectedIndex = getInitialSelectedIndex(variantData);

    const updateSelection = () => {
      setShowSelection(index);
    };
    const toggleSelection = () => {
      trackPhoenixDetailLocalClickEvent({
        eventName: 'package_variant_select_',
        suffix: item?.variantType,
      });
      updatePackage(item?.packageId);
      closeModal();
    };
    return (
      <View>
        <RadioButtonGroup
          optionText={item.variantType}
          selected={index === showSelection}
          handleClick={updateSelection}
        >
          <Text style={styles.optionText}>
            {rupeeFormatterUtils(priceDiffVal)}
            <Text style={styles.price}>/P</Text>
          </Text>
        </RadioButtonGroup>
        {index === showSelection && index !== initialSelectedIndex && (
          <View style={styles.noteCategory}>
            <View style={styles.leftCategoryContent}>
              <Text style={[styles.noteTitle]}>Note:</Text>
              <Text style={styles.noteText}>
                Selecting a new package type will reload the page and might lead to loss of any
                customizations that were made. You can make the required changes again.
              </Text>
            </View>
            <PrimaryButton
              buttonText={'SELECT'}
              btnContainerStyles={styles.selectBtn}
              handleClick={toggleSelection}
            />
          </View>
        )}
      </View>
    );
  };

  return (
    <View>
      {loading && renderProgressView()}
      {!loading && (
        <View>
          <Text style={styles.description}>{description}</Text>
          <FlatList
            data={variantData.variants}
            keyExtractor={(_, index) => `${index}`}
            renderItem={renderPackageVariant}
            ItemSeparatorComponent={() => <View style={styles.seperator} />}
          />
        </View>
      )}
    </View>
  );
};
const PackageVariantsSection = ({
  packageVariants = {},
  dynamicPackageId = '',
  updatePackage = null,
}) => {
  const selectedPackageVariant = packageVariants.find((variant) => variant.selected);
  const noOfVariants = packageVariants?.length || 0;
  if (isEmpty(selectedPackageVariant) || noOfVariants <= 1) {
    return null;
  }
  const name = selectedPackageVariant?.type || '';
  const description = selectedPackageVariant?.description || '';
  const [variantModal, toggleVariantModal] = useState(false);
  const handleVariantModal = () => {
    toggleVariantModal(!variantModal);
  };
  return (
    <View>
      {!!name && (
        <View style={{ flexDirection: 'row', marginTop: 5 }}>
          <Text style={styles.variantName}>{name}</Text>
          <TouchableOpacity onPress={handleVariantModal} activeOpacity={1}>
            <Text style={styles.moreVariants}>{` (${noOfVariants - 1} More Type)`}</Text>
          </TouchableOpacity>
        </View>
      )}
      {variantModal && (
        <BottomSheetOverlay 
        title={'Select Package Type'} 
        toggleModal={handleVariantModal}
        visible={variantModal}
        containerStyles={styles.containerStyles}
        >
          <PackageVariantModal
            dynamicPackageId={dynamicPackageId}
            description={description}
            updatePackage={updatePackage}
            closeModal={handleVariantModal}
          />
        </BottomSheetOverlay>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  containerStyles: {
    ...paddingStyles.pa16,
  },
  progressView: {
    width: '100%',
    height: 200,
    backgroundColor: '#ffffffd9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  description: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    paddingBottom: 10,
    width: '90%',
  },
  price: {
    ...fontStyles.labelBaseRegular,
  },
  optionText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
  },
  optionContainer: {
    backgroundColor: holidayColors.white,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  btnContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  seperator: {
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
    marginVertical: 10,
  },
  variantName: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  moreVariants: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelBaseBold,
  },

  // NOTE STYLES
  noteTitle: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  noteCategory: {
    marginTop: 16,
    backgroundColor: holidayColors.lightGray2,
    borderRadius: 2,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  noteText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
    lineHeight: 16,
  },
  leftCategoryContent: {
    width: '60%',
  },
  selectBtn: {
    backgroundColor: holidayColors.primaryBlue,
    paddingHorizontal: 20,
  },
});
export default PackageVariantsSection;
