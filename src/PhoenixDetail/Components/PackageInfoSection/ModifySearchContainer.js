import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { getPokusForNewDetailContent } from '../../../utils/HolidaysPokusUtils';
import ModifySearchPresales from '../../../MimaPreSales/Components/ModifySearch';
import ModifySearch from '../ModifySearch';
import { gradientColors, holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing';

const ModifySearchContainer = ({
  roomDetails,
  editTravelDetails,
  fromPreSales = false,
  showNewGallery = false,
  count = 0,
}) => {
  const modifySearchColors = {
      linearGradientColor: holidayColors.lightBlueBg,
      borderLineColor: holidayColors.primaryBlue,
      pillColor: holidayColors.white,
  };
  return (
    <View>
      <LinearGradient
        start={{ x: 1.0, y: 0.0 }}
        end={{ x: 0.0, y: 1.0 }}
        colors={[gradientColors.lightBlueOpacity55, gradientColors.whiteOpacity76]}
        useAngle={true}
        angle={270}
      >
        <View style={[styles.borderLineNew]}>
          <LinearGradient
            colors={[
              count > 0 ? holidayColors.fadedYellow : holidayColors.white,
              count > 0 ? holidayColors.fadedYellow : holidayColors.white,
            ]}
            start={{ x: 1.0, y: 0.0 }}
            end={{ x: 0.0, y: 1.0 }}
            style={count > 0 ? styles.withMessageBackground : styles.withoutMessageBackground}
          />
        </View>
        <View
          style={[
            styles.modifySearchContainer,
            count > 0 ? {} : styles.withoutMessagesMargin,
            {
              backgroundColor: modifySearchColors.pillColor,
              borderColor: modifySearchColors.borderLineColor,
            },
          ]}
        >
          {fromPreSales ? (
            <ModifySearchPresales
              editTravelDetails={editTravelDetails}
              roomDetails={roomDetails}
            />
          ) : (
            <ModifySearch
              editTravelDetails={editTravelDetails}
              roomDetails={roomDetails}
              showNewGallery={showNewGallery}
            />
          )}
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  modifySearchContainer: {
    ...holidayBorderRadius.borderRadius20,
    borderWidth: 1,
    ...paddingStyles.ph12,
    ...paddingStyles.pv6,
    ...marginStyles.mh16,
    ...marginStyles.mv0,
  },
  borderLineNew: {
    position: 'absolute',
    height: '50%',
    width: '100%',
    bottom: 0,
  },
  withMessageBackground: {
    height: '100%', 
    width: '100%' 
  },
  withoutMessageBackground: {
    height: 0, 
    width: 0 
  },
  withoutMessagesMargin: {
    ...marginStyles.mb16,
  },
  borderLine: {
    position: 'absolute',
    height: 1,
    width: '100%',
    top: '50%',
    color: holidayColors.primaryBlue,
  },
});
export default ModifySearchContainer;
