import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles } from '../../../../Styles/Spacing';

const PremiumItem = ({ item, index, dotColor, itemsLength }) => (
  <View style={styles.itemContainer}>
    <Text style={styles.description}>{item.tag}</Text>
    {index < itemsLength - 1 && (
      <View style={[styles.dot, { backgroundColor: dotColor }]} />
    )}
  </View>
);

const styles = StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  description: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
  },
  dot: {
    fontSize: 14,
    marginHorizontal: 5,
    ...marginStyles.mt4,
    height: 4,
    width: 4,
    borderRadius: 4,
  },
});

export default PremiumItem;
