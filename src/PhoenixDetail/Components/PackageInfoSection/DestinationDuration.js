import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
const DestinationDuration = ({ detailData }) => {
  const { packageDetail } = detailData || {};
  const { destinationDetail } = packageDetail || {};
  const { destinations } = destinationDetail || {};

  //The FlatList component was removed since the list was small and numColumns does not support the horizontal prop.
  // flexWrap: 'wrap' is also not supported with FlatList.
  // A simple View component was used instead to achieve the desired result.
  return (
      <View style={styles.container}>
        {destinations.map((destination, index) => (
            <React.Fragment key={index}>
              <Text style={styles.text}>
                {destination.duration}N {destination.name}{' '}
              </Text>
              {index < destinations.length - 1 && <View style={styles.separator} />}
            </React.Fragment>
        ))}
      </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...paddingStyles.pv6,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    flexWrap: 'wrap',
  },
  separator: {
    backgroundColor: holidayColors.lightGray,
    borderRadius: 100,
    width: 6,
    height: 6,
    ...marginStyles.mh8,
    // ...marginStyles.m8,
  },
  text: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 19,
  },
});

export default DestinationDuration;
