import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import errorImageIcon from '@mmt/legacy-assets/src/visa_error_image.webp';
import PropTypes from 'prop-types';
import { HolidayNavigation } from '../../../../Navigation';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import PrimaryButton from '../../../../Common/Components/Buttons/PrimaryButton';
import { holidayNavigationClearScreens } from '../../../Utils/DetailPageNavigationUtils';
import { clearOverlays } from '../../../Components/DetailOverlays/Redux/DetailOverlaysActions';
import { connect } from 'react-redux';

const FullPageError = ({ renderStickyHeader, title, subTitle, suggestion, onRefreshPressed, clearOverlays }) => {
  const buttonText = onRefreshPressed ? 'REFRESH' : 'Back to previous page';
  const handleClick = onRefreshPressed
    ? onRefreshPressed
    : () => holidayNavigationClearScreens({ clearOverlays });

  return (
    <View style={styles.container}>
      {renderStickyHeader()}
      <View style={styles.errorDetails}>
        <View style={styles.errorDetailsText}>
          <Image style={styles.errorImage1} source={errorImageIcon} />
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.subTitle}>
            {subTitle} {'\n'}
            {suggestion}
          </Text>
        </View>
        <PrimaryButton
            buttonText={buttonText}
            btnContainerStyles={styles.button}
            handleClick={handleClick}
        />
      </View>
    </View>
  );
};

FullPageError.propTypes = {
  renderStickyHeader: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  subTitle: PropTypes.string.isRequired,
  suggestion: PropTypes.string.isRequired,
  onRefreshPressed: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    backgroundColor: holidayColors.white,
    width: '100%',
  },
  errorDetails: {
    alignItems: 'center',
    paddingVertical: 15,
    flex: 1,
    width: '100%',
  },
  errorDetailsText: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  errorImage1: {
    width: 196,
    height: 196,
    marginBottom: 30,
  },
  title: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    marginBottom: 10,
  },
  subTitle: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    marginBottom: 70,
    lineHeight: 20,
    textAlign: 'center',
  },
  button: {
    paddingHorizontal: 40,
  },
});

const mapDispatchToProps = {
  clearOverlays,
};

export default connect(null, mapDispatchToProps)(FullPageError);
