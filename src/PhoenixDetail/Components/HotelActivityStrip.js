import React, { useState } from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity } from 'react-native';

const arrowDown = require('@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp');
const arrowUp = require('@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp');

export const DEFAULT_HEADER_COLOR_CODE = '#000000';
export const DEFAULT_HEADER_COLOR_BACKGROUND_CODE = '#FFD3D4';
export const DEFAULT_MESSAGE_COLOR_CODE = '#000000';
export const DEFUALT_MESSAGE_BACKGROUND_CODE = '#ffffff';

const HotelActivityStrip = ({ headingInfo = {}, sectionInfo = {}, shouldShow = false }) => {
  const [messageVisibility, setMessageVisibility] = useState(false);
  const {
    text = '',
    colorCode = DEFAULT_HEADER_COLOR_CODE,
    colorCodeBg = DEFAULT_HEADER_COLOR_BACKGROUND_CODE,
  } = headingInfo;
  const {
    messages = [],
    colorCode: colorCodeSection = DEFAULT_MESSAGE_COLOR_CODE,
    colorCodeBg: colorCodeSectionBg = DEFUALT_MESSAGE_BACKGROUND_CODE,
  } = sectionInfo;

  if (!shouldShow || !text) {
    return null;
  }

  const toggleMessageVisibility = () => {
    setMessageVisibility((prevProps) => {
      return !prevProps;
    });
  };
  return (
    <TouchableOpacity onPress={toggleMessageVisibility}>
      <View
        style={{
          backgroundColor: colorCodeSectionBg,
          borderTopLeftRadius: 6,
          borderTopRightRadius: 6,
          width: '100%',
          display: 'flex',
        }}
      >
        <View style={styles.stripContainer}>
          <View style={[{ backgroundColor: colorCodeBg }, styles.headingInfo]}>
            <Text style={[styles.headingText, { color: colorCode }]}>{text}</Text>
          </View>
          <View style={styles.imageContainer}>
            <Image source={messageVisibility ? arrowDown : arrowUp} style={styles.image} />
          </View>
        </View>
        {messageVisibility ? (
          <View style={styles.messageListContainer}>
            {messages?.map((message) => {
              return (
                <View style={styles.messageContainer}>
                  <Text style={[styles.bulletPoint, { color: colorCodeSection }]}>{'\u2B24'} </Text>
                  <Text style={[{ color: colorCodeSection }, styles.messageText]}>{message}</Text>
                </View>
              );
            })}
          </View>
        ) : null}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  stripContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  headingInfo: {
    borderRadius: 4,
    paddingVertical: 4,
    paddingHorizontal: 10,
  },
  headingText: {
    fontStyle: 'normal',
    fontWeight: '700',
    lineHeight: 13,
    fontSize: 10,
  },
  imageContainer: {},
  image: {
    width: 20,
    height: 20,
    display: 'flex',
  },
  messageListContainer: {
    display: 'flex',
    flexDirection: 'column',
    paddingHorizontal: 10,
    paddingBottom: 10,
  },
  bulletPoint: {
    fontSize: 4,
    paddingHorizontal: 5,
  },
  messageContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  messageText: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: '400',
  },
});

export default HotelActivityStrip;
