import React from 'react';
import { Animated, Easing, ScrollView, StyleSheet, Text, TouchableHighlight, View } from 'react-native';
import PropTypes from 'prop-types';
import OverlayHeader from '../../Common/Components/OverlayHeader';
import { extraInfoHeadings, OVERLAY_FULL_BOTTOM } from '../DetailConstants';
import { animateOverlay } from '../Utils/HolidayDetailUtils';
import { isEmpty } from 'lodash';
import InnerContent from '../../ZeroCancellation/CancellationDateChange/InnerContent';
import { connect } from 'react-redux';
import { PDTConstants } from '../../Review/HolidayReviewConstants';

export const ZC_TAB_INDEX = 0;
export const DATE_CHANGE_TAB_INDEX = 1;

class HolidayCancellationOverlay extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      overlayPosition: new Animated.Value(0),
      selected: 'CANCELLATION',
      showProgress: false,
      activeTab: props.activeTab,
    };
    this.cancellationResponse = null;
  }

  onHandleTabChange = (index) => {
    this.setState({
      activeTab: index,
    });
    if (index == ZC_TAB_INDEX){
      this.props.trackClickEvent(PDTConstants.CANCELLATION_POLICY,'');
    }
    else if (index == DATE_CHANGE_TAB_INDEX){
      this.props.trackClickEvent(PDTConstants.DATE_CHANGE_POLICY,'');
    }
  }

  static navigationOptions = {header: null};

  componentDidMount() {
    animateOverlay(this, OVERLAY_FULL_BOTTOM);
    if (isEmpty(this.props.details)) {
        //this.getCancellationData();
    }
  }

  startAnimate(bottom, duration, delay) {
    Animated.timing(this.state.overlayPosition, {
      toValue: bottom,
      easing: Easing.easeInOut,
      duration,
      delay,
    })
      .start();
  }

  render() {
    const response = this.props.cancellationPolicyData ? this.props.cancellationPolicyData : this.cancellationResponse;
    const { fromPresalesCompare = false } = this.props || {};
    const renderInnerContent = () => {
      return (
        <InnerContent
          cancellationPolicyData={response}
          onHandleTabChange={this.onHandleTabChange}
          activeTabIndex={this.state.activeTab}
          zcResponse={this.props.zcResponse}
          travellerCount={this.props.travellerCount}
          pageName={this.props.pageName}
        />
      );
    };

    if (fromPresalesCompare) {
      return <ScrollView>{renderInnerContent()}</ScrollView>;
    }

    return (
      <View style={styles.overlayContainer}>
        <TouchableHighlight style={styles.overlayBg}><Text>.</Text></TouchableHighlight>
        <Animated.View style={[styles.overlayContent, {bottom: this.state.overlayPosition}]}>
          <OverlayHeader
            showBackBtn={true}
            handleBack={() => this.props.togglePopup('')}
            title={extraInfoHeadings.MAIN_HEADING}
          />
          <ScrollView style={styles.cardList} ref={this.setScrollViewRef} scrollEnabled={!this.state.showProgress}>
            {renderInnerContent()}
          </ScrollView>
        </Animated.View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  overlayContainer: {
    justifyContent: 'flex-end',
    position: 'absolute',
    bottom: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 12,
    elevation: 12,
  },
  overlayBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 3,
    elevation: 3,
  },
  overlayContent: {
    backgroundColor: '#fff',
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 4,
    zIndex: 4,
    position: 'absolute',
    bottom: 0,
    marginBottom: -700,
    width: '100%',
    shadowOffset: {
      width: 1,
      height: 0,
    },
    height: '100%',
  },
  Bullet: {
    height: 5,
    width: 5,
    borderRadius: 2.5,
    backgroundColor: '#000',
    marginRight: 11,
    marginTop: 10,
  },
  generalInfoHeading: {
    fontSize: 12,
    color: '#000',
    lineHeight: 19,
    fontFamily: 'Lato-Black',
    marginBottom: 21,
    marginHorizontal: 27,
    marginTop: 15,
  },
  generalInfo: {
    fontSize: 12,
    color: '#000000',
    lineHeight: 23,
    fontFamily: 'Lato-Regular',
    flex: 1,
    marginLeft: 5,
  },
  container: {
    flexDirection: 'row',
    alignSelf: 'center',
    marginBottom: 33,
  },
  switchButtonContainer: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    minWidth: 132,
    alignItems: 'center',
    borderRadius: 34,
  },
  switchButtonText: {
    fontFamily: 'Lato-Bold',
    fontSize: 12,
    color: '#4a4a4a',
  },
  priceContainer: {
    marginHorizontal: 36,
    height: 68,
    marginBottom: 16,
    flexDirection: 'row',
    borderRadius: 4,
    shadowColor: 'rgba(0,0,0, 0.2)',
    shadowOffset: { height: 2, width: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 4,
  },
  dateContainer: {
    height: '100%',
    backgroundColor: '#f2f2f2',
    flex: 1,
    alignContent: 'center' ,
    justifyContent: 'center',
  },
  dateText: {
    alignSelf: 'center',
    fontFamily: 'Lato-Regular',
    fontSize: 14,
    textAlign: 'center',
  },
  priceBox: {
    height: '100%',
    backgroundColor: 'white',
    flex: 1,
    justifyContent: 'center',
  },
  priceTextContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  perPerson: {
    backgroundColor: 'white',
    marginLeft: 3,
    textAlign: 'center',
    justifyContent: 'flex-end',
    alignSelf: 'flex-end',
    fontFamily: 'Lato-Italic',
    fontSize: 8,
    color: '#4a4a4a',
  },
  subtitleText: {
    marginHorizontal: 28,
    textAlign: 'center',
    color: '#4a4a4a',
    fontFamily: 'Lato-Regular',
    fontSize: 10,
  },
});

HolidayCancellationOverlay.propTypes = {
  dynamicId: PropTypes.string.isRequired,
  togglePopup: PropTypes.func.isRequired,
  details: PropTypes.object,
  isPerPerson: PropTypes.bool.isRequired,
 trackClickEvent: PropTypes.func.isRequired,
};

const mapStateToProps = state => ({
  ...state.holidaysReview,
});

export default connect(mapStateToProps, null)(HolidayCancellationOverlay);
