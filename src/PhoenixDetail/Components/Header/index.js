import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import fecha from 'fecha';
import { getNewDate } from '@mmt/legacy-commons/Common/utils/DateUtils';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import PageHeader from 'mobile-holidays-react-native/src/Common/Components/PageHeader';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { capitalizeText } from '../../../../src/utils/textTransformUtil';

export const SUB_HEADER_DATE_FORMAT = 'MMM DD';
const Header = (props) => {
  const { adultCount, kidsCount, checkInDate, cityName, onBackPress, showSearch, onTextChange, searchTextPlaceholder, activeTabIndex } = props || {};
  if (!adultCount && !checkInDate || !cityName) {
    return [];
  }

  let GetSubTitleComponent = () => {
    if (checkInDate && !isNaN(adultCount) && !isNaN(kidsCount)) {
      return (
        <DetailPageSubHeaderText
          checkInDate={checkInDate}
          adultCount={adultCount}
          kidsCount={kidsCount}
        />
      );
    };
  }
  const TitleAndSubTitleView = () => {
    return (
      <View style={styles.titleStyles}>
        <Text style={styles.title}>{capitalizeText(cityName)}</Text>
        {<GetSubTitleComponent />}
      </View>
    );
  };

  return (
    <PageHeader
      showShadow
      showBackBtn
      onBackPressed={onBackPress}
      containerStyles={[styles.header]}
      showSearch={showSearch}
      onTextChange={onTextChange}
      searchTextPlaceholder={searchTextPlaceholder}
      activeTabIndex={activeTabIndex}
      leftComponent={<TitleAndSubTitleView />}
    />
  );
};

const DetailPageSubHeaderText = ({ checkInDate, adultCount, kidsCount }) => {
  try {
    if (!checkInDate) { return []; }
    const date = fecha.format(getNewDate(checkInDate), SUB_HEADER_DATE_FORMAT);
    return (
      <View style={styles.subTitleContainer}>
        {!!date && <Text style={styles.subTitle}>{date} </Text>}
        {/* Handle Adult cases*/}
        {adultCount && adultCount > 0 && (
          <>
            <View style={styles.dot} />
            <Text style={styles.subTitle}>
              {' '}
              {adultCount} {adultCount === 1 ? 'Adult' : 'Adults'}{' '}
            </Text>
          </>
        )}
        {/*Handle Child cases*/}
        {kidsCount > 0 && (
          <>
            <View style={styles.dot} />
            <Text style={styles.subTitle}>
              {' '}
              {kidsCount} {kidsCount === 1 ? 'Child' : 'Children'}
            </Text>
          </>
        )}
      </View>
    );
  } catch (e) {
    console.error(e);
    return [];
  }
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: holidayColors.white,
  },
  iconArrowLeft: {
    width: 16,
    height: 16,
  },
  subTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pt2,
  },
  subTitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  dot: {
    height: 4,
    width: 4,
    backgroundColor: holidayColors.green,
    borderRadius: 50,
  },
  title: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
  },
  titleStyles: {
    flex: 1,
  },
});

export default Header;
