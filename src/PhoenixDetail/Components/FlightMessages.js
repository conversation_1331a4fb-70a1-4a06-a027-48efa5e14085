import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { isEmpty } from 'lodash';
import HolidaysMessageStrip from '../../Common/Components/HolidaysMessageStrip';
import { ischeckinBaggageInfoAvailable } from '../../utils/HolidayUtils';
import { isDOM_ONWARDS, isDOM_RETURN, isOBT } from './FlightDetailPage/FlightListing/FlightsUtils';
import AlertIcon from '@mmt/legacy-assets/src/alert_cream.webp';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { FLIGHT_MESSAGE_TYPES } from '../../HolidayConstants';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';

const FlightMessages = ({ flightObject, fromPresales, flightDetail, openFlightPage }) => {
  const { flightMetadataDetail = {} } = flightObject || {};
  const { /*checkInBaggage = true,*/ isOvernight = false } = flightMetadataDetail || {};
  const { flightDetailType } = flightDetail || {};

  const messages = {
    [FLIGHT_MESSAGE_TYPES.UPGRADE_MESSAGE]:
      'Upgrade your flight for an improved in-flight experience',
  };

  const isDOMAndOvernight =
    (isDOM_RETURN(flightDetailType) || isDOM_RETURN(flightDetailType)) && isOvernight;

  const isFlightUpgradable = flightDetail?.flightUpgradeDetail?.flightUpgradeInfos?.some(
    (flight) =>
      (flight.flightUpgradeDetailType ===
        flightObject.flightType /* For Flights having DEPARTURE AND RETURN */ ||
        ((isOBT(flightDetailType) || isDOM_ONWARDS(flightDetailType)) &&
          flight.flightUpgradeDetailType === 'GROUP')) /* For Flights having a GROUP TYPE */ &&
      flight.upgradeAvailable,
  );

  const QuickUpgradeComponent = () => {
    const openUpgradbleFlights = () => {
      openFlightPage({ showUpgradeableFlights: true });
    };
    return (
      <TouchableOpacity onPress={openUpgradbleFlights} style={styles.upgradeFlightContainer}>
        <Text style={styles.quickUpgradeText}>Quick Upgrade</Text>
      </TouchableOpacity>
    );
  };
  return (
    <View>
      {isFlightUpgradable && !fromPresales && !isDOMAndOvernight ? (
        <HolidaysMessageStrip
          shouldShow={true}
          message={messages[FLIGHT_MESSAGE_TYPES.UPGRADE_MESSAGE]}
          messageStyle={styles.upgradeMessageStyle}
          showChild={true}
          child={QuickUpgradeComponent}
          containerStyles={styles.upgradeMessageContainer}
        />
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  upgradeFlightContainer: {
    marginLeft: 'auto',
  },
  upgradeMessageContainer: {
    alignItems: 'flex-start',
  },
  quickUpgradeText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.primaryBlue,
  },
  upgradeMessageStyle: {
    color: holidayColors.gray,
    marginRight: 10,
  },
});
export default FlightMessages;
