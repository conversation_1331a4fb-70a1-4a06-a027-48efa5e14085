import React from 'react';
import {Dimensions, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';

const EmptySlide = ({text, onPress}) => {
  return (
    <View style={styles.container}>
      <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.justifyCenter]}>
        <View style={[AtomicCss.alignCenter]}>
          <View>
            <TouchableOpacity onPress={() => onPress()}>
              <View style={styles.buttonStyle}>
                <Text style={styles.textStyle}>{text?.toUpperCase()}</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.white,
    padding: 10,
    height: Dimensions.get('window').height,
    justifyContent: 'center',
  },
  textStyle: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
  },
  buttonStyle: {
    backgroundColor: holidayColors.primaryBlue,
    borderRadius: 15,
    width: 130,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
export default EmptySlide;
