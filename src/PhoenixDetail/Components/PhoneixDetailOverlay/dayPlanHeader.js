import React from 'react';
import {FlatList, StyleSheet, Text, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors} from '../../../Styles/holidayColors';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';
import { borderRadiusValues, holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
const DayPlanHeader = (props) => {
  const {list, dayNumber, activeItemType} = props || {};

  const getDayItemText = (count, type) => {
    if (count <= 1) {
      if (type === 'CAR') {
        return 'Transfer';
      }
      return type;
    } else {
      if (type === 'ACTIVITY') {
        return count + ' Activities';
      } else {
        return type + 's';
      }
    }
  };

  const renderContent = (item, index) => {
    const {count, type} = item || {};
    const activeStyle = type === activeItemType ? styles.dayPlanItemTextActive : null;
    return (
      <View style={styles.dayPlanItem}>
        <Text style={[styles.dayPlanItemText, activeStyle]}>{getDayItemText(count, type)} </Text>
      </View>
    );
  };

  return (
    <View style={styles.dayPlanHeader}>
      <LinearGradient
        start={{ x: 1.0, y: 0.0 }}
        end={{ x: 0.0, y: 1.0 }}
        colors={[holidayColors.fadedRed, holidayColors.lightBeige]}
        style={styles.dayPlanNumber}
      >
        <Text style={styles.dayPlanNumberText}>Day {dayNumber}</Text>
      </LinearGradient>
      <Text style={styles.dayPlanIncludedText}>INCLUDED: </Text>
      <FlatList
        data={list}
        horizontal
        renderItem={({ item, index }) => renderContent(item, index)}
        keyExtractor={(item) => item._id}
        initialNumToRender={7}
        showsHorizontalScrollIndicator={false}
        ItemSeparatorComponent={() => (
          <View style={styles.dayPlamBulletContainer}>
            <View style={styles.dayPlanItemBullet} />
          </View>
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  dayPlanHeader: {
    ...AtomicCss.flexRow,
    backgroundColor: holidayColors.white,
    alignSelf: 'flex-start',
    ...paddingStyles.pr20,
    alignItems: 'center',
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
    marginHorizontal: -15,
    ...marginStyles.mv12,
  },
  dayPlanIncludes: {
    ...AtomicCss.flexRow,
    backgroundColor: holidayColors.white,
  },
  dayPlanNumber: {
    minHeight: 28,
    alignItems: 'center',
    justifyContent: 'center',
    ...paddingStyles.ph16,
    ...marginStyles.mr10,
  },
  dayPlanNumberText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  dayPlanIncludedText: {
    ...fontStyles.labelSmallBold,
    display: 'flex',
    alignItems: 'center',
    color: holidayColors.black,
    ...marginStyles.mr10,
  },
  dayPlanItemText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
  },
  dayPlanItemTextActive: {
    ...fontStyles.labelBaseBold,
  },
  dayPlanItem: {
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 28,
  },
  dayPlamBulletContainer : {
    height:'100%',
  },
  dayPlanItemBullet: {
    backgroundColor: holidayColors.gray,
    ...holidayBorderRadius.borderRadius4,
    width: 3,
    height: 3,
    ...marginStyles.mh10,
    top:'50%',
  },
});

export default DayPlanHeader;
