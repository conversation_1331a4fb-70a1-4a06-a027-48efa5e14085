import {has} from 'lodash';
import { HOLIDAYS_AIRPORT_OVERLAY_LISTING, HOLIDAYS_TRANSFER_OVERLAY_LISTING } from '../../Utils/PheonixDetailPageConstants';

export const getUpdatedBlackStripObject =
  (durationText, dateText, perPersonPrice, showUpdateButtonOnHeader, name, priceDiff, selectedItem) => {
  return {
    durationText : 'DAY ' + durationText,
    dateText,
    perPersonPrice,
    showUpdateButtonOnHeader,
    name,
    priceDiff,
    selectedItem,
  };
};

export const getSelectedItemForBlackStrip = (transfersList, selectedCabIndex) => {
  // Handle Add case.
  if (transfersList && selectedCabIndex >= 0 && transfersList[selectedCabIndex]) {
    return transfersList[selectedCabIndex];
    // Handle remove case.
  } else {
    return transfersList[0];
  }
};

export const getSelectedItemNameForBlackStrip = item => {
  if (item && has(item, 'commute.vehicleInfo.model')) {
    return item.commute.vehicleInfo.model;
  } else if (item && has(item, 'transferObj.vehicleInfo.model')) {
    return item.transferObj.vehicleInfo.model;
  } else {
    return '';
  }
};

export const getVehicleNameForBlackStrip = (item, index, action) => {
  if (action === 'select') {
    if (item && has(item, 'commute.vehicleInfo.model')) {
      return item.commute.vehicleInfo.model;
    } else if (item && has(item, 'transferObj.vehicleInfo.model')) {
      // Handle Airport transfer case.
      return item.transferObj.vehicleInfo.model;
    } else {
      return '';
    }
  } else if (action === 'remove') {
    // Handle removal case for car itinerary
    if (has(item, 'transferObj.carItinerary.vehicleInfo.model')) {
      return item.transferObj.carItinerary.vehicleInfo.model;
      // Handle removal case for transfer.
    } else if (has(item, 'transferObj.vehicleInfo.model')) {
      return item.transferObj.vehicleInfo.model;
    } else {
      return '';
    }
  }
};

export const getPageName = transfersList => {
  if (!transfersList || transfersList.length === 0) {
    return '';
  } else if (transfersList[0].commute) {
    return HOLIDAYS_TRANSFER_OVERLAY_LISTING;
  } else if (transfersList[0].transferObj) {
    return HOLIDAYS_AIRPORT_OVERLAY_LISTING;
  }
};
