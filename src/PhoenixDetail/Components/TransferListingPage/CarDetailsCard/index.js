import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {has, isEmpty} from 'lodash';
import {rupeeAmount} from '@mmt/legacy-commons/Helpers/currencyUtils';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import { Dimensions } from 'react-native';
import SelectedTag from 'mobile-holidays-react-native/src/Common/Components/Tags/SelectedTag';
import { smallHeightSeperator } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { getFacilityItem } from 'mobile-holidays-react-native/src/DetailMimaComponents/Transfer/TransferDetail/TransferFacilites';
import { paddingStyles } from '../../../../Styles/Spacing';
import { holidayColors } from '../../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { actionStyle } from '../../DayPlan/dayPlanStyles';
import { fontStyles } from '../../../../Styles/holidayFonts';
import MySafetyTag from '../../../../Common/Components/Tags/MySafetyTag';
import DetailListingSelectButton from '../../DetailListingSelectButton';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';
const CarDetailsCard = ({handleSelectCab, index, item, selectCab, handleRemoveCab, selectedItem, removeTransferRestricted}) => {
  const activeSuffix = index === selectCab ? 'Active' : '';
  const isSelected = index === selectCab;

  let defaultSelectedSellableId = '';
  if (has(selectedItem, 'data.sellableId')) {
    defaultSelectedSellableId = selectedItem.data.sellableId;
  }

  let vehicleModel = '';
  let vehicleName = '';
  let mySafe = false;
  let vehicleCategory = '';
  let onlyTransfer;
  let imageUrl = '';
  let facilities = [];
  let inclusionText = '';
  const windowWidth = Dimensions.get('window').width;

  if (!item){
    return [];
  }

  const {type, price, transferObj, commute, inclusionText : inclTxt} = item || {};
  onlyTransfer = item.onlyTransfer;
  inclusionText = inclTxt;

  if (typeof onlyTransfer === 'undefined' && transferObj){
    onlyTransfer = transferObj.onlyTransfer;
  }

  if (onlyTransfer) {
    // Return if Transfer object is empty
    if (isEmpty(transferObj)){return [];}

    const {vehicleInfo} = item || {};
    const {metaData, additionalData, imageDetail, transferSequence, sellableId} = transferObj || {};

    const {name} = metaData || {};
    const {safe, inclusionText: inclTxt} = additionalData || {};
    inclusionText = inclTxt;

    if (vehicleInfo && has(vehicleInfo, 'PRIVATE')) {
      const {PRIVATE} = vehicleInfo || {};
      const {facilities: transferFacilities, vehicleName: carName, model, imageUrl : url, vehicleName : name} = PRIVATE || {};

      facilities = transferFacilities;
      vehicleModel = model;
      imageUrl = url;
      vehicleName = name;
    }

    else if (vehicleInfo && has(vehicleInfo, 'SHARED')) {
      const {SHARED} = vehicleInfo || {};
      const {facilities: transferFacilities, vehicleName: carName, model, imageUrl : url, vehicleName : name} = SHARED || {};

      facilities = transferFacilities;
      vehicleModel = model;
      imageUrl = url;
      vehicleName = name;
    }

    mySafe = safe;
  }

  else {
    // Handle Car itinerary case
    const {safe, vehicleInfo} = commute || {};
    const { vehicleName : name, vehicleCategory : category, maxPaxCapacity, facilities : transferFacilities, model,  imageUrl : url} = vehicleInfo || {};

    mySafe = safe;
    vehicleModel = model;
    vehicleCategory = category;
    vehicleName = name;
    facilities =  transferFacilities;
    imageUrl = url;

   /* {"privateOrShared":"PRIVATE","vehicleName":"Sedan - AC","vehicleCategory":"AC","maxPaxCapacity":3}*/
  }

  const handleCabSelect = () => {
    handleSelectCab(index);
  };
  return (
    <View style={[styles.carDtlsWrapper, styles[`carDtlsWrapper${activeSuffix}`]]}>
      <View style={[AtomicCss.flexRow, AtomicCss.marginBottom20]}>
        <PlaceholderImageView url={imageUrl} style={styles.cabStyle} />
        <View>
          <View style={{ width: windowWidth - 210, flexGrow: 1, flex: 1 }}>
            {!isEmpty(vehicleModel) && (
              <Text style={styles.transferModel}>{vehicleModel} or similar</Text>
            )}
            <Text style={styles.transferDetail}>
              {type} {isEmpty(vehicleName) ? '' : vehicleName}
            </Text>
          </View>
          {mySafe && <MySafetyTag />}
        </View>
      </View>
      {!isEmpty(inclusionText) && <View style={AtomicCss.marginBottom10}>
        <Text style={styles.inclusionText}>{inclusionText}</Text>
      </View>}
      <Facilities facilities={facilities} activeSuffix={activeSuffix}/>
      {type === 'bus' &&
      <>
        <View style={[AtomicCss.flexRow, marginStyles.mb4]}>
          <Text style={styles.inclusionHeading}>Boarding point:</Text>
          <Text style={styles.inclusionItemText}> Manju ka Tila, Delhi</Text>
        </View>
        <View style={[AtomicCss.flexRow, marginStyles.mb4]}>
          <Text style={styles.inclusionHeading}>Pick Up:</Text>
          <Text style={styles.inclusionItemText}>11:00 PM, 12 Jan </Text>
        </View>
        <View style={AtomicCss.flexRow}>
          <Text style={styles.inclusionHeading}>Duration:</Text>
          <Text style={styles.inclusionItemText}> 8 Hrs</Text>
        </View>
      </>
      }
      <View style={styles.viewDtlsWrapper}>
      <View style={styles.viewDetailInnerContent}>
          {activeSuffix !== 'Active' && (
            <View>
              <Text
                style={styles.price}
              >
                {price >= 0 ? '+ ' : '- '}
                {rupeeAmount(Math.abs(price))}
              </Text>
              <Text
                style={styles.perPerson}
              >
                Price/Person
              </Text>
            </View>
          )}
          {!isSelected && (
            <DetailListingSelectButton
              onPress={handleCabSelect}
              textStyles={styles.capsuleBlueBtn}
            />
          )}
          {isSelected && (
            <View>
              <View style={AtomicCss.marginBottom10}>
                <SelectedTag />
              </View>
              {!removeTransferRestricted && (
                <TextButton
                  buttonText="Remove"
                  handleClick={() => handleRemoveCab(index)}
                  btnTextStyle={[actionStyle]}
                  btnWrapperStyle={ [AtomicCss.alignSelfFlexEnd]}
                />
              )}
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const Facilities = ({facilities, activeSuffix}) => {
  if (!facilities ||  !facilities.length > 0){
    return [];
  }

  const MAX_FACILITY_COUNT = 3;
  let FacilitiesView = [];
  facilities.every(facility => {
        if (FacilitiesView.length < MAX_FACILITY_COUNT) {
          FacilitiesView.push(getFacilityItem({facility}));
          return true;
        } else {
          return false;
        }
      }
  );
  return (
    <React.Fragment>
      <View
        style={[smallHeightSeperator, marginStyles.mv16]}
      />
      <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.flexWrap]}>
        <Text style={styles.facilitiesHeading}>Facilities:</Text>
        {FacilitiesView}
        {facilities.length > MAX_FACILITY_COUNT && (
          <Text style={styles.viewMoreFacilities}>
            + {facilities.length - MAX_FACILITY_COUNT} More
          </Text>
        )}
      </View>
      <View
        style={[smallHeightSeperator, marginStyles.mv16]}
      />
    </React.Fragment>
  );
};

const styles = StyleSheet.create({
  carDtlsWrapper: {
    backgroundColor: holidayColors.white,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pa16,
  },
  carDtlsWrapperActive: {
    borderColor: holidayColors.primaryBlue,
    backgroundColor: holidayColors.lightBlueBg,
  },
  capsuleBlueBtn: {
    alignSelf: 'flex-end',
  },
  transferModel: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    ...marginStyles.mb6,
  },
  transferDetail: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.lightGray,
  },
  inclusionHeading: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.lightGray,
  },
  inclusionItemText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
  },
  inclusionText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  cabStyle: {
    width: 120,
    height: 74,
    marginRight: 15,
  },
  viewDtlsWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  viewDetailInnerContent: {
    marginLeft: 'auto',
  },
  price: {
    color: holidayColors.black,
    ...fontStyles.headingBase,
    ...marginStyles.mb4,
    ...AtomicCss.alignRight,
  },
  perPerson: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
    ...AtomicCss.alignRight,
    ...marginStyles.mb10,
   },
  viewMoreSection: {
    alignSelf: 'flex-end',
    flexDirection: 'row',
    alignItems: 'center',
  },
  arrowStyle: {
    width: 14,
    height: 8,
    marginLeft: 10,
    top: 3,
  },
  facilitiesHeading: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
    ...marginStyles.mr10,
  },
  viewMoreFacilities: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.primaryBlue,
  },
  selectTagWrap: {
    position: 'absolute',
    top: 8,
    left: 6,
    zIndex: 1,
  },
});

export default CarDetailsCard;
