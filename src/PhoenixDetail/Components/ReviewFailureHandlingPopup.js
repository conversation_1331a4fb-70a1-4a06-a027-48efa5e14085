import React, { useMemo, useState } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View, ScrollView, Dimensions } from 'react-native';
import flightIcon from '@mmt/legacy-assets/src/flight.webp';
import hotelIcon from '@mmt/legacy-assets/src/hotels.webp';
import genericErrorIcon from '@mmt/legacy-assets/src/ic-genericerror.webp';
import CloseIcon from '@mmt/legacy-assets/src/ic_cross_gray.webp';
import { ButtonText, ErrorSubText } from '../../Common/Components/ComponentFailurePopUp/ComponentFailureConstants';
import { detailReviewFailure, PDTConstants } from '../DetailConstants';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';
import SecondaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/SecondaryButton';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import BottomSheetOverlay from '../../Common/Components/BottomSheetOverlay';
import { logPhoenixDetailPDTEvents } from '../../utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES, EVENT_NAMES} from '../../utils/HolidayPDTConstants';

const CONTACT_TRAVEL_EXPERT = 'CONTACT TRAVEL EXPERT';
const ReviewFailureHandlingPopup = (props) => {

  const [modalVisible, setModalVisible] = useState(true);
  // const snapPoints = useMemo(() => ["45%", "50%"], []);

  let message = 'Oops! Something went wrong.';
  let listItems = [];
  let actions = {};
  let buttonText = 'Explore Packages';
  let icon = genericErrorIcon;
  let componentFailed = false;
  let clickEvent = PDTConstants.GENERIC_FORWARD_FLOW_ERROR;
  const { type, componentAccessRestriction, trackLocalClickEvent , openListingPage, onReviewFailurePopupClosed ,showPopupActionButton = true ,contactExpert = false,handleContactExpertClick,reviewError} = props || {};
  let subMessage = contactExpert ?
  ErrorSubText.DEFAULT_PRESALES :
  ErrorSubText.DEFAULT;

  let showButton = false;
  if (type === detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL) {
    icon = hotelIcon;
    message = 'Oops! It seems one of the included hotel(s) are sold out';
    clickEvent = PDTConstants.HOTEL_SOLD_OUT;
    const {changeHotelRestricted} = componentAccessRestriction || {};
    if (!changeHotelRestricted) {
      if (!contactExpert) {subMessage = ErrorSubText.CHANGE_HOTEL;}
      else {subMessage = ErrorSubText.CHANGE_HOTEL_PRESALES;}
      buttonText = contactExpert ? ButtonText.CHANGE_HOTEL : 'CHANGE';
      componentFailed = true;
      showButton = true;
    }
  } else if (type === detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT) {
    icon = flightIcon;
    message = 'Oops! It seems the included flight(s) are sold out';
    clickEvent = PDTConstants.FLIGHT_SOLD_OUT;
    const {changeFlightRestricted} = componentAccessRestriction || {};
    if (!changeFlightRestricted) {
      if (!contactExpert) {subMessage = ErrorSubText.CHANGE_FLIGHT;}
      else {subMessage = ErrorSubText.CHANGE_FLIGHT_PRESALES;}
      buttonText = contactExpert ? ButtonText.CHANGE_FLIGHT : 'CHANGE';
      componentFailed = true;
      showButton = true;
    }
  }
  else if(type === detailReviewFailure.REVIEW_FAILED_TYPE_VISA) {
    icon = flightIcon;
    message = 'Visa is not available for dates';
    clickEvent = PDTConstants.VISA_SOLD_OUT;
    const {changeVisaRestricted = false} = componentAccessRestriction || {};
    if(!changeVisaRestricted) {
      if(!contactExpert) subMessage = ErrorSubText.CHANGE_VISA;
      else subMessage=ErrorSubText.CHANGE_VISA_PRESALES
      buttonText = "Remove Visa";
      componentFailed = true;
      showButton=true;
    } else {
      subMessage=ErrorSubText.DEFAULT_VISA_PRESALES
    }
  } else if (type === detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY || type === detailReviewFailure.REVIEW_FAILED_TYPE_ADDON_INCLUSION || type === detailReviewFailure.REVIEW_FAILED_TYPE_TRANSFER) {
    const dialogResponse = reviewError?.error?.errorData?.dialogResponse;
    icon = dialogResponse?.header?.icon?.iconUrl;
    message = dialogResponse?.header?.title?.text;
    subMessage = dialogResponse?.content?.message?.text;
    listItems = dialogResponse?.content?.soldOutItems;
    actions = dialogResponse?.actions;
    componentFailed = true;
    clickEvent = PDTConstants.ACTIVITY_SOLD_OUT;
  }


  const fireClickEvent = (event) => {
    trackLocalClickEvent(event, clickEvent);
  };

  const openCorrespondingListingPage = () => {
    setModalVisible(false);
    props.openCorrespondingListingPage();
  };

  const onButtonPress = () => {
    fireClickEvent(PDTConstants.CHANGE);
    if (componentFailed) {
      openCorrespondingListingPage();
    } else {
      setModalVisible(false);
      openListingPage();
    }
  };
  const handleClick = (action) => {
    if (action === 'REPLACE_ACTIVITY') {
      logPhoenixDetailPDTEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: EVENT_NAMES.REPLACE_ACTIVITY,
      });
      onButtonPress();
    } else if (action === 'CONTINUE_TO_BOOK') {
      logPhoenixDetailPDTEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: EVENT_NAMES.CONTINUE_TO_BOOK,
      });
      props?.packageReview("ERROR_POPUP_REVIEW");
    }
  };

  const onClose = () => {
    if(type === detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY || type === detailReviewFailure.REVIEW_FAILED_TYPE_ADDON_INCLUSION || type === detailReviewFailure.REVIEW_FAILED_TYPE_TRANSFER) {
      logPhoenixDetailPDTEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: EVENT_NAMES.VALIDATION_ERROR_POPUP_CLOSED,
      });
    }
    fireClickEvent(PDTConstants.CLOSE);
    setModalVisible(false);
    onReviewFailurePopupClosed();
  };

  if (!modalVisible){
    return []
  }

  return (
    <BottomSheetOverlay
      toggleModal={onClose}
      visible={modalVisible}
      containerStyles={styles.bottomcontainerStyles}
      headingContainerStyles={styles.headingContainerStyles}
      title={message}
    >
      <View>
        <View style={styles.contentSubWrapper}>
          <View style={styles.imageWrapper}>
            <Image
              source={typeof icon === 'string' ? { uri: icon } : icon}
              style={styles.lobIcon}
            />
          </View>
          <Text style={styles.subMessage}>{subMessage}</Text>
          {listItems.length > 0 && (
            <ScrollView
              contentContainerStyle={styles.listContainer}
              style={styles.scrollViewContainer}
              showsVerticalScrollIndicator={false}
            >
              {listItems.map((item, index) => (
                <View key={index} style={[styles.listItemContainer]}>
                  {item.displayText?.textBlocks?.map((textLine, lineIndex) => (
                    <Text key={lineIndex} style={styles.textLineContainer}>
                      {textLine.map((textObj, textIndex) => (
                        <Text
                          key={textIndex}
                          style={[
                            styles.listItemText,
                            textObj.bold && styles.boldText,
                            { color: item.displayText?.color || '#4A4A4A' },
                          ]}
                        >
                          {textObj.text}
                        </Text>
                      ))}
                    </Text>
                  ))}
                </View>
              ))}
            </ScrollView>
          )}
        </View>
        {actions && Object.keys(actions).length > 0 ? (
            <>
              {actions?.secondary && (
                <SecondaryButton
                  buttonText={actions?.secondary?.label}
                  handleClick={() => handleClick(actions?.secondary?.action)}
                  btnContainerStyles={styles.chnageBtn}
                />
              )}
              {actions?.primary && (
                <PrimaryButton
                  buttonText={actions?.primary?.label}
                  handleClick={() => handleClick(actions?.primary?.action)}
                  btnContainerStyles={[styles.submitBtn]}
                />
              )}
            </>
          ) : (
            <>
              {!contactExpert && showPopupActionButton && type !== detailReviewFailure.REVIEW_FAILED_TYPE_ADDON_INCLUSION && (
                <PrimaryButton
                  buttonText={buttonText}
                  handleClick={onButtonPress}
                  btnContainerStyles={styles.submitBtn}
                />
              )}
              {contactExpert && showButton && showPopupActionButton && (
                <SecondaryButton
                  buttonText={buttonText}
                  handleClick={onButtonPress}
                  btnContainerStyles={styles.chnageBtn}
                />
              )}
              {contactExpert && (
                <PrimaryButton
                  buttonText={CONTACT_TRAVEL_EXPERT}
                  handleClick={handleContactExpertClick}
                  btnContainerStyles={styles.submitBtn}
                />
              )}
            </>
          )}
      </View>
    </BottomSheetOverlay>
  );
};

const styles = StyleSheet.create({
  width:{
    width:230,
    paddingHorizontal:25,
  },
  container:{
    flex: 1,
    padding: 24,
    backgroundColor: 'rgba(74,74,74,0.65)',
    width: '100%',
    height: '100%',
    position: 'absolute',
    zIndex:5,
  },
  centeredView: {
    padding: 24,
  },
  headingContainerStyles: {
    flexDirection: 'column',
  },
  modalView: {
    backgroundColor: holidayColors.white,
    borderRadius: 4,
    padding: 25,
    paddingTop: 38,
    width: 320,
    position: 'relative',
  },
  imageWrapper: {
    backgroundColor: holidayColors.lightBlueBg,
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
    ...marginStyles.mt20,
  },
  message: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    textAlign: 'center',
  },
  subMessage: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    ...marginStyles.mt16,
    ...marginStyles.ma16,
    textAlign: 'center',
  },
  submitBtn: {
    ...marginStyles.mt20,
    alignSelf:'center',
    width:'95%',

  },
  chnageBtn: {
    width:'95%',
    ...marginStyles.mt20,
    alignSelf:'center'

  },
  lobIcon: {
    resizeMode: 'contain',
    width: 50,
    height: 50,
  },
  // closeBtn: {
  //   ...paddingStyles.pa6,
  //   backgroundColor: holidayColors.lightGray,
  //   borderRadius: 40,
  //   ...marginStyles.ml30,
  //   marginTop: -10,
  // },
  // closeIcon: {
  //   tintColor: holidayColors.white,
  //   width: 10,
  //   height: 10,
  // },
  // closeIconWrapper: {
  //   alignItems: 'flex-end',
  //   ...marginStyles.mr16
  // },
  contentWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 16
  },
  contentSubWrapper: { 
    justifyContent: 'center', 
    alignItems: 'center',
  },
  bottomcontainerStyles: {
    ...paddingStyles.ph16,
    ...paddingStyles.pv20,
  },
  scrollViewContainer: {
    maxHeight: Dimensions.get('window').height * 0.4,
    width: '100%',
    flexGrow:0,
  },
  listContainer: {
    ...marginStyles.mt16,
    ...marginStyles.ma16,
  },
  listItemContainer: {
    marginBottom: 12,
  },
  textLineContainer: {
    flexDirection: 'row',
    marginBottom: 4,
    textAlign:'center'
  },
  listItemText: {
    ...fontStyles.labelMediumRegular,
    hyphens:'auto',
  },
  boldText: {
    fontWeight: 'bold',
  },
  actionsContainer: {
    ...marginStyles.mt16,
    ...marginStyles.ma16,
    width: '100%',
  },
  actionText: {
    ...fontStyles.labelBaseRegular,
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
    marginBottom: 8,
  },
});

export default ReviewFailureHandlingPopup;
