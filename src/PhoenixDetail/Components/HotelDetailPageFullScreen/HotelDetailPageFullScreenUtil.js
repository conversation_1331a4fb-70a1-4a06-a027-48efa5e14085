import {has, isEmpty} from 'lodash';
import {findDaysBetweenDates} from '@mmt/legacy-commons/Common/utils/DateUtils';
import { getDayName } from '../../../Common/HolidaysCommonUtils';
import {formatDate} from '../../../utils/HolidayUtils';

export const getHotelName = (hotelDetailData, blackStripObject) => {
  if (blackStripObject && has(blackStripObject, 'name')) {
    return blackStripObject?.name;
  } else {
    return hotelDetailData?.hotel?.name;
  }
};

/*This function parse roomPriceMap and returns  price for per room for a roomTypeCode and ratePlanCode.
* pricingDetail node is for Package level Price
* Sample data item in  roomPriceMap "24501#990000920886:MSE:1180P:MSE:INGO": 20822,
* */
export function getPriceDiff(roomPriceMap, packagePrice, roomTypeCode, ratePlanCode, discountedFactor,finalRoomPriceDataMap ) {
  if (!roomPriceMap || !packagePrice || !roomTypeCode || !ratePlanCode || !discountedFactor) {
    return 0;
  }
  const price = finalRoomPriceDataMap ? finalRoomPriceDataMap[roomTypeCode + '#' + ratePlanCode]?.price - packagePrice
  : roomPriceMap[roomTypeCode + '#' + ratePlanCode] - packagePrice;
  return isNaN(price) ? 0 : Math.ceil(Math.ceil(price) * discountedFactor);
}

export const getUpdatedBlackStripObject = (hotelDetailData, selectedRoom, packageDetailDTO, showUpdateButtonOnHeader) => {
  const {hotel, discountedFactor, roomPriceMap ,finalRoomPriceDataMap} = hotelDetailData || {};
  const {price} = packageDetailDTO || {};
  const {ratePlan, code : roomTypeCode} =  selectedRoom || {};
  const {code : ratePlanCode} =  ratePlan || {};
  const packagePrice = Math.ceil((price * discountedFactor));
  const priceDiff = getPriceDiff( roomPriceMap, price, roomTypeCode, ratePlanCode, discountedFactor ,finalRoomPriceDataMap );
  const code = roomTypeCode + '#' + ratePlanCode;
  const finalRoomPrice = finalRoomPriceDataMap?.[code];
  const roomPrice = roomPriceMap?.[code];
  return {
      perPersonPrice : packagePrice,
      priceDiff,
      showUpdateButtonOnHeader,
      durationText : getDurationText(hotel),
      name : hotel.name,
      packageDetailDTO,
      dateText: getDateTextForBlackStrip(hotel),
      selectedHotel: {...hotel, 'roomTypes' :[selectedRoom], finalPrice : finalRoomPriceDataMap ? finalRoomPrice : roomPrice},
  };
};

export const getDateTextForBlackStrip = hotel => {
  const {checkInDate, checkOutDate} = hotel || {};
  if (!checkInDate || !checkOutDate){
    return '';
  }
  return getDayName(checkInDate, true) + ' ' + formatDate(checkInDate, 'DD MMM')
    + ' - ' + getDayName(checkOutDate, true) + ' ' + formatDate(checkOutDate, 'DD MMM');
};

export const getDurationText = hotel => {
  const numberOfNights = getNumberOfNights(hotel);
  return numberOfNights > 1 ? numberOfNights + ' Nights Stay' : numberOfNights + ' Night Stay';
};

export const getNumberOfNights = hotel => {
  const {checkInDate, checkOutDate} = hotel || {};
  return findDaysBetweenDates(checkInDate, checkOutDate);
};

export const getSelectedRoomIndex = (roomTypeList, selectedRoom) => {
  if (!roomTypeList || roomTypeList.length === 0 || !selectedRoom){
    return 0;
  }
  const index = roomTypeList.findIndex((item) => {
    const singleRoom = item[0];
    const {code} = singleRoom || {};
    const {code: selectedRoomCode} = selectedRoom || {};
    return selectedRoomCode === code;
  });
  return index > 0 ? index : 0;
};


  export const getHouseRulesIcon = (rule) => {
  if (isEmpty(rule)){
    return require('../images/other_info.webp');
  }
  switch (rule){
    case 'Safety and Hygiene':
      return require('../images/room-safety-and-hygiene.webp');
    case 'Guest Profile':
      return require('../images/guest-profile.webp');
    case 'Payment Related':
      return require('../images/payment.webp');
    case 'Food Arrangement':
      return require('../images/food-and-drinks-hygiene.webp');
    case 'Food and Drinks Hygiene':
      return require('../images/food-and-drinks-hygiene.webp');
    case 'Smoking/Alcohol consumption Rules':
      return require('../images/smoking-alcohol.webp');
    case 'Property Accessibility':
      return require('../images/property-accessibility.webp');
    case 'Room Safety and Hygiene':
      return require('../images/room-safety-and-hygiene.webp');
    case 'Pet(s) Related':
      return require('../images/pet.webp');
    case 'Physical Distancing':
      return require('../images/physical-distancing.webp');
    case 'ID Proof Related':
      return require('../images/id_icon.webp');
    case 'Other Rules':
      return require('../images/other_info.webp');
    default:
      return require('../images/other_info.webp');
  }
};
