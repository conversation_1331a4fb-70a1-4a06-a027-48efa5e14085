import React, { useCallback, useState } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import iconArrowUp from '../images/ic_upArrow.png';
import iconArrowDown from '../images/ic_downArrow.png';
import { isEmpty } from 'lodash';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { sectionHeaderSpacing } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';

/* Components */
import BottomSheetOverlay from '../../../Common/Components/BottomSheetOverlay';
import CuratedBySection from './CuratedBySection';
import HtmlHeadingV2 from '../../../Common/Components/HTML/V2';
import DayDetails from '../DayPlan/index';
import ActivityRow from '../DayPlan/activityRow';
import DayPlanHeader from '../DayPlan/dayPlanHeader';
import FlightRow from '../DayPlan/Flight/FlightRow';
import HotelRow from '../DayPlan/hotelRow';
import TransferRow from '../DayPlan/transferRow';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { logPhoenixDetailPDTEvents } from '../../../utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';
import { capitalizeText } from '../../../../src/utils/textTransformUtil';

const MAX_NUMBER_OF_LINES_IN_DESC = 3;

const getProfileImageUrl = (profileImage) => {
  if (profileImage) {
    const pImage = profileImage.find((profileImage) => profileImage.type === 'profile');
    if (pImage) {
      return pImage.url;
    }
  }
  return null;
};

export const DayplanSection = (props) => {
  if (!props.isVisible) {
    return null;
  }
  return (
    <View style={styles.dayPlanSection}>
      <DayPlanHeader {...props} />
      <FlightRow />
      <DayDetails />
      <TransferRow />
      <HotelRow />
      <ActivityRow />
    </View>
  );
};

export const ItineraryHeader = (props) => {
  return (
    <TouchableOpacity onPress={() => props.onToggle()}>
      <View style={styles.itineraryContainer} />
      <View style={styles.itenaryHdr}>
        <View>
          <Text style={styles.itineraryHeading}>Itinerary</Text>
          <View style={cStyles.paddingTop3}>
            <Text style={styles.subHeading}>Day Wise Details of your package</Text>
          </View>
          <View style={{ marginTop: 0 }} />
        </View>
        <View>
          <Image
            source={props.isVisible ? iconArrowUp : iconArrowDown}
            style={styles.iconArrowUp}
          />
        </View>
      </View>
      {!props.isVisible && <View style={styles.seperator} />}
    </TouchableOpacity>
  );
};

export const FeedbackCard = ({ packageDetail = {}, toggleReadMore }) => {
  const { additionalDetail, holidayExpert, destinationTips } = packageDetail || {};
  const { name, workDescription, profileImage, credentialUnofficial } = holidayExpert || {};
  const proImage = getProfileImageUrl(profileImage);
  const { usp } = additionalDetail || {};
  return (
    <View>
      <View style={styles.itenaryContent}>
        <View style={[cStyles.flexRow, cStyles.alignCenter, cStyles.flex1]}>
          {!isEmpty(usp) && proImage && (
            <PlaceholderImageView url={proImage} style={styles.imageSiddhi} />
          )}
          {!isEmpty(usp) && (
            <View style={[proImage ? styles.textWrapProfileImage : styles.textWrap, { flex: 1 }]}>
              <HtmlHeadingV2
                style={{ heading: { ...styles.uspHeading } }}
                htmlText={usp}
                mWebStyle={StyleSheet.flatten(styles.uspHeading)}
              />
            </View>
          )}
        </View>
        <CuratedBySection
          usp={usp}
          holidayExpert={holidayExpert}
          name={name}
          workDescription={workDescription}
          credentialUnofficial={credentialUnofficial}
        />
        {destinationTips && (
          <WhatToPack destinationTips={destinationTips} toggleReadMore={toggleReadMore} />
        )}
      </View>
    </View>
  );
};

const Tip = ({ item, index, setModalVisibility }) => {
  const [showReadMore, setReadMore] = useState(false);

  const captureClickEvents = ({ eventName = '' , suffix = ''})=> {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName + suffix,
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix
    })
  }
  const onTextLayout = useCallback((e) => {
    const temp = e.nativeEvent.lines.length >= MAX_NUMBER_OF_LINES_IN_DESC;
    setReadMore(temp);
    if (temp) {
      captureClickEvents({
        eventName: 'read_',
        suffix: `${index}_${item.title}`,
      });
    }
  }, []);

  return (
    <View style={[cStyles.flex1, cStyles.marginRight10]}>
      <View style={[cStyles.flexRow, { alignItems: 'flex-end' }]}>
        {!!item.title && <Text style={styles.itemTipTitle}>{capitalizeText(item.title)}</Text>}
      </View>
      {!!item.description && (
        <View style={[cStyles.paddingTop3]}>
          <Text
            numberOfLines={MAX_NUMBER_OF_LINES_IN_DESC}
            style={styles.itemTipDescription}
            onTextLayout={onTextLayout}
          >
            {item.description}
          </Text>
          {showReadMore && (
            <TouchableOpacity
              onPress={() => {
                setModalVisibility(true);
              }}
            >
              <View style={cStyles.paddingTop3}>
                <Text style={[styles.readMoreLink]}>Read More</Text>
              </View>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

const WhatToPack = ({ destinationTips, toggleReadMore = null }) => {
  const [modalVisibility, setModalVisibility] = useState(false);
  if (destinationTips === 'undefined' && destinationTips.size !== 2) {
    return null;
  }
  const tipsArr = destinationTips.slice(0, 2);
  const setModalVisible = (val) => {
    setModalVisibility(val);
    if (toggleReadMore) {
      // used for intervention
      toggleReadMore(val);
    }
  };
  return (
    <View style={styles.cardRowContent}>
      {tipsArr.map((item, index) => (
        <Tip item={item} index={index} setModalVisibility={setModalVisible} />
      ))}
      {modalVisibility ? (
        <BottomSheetOverlay 
        isCloseBtnVisible 
        toggleModal={()=>setModalVisibility(!modalVisibility)}
        visible={modalVisibility}
        containerStyles={styles.containerStyles}
        >
          <OverlayContent destinationTips={destinationTips} />
        </BottomSheetOverlay>
      ) : null}
    </View>
  );
};

const OverlayContent = ({ destinationTips }) => {
  if (destinationTips && destinationTips.length > 0) {
    return (
      <View>
        {destinationTips.map((item, index) => (
          <View style={styles.conversationItem}>
            <View style={styles.conversationIconWrapper}>
              {!!item.iconUrl && (
                <Image source={{ uri: item.iconUrl }} style={styles.conversationIcon} />
              )}
            </View>
            <View style={cStyles.flex1}>
              {!!item.title && <Text style={styles.conversationTitle}>{capitalizeText(item.title)}</Text>}
              {!!item.description && <Text style={styles.conversationDesc}>{item.description}</Text>}
            </View>
          </View>
        ))}
      </View>
    );
  }
  return null;
};

export const styles = StyleSheet.create({
  containerStyles: {
    ...paddingStyles.pa16,
  },
  itineraryHeading: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  subHeading: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    ...sectionHeaderSpacing,
  },
  bold: {
    fontFamily: fonts.bold,
  },
  uspHeading: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    lineHeight: 19,
  },
  itenaryCard: {
    backgroundColor: 'white',
    padding: 15,
  },
  itineraryContainer: {
    borderBottomColor: holidayColors.grayBorder,
    borderBottomWidth: 5,
    marginBottom: 6,
  },
  itenaryHdr: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: 15,
    marginTop: 8,
  },
  dayPlanSection: {
    paddingHorizontal: 15,
  },
  itenaryContent: {
    marginTop: 15,
    paddingHorizontal: 15,
  },
  imageSiddhi: {
    width: 83,
    height: 135,
    resizeMode: 'cover',
    marginRight: 30,
  },
  iconArrowUp: {
    width: 24,
    height: 24,
    resizeMode: 'cover',
    marginTop: 5,
  },

  textWrap: {
    flexWrap: 'wrap',
    width: '100%',
    flexDirection: 'row',
  },

  textWrapProfileImage: {
    flexWrap: 'wrap',
    width: '70%',
    flexDirection: 'row',
  },

  conversationItem: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 20,
  },
  conversationIconWrapper: {
    height: 24,
    width: 24,
    alignItems: 'center',
    marginRight: 12,
  },
  conversationIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  conversationTitle: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    marginBottom: 6,
  },
  conversationDesc: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    lineHeight: 19,
  },

  // DESTINATION TIPS STYLES
  itemTipTitle: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    paddingBottom: 2,
  },
  itemTipDescription: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  cardRowContent: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  readMoreLink: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
  },
  iconTips: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  seperator: {
    borderBottomColor: holidayColors.grayBorder,
    borderBottomWidth: 5,
    marginBottom: 10,
  },
});
