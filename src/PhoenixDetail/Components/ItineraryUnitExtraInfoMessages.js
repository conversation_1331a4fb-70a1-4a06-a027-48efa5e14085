import React, { useState } from 'react';
import { View, Text, FlatList, StyleSheet, TouchableOpacity } from 'react-native';
import HolidayImageHolder from '../../Common/Components/HolidayImageHolder';
import downIcon from '@mmt/legacy-assets/src/down_arrow_blue.webp';
import { holidayColors } from '../../Styles/holidayColors';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { fontStyles } from '../../Styles/holidayFonts';
import BottomSheetOverlay from '../../Common/Components/BottomSheetOverlay';
import { RESIZE_MODE_IMAGE } from '../../HolidayConstants';
import { isNullOrEmptyCollection } from '../../utils/HolidayUtils';
import { trackPhoenixDetailLocalClickEvent } from '../Utils/PhoenixDetailTracking';
import { logPhoenixDetailPDTEvents } from '../../utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';


const ItineraryUnitExtraInfoMessages = (props) => {
  const { extraInfo = [], containerStyles = {} } = props || {};
  const [openDescription, setOpenDescription] = useState(false);
  const [descriptionItem, setDescriptionItem] = useState(null);
  const captureClickEvents = ({eventName = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    })
    trackPhoenixDetailLocalClickEvent({ eventName });
  }
  const renderInformationPill = ({ item, index }) => {
    const { infoType, header, subheader, description, headerIconUrl = '' } = item || {};
    const handleClick = () => {
      if (isNullOrEmptyCollection(description)) {
        return;
      }
      captureClickEvents({
        eventName: `CLICK_${infoType}`
      })
      setOpenDescription(true);
      setDescriptionItem(item);
    };
    return (
      <TouchableOpacity
        style={styles.container}
        onPress={handleClick}
        activeOpacity={description ? 0.7 : 1}
        index={index}
      >
        <HolidayImageHolder
          imageUrl={headerIconUrl}
          style={styles.iconStyles}
          resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
        />
        <Text style={styles.header}>{header}</Text>
         {/* If description is present then only open a bottom sheet */}
        {description && (
          <HolidayImageHolder defaultImage={downIcon} style={styles.downIconStyles} />
        )}
      </TouchableOpacity>
    );
  };

  const handleCloseDescriptionModal = () => {
    setOpenDescription(false);
  };

  return (
    <View>
      <FlatList
        horizontal
        data={extraInfo}
        renderItem={renderInformationPill}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={[styles.contentContainer, containerStyles]}
      />
      {openDescription && (
        <BottomSheetOverlay
          showCross
          headingIconUrl={descriptionItem?.headerIconUrl || ''}
          title={descriptionItem?.subheader || ''}
          toggleModal={handleCloseDescriptionModal}
          containerStyles={styles.descriptionContainer}
          visible={openDescription}
        >
          <Text style={styles.descriptionText}>{descriptionItem?.description || ''}</Text>
        </BottomSheetOverlay>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    ...marginStyles.mt10,
  },
  container: {
    backgroundColor: holidayColors.fadedYellow,
    flexDirection: 'row',
    alignItems: 'center',
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pv4,
    ...paddingStyles.ph10,
    ...marginStyles.mr10,
  },
  iconStyles: {
    width: 14,
    height: 14,
  },
  downIconStyles: {
    width: 10,
    height: 5,
    tintColor: holidayColors.gray,
    ...marginStyles.mt2,
  },
  header: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    ...marginStyles.mh6,
  },
  descriptionContainer: {
    ...paddingStyles.pa16,
  },
  descriptionText: {
    ...fontStyles.labelBaseRegular,
    ...marginStyles.mv10,
    color: holidayColors.gray,
    width: '90%'
  },
});
export default ItineraryUnitExtraInfoMessages;
