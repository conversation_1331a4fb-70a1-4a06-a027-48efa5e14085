import React from 'react';
import { StyleSheet, View } from 'react-native';
import { marginStyles } from '../../../Styles/Spacing';
import { fontStyles } from '../../../Styles/holidayFonts';

import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';

/* Components */
import SelectableVisaOption from '../FDFeatureEditOverlayV2/SelectableVisaOption';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { holidayNavigationPop } from '../../Utils/DetailPageNavigationUtils';
import { Overlay } from '../DetailOverlays/OverlayConstants';

const VisaAssistance = (props) => {
  const { data, updateVisa, hideOverlays} = props;
  const captureClickEvents = ({eventName = '', suffix = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType:PDT_EVENT_TYPES.buttonClicked,
      value: eventName + suffix
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
    });
  }
  const handleUpdateVisa = (sellableId, visaIncluded) => {
    captureClickEvents({
      eventName: 'feature_select_Visa_',
      suffix: `${sellableId}`,
    });
    updateVisa(visaIncluded);
    holidayNavigationPop({
      overlayKeys:[Overlay.PACKAGE_ADD_ONS],
      hideOverlays,
    })
  };

  const { visaListingData } = data || {};
  const { availableVisas = '', discountedFactor, packagePriceMap } = visaListingData || {};
  return (
      <SelectableVisaOption
        options={availableVisas}
        discountedFactor={discountedFactor}
        packagePriceMap={packagePriceMap}
        updateVisa={handleUpdateVisa}
        containerStyles={styles.selectableOptions}
      />
  );
};

const styles = StyleSheet.create({
  selectableOptions: {
    marginTop: 16,
  },
  headerContainer: {
    flexDirection: 'row',
  },
  visa: {
    height: 20,
    width: 20,
  },
  header: {
    ...fontStyles.labelMediumBlack,
    ...marginStyles.ml8,
  },
});
export default VisaAssistance;
