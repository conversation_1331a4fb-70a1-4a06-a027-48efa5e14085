import React from 'react';
import fecha from 'fecha';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ScrollView,
  BackHandler,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import RecBlueBtn from '@mmt/legacy-commons/Common/Components/Buttons/RecBlueBtn';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  addDays,
  getFormattedDate,
  today,
  convertDateObject,
} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {
  DATE_FORMAT,
  NEW_DATE_FORMAT,
  ONLY_YEAR_DATE_FORMAT,
  CALENDAR_SELECTED_DATE_DIFF,
  HARDWARE_BACK_PRESS,
} from '../../SearchWidget/SearchWidgetConstants';
import { NO_DEPARTURE_CITY } from '../../HolidayConstants';
import { getPackageExcludedDates } from '../../utils/HolidayNetworkUtils';
import {
  createRoomDataFromRoomDetails,
  createRoomDetailsFromRoomData,
  getPaxCount,
} from '../Utils/HolidayDetailUtils';
import { PDTConstants } from '../DetailConstants';
import HolidayCalendar from '../../Calender/MmtHolidayCalender';
import DepartureCities from '../../SearchWidget/Components/getDepartureCities';
import TravellerPage from '../../Detail/TravellerPage/TravellerPage';
import withBackHandler from '../../hooks/withBackHandler';

const errorMsg = 'Starting city of this package cannot be changed.';
const nativeImg = require('@mmt/legacy-assets/src/navigateImg.webp');
const backArrowAndroid = require('@mmt/legacy-assets/src/backArrowAndroid.webp');

class EditOverlay extends React.Component {
  constructor(props) {
    super(props);
    const { departureDate, departureCity, cityId, roomDetails, checkoutDate,allowChangeHub} = this.props;
    this.state = {
      correctDate: getFormattedDate(departureDate, DATE_FORMAT, NEW_DATE_FORMAT),
      correctDay: getFormattedDate(departureDate, DATE_FORMAT, ONLY_YEAR_DATE_FORMAT),
      selectedDate: getFormattedDate(departureDate, DATE_FORMAT, DATE_FORMAT),
      departureDate: getFormattedDate(departureDate, DATE_FORMAT, DATE_FORMAT),
      correctCheckoutDate: getFormattedDate(checkoutDate, DATE_FORMAT, NEW_DATE_FORMAT),
      correctCheckoutDay: getFormattedDate(checkoutDate, DATE_FORMAT, ONLY_YEAR_DATE_FORMAT),
      selectedCheckoutDate: getFormattedDate(checkoutDate, DATE_FORMAT, DATE_FORMAT),
      checkoutDate: getFormattedDate(checkoutDate, DATE_FORMAT, DATE_FORMAT),
      departureCity,
      departureCityId: cityId,
      adult: getPaxCount(roomDetails, 'noOfAdults'),
      child: getPaxCount(roomDetails, 'noOfChildrenWB'),
      noOfRooms: roomDetails.length,
      roomData: createRoomDataFromRoomDetails(roomDetails),
      infantCount: getPaxCount(roomDetails, 'noOfInfants'),
      excludedDates: [],
      showCalendar: false,
      showDepartureCityPopup: false,
      showTravellerPopup: false,
      showLoader: false,
      dateCalendarType: null,
      showMsg:false,
      showToastMsg:!allowChangeHub,
    };
    if (!allowChangeHub) {
      setTimeout(()=>{
          this.setState({
            showToastMsg:false,
          });
      },5000);
    }
  }


  static navigationOptions = { header: null };

  onBackClick = ()=> {
    return this.handleBackPress();
  }

  componentDidMount() {
    if (this.props.packageId) {
      this.fetchPackageExcludedDates();
    }
  }

  async fetchPackageExcludedDates() {
    this.setState({
      showLoader: true,
    });
    const response = await getPackageExcludedDates(
      this.state.departureCityId,
      this.props.packageId,
      this.props.categoryId,
    );
    this.setState({
      excludedDates: response,
      showLoader: false,
    });
  }

  handleBackPress = () => {
    if (this.state.showCalendar) {
      this.onCalendarBack();
    } else if (this.state.showTravellerPopup) {
      this.setState({
        showTravellerPopup: false,
      });
    } else if (this.state.showDepartureCityPopup) {
      this.setState({
        showDepartureCityPopup: false,
      });
    } else {
      this.props.onBackPressed();
    }
    return true;
  };
  showMsg=()=>{
    this.setState({
      showMsg:true,
    });
  }
  render() {
    const { startDateTxt, endDateTxt, hideFromCity,allowChangeHub } = this.props;
    const navigateImg = nativeImg;
    const { correctCheckoutDate, dateCalendarType, minDate } = this.state;
    const totalChildCount = this.state.child + this.state.infantCount;
    const childText =
      totalChildCount > 1
        ? `, ${totalChildCount} Children `
        : totalChildCount > 0
        ? `, ${totalChildCount} Child `
        : '';
    const isWG = this.props.isWG;

    return (
      <View style={styles.overlayContainer}>
        {this.state.showLoader && (
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#f2f2f2',
            }}
          >
            <ActivityIndicator size="large" color="#008b8b" />
          </View>
        )}

        {this.state.showTravellerPopup && (
          <TravellerPage
            flightCount={this.props.flightCount}
            roomData={this.state.roomData}
            handleTravellerChange={this.handleTravellerChange}
          />
        )}

        {this.state.showDepartureCityPopup && (
          <DepartureCities
            packageId={this.props.packageId}
            handleFromChangeSelection={this.handleFromChangeSelection}
            onBack={this.handleBackPress}
          />
        )}
        {this.state.showCalendar && (
          <HolidayCalendar
            headerTitle={dateCalendarType === 'checkoutDate' ? 'Return Date' : 'Start Date'}
            selectedDate={this.state.currentPackageDate}
            onDone={this.onDateSelect}
            availableDates={this.state.excludedDates}
            onCalendarBack={this.onCalendarBack}
            minDate={minDate}
          />
        )}

        {!this.state.showCalendar &&
          !this.state.showDepartureCityPopup &&
          !this.state.showTravellerPopup &&
          !this.state.showLoader && (
            <View style={{ flex: 1 }}>
              <View style={[styles.overlayContent, { bottom: 0 }]}>
                <View style={styles.overlayHeader}>
                  <TouchableOpacity onPress={this.onBackButtonPressed} style={styles.backWrapper}>
                    <Image style={styles.overlayIconBack} source={backArrowAndroid} />
                  </TouchableOpacity>
                  <Text style={styles.overlayTitle}>Select Booking Details</Text>
                </View>

                <ScrollView style={styles.cardList}>
                  {this.state.departureCity !== NO_DEPARTURE_CITY && !hideFromCity && (
                    <View>
                    <View style={[styles.bookingDtlsWrapper, styles.bookingDtlsSection]}>
                      <View style={AtomicCss.flexRow}>
                        <Text style={[styles.bookingDtlLabel, AtomicCss.marginTop5]}> FROM</Text>
                        <Text style={styles.bookingDtlsTxt}>{this.state.departureCity}</Text>
                        <Image style={styles.navigateImg} source={navigateImg} />
                      </View>
                      {!isWG && (
                        <TouchableOpacity>
                          {allowChangeHub ?
                            <Text
                              style={[AtomicCss.font12, AtomicCss.boldFont, AtomicCss.azure]}
                              onPress={this.changeDepartureCity}
                            >
                            CHANGE
                            </Text>
                            :
                            <Text
                            style={[AtomicCss.font12, AtomicCss.boldFont, AtomicCss.azure,{opacity:0.5}]}
                            onPress={this.showMsg}
                          >
                            CHANGE
                          </Text>
                          }

                        </TouchableOpacity>
                      )}

                    </View>
                  {this.state.showMsg && <View style={[styles.msg,AtomicCss.font12]}>
                      <Text style={AtomicCss.azure}>This Package is starting from {this.state.departureCity}</Text>
                      <Text style={AtomicCss.azure}>Starting city of this package cannot be changed.</Text>
                    </View>}
                    </View>
                  )}

                  <View style={[styles.bookingDtlsWrapper]}>
                    <Text style={[styles.bookingDtlLabel, AtomicCss.marginBottom20]}>
                      {startDateTxt || 'STARTING ON'}
                    </Text>
                    <View style={styles.bookingDtlsSection}>
                      <View style={AtomicCss.flexRow}>
                        <Text style={styles.bookingDtlsTxt}>{this.state.correctDate}</Text>
                        <Text style={styles.bookingDtlsDaysTxt}>{this.state.correctDay}</Text>
                      </View>
                      <TouchableOpacity>
                        <Text
                          style={[AtomicCss.font12, AtomicCss.boldFont, AtomicCss.azure]}
                          onPress={() => this.onSpecificDateClick('departureDate')}
                        >
                          CHANGE
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  {correctCheckoutDate ? (
                    <View style={[styles.bookingDtlsWrapper]}>
                      <Text style={[styles.bookingDtlLabel, AtomicCss.marginBottom20]}>
                        {endDateTxt || 'ENDING ON'}
                      </Text>
                      <View style={styles.bookingDtlsSection}>
                        <View style={AtomicCss.flexRow}>
                          <Text style={styles.bookingDtlsTxt}>{correctCheckoutDate}</Text>
                          <Text style={styles.bookingDtlsDaysTxt}>
                            {this.state.correctCheckoutDay}
                          </Text>
                        </View>
                        <TouchableOpacity>
                          <Text
                            style={[AtomicCss.font12, AtomicCss.boldFont, AtomicCss.azure]}
                            onPress={() => this.onSpecificDateClick('checkoutDate')}
                          >
                            CHANGE
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : null}

                  <View style={[styles.bookingDtlsWrapper]}>
                    <Text style={[styles.bookingDtlLabel, AtomicCss.marginBottom20]}>
                      TRAVELLERS - ROOM WISE
                    </Text>
                    <View style={styles.bookingDtlsSection}>
                      <View style={AtomicCss.flexRow}>
                        <Text style={styles.bookingDtlsTxt}>
                          {this.state.adult > 1
                            ? `${this.state.adult} Adults `
                            : `${this.state.adult} Adult `}
                          {childText}
                          <Text
                            style={[AtomicCss.greyText, AtomicCss.font18, AtomicCss.regularFont]}
                          >
                            in
                          </Text>
                          {this.state.noOfRooms > 1
                            ? ` ${this.state.noOfRooms} Rooms`
                            : ` ${this.state.noOfRooms} Room`}
                        </Text>
                      </View>
                      <TouchableOpacity>
                        <Text
                          style={[AtomicCss.font12, AtomicCss.boldFont, AtomicCss.azure]}
                          onPress={this.openTraveller}
                        >
                          CHANGE
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </ScrollView>
                {this.state.showToastMsg &&
                  <View style={styles.errorMsg}>
                      <Text style={AtomicCss.whiteText}>{errorMsg}</Text>
                </View>}
                <View>
                  <RecBlueBtn label="DONE" onPress={() => this.reloadTravellerContent()} />
                </View>
              </View>
            </View>
          )}

      </View>
    );
  }

  reloadTravellerContent = () => {
    this.props.refreshFetchContent(
      this.state.selectedDate,
      this.state.departureCity,
      this.state.roomData,
      this.state.selectedCheckoutDate,
    );
  };

  onBackButtonPressed = () => {
    this.props.togglePopup('');
  };

  openTraveller = () => {
    this.props.handlePDT(PDTConstants.CHANGE_PAX);
    this.setState({
      showTravellerPopup: true,
    });
  };

  changeDepartureCity = () => {
    this.props.handlePDT(PDTConstants.CHANGE_FROM_CITY);
    this.setState({
      showDepartureCityPopup: true,
    });
  };

  handleTravellerChange = (roomData) => {
    const roomDetails = createRoomDetailsFromRoomData(roomData);
    const adult = getPaxCount(roomDetails, 'noOfAdults');
    const child = getPaxCount(roomDetails, 'noOfChildrenWB');
    const noOfRooms = roomDetails.length;
    const infantCount = getPaxCount(roomDetails, 'noOfInfants');
    const eventName = `changed_pax_adult_${adult}_child_${child}_infant_${infantCount}_noOfRooms_${noOfRooms}`;
    this.props.handlePDT(eventName);
    this.setState({
      adult,
      child,
      noOfRooms,
      roomData: createRoomDataFromRoomDetails(roomDetails),
      infantCount,
      showTravellerPopup: false,
    });
  };

  handleFromChangeSelection = (city, cityId) => {
    const eventName = `${PDTConstants.SELECT_FROM_CITY}_${city}`;
    this.props.handlePDT(eventName);
    this.setState({
      departureCity: city,
      departureCityId: cityId,
      showDepartureCityPopup: false,
    });
    this.setState({
      excludedDates: this.fetchPackageExcludedDates(),
    });
  };

  getDateChangeEvent = (isFPHPage, dateType) => {
    if (isFPHPage) {
      return dateType === 'checkoutDate' ? 'change_returndate' : 'change_startdate';
    }
    return PDTConstants.CHANGE_DATE;
  };

  onSpecificDateClick = async (dateType) => {
    const { correctCheckoutDate, departureDate } = this.state;

    const eventName = this.getDateChangeEvent(correctCheckoutDate, dateType);
    this.props.handlePDT(eventName);

    let minDate = null;
    if (correctCheckoutDate && dateType !== 'departureDate') {
      const nextDay = addDays(fecha.parse(departureDate, DATE_FORMAT), 1);
      minDate = fecha.format(nextDay, DATE_FORMAT);
    }

    const currentPackageDate = this.state[dateType]
        ? convertDateObject(this.state[dateType])
        : addDays(today(), CALENDAR_SELECTED_DATE_DIFF);

    this.setState({
      showCalendar: true,
      dateCalendarType: dateType,
      currentPackageDate,
      minDate,
    });
  };

  onCalendarBack = () => {
    this.setState({
      showCalendar: false,
    });
  };

  onDateSelect = (selectedDate) => {
    let formattedDate = fecha.format(selectedDate, NEW_DATE_FORMAT);
    let formattedDay = fecha.format(selectedDate, ONLY_YEAR_DATE_FORMAT);
    let newSelectedDate = fecha.format(selectedDate, DATE_FORMAT);
    const { correctCheckoutDate, dateCalendarType } = this.state;
    if (correctCheckoutDate) {
      let eventName = this.getDateChangeEvent(correctCheckoutDate, dateCalendarType);
      eventName += newSelectedDate;
      this.props.handlePDT(eventName);
    }
    if (dateCalendarType === 'checkoutDate') {
      this.setState({
        correctCheckoutDate: formattedDate,
        correctCheckoutDay: formattedDay,
        selectedCheckoutDate: newSelectedDate,
        checkoutDate: newSelectedDate,
        showCalendar: false,
        dateCalendarType: null,
      });
    } else {
      this.setState(
        {
          correctDate: formattedDate,
          correctDay: formattedDay,
          selectedDate: newSelectedDate,
          departureDate: newSelectedDate,
          showCalendar: false,
          dateCalendarType: null,
        },
        () => {
          if (this.state.correctCheckoutDate) {
            const { checkoutDate, departureDate } = this.state;
            const formattedCheckoutDate = fecha.parse(checkoutDate, DATE_FORMAT);
            const formattedDepartureDate = fecha.parse(departureDate, DATE_FORMAT);
            if (formattedCheckoutDate <= formattedDepartureDate) {
              const newDate = addDays(formattedDepartureDate, 1);
              formattedDate = fecha.format(newDate, NEW_DATE_FORMAT);
              formattedDay = fecha.format(newDate, ONLY_YEAR_DATE_FORMAT);
              newSelectedDate = fecha.format(newDate, DATE_FORMAT);
              this.setState(
                {
                  correctCheckoutDate: formattedDate,
                  correctCheckoutDay: formattedDay,
                  selectedCheckoutDate: newSelectedDate,
                  checkoutDate: newSelectedDate,
                },
                () => {
                  this.onSpecificDateClick('checkoutDate');
                },
              );
            } else {
              this.onSpecificDateClick('checkoutDate');
            }
          }
        },
      );
    }
  };
}

const styles = StyleSheet.create({
  overlayContainer: {
    justifyContent: 'flex-end',
    position: 'absolute',
    bottom: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 15,
    elevation: 15,
  },
  errorMsg:{
    paddingHorizontal:5,
    backgroundColor:'#4A4A4A',
    height:50,
    justifyContent:'center',
    marginHorizontal:10,
    borderRadius:5,
    alignItems: 'center',
  },
  msg:{
    paddingVertical:10,
    paddingLeft:20,
    backgroundColor:'#EAF5FF'},
  backWrapper: {
    paddingRight: 10,
    paddingLeft: 10,
    paddingVertical: 15,
  },
  overlayIconBack: {
    height: 24,
    width: 24,
  },
  overlayTitle: {
    flex: 5,
    color: '#000000',
    fontFamily: 'Lato-Bold',
    letterSpacing: 0.3,
    fontSize: 18,
  },
  overlayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingVertical: 20,
    height: 60,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    elevation: 4,
    zIndex: 4,
  },
  overlayBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 3,
    elevation: 3,
  },
  overlayContent: {
    backgroundColor: '#f2f2f2',
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 4,
    zIndex: 4,
    position: 'absolute',
    bottom: 0,
    width: '100%',
    shadowOffset: {
      width: 1,
      height: 0,
    },
    height: '100%',
    flex: 1,
  },
  bookingDtlsWrapper: {
    borderColor: '#d8d8d8',
    borderWidth: 1,
    backgroundColor: '#fff',
    paddingVertical: 20,
    paddingHorizontal: 16,
    marginTop: 20,
  },
  bookingDtlsSection: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },
  bookingDtlLabel: {
    fontFamily: fonts.regular,
    color: '#4a4a4a',
    fontSize: 12,
    marginRight: 10,
  },
  bookingDtlsTxt: {
    fontFamily: fonts.bold,
    color: '#000',
    fontSize: 18,
  },
  navigateImg: {
    width: 15,
    height: 16,
    marginTop: 4,
    marginLeft: 10,
  },
  bookingDtlsDaysTxt: {
    fontFamily: fonts.regular,
    color: '#000',
    fontSize: 18,
    marginLeft: 10,
  },
});

EditOverlay.defaultProps = {
  checkoutDate: '',
  startDateTxt: '',
  endDateTxt: '',
};

EditOverlay.propTypes = {
  departureCity: PropTypes.string.isRequired,
  departureDate: PropTypes.string.isRequired,
  checkoutDate: PropTypes.string,
  startDateTxt: PropTypes.string,
  endDateTxt: PropTypes.string,
  roomDetails: PropTypes.array.isRequired,
  packageId: PropTypes.number.isRequired,
  flightCount: PropTypes.number.isRequired,
  categoryId: PropTypes.number.isRequired,
  cityId: PropTypes.number.isRequired,
  handlePDT: PropTypes.func.isRequired,
  onBackPressed: PropTypes.func.isRequired,
  isWG: PropTypes.bool.isRequired,
};
export default withBackHandler(EditOverlay);
