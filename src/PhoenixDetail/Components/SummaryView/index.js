import React from 'react';
import { FlatList, Image, StyleSheet, Text, View, Platform } from 'react-native';
import { holidayColors } from '../../../Styles/holidayColors';
import { borderRadiusValues, holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { fontStyles } from '../../../Styles/holidayFonts';
import HtmlHeadingV2 from 'mobile-holidays-react-native/src/Common/Components/HTML/V2';
export const SUMMARY_TYPE = {
    INLINE: 'INLINE',
    CITY: 'CITY',
};
const SummaryView = ({ summaryData = [] }) => {
    const renderInlineSummaryItem = ({ description, lobType, iconUrl }, index) => (
        <View key={index} style={styles.viewLine}>
            <View style={styles.itineraryList}>
                <View style={{ flexDirection: 'row', alignItems: 'center'}}>
                    <View style={styles.bulletDots} />
                    <View>
                        <Image source={{ uri: iconUrl }} style={styles.iconImage} />
                    </View>
                </View>
                <View style={[styles.rowContent, styles.mr30, { flex: 1 } ]}>
                    <HtmlHeadingV2
                        style={{ heading: { ...styles.itineraryListDesc }, bold: styles.itineraryListDescBold }}
                        htmlText={description}
                        mWebStyle={StyleSheet.flatten(styles.itineraryListDesc)}
                    />
                </View>
            </View>
        </View>
    );

    const renderCitySummaryItem = ({ cityName, stay, dayDetailList = [] }, index) => (
        <View key={`_key${cityName?.toString()}_${index}`} style={styles.cityItemSummaryContainer}>
            <View style={styles.tripTableHdr}>
                <Text style={styles.tripTableHdrTxt}>{cityName}</Text>
                <Text style={styles.noOfNights}> {`(${stay} ${stay > 1 ? 'Nights' : 'Night'} Stay)`}</Text>
            </View>
            <FlatList
                data={dayDetailList}
                listKey={(item, i) => `_key${i.toString()}_${item?.day?.toString()}`}
                keyExtractor={(item, i) => `_key${i.toString()}_${item?.day?.toString()}`}
                ItemSeparatorComponent={
                    () => <View style={styles.seperator} />
                }
                renderItem={({ item: { day, date, inlineDetails }, index: itemIndex }) => {
                    const lastIndex = itemIndex === dayDetailList.length - 1;
                    return (
                    <View style={styles.rowContent}>
                        <View style={[styles.tableLeftContent, lastIndex && styles.borderBottomLeft16 ]}>
                            <Text style={styles.dayTxt}>{day}</Text>
                            <Text style={styles.dateTxt}>{date}</Text>
                        </View>
                        <View style={styles.tableRightContent}>{getTable(inlineDetails)}</View>
                    </View>
                )}}
            />
        </View>
    );

    const renderSummaryItem = (summaryItem, index) => {
        switch (summaryItem?.type) {
            case SUMMARY_TYPE.INLINE:
                return renderInlineSummaryItem(summaryItem, index);
            case SUMMARY_TYPE.CITY:
                return renderCitySummaryItem(summaryItem, index);
            default:
                return null;
        }
    };

    return <>{summaryData?.map(renderSummaryItem)}</>;
};


const getDescription = ({item, index}) => {
    const { description, lobType, iconUrl } = item || {};
    return (
        <View style={styles.itineraryRow}>
            {lobType !== 'LEISURE' && (
                <View>
                    <Image source={{ uri: iconUrl }} style={styles.iconImage} />
                </View>
            )}
            <View style={{ marginRight: 20, flex:1 }}>
                <HtmlHeadingV2
                    style={{ heading: { ...styles.itineraryListDesc }, bold: styles.itineraryListDescBold }}
                    htmlText={description}
                    mWebStyle={StyleSheet.flatten(styles.itineraryListDesc)}
                />
            </View>
        </View>
    );
};

const getTable = (inlineDetails = []) => {
    return (
        <FlatList
          data={inlineDetails}
          renderItem={getDescription}
          ItemSeparatorComponent={
            () => <View style={styles.seperator} />
          }
        />
      );
};

const styles = StyleSheet.create({
    cityItemSummaryContainer: {
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
        marginVertical: 12,
        ...holidayBorderRadius.borderRadius16,
        overflow:'hidden',
    },
    tripTableHdr: {
        backgroundColor: holidayColors.fadedYellow,
        paddingVertical: 12,
        paddingHorizontal: 16,
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: holidayColors.grayBorder,
        overflow:'hidden',
    },
    tripTableHdrTxt: {
        ...fontStyles.headingBase,
        color: holidayColors.black,
    },

    rowContent: {
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    tableLeftContent: {
        width: '25%',
        backgroundColor: holidayColors.lightGray2,
        padding: 10,
    },
    tableRightContent: {
        width:'75%',
        borderBottomRightRadius: borderRadiusValues.br16,
    },
    itineraryRow: {
        paddingHorizontal: 10,
        paddingVertical: 12,
        flexDirection: 'row',
        alignItems: 'flex-start',
    },
    iconImage: {
        width: 18,
        height: 18,
        resizeMode: 'contain',
        marginRight: 10,
        marginTop: 3,
    },
    iconspoon: {
        width: 6,
        height: 14,
        resizeMode: 'cover',
    },
    iconfork: {
        width: 6,
        marginHorizontal:1,
        height: 14,
        resizeMode: 'cover',
    },
    itineraryDesc: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.gray,
        lineHeight:17,
    },
    itineraryDescBold: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.gray,
        lineHeight:17,
    },
    txtBold: {
        fontWeight: 'bold',
    },
    dayTxt: {
        ...fontStyles.labelSmallBlack,
        color: holidayColors.gray,
    },
    dateTxt: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.gray,
        paddingTop: 3,
    },
    noOfNights: {
        ...fontStyles.labelMediumRegular,
        color: holidayColors.gray,
    },
    itineraryList: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        paddingVertical: 4,
        marginLeft: -4,
        marginRight: 10,
    },
    itineraryListDesc: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.gray,
    },
    itineraryListDescBold: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.gray,
    },
    bulletDots: {
        width: 7,
        height: 7,
        borderRadius: 8,
        backgroundColor: holidayColors.lightGray,
        marginRight: 10,
    },
    viewLine: {
        borderLeftWidth: 1,
        borderColor: holidayColors.grayBorder,
        marginBottom: 1,
    },
    borderBottomLeft16: {
        borderBottomLeftRadius: borderRadiusValues.br16,
    },
    borderBottomRight16: {
        borderBottomRightRadius: borderRadiusValues.br16,
    },
    mr30: {
        marginRight: 30,
    },
    seperator: {
        borderBottomWidth: 1,
        borderBottomColor: holidayColors.grayBorder,
    },

});

export default SummaryView;
