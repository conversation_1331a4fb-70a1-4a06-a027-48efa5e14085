import React from 'react';
import { Modal, View } from 'react-native';
import { connect } from 'react-redux';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { Overlay } from './OverlayConstants';
import TransferDetailPage from '../TransferDetailPage';
import TransferListingPage from '../TransferListingPage';
import ActivityListingPage from '../../Containers/PhoenixActivityOverlayContainer';
import ActivityDetail from '../ActivityOverlay/ActivityDetail';
import SightSeeingDetail from '../SightSeenDetail/SightSeeingDetail';
import FullPageGallery from '../Gallery/FullPageGallery';
import PhoenixHotelsListing from '../PhoenixHotelsListing/PhoenixHotelsListing';
import PhoenixDetailHotelFullPageContainer from '../../Containers/PhoenixDetailHotelFullPageContainer';
import HolidaysHotelReviewsContainer from '../../../PhoenixDetail/Components/ReviewRating/index';
import PropertyType from '../../../FilterHotels/FilterHotelList/PropertyType/PropertyTypeList';
import FilterList from '../../../FilterHotels/FilterHotelList/FilterList';
import PopularSection from '../../../FilterHotels/FilterHotelList/PopularSection';
import PriceRangeList from '../../../FilterHotels/FilterHotelList/PriceRange/PriceRangeList';
import PhoenixDetailOverlayContainer from '../../Containers/PhoenixDetailOverlayContainer';
import HolidaysFlightOverlayContainer from '../../Containers/HolidaysFlightOverlayContainer';
import FlightDetail from '../FlightDetailPage/FlightDetail';
import GridGallery from '../Gallery/GridGallery';
import GridGalleryV2 from '../Gallery/GridGalleryV2';
import HouseRulesExpand from '../HotelDetailPage/HouseRules/HouseRulesExpand';
import SearchAutoComplete from '../PhoenixHotelsListing/HotelListing/SearchAutoComplete';
import CommuteComboPage from '../combo';
import CommuteDetailsPage from '../combo/CommuteDetailsPage';
import TravelTidbitsOverlayContainer from '../../Containers/TravelTidbitsOverlayContainer';
import ActivityDetailV2 from '../../Components/ItineraryV2/Activity/ActivityDetailPage';
import ActivityOptionRatePlan from '../../Components/ItineraryV2/Activity/ActivityDetailPage/ActivityOptionRatePlan';
import PackageAddOnsOverlay from '../../../PhoenixDetail/Containers/PackageAddOnsOverlayContainer'
import VPPDetailPage from 'mobile-holidays-react-native/src/Common/Components/VisaProtectionPlan/VPPDetailPage';
import { REVIEW_OVERLAYS } from 'mobile-holidays-react-native/src/PhoenixReview/Components/ReviewOverlays';
import { COMMON_OVERLAYS } from 'mobile-holidays-react-native/src/Common/Components/CommonOverlay';
import TravelInsuranceDetails from 'mobile-holidays-react-native/src/Travelnsurance/details/TravelInsuranceDetails';

const ComponentMap = {
  [Overlay.VIEW_DETAILS]: PhoenixDetailOverlayContainer,
  [Overlay.TRANSFER_LISTING_PAGE]: TransferListingPage,
  [Overlay.TRANSFER_DETAIL_PAGE]: TransferDetailPage,
  [Overlay.ACTIVITY_LISTING_PAGE]: ActivityListingPage,
  [Overlay.ACTIVITY_DETAIL_PAGE]: ActivityDetail,
  [Overlay.SIGHTSEEING_DETAIL_PAGE]: SightSeeingDetail,
  [Overlay.GRID_GALLERY]: GridGallery,
  [Overlay.GRID_GALLERY_V2] :GridGalleryV2,
  [Overlay.FULL_PAGE_GALLERY]:FullPageGallery,
  [Overlay.PHOENIX_HOTELS_LISTING]:PhoenixHotelsListing,
  [Overlay.HOTEL_DETAIL_PAGE_FULL_SCREEN]:PhoenixDetailHotelFullPageContainer,
  [Overlay.SHOW_HOLIDAY_HOTEL_REVIEWS]:HolidaysHotelReviewsContainer,
  [Overlay.PROPERTY_TYPE]:PropertyType,
  [Overlay.FILTER_LIST]:FilterList,
  [Overlay.POPULAR_SECTION]:PopularSection,
  [Overlay.PRICE_RANGE]:PriceRangeList,
  [Overlay.FLIGHT_OVERLAY]:HolidaysFlightOverlayContainer,
  [Overlay.FLIGHT_DETAIL]:FlightDetail,
  [Overlay.HOUSE_RULES]:HouseRulesExpand,
  [Overlay.SEARCH_AUTO_COMPLETE]:SearchAutoComplete,
  [Overlay.COMMUTE_VIEW_OPTIONS]: CommuteComboPage,
  [Overlay.COMMUTE_DETAIL_PAGE]: CommuteDetailsPage,
  [Overlay.TRAVEL_TIBITS]: TravelTidbitsOverlayContainer,
  [Overlay.ACTIVITY_DETAIL_V2]: ActivityDetailV2,
  [Overlay.ACTIVITY_DETAIL_RATE_PLAN_V2]: ActivityOptionRatePlan,
  [Overlay.PACKAGE_ADD_ONS]: PackageAddOnsOverlay,
  [Overlay.VPP_OVERLAY]: VPPDetailPage,
  [Overlay.INSURANCE_OVERLAY]: TravelInsuranceDetails,
};

class DetailOverlays extends BasePage {
  constructor(props) {
    super(props);
  }

  renderComponent = (key, data) => {
    const Component = ComponentMap[key];
    return (
      <Modal visible={this.props.overlayMap[key]}>
        <Component {...data} />
      </Modal>
    );
  }

  renderModal = (key, show) => {
    if (show) {
      const data = this.props.overlayDataMap[key];
      return this.renderComponent(key, data);
    }
    return [];
  }

  render() {
    const {overlayMap = {}} = this.props;
    const overlaysArray = overlayMap ? Object.entries(overlayMap) : [];
    return (
      <View>
        {overlaysArray.map(([key, value]) => this.renderModal(key, value))}
      </View>
    );
  }
}

const mapStateToProps = state => ({
  ...state.holidaysDetailOverlays,
});


export default connect(mapStateToProps, null)(DetailOverlays);
