import React, { useMemo } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import iconSelected from '@mmt/legacy-assets/src/tick_white.webp';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { getPriceText } from '../../FlightDetailPage/FlightListing/FlightsUtils';
import { rupeeFormatterUtils } from '../../../../utils/HolidayUtils';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import SelectedTag from '../../../../Common/Components/Tags/SelectedTag';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';
import PrimaryButton from '../../../../Common/Components/Buttons/PrimaryButton';

const BlackPriceStrip = ({day, numberOfActivities, activityPrice, packagePrice, showUpdate, onUpdatePress }) => {

  const getNoOfActivityText = useMemo(() => {
    if (numberOfActivities === 0) {
      return 'No Activity Selected';
    } else if (numberOfActivities === 1) {
      return '1 Activity Selected';
    } else {
      return `${numberOfActivities} Activities Selected`;
    }
  }, [numberOfActivities]);

  const finalPackagePrice = activityPrice + packagePrice;

  if (numberOfActivities === 0 && !showUpdate) {
    return [];
  }

  return (
    <View style={styles.activityUpdateCard}>
      <View style={styles.leftContent}>
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
          <Text style={styles.headingText}>Day {day}</Text>
        </View>
        <View style={styles.subHeadingContainer}>
          <Text style={styles.subHeadingText}>{getNoOfActivityText}</Text>
        </View>
        <Text style={styles.activityPriceText}>{getPriceText(activityPrice)}</Text>
      </View>
      <View style={styles.rightContent}>
        <View style={[AtomicCss.marginRight10, AtomicCss.marginBottom5]}>
          <View style={[AtomicCss.marginBottom5]}>
            <Text style={styles.priceText}>{rupeeFormatterUtils(finalPackagePrice)}</Text>
          </View>
          <View>
            <Text style={styles.perPersonText}>Per Person</Text>
          </View>
        </View>
        {showUpdate ? (
          <PrimaryButton
            buttonText={'Update'}
            handleClick={onUpdatePress}
            btnContainerStyles={styles.updateBtn}
          />
        ) : (
          <SelectedTag />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  activityUpdateCard: {
    backgroundColor: holidayColors.fadedYellow,
    alignItems: 'flex-start',
    flexDirection: 'row',
    ...paddingStyles.pa16,
  },
  headingText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  subHeadingContainer: {
    ...marginStyles.mv6,
  },
  subHeadingText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  activityPriceText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  updateCardContWrap: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...paddingStyles.pb10,
  },
  leftContent: {
    flexWrap: 'wrap',
  },
  rightContent: {
    marginLeft: 'auto',
    justifyContent: 'flex-end',
    flexDirection: 'column',
    alignItems: 'center',
  },
  priceText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  perPersonText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  updateBtn: {
    ...paddingStyles.ph14,
  },
  selectedText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
  },
  iconContainer: {
    backgroundColor: holidayColors.midLightBlue,
    ...paddingStyles.pa4,
    borderRadius: 100,
    alignItems: 'center',
    ...marginStyles.mh4,
  },
  selectedIcon: {
    width: 8,
    height: 8,
    resizeMode: 'cover',
    tintColor: holidayColors.white,
  },
  icondownArrow: {
    width: 18,
    height: 18,
    resizeMode: 'cover',
  },
  updateText: {
    ...fontStyles.labelSmallBold,
    color:holidayColors.white,
  },

});

export default BlackPriceStrip;
