import React, {useState} from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { paddingStyles, marginStyles } from '../../../../../Styles/Spacing/index';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const ToggleActivityCards = (props) => {

  const getStyle = (mL) => {
    return {
      backgroundColor: holidayColors.white,
      marginLeft: mL,
      marginBottom: 10,
      marginTop: 15,
    };
  };

  const captureClickEvents = (eventName = '') => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
    });
    trackClick(eventName);
  };


  const {title, children, marginLeft = 16, trackClick = () => {}} = props;
  const [showCard, setShowCard] = useState(true);
  const toggleCard = () => {
    const temp = showCard;
    setShowCard(!showCard);
    if (!temp) {
      captureClickEvents(`read_${title}`);

    }
  };

  return (
    <View style={getStyle(marginLeft)}>
      <TouchableOpacity onPress={() => toggleCard()}>
        <Text style={styles.title}>{title}</Text>
      </TouchableOpacity>
      {showCard && <View>{children}</View>}
    </View>
  );
};

const styles = StyleSheet.create({
  toggleCards: {
    backgroundColor: holidayColors.white,
    ...paddingStyles.pa0,
    ...marginStyles.mb10,
    ...marginStyles.mt20,
  },
  toggleArrow: {
    width: 20,
    height: 20,
    resizeMode: 'cover',
  },
  title: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
});

export default ToggleActivityCards;
