import {View, StyleSheet, ScrollView} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import ActivityDetailsCommonCard from './ActivityDetailsCommonCard';
import RatePlan from './RatePlan';
import React from 'react';
import { componentImageTypes, packageActions } from '../../../DetailConstants';
import { getPackagePrice, onRemoveActivityPress } from '../../../Utils/ActivityOverlayUtils';
import { getActivitySafetyDetails } from '../../../Utils/ActivityUtils';
import { trackPhoenixDetailLocalClickEvent} from '../../../Utils/PhoenixDetailTracking';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import { HOLIDAYS_ACTIVITY_OVERLAY } from '../../../Utils/PheonixDetailPageConstants';
import PostSalesSharedModuleHolder from '@mmt/post-sales-shared/src';
import PrimaryButton from '../../../../Common/Components/Buttons/PrimaryButton';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles } from '../../../../Styles/Spacing/index';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const ActivityOverlayCard = (props) => {
  const {
    item,
    openActivityListingPage,
    onComponentChange,
    activityReqParams,
    dynamicId,
    pricingDetail,
    subtitleData,
    branch,
    packageDetailDTO,
    roomDetails,
    lastPageName,
  } = props;
  const {data = {}, day} = item;
  const activityCode = data.metaData ? data.metaData.code : null;

  const onUpdatePress = (newRecheckKey) => {
    const activitiesRequest = activityReqParams[day]?.activityList?.map(({metaData, recheckKey, staySequence = 1}) => ({
      activityCode: metaData.code,
      optionRecheckKey: (metaData.code === activityCode && newRecheckKey) ? newRecheckKey : recheckKey,
      staySequence,
      name: metaData.name,
    })) ?? [];

    const actionData = {
      action: packageActions.MODIFY,
      dynamicPackageId: dynamicId,
      selectedActivities: activitiesRequest,
      day: day,
    };

    onComponentChange(actionData, componentImageTypes.ACTIVITY);
    HolidayNavigation.navigate(lastPageName);
  };

  const onRemovePress = () => {
    onRemoveActivityPress(activityReqParams[day], activityCode, dynamicId, day, onComponentChange);
  };

  const onChangePress = () => {
    HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.ACTIVITY_LISTING, {
      activityReqParams: activityReqParams[day],
      day: day,
      dynamicId: dynamicId,
      pricingDetail: pricingDetail,
      onComponentChange: onComponentChange,
      subtitleData: subtitleData,
      lastPage: 'PhoneixDetailOverlay',
      branch: branch,
      packageDetailDTO,
      roomDetails,
    });
  };

  const openActivityDetailPage = (scrollToRatePlan) => {
    const packagePrice = getPackagePrice(pricingDetail);
    const {activityList = []} = activityReqParams[day] || {};
    const activityObj = activityList.find(x => {
      const {metaData} = x;
      const {code} = metaData;
      return code === activityCode;
    });
    const {recheckKey, staySequence = 1} = activityObj || {};

    const blackStripData = {
      day: day,
      numberOfActivities: activityList.length,
      activityPrice: 0,
      packagePrice: packagePrice,
      showUpdate: false,
    };
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL, {
      blackStripData: blackStripData,
      packagePrice: packagePrice,
      dynamicPackageId: dynamicId,
      staySequence: staySequence,
      day: day,
      activityCode: activityCode,
      selectedRecheckKey: recheckKey,
      modifyActivityDetail: () => {},
      onUpdatePress: onUpdatePress,
      selected: true,
      onRemovePress: onRemovePress,
      onChangePress: onChangePress,
      isActivityDetailFirstPage: true,
      scrollToRatePlan: !!scrollToRatePlan,
      subtitleData: subtitleData,
      branch: branch,
      packageDetailDTO,
      roomDetails,
    });
  };

  const safetyDetails = getActivitySafetyDetails(data, branch);

  const captureClickEvents = (eventName = '') => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
      subPageName: HOLIDAYS_ACTIVITY_OVERLAY,
    })
    trackClickEvent(eventName);
  }

  const trackClickEvent = event => {
    if (event){
      trackPhoenixDetailLocalClickEvent({ eventName: event, prop1: HOLIDAYS_ACTIVITY_OVERLAY });
    }
  };

  return (
    <View>
      <ScrollView>
        <View>
          <ActivityDetailsCommonCard
            selected={true}
            activity={data}
            onChangePress={() => openActivityListingPage(item.day)}
            onRemovePress={() =>
              onRemoveActivityPress(
                activityReqParams[day],
                activityCode,
                dynamicId,
                day,
                onComponentChange,
              )
            }
            safetyDetails={safetyDetails}
            trackClick={captureClickEvents}
          />
        </View>
        <RatePlan
          editable={false}
          activity={data}
          openDetailPage={openActivityDetailPage}
          trackClick={trackClickEvent}
        />
        <View style={styles.seeMore}>
        <PrimaryButton 
        btnContainerStyles={styles.searchCta}
        buttonText={'SEE MORE'}
        handleClick={() => {
          openActivityDetailPage();
          trackClickEvent('see_activity');
        }}
        />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  tickmark: {
    color: holidayColors.green,
  },
  seeMore: {
    ...marginStyles.ma16,
  },
  separator: {
    ...marginStyles.mh5,
    backgroundColor: holidayColors.grayBorder,
    width: 1,
    height: 10,
  },
});

export default ActivityOverlayCard;
