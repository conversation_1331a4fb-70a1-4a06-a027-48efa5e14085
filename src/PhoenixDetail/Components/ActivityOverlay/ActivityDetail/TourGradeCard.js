import React, { useMemo, useState } from 'react';
import { View, StyleSheet, Text, TouchableOpacity, ScrollView } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { getPriceText } from '../../FlightDetailPage/FlightListing/FlightsUtils';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';
import SecondaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/SecondaryButton';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { capitalizeText } from '../../../../../src/utils/textTransformUtil';

const TourGradeCard = (props) => {
  // text truncate
  const {
    activityName,
    description,
    activityTourGrade,
    editable = true,
    selectedReCheckKey,
    updateRecheckKey,
    recheckKey,
    getPrice,
    openDetailPage,
    trackClick,
    showOnlyDefault,
  } = props;
  const {slots = []} = activityTourGrade || {};
  const [trunketString, settrunketString] = useState(true);

  const showSelected = useMemo(() => {
    if (selectedReCheckKey === recheckKey) {
      return true;
    }
    if (slots?.some(slot => slot?.slotRecheckKey === selectedReCheckKey)) {
      return true;
    }
    return false;
  }, [selectedReCheckKey, recheckKey, slots]);

  const defaultSelectedSlot = useMemo(() => {
    const foundSlot = slots && slots.find(slot => slot.slotRecheckKey === selectedReCheckKey);
    return foundSlot ? selectedReCheckKey : recheckKey;
  }, [selectedReCheckKey, recheckKey, slots]);

  const [selectedSlotKey, setSlotKey] = useState(defaultSelectedSlot);

  const handleMoreTxt = () => {
    settrunketString(!trunketString);
  };

  const captureClickEvents = ({ eventName = '', value = '' }) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value || eventName.replace(/_/g, '|'),
    });
    trackClick(eventName);
  };

  const updateSlot = (key) => {
    captureClickEvents({eventName : 'change_timeslot' });
    setSlotKey(key);
    if (showSelected) {
      updateRecheckKey(key);
    }
  };

  const price = getPrice(selectedSlotKey);

  const handleChange = () => {
      openDetailPage(true);
      if (trackClick) {
        captureClickEvents({eventName : 'see_activity_rate_plan', value: 'see|activity|rate_plan'});
      }
  };
  const showDefault = (showSelected && showOnlyDefault) || (!showOnlyDefault);
  const showPrice = showSelected && showOnlyDefault
  return showDefault ? (
    <View
      style={[styles.gradeCard, showSelected ? styles.selectTourGradeCard : styles.tourGradeCard]}
    >
      <View>
      <View style={styles.headerSection}>
        {/* TODO - This section is not being shown anywhere, openDetailPage is also not present. Do we need this? */}
        {!editable && (
              <TouchableOpacity onPress={handleChange}>
                <View style={[{ alignItems: 'flex-end' }, marginStyles.mb4]}>
                  <Text style={styles.changeText}>Change</Text>
                </View>
              </TouchableOpacity>
            )}
          <Text style={styles.activityName}>{capitalizeText(activityName)}</Text>
      </View>
          {!!description && (
            <View >
              <Text style={styles.cardDesc}>
                {trunketString ? description.slice(0, 150) : description}
              </Text>
            </View>
          )}
          {!!description && description.length > 150 && (
            <TextButton
              buttonText={trunketString ? 'Read More' : 'Read Less'}
              handleClick={() => handleMoreTxt()}
              btnTextStyle={styles.readMore}
            />
          )}
      </View>
        {editable && !showPrice && (
          <View >
            <View>
              <Text style={[styles.priceText, marginStyles.mt8]}>{getPriceText(price)} </Text>
            </View>
            <View>
              <Text style={styles.perPersonText}>Price/Person</Text>
            </View>
            {!showSelected && (
              <SecondaryButton
                buttonText={'SELECT'}
                handleClick={() => updateRecheckKey(selectedSlotKey)}
                btnContainerStyles={styles.selectBtn}
              />
          )}
          </View>
        )}
      {slots && slots.length > 0 &&
        <View style={[marginStyles.mt16]}>
          <View style={[ marginStyles.mb10]}>
            <Text style={[styles.addmissionText]}>Admission Time</Text>
          </View>
          <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
            <ScrollView
              horizontal
              style={{width: '100%'}}
            >
              {slots.map((item, i) =>
                <Slot
                  key={i}
                  item={item}
                  editable={editable}
                  selectedSlotKey={selectedSlotKey}
                  updateSlot={updateSlot}
                />
              )}
            </ScrollView>
          </View>
        </View>}
    </View>
  ) : null;
};

const Slot = ({item, editable, selectedSlotKey, updateSlot}) => {
  const {slotRecheckKey, startTime, endTime} = item;
  const active = selectedSlotKey === slotRecheckKey;
  return (
    <TouchableOpacity disabled={!editable} onPress={() => updateSlot(slotRecheckKey)}>
      <View style={[styles.timeSlotTabs, active && styles.timeSlotTabsActive]}>
        <Text style={[styles.timeSlotText, active && styles.timeSlotActive]}>
          {startTime} - {endTime}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  gradeCard: {
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pa16,
    borderWidth: 1,
  },
  tourGradeCard: {
    borderColor: holidayColors.grayBorder,
  },
  selectTourGradeCard: {
    backgroundColor: holidayColors.lightBlueBg,
    borderColor: holidayColors.primaryBlue,
  },
  selectBtn: {
    ...marginStyles.mt8,
    ...paddingStyles.ph20,
  },
  selectBtnText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.primaryBlue,
  },
  activityName: {
   ...fontStyles.labelMediumBlack,
   color: holidayColors.black,
  },
  timeSlotTabs: {
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.mr10,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderColor: holidayColors.grayBorder,
    ...paddingStyles.pv5,
    ...paddingStyles.ph10,
    backgroundColor: holidayColors.lightGray2,
  },
  timeSlotTabsActive: {
    borderStyle: 'solid',
    borderColor: holidayColors.primaryBlue,
    backgroundColor: holidayColors.lightBlueBg,
  },
  headerSection: {
  },

  leftContent: {
    width: '68%',
    flexWrap: 'wrap',
  },
  rightContent: {
    width: '32%',
    alignItems: 'flex-end',
  },
  cardDesc: {
    lineHeight: 19,
    ...fontStyles.labelBaseRegular,
    ...marginStyles.mt10,
    color: holidayColors.gray,
  },
  changeText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelBaseBold,
    ...AtomicCss.textUpper,
  },
  priceText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  perPersonText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  timeSlotText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.lightGray,
  },
  timeSlotActive: {
    color: holidayColors.primaryBlue,
  },
  readMore: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
    ...paddingStyles.pt6,
  },
  addmissionText: {
    ...fontStyles.labelSmallBold,
    color:holidayColors.gray,
  },
});

export default TourGradeCard;