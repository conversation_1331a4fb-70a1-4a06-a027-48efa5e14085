import React, { useEffect, useMemo, useState } from 'react';
import { FlatList, Modal, StyleSheet, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import FilterTag from './FilterTag';
import { cloneDeep } from 'lodash';
import { applyAllFilters, createFilter<PERSON>son, getPackagePrice } from '../../../Utils/ActivityOverlayUtils';
import ActivityFilters from './ActivityFilters';
import { getPriceDiff } from '../../../../utils/HolidayUtils';
import { getActivitySafetyDetails } from '../../../Utils/ActivityUtils';
import HolidayDataHolder from '../../../../utils/HolidayDataHolder';
import { SUB_PAGE_NAMES } from '../../../../HolidayConstants';
import { HOLIDAYS_ACTIVITY_OVERLAY_LISTING } from '../../../Utils/PheonixDetailPageConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { checkAndChangePriceToZero } from '../../../Utils/PhoenixDetailUtils';
import { holidayColors } from '../../../../Styles/holidayColors';
import { paddingStyles } from '../../../../Styles/Spacing/index';
import { getSubPageNameListing, initializeMap, logSortByAndFilters } from '../ActivityUtils';
import { createStaySeqData, getFilterTagBottomMargin } from '../../ItineraryV2/TravelTidbits/TidBitsUtils';
import ActivityListCardV2 from '../../ItineraryV2/Activity/ActivityListingPage/ActivityListCardV2';
import isEmpty from 'lodash/isEmpty';
import { connect } from 'react-redux';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

export const HOLIDAYS_ACTIVITY_INITIAL_LIST = -1;
export const DEFAULT_SELECTED_STAY_SEQUENCE = 1;
const MINIMUM_ACTIVITY_COUNT_TO_SHOW_FILTER = 3;

const ActivityListingPage = (props) => {
  if (!props.pageData){
    return []
  }
  const {
    activityReqParams,
    pricingDetail,
    pageData,
    activityType,
    removeActivity,
    selectActivity,
    searchedText,
    selectedActivities,
    openActivityDetailPage,
    selectedActivitiesStripData,
    selectedActivitiesInfoStripState,
    activityProductType
  } = props || {};
  const filterTagBottomMargin = getFilterTagBottomMargin(selectedActivitiesInfoStripState, selectedActivitiesStripData);
  const staySequences = createStaySeqData(activityReqParams);
  const [selectedStaySequence, setSelectedStaySequence] = useState(staySequences ? staySequences[0].staySequence : DEFAULT_SELECTED_STAY_SEQUENCE);
  const [baseActivities, setBaseActivities] = useState(new Map());
  const [filteredActivities, setFilteredActivities] = useState(new Map());
  const [packagePriceMap, setPackagePriceMap] = useState(new Map());
  const [discountedFactor, setDiscountedFactor] = useState(new Map());
  const [showSortByAndFilters, setShowSortByAndFilters] = useState(false);
  const [filterData, setFilterData] = useState(new Map());
  const [initialActivityList, setInitialActivityList] = useState(HOLIDAYS_ACTIVITY_INITIAL_LIST);

  const packagePrice = useMemo(() => getPackagePrice(pricingDetail), [pricingDetail]);

  // const SHOW_FILTER =  pageData?.listingActivities.length >= MINIMUM_ACTIVITY_COUNT_TO_SHOW_FILTER;
  HolidayDataHolder.getInstance().setCurrentPage('phoenixActivityListing');
  const { day } = props || {};

  useEffect(() => {
    const { listingActivities, packagePriceMap, discountedFactor } = pageData || {};
    const filterData = createFilterJson(listingActivities, null, packagePrice, packagePriceMap, discountedFactor);
    const staySeq = staySequences[0].staySequence;

    setBaseActivities(initializeMap(staySeq, listingActivities || []));
    setPackagePriceMap(initializeMap(staySeq, packagePriceMap || {}));
    setFilterData(initializeMap(staySeq, filterData));
    setDiscountedFactor(initializeMap(staySeq, discountedFactor || {}));
    setFilteredActivities(initializeMap(staySeq, listingActivities || []));

  }, [pageData]);

  useEffect(() => {
    findActivityByName(searchedText);
  }, [searchedText]);

  const captureClickEvents = ({ eventName = '', suffix = ''}) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
      subPageName: getSubPageNameListing(activityType),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
    })
  }
  const updateActivities = async (add, activityCode, name, recheckKey, acmeType, acmeSubType) => {
    setInitialActivityList(HOLIDAYS_ACTIVITY_INITIAL_LIST);
    const selectedActivity = selectedActivities.find((activity) => activity.code === activityCode);
    if (add) {
      selectActivity(activityCode, recheckKey, name, acmeType, acmeSubType);
      if (!selectedActivity) {
        captureClickEvents({
          eventName: 'add_',
          suffix: activityCode,
        });
      }
    } else {
      removeActivity(activityCode);
      captureClickEvents({
        eventName: 'remove_',
        suffix: activityCode,
      });
    }
  };

  const applyFilters = (allFilters, staySeq, data) => {
    const filterData = allFilters.get(staySeq);
    if (!data) {
      data = cloneDeep(baseActivities.get(staySeq));
    }
    const priceMap = packagePriceMap.get(staySeq);
    const discFactor = discountedFactor.get(staySeq);
    data = applyAllFilters(filterData, data, packagePrice, priceMap, discFactor);

    if (data.length > 0) {
      logSortByAndFilters(filterData, staySeq ,activityType);
      const newFilteredActivities = cloneDeep(filteredActivities);
      newFilteredActivities.set(staySeq, data);
      setSelectedStaySequence(staySeq);
      setFilteredActivities(newFilteredActivities);
      setFilterData(allFilters);
      return true;
    }
    return false;
  };

  const getActivityPriceDiff = (code, recheckKey) => {

    const PriceMap = packagePriceMap.get(selectedStaySequence);
    const discFactor = discountedFactor.get(selectedStaySequence);
    const activityPrice = PriceMap[recheckKey] || PriceMap[code];
    return checkAndChangePriceToZero(getPriceDiff(packagePrice, activityPrice, discFactor));
  };

  const renderActivityCard = ({ item, index }) => {
    const { metaData, recheckKey } = item;
    const { code  } = metaData || {};
    const price = getActivityPriceDiff(code, recheckKey);
    const safetyDetails = getActivitySafetyDetails(item, props.branch);
    return (
      <View style={[AtomicCss.marginBottom4, AtomicCss.marginTop10]}>
        <ActivityListCardV2
          key={index}
          item={item}
          index={index}
          price={price}
          openActivityDetailPage={openActivityDetailPage}
          updateActivities={updateActivities}
          safetyDetails={safetyDetails}
          activityType={activityType}
        />
      </View>
    );
  };

  const openSortByAndFilter = (prefix) => {
    captureClickEvents({ eventName: prefix });
    setShowSortByAndFilters(true);
  };

  const findActivityByName = (value) => {
    if (isEmpty(value)) {
      setInitialActivityList(HOLIDAYS_ACTIVITY_INITIAL_LIST);
    } else {
      const newValue = filteredActivities.get(selectedStaySequence).filter((item) => item?.metaData?.name?.toLowerCase().includes(value?.toLowerCase()));
      setInitialActivityList(newValue);
    }
  };
const handleSort = () => {  openSortByAndFilter('sorter')};
const handleFilter = () => {  openSortByAndFilter('filter')};

  const renderContent = () => {
    const activityList = initialActivityList === HOLIDAYS_ACTIVITY_INITIAL_LIST ? filteredActivities.get(selectedStaySequence) : initialActivityList;
    return (
      <View style={{ flex: 1, backgroundColor: holidayColors.lightGray2 }}>
        <FlatList
          data={activityList}
          renderItem={renderActivityCard}
          style={styles.activityPageInnerWrap}
          contentContainerStyle={{
            paddingBottom: filterTagBottomMargin + 60,
            horizontalPadding: 7,
          }}
          keyExtractor={(item, index) => `${item.recheckKey} ${index}`}
        />
        <Modal
          onRequestClose={() => setShowSortByAndFilters(false)}
          animationType="slide"
          transparent={true}
          visible={showSortByAndFilters}
          propagateSwipe={true}
        >
          {showSortByAndFilters &&
            <ActivityFilters
              staySequences={staySequences}
              selectedStaySequence={selectedStaySequence}
              filterData={cloneDeep(filterData)}
              onClose={() => setShowSortByAndFilters(false)}
              applyFilters={applyFilters}
              cityFilterEnabled={false}
            />}
        </Modal>
        <View style={{bottom: filterTagBottomMargin}}>
        <FilterTag
          // show={SHOW_FILTER}
          handleSort={handleSort}
          handleFilter={handleFilter}
        />
        </View>
      </View>
    );
  };


  return (
    <View style={styles.activityPageWrap}>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  activityPageWrap: {
    flex: 1,
    backgroundColor: holidayColors.white,
  },
  activityPageInnerWrap: {
    ...paddingStyles.ph8,
  },
});

const  mapStateToProps = state => {
  const { selectedActivitiesStripData,  selectedActivitiesInfoStripState} = state.travelTidbitsReducer;
  return { selectedActivitiesStripData, selectedActivitiesInfoStripState };
}

export default connect(mapStateToProps, null)(ActivityListingPage);
