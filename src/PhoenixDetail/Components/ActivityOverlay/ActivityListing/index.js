import React from 'react';
import { View, StyleSheet, Platform, StatusBar, FlatList, Modal, BackHandler } from 'react-native';
import ActivityListCard from './ActivityListCard';
import BlackPriceStrip from '../BlackPriceStrip';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import FilterTag from './FilterTag';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { cloneDeep } from 'lodash';
import { fetchActivityListingResponse } from '../../../../utils/HolidayNetworkUtils';
import {
  applyAllFilters,
  createFilterJson, FiltersType, getPackagePrice,
} from '../../../Utils/ActivityOverlayUtils';
import ActivityFilters from './ActivityFilters';
import { getDiscountedPrice, getPriceDiff, isMobileClient } from '../../../../utils/HolidayUtils';
import { componentImageTypes, itineraryUnitTypes, packageActions } from '../../../DetailConstants';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import PhoenixHeader from '../../PhoenixHeader';
import { getActivitySafetyDetails } from '../../../Utils/ActivityUtils';
import { createTravellerObjForLoader } from '../../../Utils/HolidayDetailUtils';
import HolidayDetailLoader from '../../HolidayDetailLoader';
import ConfirmationPopup from '../../ConfirmationPopup/ConfirmationPopup';
import FullPageError from '../../ReviewRating/components/FullPageError';
import HolidayDataHolder from '../../../../utils/HolidayDataHolder';
import {
  HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
} from '../../../Utils/PheonixDetailPageConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { checkAndChangePriceToZero } from '../../../Utils/PhoenixDetailUtils';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import ChangeActivity from './ChangeActivity';
import { holidayColors } from '../../../../Styles/holidayColors';
import { paddingStyles } from '../../../../Styles/Spacing/index';
import {Overlay} from "../../DetailOverlays/OverlayConstants";
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import withBackHandler from '../../../../hooks/withBackHandler';

/**
 * props;
 * activityReqParams: activityReqData,
 dynamicId: dynamicId,
 day: day,
 pricingDetail: pricingDetail,
 onComponentChange: onComponentChange,
 subtitleData: subtitleData,
 lastPage: "PhoneixDetailOverlay"
 * */

export const HOLIDAYS_ACTIVITY_INITIAL_LIST = -1;
class  ActivityListingPage extends BasePage {
  constructor(props) {
    super(props);
    this.staySequences = this.createStaySeqData();
    this.preSelectedActivities = this.createPreSelectedActivitiesMap();
    this.packagePrice = getPackagePrice(props.pricingDetail);
    this.selectedActivityTotalPrice = 0;
    this.addedActivityPriceMap = {};
    this.header = this.props?.packageDetailDTO?.name || 'Add Activity';
    this.state = {
      selectedStaySequence: this.staySequences ? this.staySequences[0].staySequence : 1,
      listingLoading: true,
      listingSuccess: false,
      listingError: false,
      baseActivities: new Map(),
      filteredActivities: new Map(),
      packagePriceMap: new Map(),
      discountedFactor: new Map(),
      selectedActivities: this.preSelectedActivities,
      showSortByAndFilters: false,
      selectedFilters: new Map(),
      filterData: new Map(),
      showConfirmationDialog: false,
      initialActivityList: HOLIDAYS_ACTIVITY_INITIAL_LIST, // to render original activity list
    };
    HolidayDataHolder.getInstance().setCurrentPage('phoenixActivityListing');
  }

  /**
   * Creates a stay sequence array of an object of type-
   * {
   *   name: Cochin,
   *   staySequence: 1
   * }
   * */
  createStaySeqData = () => {
    const {activityReqParams} = this.props;
    const {citySeq = []} = activityReqParams;
    const newCitySeq = [];
    if (citySeq) {
      citySeq.forEach((cs, i) => {
        newCitySeq.push({
          name: cs.name,
          staySequence: cs.staySequence,
          data: cs,
        });
      });
    }
    return newCitySeq;
  }

  /**
   * Returns a map of preselected activity stay sequence wise-
   * {
   *   staySeq1: {
   *     code,
   *     recheckKey
   *   }
   * }
   * */
  createPreSelectedActivitiesMap = () => {
    const {activityReqParams} = this.props;
    const {activityList = []} = activityReqParams;
    const newActivitySeqMap = new Map();
    this.staySequences.forEach((ss) => {
      newActivitySeqMap.set(ss.staySequence, new Map());
    });
    if (activityList) {
      activityList.forEach((activity) => {
        const {metaData, recheckKey, staySequence} = activity;
        const {code, name, availableOptionsCount, hasSlot} = metaData;
        const actObj = {
          activityCode: code,
          recheckKey: availableOptionsCount > 1 || hasSlot ? recheckKey : null,
          name: name,
        };
        if (newActivitySeqMap.has(staySequence)) {
          newActivitySeqMap.set(staySequence, newActivitySeqMap.get(staySequence).set(code, actObj));
        } else {
          const activityMap = new Map();
          activityMap.set(code, actObj);
          newActivitySeqMap.set(staySequence, activityMap);
        }
      });
    }
    return newActivitySeqMap;
  }

  async componentDidMount() {
    super.componentDidMount();
    const {dynamicId, day} = this.props;
    const responseArray = await Promise.all(
      this.staySequences.map(async x => {
        return await new Promise((resolve, reject) => {
          const responseBody = this.hitActivityListingApi({
            dynamicPackageId: dynamicId,
            staySequence: x.staySequence,
            day: day,
            activityCodes: this.getSelectedActivityCodes(this.state.selectedActivities, x.staySequence),
            v2: true,
          });
          resolve(responseBody);
        });
      }
    ));

    let error = false;
    let baseAct = new Map();
    let pPriceMap = new Map();
    let fData = new Map();
    let dFactor = new Map();

    responseArray.forEach((responseBody, index) => {
      const {activityListingData} = responseBody || {};
      const {listingActivities, packagePriceMap, discountedFactor} = activityListingData || {};
      const filterData = createFilterJson(listingActivities, null, this.packagePrice, packagePriceMap, discountedFactor);
      const staySeq = this.staySequences[index].staySequence;
      if (listingActivities && packagePriceMap) {
        baseAct.set(staySeq, listingActivities);
        pPriceMap.set(staySeq, packagePriceMap);
        fData.set(staySeq, filterData);
        dFactor.set(staySeq, discountedFactor);
      } else {
        baseAct.set(staySeq, []);
        pPriceMap.set(staySeq, {});
        fData.set(staySeq, filterData);
        dFactor.set(staySeq, discountedFactor);
        error = true;
      }
    });
    if (!error) {
      this.setState({
        listingLoading: false,
        listingSuccess: true,
        listingError: false,
        baseActivities: baseAct,
        filteredActivities: baseAct,
        packagePriceMap: pPriceMap,
        filterData: fData,
        discountedFactor: dFactor,
      });
    } else {
      this.setState({
        listingLoading: false,
        listingSuccess: false,
        listingError: true,
      });
    }
  }
  onBackClick = ()=> {
    return this.onBackPress();
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  captureClickEvents = ({ eventName = '', suffix = ''}) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
      subPageName: HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
    })
  }

  onBackPress = () => {
    if (this.state.showConfirmationDialog === true) {
      return true;
    }
    const blackStripData = this.createBlackStripData();
    const {showUpdate} = blackStripData;
    if (this.state.showConfirmationDialog) {
      this.captureClickEvents({ eventName: 'close' });
      this.handleNavigationToListingPage();
    } else if (showUpdate) {
      this.setState({
        showConfirmationDialog: true,
      });
    } else {
      this.captureClickEvents({ eventName: 'close' });
      this.handleNavigationToListingPage();
    }
    return true;
  }

  onNotNowClicked = () => {
    this.captureClickEvents({ eventName: 'close' });
    this.handleNavigationToListingPage();
  }

  handleNavigationToListingPage() {
    if (isMobileClient()) {
      HolidayNavigation.navigate(this.props.lastPage);
    } else {
      this.props.hideOverlays([Overlay.ACTIVITY_LISTING_PAGE, Overlay.ACTIVITY_DETAIL_PAGE]);
    }
  }

  onShowMeClicked = () => {
    this.setState({
      showConfirmationDialog: false,
    });
  }

  getSelectedActivityCodes = (selectedActivities, staySeq) => {
    const selectedActivitiesMap = selectedActivities.get(staySeq);
    const arr = Array.from(selectedActivitiesMap.keys());
    return arr;
  }

  /**
   * This function clones a map and add value to it's corresponding selected stay sequence
   * */
  addValueToMap = (mapObj, value) => {
    const newMapObj = cloneDeep(mapObj);
    newMapObj.set(this.state.selectedStaySequence, value);
    return newMapObj;
  }

  getValueFromMap = (mapObj) => {
    const newMapObj = mapObj.get(this.state.selectedStaySequence);
    return cloneDeep(newMapObj);
  }

  hitActivityListingApi = async (requestBody) => {
    const responseBody = await fetchActivityListingResponse(requestBody);
    if (responseBody && responseBody.success) {
      return responseBody;
    }
    return null;
  }

  modifyActivityDetail = async (add, activityCode, recheckKey, name, price) => {
    const {selectedActivities, selectedStaySequence} = this.state;

    const newSelectedActivities = cloneDeep(selectedActivities);
    let selectedActivitiesMap = newSelectedActivities.get(selectedStaySequence);

    if (!selectedActivitiesMap) {
      selectedActivitiesMap = new Map();
    }
    let flag = -1;
    if (add) {
      if (selectedActivitiesMap.has(activityCode)) {
        flag = 1;
      } else {
        flag = 2;
        this.captureClickEvents({ eventName: 'add_', suffix: activityCode });
      }
      selectedActivitiesMap.set(activityCode, {activityCode: activityCode, recheckKey: recheckKey, name: name});
    } else {
      selectedActivitiesMap.delete(activityCode);
      flag = 3;
      this.captureClickEvents({ eventName: 'add_', suffix: activityCode });
    }

    newSelectedActivities.set(selectedStaySequence, selectedActivitiesMap);
    if (flag === 2 || flag === 3) {
      this.setState({
        listingLoading: true,
        listingSuccess: false,
        listingError: false,
        initialActivityList: HOLIDAYS_ACTIVITY_INITIAL_LIST,
      });

      const responseBody = await this.hitActivityListingApi({
        dynamicPackageId: this.props.dynamicId,
        staySequence: this.state.selectedStaySequence,
        day: this.props.day,
        activityCodes: this.getSelectedActivityCodes(newSelectedActivities, this.state.selectedStaySequence),
        v2: true,
      });

      const {activityListingData} = responseBody || {};
      const {listingActivities, packagePriceMap, discountedFactor, removedPriceMap} = activityListingData || {};
      const filterData = createFilterJson(listingActivities, this.getValueFromMap(this.state.filterData), this.packagePrice, packagePriceMap, discountedFactor);
      const allFilters = this.addValueToMap(this.state.filterData, filterData);
      const newFilteredActivities = applyAllFilters(filterData, cloneDeep(listingActivities), this.packagePrice, packagePriceMap, discountedFactor);

      if (listingActivities && packagePriceMap) {
        if (flag === 2) {
          this.selectedActivityTotalPrice += price;
          this.addedActivityPriceMap[activityCode] = price;
        } else {
          if (removedPriceMap && removedPriceMap[activityCode]) {
            const temp = removedPriceMap[activityCode];
            this.selectedActivityTotalPrice += checkAndChangePriceToZero(getPriceDiff(this.packagePrice, temp, discountedFactor));
          } else {
            this.selectedActivityTotalPrice -= price;
          }
          delete this.addedActivityPriceMap[activityCode];
        }
        this.setState({
          listingLoading: false,
          listingSuccess: true,
          listingError: false,
          selectedActivities: newSelectedActivities,
          baseActivities: this.addValueToMap(this.state.baseActivities, listingActivities),
          filteredActivities: this.addValueToMap(this.state.filteredActivities, newFilteredActivities),
          packagePriceMap: this.addValueToMap(this.state.packagePriceMap, packagePriceMap),
          discountedFactor: this.addValueToMap(this.state.discountedFactor, discountedFactor),
          filterData: allFilters,
        });
      } else {
        showShortToast('Oops, something went wrong');
        return false;
      }
    }
    else {
      this.selectedActivityTotalPrice += price;
      this.addedActivityPriceMap[activityCode] = price;
      this.setState({
        selectedActivities: newSelectedActivities,
      });
    }
    return true;
  }

  /**
   * This function updates the state of selected activity based on the api response.
   *
   * @param add Boolean variable, true: add activity to the list, false: remove activity from the list.
   * @param activityCode Unique id of an activity.
   * @param recheckKey Unique id of a tourGrade.
   * @return true/false denoting whether the operation was successful or not
   **/
  updateActivities = async (add, activityCode, name) => {
    // Loading State
    this.setState({
      listingLoading: true,
      listingSuccess: false,
      listingError: false,
      initialActivityList: HOLIDAYS_ACTIVITY_INITIAL_LIST,
    });
    const newSelectedActivities = cloneDeep(this.state.selectedActivities);
    let selectedActivitiesMap = newSelectedActivities.get(this.state.selectedStaySequence);
    if (!selectedActivitiesMap) {
      selectedActivitiesMap = new Map();
    }
    if (add) {
      if (!selectedActivitiesMap.has(activityCode)) {
        this.captureClickEvents({ eventName: 'add_', suffix: activityCode });
      }
      selectedActivitiesMap.set(activityCode, {activityCode: activityCode, recheckKey: null, name: name});
    } else {
      selectedActivitiesMap.delete(activityCode);
      this.captureClickEvents({ eventName: 'remove_', suffix: activityCode });

    }
    newSelectedActivities.set(this.state.selectedStaySequence, selectedActivitiesMap);

    const responseBody = await this.hitActivityListingApi({
      dynamicPackageId: this.props.dynamicId,
      staySequence: this.state.selectedStaySequence,
      day: this.props.day,
      activityCodes: this.getSelectedActivityCodes(newSelectedActivities, this.state.selectedStaySequence),
      v2: true,
    });

    const {activityListingData, success} = responseBody || {};
    const {listingActivities, packagePriceMap, discountedFactor, removedPriceMap} = activityListingData || {};
    // Handle Error case.
    if (!success){
      this.setState({
        listingLoading: false,
        listingSuccess: true,
        listingError: false,
        initialActivityList: HOLIDAYS_ACTIVITY_INITIAL_LIST,
      });
      showShortToast('Oops, something went wrong while updating package.');
      return false;
    }

    const filterData = createFilterJson(listingActivities, this.getValueFromMap(this.state.filterData), this.packagePrice, packagePriceMap, discountedFactor);
    const allFilters = this.addValueToMap(this.state.filterData, filterData);
    const newFilteredActivities = applyAllFilters(filterData, cloneDeep(listingActivities), this.packagePrice, packagePriceMap, discountedFactor);

    if (listingActivities && packagePriceMap) {
      if (add) {
        const tempPrice = packagePriceMap[activityCode];
        const price = checkAndChangePriceToZero(getPriceDiff(this.packagePrice, tempPrice, discountedFactor));
        this.selectedActivityTotalPrice += price;
        this.addedActivityPriceMap[activityCode] = price;
      } else {
        if (removedPriceMap && removedPriceMap[activityCode]) {
          const tempPrice = removedPriceMap[activityCode];
          this.selectedActivityTotalPrice += checkAndChangePriceToZero(getPriceDiff(this.packagePrice, tempPrice, discountedFactor));
        } else if (this.addedActivityPriceMap[activityCode]) {
          this.selectedActivityTotalPrice -= this.addedActivityPriceMap[activityCode];
        } else {
          const tempPrice = packagePriceMap[activityCode];
          this.selectedActivityTotalPrice -= checkAndChangePriceToZero(getPriceDiff(this.packagePrice, tempPrice, discountedFactor));
        }
        delete this.addedActivityPriceMap[activityCode];
      }
      this.setState({
        listingLoading: false,
        listingSuccess: true,
        listingError: false,
        selectedActivities: newSelectedActivities,
        baseActivities: this.addValueToMap(this.state.baseActivities, listingActivities),
        filteredActivities: this.addValueToMap(this.state.filteredActivities, newFilteredActivities),
        packagePriceMap: this.addValueToMap(this.state.packagePriceMap, packagePriceMap),
        discountedFactor: this.addValueToMap(this.state.discountedFactor, discountedFactor),
        filterData: allFilters,
      });
      return true;
    } else {
      showShortToast('Oops, something went wrong');
      this.setState({
        listingLoading: false,
        listingSuccess: true,
        listingError: false,
      });
      return false;
    }
  }

  getActivityObject = (activityCode, recheckKey) => {
    return {
      activityCode: activityCode,
      optionRecheckKey: recheckKey,
    };
  }

  applyFilters = (allFilters, staySeq, data) => {
    const filterData = allFilters.get(staySeq);
    const {baseActivities, packagePriceMap, discountedFactor} = this.state;
    if (!data) {
      data = cloneDeep(baseActivities.get(staySeq));
    }
    const priceMap = packagePriceMap.get(staySeq);
    const discFactor = discountedFactor.get(staySeq);
    data = applyAllFilters(filterData, data, this.packagePrice, priceMap, discFactor);

    if (data.length > 0) {
      this.logSortByAndFilters(filterData, staySeq);
      const newFilteredActivities = cloneDeep(this.state.filteredActivities);
      newFilteredActivities.set(staySeq, data);
      this.setState({
        selectedStaySequence: staySeq,
        filteredActivities: newFilteredActivities,
        filterData: allFilters,
      });
      return true;
    }
    return false;
  }

  logSortBy = (data, staySeq) => {
    data.forEach((item, i) => {
      if (item.selected) {
        this.captureClickEvents({
          eventName: 'sorter_',
          suffix: `${staySeq}_${item.title}_${item.subTitle}`,
        });
      }
    });
  }

  logFilters = (type, data, staySeq) => {
    const temp = [];
    if (data) {
      data.forEach((item) => {
        if (item.selected) {
          temp.push(item.name);
        }
      });
    }
    if (temp.length > 0) {
      const val = temp.join(':');
      this.captureClickEvents({
        eventName: 'filter_',
        suffix: `${staySeq}_${type}_${val}`,
      });
    }
  }

  logPriceFilter = (priceRange, defaultPriceRange, staySeq) => {
    if (priceRange && defaultPriceRange) {
      if (!(priceRange.max === defaultPriceRange.max && priceRange.min === defaultPriceRange.min)) {
        this.captureClickEvents({
          eventName: 'filter_',
          suffix: `${staySeq}_${FiltersType.PRICE_RANGE}_${priceRange.min},${priceRange.max}`,
        });
      }
    }
  }

  logSortByAndFilters = async (filterData, staySeq) => {
    filterData.map((item, index) => {
      switch (item.type) {
        case FiltersType.SORT_BY:
          this.logSortBy(item.data, staySeq);
          break;
        case FiltersType.THEMES:
        case FiltersType.CATEGORY:
          this.logFilters(item.type, item.data, staySeq);
          break;
        case FiltersType.PRICE_RANGE:
          this.logPriceFilter(item.priceRange, item.defaultPriceRange, staySeq);
          break;
      }
    });
  }

  onUpdatePress = () => {
    const {selectedActivities} = this.state;
    const activitiesRequest = [];
    const tempName = [];
    this.staySequences.forEach((item, index) => {
      tempName.push(item.name);
      const activities = selectedActivities.get(item.staySequence);
      for (const [key, value] of activities.entries()) {
        activitiesRequest.push({
          activityCode: value.activityCode,
          optionRecheckKey: value.recheckKey,
          staySequence: item.staySequence,
          name: value.name,
        });
      }
    });
    const actionData = {
      action: packageActions.MODIFY,
      dynamicPackageId: this.props.dynamicId,
      selectedActivities: activitiesRequest,
      day: this.props.day,
    };
    this.props.onComponentChange(actionData, componentImageTypes.ACTIVITY);
    const val = tempName.join(':');
    this.captureClickEvents({
      eventName: 'update_',
      suffix: `${itineraryUnitTypes.ACTIVITY}_${val}_${this.selectedActivityTotalPrice}`,
    });
    this.captureClickEvents({ eventName: 'close' });

    this.handleNavigationToListingPage();
  }

  getActivityPriceDiff = (code) => {
    const {packagePriceMap, selectedStaySequence, discountedFactor} = this.state;
    const PriceMap = packagePriceMap.get(selectedStaySequence);
    const discFactor = discountedFactor.get(selectedStaySequence);
    const activityPrice = PriceMap[code];
    const price = checkAndChangePriceToZero(getPriceDiff(this.packagePrice, activityPrice, discFactor));
    return price;
  }

  createBlackStripData = () => {
    const {selectedStaySequence, discountedFactor, selectedActivities} = this.state;
    const selectedActivitiesMap = selectedActivities.get(selectedStaySequence);
    let finalPackagePrice = getDiscountedPrice(this.packagePrice, discountedFactor.get(selectedStaySequence));

    const preSelectedActivityMap = this.preSelectedActivities.get(selectedStaySequence);
    const numberOfActivities = selectedActivitiesMap.size;
    let showUpdate = selectedActivitiesMap.size !== preSelectedActivityMap.size;

    for (const [key] of selectedActivitiesMap.entries()) {
      if (!preSelectedActivityMap.has(key)) {
        showUpdate = true;
        break;
      }
    }
    return {
      day: this.props.day,
      numberOfActivities: numberOfActivities,
      activityPrice: this.selectedActivityTotalPrice,
      packagePrice: finalPackagePrice,
      showUpdate: showUpdate,
    };
  }

  render() {
    const {listingLoading, listingSuccess, listingError} = this.state;
    return (
      <View style={styles.activityPageWrap}>
        {listingError && this.renderError()}
        {listingLoading && this.renderLoader()}
        {listingSuccess && this.renderContent()}
      </View>
    );
  }

  renderError() {
    return (
      <View style={{flex: 1}}>
        <FullPageError
          title="Oops! Page not found"
          subTitle="We can’t seem to find the page you’re looking for"
          suggestion=""
          onRefreshPressed={null}
          renderStickyHeader={() => (
            <PhoenixHeader
              title={this.header}
              subtitleData={this.props.subtitleData}
              handleClose={this.onBackPress}
            />
          )}
          onBackPressed={this.onBackPress}
        />
      </View>
    );
  }

  renderLoader() {
    const {packageDetailDTO, roomDetails} = this.props;
    return (
      <HolidayDetailLoader
        departureCity={packageDetailDTO.depCityName}
        departureDate={packageDetailDTO.departureDate}
        duration={packageDetailDTO.duration}
        travellerObj={createTravellerObjForLoader(roomDetails)}
        changeAction={false}
      />
    );
  }

  setActivityList = ({ value }) => {
    this.setState({
      initialActivityList: value,
    });
  }
  openActivityDetailPage = (code, safe) => {
    const {selectedStaySequence, selectedActivities} = this.state;
    const selectedActivitiesMap = selectedActivities.get(selectedStaySequence);
    let selectedRecheckKey = null;
    let selected = false;
    if (selectedActivitiesMap.has(code)) {
      selected = true;
      const obj = selectedActivitiesMap.get(code);
      selectedRecheckKey = obj.recheckKey;
    }
    let suffix = code;
    if (safe) {
      suffix += '_safe_activity';
    }
    this.captureClickEvents({ eventName: 'select_', suffix });
    const activityDetailProps = {
      blackStripData: this.createBlackStripData(),
      packagePrice: this.packagePrice,
      dynamicPackageId: this.props.dynamicId,
      staySequence: this.state.selectedStaySequence,
      day: this.props.day,
      activityCode: code,
      selectedRecheckKey: selectedRecheckKey,
      modifyActivityDetail: this.modifyActivityDetail,
      modifyActivityTrue: true,
      onUpdatePress: this.onUpdatePress,
      selected: selected,
      subtitleData: this.props.subtitleData,
      branch: this.props.branch,
      packageDetailDTO: this.props.packageDetailDTO,
      roomDetails: this.props.roomDetails,
      hideOverlays: this.props.hideOverlays,
      showOverlay: this.props.showOverlay,
      setActivityList: this.setActivityList,
    };

    isMobileClient()
        ? HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL, activityDetailProps)
        : this.props.showOverlay(Overlay.ACTIVITY_DETAIL_PAGE, activityDetailProps);
  }

  renderActivityCard = ({item, index}) => {
    const {metaData} = item;
    const {code} = metaData || {};
    const price = this.getActivityPriceDiff(code);
    const safetyDetails = getActivitySafetyDetails(item, this.props.branch);
    return (
      <View style={[AtomicCss.marginBottom16]}>
        <ActivityListCard
          key={index}
          item={item}
          index={index}
          price={price}
          openActivityDetailPage={this.openActivityDetailPage}
          updateActivities={this.updateActivities}
          safetyDetails={safetyDetails}
        />
      </View>
    );
  }

  openSortByAndFilter = (prefix) => {
    this.captureClickEvents({ eventName: prefix });
    this.setState({
      showSortByAndFilters: true,
    });
  }

  findActivityByName = (value) => {
    const { filteredActivities, selectedStaySequence } = this.state;
    const newValue = filteredActivities.get(selectedStaySequence).filter((item)=> item?.metaData?.name?.toLowerCase().includes(value?.toLowerCase()));
    this.setState({
      initialActivityList: value ? newValue : HOLIDAYS_ACTIVITY_INITIAL_LIST,
    });
  }
  renderContent () {
    const {subtitleData} = this.props;
    const {filteredActivities, selectedStaySequence, filterData, showSortByAndFilters, showConfirmationDialog, initialActivityList} = this.state;
    const activityList = initialActivityList === HOLIDAYS_ACTIVITY_INITIAL_LIST ? filteredActivities.get(selectedStaySequence) : initialActivityList;
    const blackStripData = this.createBlackStripData();
    return (
      <View style={{flex: 1, backgroundColor:holidayColors.lightGray2}}>
        <PhoenixHeader
          title={this.header}
          subtitleData={subtitleData}
          handleClose={this.onBackPress}
        />
        <BlackPriceStrip
          {...blackStripData}
          onUpdatePress={this.onUpdatePress}
        />
        <ChangeActivity findActivityByName={this.findActivityByName} resetValue={!(Array.isArray(initialActivityList))}/>
        <FlatList
          data={activityList}
          renderItem={this.renderActivityCard}
          style={styles.activityPageInnerWrap}
          contentContainerStyle={{
            paddingBottom: 60,
            horizontalPadding: 7,
          }}
          keyExtractor={(item, index) => `${item.recheckKey} ${index}`}
        />
        <Modal
          onRequestClose={() => this.setState({showSortByAndFilters : false})}
          animationType="slide"
          transparent={true}
          visible={showSortByAndFilters}
          propagateSwipe={true}
        >
          {showSortByAndFilters &&
            <ActivityFilters
              staySequences={this.staySequences}
              selectedStaySequence={selectedStaySequence}
              filterData={cloneDeep(filterData)}
              onClose={() => this.setState({ showSortByAndFilters: false }) }
              applyFilters={this.applyFilters}
            />}
        </Modal>
        <FilterTag
          handleSort={() => this.openSortByAndFilter('sorter')}
          handleFilter={() => this.openSortByAndFilter('filter')}
        />
        <ConfirmationPopup
          confirmDialogVisibility={showConfirmationDialog}
          onUpdatePackageClickFromPopup={this.onUpdatePress}
          onNotNowClicked={this.onNotNowClicked}
          onShowMeClicked={this.onShowMeClicked}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  activityPageWrap: {
    flex: 1,
    backgroundColor: holidayColors.white,
  },
  activityPageInnerWrap: {
    ...paddingStyles.ph16,
    ...paddingStyles.pb16,
  },
});

export default withBackHandler(ActivityListingPage);
