import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const MultiFilterSection = ({heading, data, type, index, updateFilters}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.heading}>{heading}</Text>
      <View style={styles.themeList}>
        {data.map((item, i) => {
          return (
              <TouchableOpacity
                  key={i}
                  onPress={() => updateFilters(type, index, i)}
                  activeOpacity={0.6}
              >
              <Box name={item.name} active={item.selected}/>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const Box = ({name, active}) => {
  const themeBoxText = active ? styles.themeBoxTextSelected : styles.themeBoxText;
  return (
    <View style={[styles.themeBox, active && styles.themeBoxSelected]}>
      <Text style={themeBoxText}>{name}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 17,
  },
  heading: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
    ...marginStyles.mb12,
  },
  themeList: {
    width: '100%',
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  themeBoxSelected: {
    backgroundColor: holidayColors.lightBlueBg,
    borderColor: holidayColors.primaryBlue,
  },
  themeBox: {
    paddingHorizontal: 9,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 10,
    backgroundColor: holidayColors.white,
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
    borderRadius: 4,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.20,
    shadowRadius: 1.41,

    elevation: 2,
  },
  themeBoxTextSelected: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  themeBoxText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
});

export default MultiFilterSection;
