import React from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import starIcon from '../../../../../FilterHotels/FilterHotelList/Images/star.webp';
import rupeeLowIcon from '../../../../../FilterHotels/FilterHotelList/Images/ruppeLowIcon.webp';
import rupeeHighIcon from '../../../../../FilterHotels/FilterHotelList/Images/ruppeHighIcon.webp';
import { SortByTypes } from '../../../../Utils/ActivityOverlayUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { widthPixel } from 'mobile-holidays-react-native/src/Styles/holidayNormaliseSize';

const IconMap = {
  [SortByTypes.POPULARITY]: starIcon,
  [SortByTypes.PRICE_LOW_TO_HIGH]: rupeeLowIcon,
  [SortByTypes.PRICE_HIGH_TO_LOW]: rupeeHighIcon,
};

const SortBy = ({data, type, index, updateFilters}) => {
  return (
    <View style={styles.sortingCard}>
      <Text style={styles.headingText}>Sort By</Text>
      <View style={[AtomicCss.flexRow, styles.flexWrap]}>
        {data.map((item, i) => {
          return (
              <TouchableOpacity
                  key={i}
                  onPress={() => updateFilters(type, index, i)}
                  activeOpacity={0.6}
                  style={[styles.sortingTabs, item.selected && styles.sortingTabsActive]}
              >
              <SortingTab
                active={item.selected}
                item={item}
              />
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const SortingTab = ({active, item}) => {
  const {title, subTitle, type} = item;
  return (
    <View style={styles.tabContainer}>
      <Image
        style={styles[`${type}Icon`]}
        source={IconMap[type]}
      />
      <Text style={[styles.sortingText, active && styles.sortingTextActive]}>{title}</Text>
      <Text style={styles.subSortingText}>{subTitle}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  sortingCard: {
    backgroundColor: '#e9e9e9', paddingTop: 20, paddingBottom: 10, paddingHorizontal: 16,
  },
  headingText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
    ...marginStyles.mb12,
  },
  flexWrap: {flexWrap: 'wrap'},
  sortingTabs: {
    ...marginStyles.mb10,
    ...marginStyles.mr10,
    alignSelf: 'flex-start',
    borderRadius: 4,
    backgroundColor: '#fff',
    elevation: 3,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    width: widthPixel(79),
    height: 98,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  sortingTabsActive: {
    backgroundColor: holidayColors.lightBlueBg,
    borderColor: holidayColors.primaryBlue,
  },
  tabContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  starIcon: {width: 26, height: 26, marginBottom: 8},
  priceLowIcon: {width: 22, height: 18, marginBottom: 10},
  priceHighIcon: {width: 23, height: 18, marginBottom: 10},
  userRatingIcon: {width: 25, height: 26, marginBottom: 10},
  sortingText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
    ...marginStyles.mb2,
  },
  subSortingText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  sortingTextActive: {
    color: holidayColors.primaryBlue,
  },
});

export default SortBy;
