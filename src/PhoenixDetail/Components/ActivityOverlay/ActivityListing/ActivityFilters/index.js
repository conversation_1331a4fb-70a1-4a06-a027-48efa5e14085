import React, { useState } from 'react';
import {Platform, ScrollView, StatusBar, StyleSheet, View} from 'react-native';
import FilterHeader from '../../../FlightDetailPage/FilterHeader';
import SelectedFilters from './SelectedFilters';
import CityFilter from './CityFilter';
import SortBy from './SortBy';
import MultiFilterSection from './MultiFilterSection';
import PriceRange from './PriceRange';
import { FiltersType } from '../../../../Utils/ActivityOverlayUtils';
import {cloneDeep} from 'lodash';
import { showShortToast } from '@mmt/core/helpers/toast';
import {statusBarHeightForIphone} from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import PrimaryButton from '../../../../../Common/Components/Buttons/PrimaryButton';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { smallHeightSeperator } from '../../../../../Styles/holidaySpacing';

const ActivityFilters = (props) => {
  const { cityFilterEnabled = true } = props || {};
  const [staySeq, setStaySeq] = useState(props.selectedStaySequence);
  const [allFilterData, setAllFilterData] = useState(props.filterData);

  const updateStaySeq = (newStaySeq) => {
    setStaySeq(newStaySeq);
  };

  const filterData = allFilterData.get(staySeq);

  const onCrossPress = (index1, index2) => {
    const tempFilters = cloneDeep(allFilterData);
    const newFilterData = tempFilters.get(staySeq);
    newFilterData[index1].data[index2].selected = false;
    tempFilters.set(staySeq, newFilterData);
    setAllFilterData(tempFilters);
  };

  const checkAndApply = () => {
    const value = props.applyFilters(allFilterData, staySeq);
    if (!value) {
      showShortToast('Too many filters applied !');
    } else {
      props.onClose();
    }
  };

  const clearAllFilters = () => {
    const tempFilters = cloneDeep(allFilterData);
    const newFilterData = tempFilters.get(staySeq);
    newFilterData.forEach((filterObj) => {
      if (filterObj.type === FiltersType.PRICE_RANGE) {
        filterObj.priceRange = filterObj.defaultPriceRange;
      } else {
        filterObj.data.forEach((obj) => {
          obj.selected = false;
        });
      }
    });
    tempFilters.set(staySeq, newFilterData);
    setAllFilterData(tempFilters);
  };

  const updateFilters = (type, index, val) => {
    const tempFilters = cloneDeep(allFilterData);
    const newFilterData = tempFilters.get(staySeq);
    switch (type) {
      case FiltersType.SORT_BY:
        const sortByObj = newFilterData[index].data[val];
        if (sortByObj.title === 'Price') {
          if (sortByObj.selected) {
            sortByObj.selected = false;
          } else {
            newFilterData[index].data.forEach((item, i) => {
              if (item.title === 'Price') {
                item.selected = false;
              }
            });
            sortByObj.selected = true;
          }
        } else {
          sortByObj.selected = !sortByObj.selected;
        }
        break;
      case FiltersType.THEMES:
        newFilterData[index].data[val].selected = !newFilterData[index].data[val].selected;
        break;
      case FiltersType.PRICE_RANGE:
        newFilterData[index].priceRange = {
          min: val[0],
          max: val[1],
        };
        break;
      case FiltersType.CATEGORY:
        newFilterData[index].data[val].selected = !newFilterData[index].data[val].selected;
        break;
      default: break;
    }
    tempFilters.set(staySeq, newFilterData);
    setAllFilterData(tempFilters);
  };

  const renderFilterObj = (filterObj, index) => {
    const {type, data, priceRange, defaultPriceRange} = filterObj;
    switch (type) {
      case FiltersType.SORT_BY:
        return (
          <>
            <View style={styles.line}/>
            <SortBy
              data={data}
              type={FiltersType.SORT_BY}
              index={index}
              updateFilters={updateFilters}
            />
          </>
        );
      case FiltersType.THEMES:
        if (!data || data.length === 0) {
          return [];
        }
        return (
          <>
            <View style={styles.line}/>
            <MultiFilterSection
              heading={'Themes'}
              data={data}
              type={FiltersType.THEMES}
              index={index}
              updateFilters={updateFilters}
            />
          </>
        );
      case FiltersType.PRICE_RANGE:
        if (defaultPriceRange.min === defaultPriceRange.max) {
          return [];
        }
        return (
          <>
            <View style={styles.line}/>
            <PriceRange
              priceRange={priceRange}
              defaultPriceRange={defaultPriceRange}
              type={FiltersType.PRICE_RANGE}
              index={index}
              updateFilters={updateFilters}
            />
          </>
        );
      case FiltersType.CATEGORY:
        if (!data || data.length === 0) {
          return [];
        }
        return (
          <>
            <View style={styles.line}/>
            <MultiFilterSection
              heading={'Category'}
              data={data}
              type={FiltersType.CATEGORY}
              index={index}
              updateFilters={updateFilters}
            />
          </>
        );
      default: break;
    }
    return [];
  };

  return (
    <View style={styles.modalView}>
      <FilterHeader
        headerTxt="Sort & Filters"
        subHeaderTxt="Showing 23 out of 210 Results"
        handleClose={props.onClose}
        clearFilterData = {clearAllFilters}
      />
      <ScrollView style={{flex: 1}}>
        <SelectedFilters
          filterData={filterData}
          onCrossPress={onCrossPress}
        />
        {props.staySequences && props.staySequences.length > 1 && cityFilterEnabled &&
          <CityFilter
            staySequences={props.staySequences}
            staySeq={staySeq}
            updateStaySeq={updateStaySeq}
          />}
        {filterData && filterData.map((filterObj, index) => renderFilterObj(filterObj, index))}
      </ScrollView>
      <PrimaryButton
        buttonText={'Apply'}
        handleClick={checkAndApply}
        btnContainerStyles={styles.filterBtn}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  gallaryView: {
    width: '50%',
  },
  modalView: {
    backgroundColor: 'white',
    width: '100%',
    height: '100%',
    flex: 1,
    marginTop:statusBarHeightForIphone,
  },
  line: {
    ...smallHeightSeperator,
  },
  filterBtn: {
    ...paddingStyles.ph16,
    ...marginStyles.ma10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  applyFilter: {
    ...fontStyles.headingBase,
    color: holidayColors.white,
  },
});

export default ActivityFilters;
