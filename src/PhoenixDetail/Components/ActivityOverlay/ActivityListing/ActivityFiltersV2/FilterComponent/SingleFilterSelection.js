import { holidayColors } from "mobile-holidays-react-native/src/Styles/holidayColors";
import { fontStyles } from "mobile-holidays-react-native/src/Styles/holidayFonts";
import { marginStyles } from "mobile-holidays-react-native/src/Styles/Spacing";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
const SingleFilterSelection = (props) => {
    const {name, filterOptions,updateByFilter,appliedFilterData,type,id} = props;
    const selectedFilter = appliedFilterData?.filter?.find(filter => filter.id === id);
    return (
        <View style={styles.container}>
        <Text style={styles.heading}>{name}</Text>
        <View style={styles.themeList}>
          {filterOptions.map((item, i) => {
            return (
                <TouchableOpacity
                    key={i}
                    onPress={() =>updateByFilter(item.uniqueId,type,id)}
                    activeOpacity={0.6}
                >
                <Box name={item.filterText} 
                active={selectedFilter?.values.includes(item.uniqueId)}
                />
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
}
const Box = ({name, active}) => {
    const themeBoxText = active ? styles.themeBoxTextSelected : styles.themeBoxText;
    return (
      <View style={[styles.themeBox, active && styles.themeBoxSelected]}>
        <Text style={themeBoxText}>{name}</Text>
      </View>
    );
  };
  
  const styles = StyleSheet.create({
    container: {
      paddingVertical: 17,
    },
    heading: {
      ...fontStyles.labelBaseBlack,
      color: holidayColors.gray,
      ...marginStyles.mb12,
    },
    themeList: {
      width: '100%',
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    themeBoxSelected: {
      backgroundColor: holidayColors.lightBlueBg,
      borderColor: holidayColors.primaryBlue,
    },
    themeBox: {
      paddingHorizontal: 9,
      paddingVertical: 8,
      marginRight: 8,
      marginBottom: 10,
      backgroundColor: holidayColors.white,
      borderColor: holidayColors.grayBorder,
      borderWidth: 1,
      borderRadius: 4,
      shadowColor: 'rgba(0, 0, 0, 0.2)',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.20,
      shadowRadius: 1.41,
  
      elevation: 2,
    },
    themeBoxTextSelected: {
      ...fontStyles.labelSmallBold,
      color: holidayColors.primaryBlue,
    },
    themeBoxText: {
      ...fontStyles.labelSmallRegular,
      color: holidayColors.gray,
    },
  });
export default SingleFilterSelection;
