import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import React, { useMemo, useState } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, View } from 'react-native';
import FilterHeader from '../../../FlightDetailPage/FilterHeader';
import SortComponent from './SortComponent';
import FilterComponent from './FilterComponent';
import PrimaryButton from '../../../../../Common/Components/Buttons/PrimaryButton';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { filtersEqual, sortersEqual } from 'mobile-holidays-react-native/src/PhoenixDetail/Components/ItineraryV2/ItineraryV2Utils';

const ActivityFiltersV2 = ({ setShowSortByAndFilters, filterListData, appliedFilters ,applyFilters ,activityType, applyClick }) => {
    const [appliedFilterData, setAppliedFilterData] = useState(appliedFilters);

    // Check if current filter data has changed from initially applied filters
    const hasChanges = useMemo(() => {
        // Compare sorters
        const sortersEqualResult = sortersEqual(appliedFilterData.sorter, appliedFilters.sorter);

        // Compare filters
        const filtersEqualResult = filtersEqual(appliedFilterData.filter, appliedFilters.filter);

        return !sortersEqualResult || !filtersEqualResult;
    }, [appliedFilterData, appliedFilters]);


    // Transform API response to filter format
    const getFilterData = () => {
        if (!filterListData || !activityType || !filterListData[activityType.toLowerCase()]) {
            return [];
        }

        const activityData = filterListData[activityType.toLowerCase()];

        const filters = [];

        // Category filter (multiselect dropdown)
        const filterConfigs = [
            {
                key: 'categoryInfo',
                id: 1,
                name: 'Category',
                type: 'Dropdown_Multiselect',
                getOptions: (data) =>
                    data?.map(cat => ({
                        uniqueId: cat?.code,
                        filterText: cat?.name,
                    })) || [],
            },
        ];

        filterConfigs.forEach(({ key, id, name, type, getOptions }) => {
            const info = activityData?.[key];
            if (Array.isArray(info) && info.length)
                {filters.push({
                    id,
                    name,
                    type,
                    filterOptions: getOptions(info),
                });}
        });

        if (
            typeof activityData?.minPrice === 'number' &&
            typeof activityData?.maxPrice === 'number'
        ) {
            filters.push({
                id: 2,
                name: 'Price Range',
                type: 'range',
                filterOptions: [
                    {
                        filterText: `${activityData.minPrice}_${activityData.maxPrice}`,
                    },
                ],
            });
        }
        return filters;
    };

    const updateBySort = (data) => {
        setAppliedFilterData(prevState => {
            const isAlreadySorted = prevState.sorter.some(sort => sort.id === data.id);
            return {
                ...prevState,
                sorter: isAlreadySorted ? [] : [{ id: data.id }],
            };
        });
    };
    const updateByFilter = (value, type, id) => {
        const existingFilter = appliedFilterData.filter?.find(filter => filter.id === id);
        let updatedValues = [];

        if (type === 'Dropdown_Multiselect') {
            if (existingFilter) {
                updatedValues = existingFilter.values.includes(value)
                    ? existingFilter.values.filter(v => v !== value)
                    : [...existingFilter.values, value];
            } else {
                updatedValues = [value];
            }
        } else if (type === 'range') {
            // For price range, value should be an object with min and max
            updatedValues = [value];
        } else {
            updatedValues = [value];
        }

        const otherFilters = appliedFilterData.filter?.filter(filter => filter.id !== id) || [];

        setAppliedFilterData(prevState => ({
            ...prevState,
            filter: updatedValues.length > 0 ? [{ id, values: updatedValues }, ...otherFilters] : otherFilters,
        }));
    };

const activityData = filterListData[activityType.toLowerCase()];
const {sorting = []} = activityData || {};
// Use transformed filter data from API response
const transformedFilterData = getFilterData();
const productCount = filterListData?.[activityType]?.productCount || 0;
    return (
        <View style={styles.container}>
            <FilterHeader
                headerTxt="Sort & Filters"
                subHeaderTxt={`Showing ${productCount} Results`}
                handleClose={() => setShowSortByAndFilters(false)}
                clearFilterData={() => setAppliedFilterData({
                    sorter: [],
                    filter: [],
                })}
            />
            <ScrollView style={styles.scrollContainer}>
                {sorting.length > 0 && (
                    <SortComponent
                        sortData={sorting}
                        updateBySort={updateBySort}
                        filterData={appliedFilterData}
                    />
                )}
                {transformedFilterData.length > 0 && (
                    <FilterComponent
                        filterData={transformedFilterData}
                        updateByFilter={updateByFilter}
                        appliedFilterData={appliedFilterData}
                    />
                )}
            </ScrollView>
            <SafeAreaView>
                <PrimaryButton
                  buttonText="Apply"
                  handleClick={() => applyClick(appliedFilterData)}
                  btnContainerStyles={styles.btnContainerStyles}
                  isDisable={!hasChanges}
                />
            </SafeAreaView>
        </View>
    );
};

export default ActivityFiltersV2;

const styles = StyleSheet.create({
    container: {
        backgroundColor: 'white',
        flex: 1,
        marginTop: statusBarHeightForIphone,
    },
    scrollContainer: {
        paddingHorizontal: 16,
    },
    btnContainerStyles: {
        ...paddingStyles.ph16,
        ...marginStyles.ma16,
        alignItems: 'center',
        justifyContent: 'center',
    },
});
