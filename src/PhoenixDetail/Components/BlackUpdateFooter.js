import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import PrimaryButton from '../../Common/Components/Buttons/PrimaryButton';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { rupeeFormatterUtils } from '../../utils/HolidayUtils';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { borderRadiusValues } from '../../Styles/holidayBorderRadius';
import { connect } from 'react-redux';
import BottomBar from '@Frontend_Ui_Lib_App/BottomBar';
const BOOK_NOW = 'UPDATE';

const BlackUpdateFooter = (props) => {
  const { onUpdatePress, blackStripData, packageDetail } = props || {};
  const { activityPrice, packagePrice, showUpdate } = blackStripData || {};
  const addonPrice=packageDetail?.pricingDetail?.categoryPrices?.[0]?.addonsPrice || 0

  if (!showUpdate) {
    return [];
  }

  const price = packagePrice + activityPrice + addonPrice
  const { trackLocalClickEvent = () => {}, zIndexVal = 2 } = props || {};
  const TitleComponent = () => {
    return (
      <Text style={styles.grossPrice}>
        {rupeeFormatterUtils(activityPrice)}
        <Text style={styles.netPrice}> /person</Text>
      </Text>
    );
  };
  const RenderDescription = () => {
    return (
      <Text style={styles.netPrice}>
            Total Price {rupeeFormatterUtils(price)}
            <Text style={styles.perPerson}></Text>
          </Text>
    );
  };
  return (
      <BottomBar
        titleRightComponent={ <TitleComponent /> }
        description1={<RenderDescription />}
        rightComponent={
          <PrimaryButton
            buttonText={BOOK_NOW}
            handleClick={onUpdatePress}
            btnContainerStyles={styles.CapsuleBtnFill}
            show={true}
          />
        }
        customStyles = {
          customStyles
        }
      />
  );
};

const styles = StyleSheet.create({
  
  CapsuleBtnFill: {
    ...paddingStyles.ph30,
    overflow: 'hidden',
    alignItems: 'center',
    ...marginStyles.mr8,
    borderRadius: borderRadiusValues.br8,
  },
  perPerson: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.white,
  },
  netPrice: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.white,
    ...marginStyles.mt4,
    ...marginStyles.mb2,
  },
  grossPrice: {
    ...fontStyles.headingBase,
    color: holidayColors.white,
  },
  totalPrice: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.white,
    opacity: 0.7,
  },
});
const customStyles = StyleSheet.create({
  containerStyle: { 
    height: 70, 
    backgroundColor: holidayColors.black,
  },
});
const mapStateToProps = (state) => {
  const { blackStripData } = state.travelTidbitsReducer;
  const { detailData } = state.holidaysDetail || {};
  const { packageDetail } = detailData || {};
  return { blackStripData, packageDetail };
};

export default connect(mapStateToProps)(BlackUpdateFooter);
