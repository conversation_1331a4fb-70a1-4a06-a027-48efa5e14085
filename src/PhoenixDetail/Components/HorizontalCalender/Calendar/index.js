import React, {Component} from 'react';

import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from 'react-native';

import LinearGradient from 'react-native-linear-gradient';
import {getDate, getDay, getMonth} from './utilities';
import { formatDate } from '@mmt/legacy-commons/Helpers/dateTimehelpers';
import _ from 'lodash';
import fecha from 'fecha';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';
import { widthPixel } from 'mobile-holidays-react-native/src/Styles/holidayNormaliseSize';

 const formatDateToYYYYMMDD = (d) => {
  if (!_.isDate(d)) {
    return '';
  }
  return fecha.format(d, 'YYYY-MM-DD');
};

class Calendar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      firstViewableDate: props.startDate,
    };
    this.myRef = React.createRef();
  }

  dateArray = () => {
    const {startDate, endDate} = this.props || {};
    let tempStartDate = new Date(startDate);
    let arr = [];
    while (endDate.toDateString() !== tempStartDate.toDateString()) {
      arr.push(new Date(tempStartDate));
      tempStartDate.setDate(tempStartDate.getDate() + 1);
    }
    arr.push(tempStartDate);
    return arr;
  };

  renderDate = ({ item, index }) => {
    const {selectedDate} =  this.props || {};
    const isFirstDay = item.getDate() === 1;
    const isSelected = formatDateToYYYYMMDD(item) === selectedDate;
    let dateContainerStyle = [styles.dateContainer];
    let dayTextStyle = [styles.dayText];
    let dateTextStyle = [styles.dateText];

    if (isSelected) {
      dateContainerStyle.push(styles.activeDateContainer);
      dateTextStyle.push(styles.whiteText);
      dayTextStyle.push(styles.whiteText);
    }

    return (
      <>
        {isFirstDay && index !== 0 && getMonth(item) !== getMonth(this.state.firstViewableDate) && <MonthLabel month={getMonth(item)} />}
        <TouchableOpacity
          style={dateContainerStyle}
          activeOpacity={0.8}
          onPress={() => this.props.handleDateClick(item, index)}
        >
          <Text style={dayTextStyle}>{getDay(item)}</Text>
          <Text style={dateTextStyle}>{getDate(item)}</Text>
        </TouchableOpacity>
      </>
    );
  };

  updateSelectedDate = (day) => {
    // scroll calender to the selected date.
    const dates = this.dateArray();
    const index = dates.findIndex(item => {
      if (formatDate(item, 'YYYY-MM-DD').toString().includes(day)){
        return true;
      }
    });
    if (this.myRef !== null && this.myRef.current !== null && index >= 0){
      this.myRef.current.scrollToIndex({ index, animated: true });
    }
  }

  handleViewableItemsChanged = ({ viewableItems, changed }) => {
    const firstViewItem = viewableItems[0];
    if (getMonth(this.state.firstViewableDate) !== getMonth(firstViewItem.item)) {
      this.setState({
        firstViewableDate: firstViewItem.item,
      });
    }
  }

  render() {
    const dates = this.dateArray();
    const {selectedDate} =  this.props || {};

    const index = dates.findIndex(item => {
      if (formatDate(item, 'YYYY-MM-DD').toString().includes(selectedDate)){
        return true;
      }
    });

    return (
      <View style={styles.container}>
        <View style={styles.monthLabelContainer}>
          <MonthLabel month={getMonth(this.state.firstViewableDate)} />
        </View>
        <View
          style={styles.listContainer}
          onLayout={() => this.myRef.current.scrollToIndex({ index: index, animated: false })}
        >
          <FlatList
            ref={this.myRef}
            showsHorizontalScrollIndicator={false}
            horizontal
            data={dates}
            renderItem={this.renderDate}
            keyExtractor={(date) => date.toDateString()}
            onViewableItemsChanged={this.handleViewableItemsChanged}
            onScrollToIndexFailed={info => {
              const wait = new Promise(resolve => setTimeout(resolve, 500));
              wait.then(() => {
                this.myRef.current.scrollToIndex({ index: info.index, animated: true });
              });
            }}
            removeClippedSubviews
            scrollEventThrottle={16}
          />
        </View>
      </View>
    );
  }
}

const MonthLabel = ({ month }) => {
  return (
    <LinearGradient
      start={{ x: 0.0, y: 1.0 }}
      end={{ x: 0.0, y: 0.0 }}
      colors={[holidayColors.gray, holidayColors.black]}
      style={styles.labelContainer}
    >
      <Text style={styles.labelText}>{month}</Text>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  whiteText: {
    color: holidayColors.white,
    opacity: 1,
  },
  container: {
    ...paddingStyles.ph16,
    ...paddingStyles.pb10,
  },
  monthLabelContainer: {
    position: 'absolute',
    zIndex: 1,
    left: 15,
  },
  listContainer: {
    left: 12,
    ...marginStyles.ml12,
  },
  dateContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 46,
    ...holidayBorderRadius.borderRadius8,
    backgroundColor: holidayColors.white,
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
    ...paddingStyles.ph16,
    ...paddingStyles.pt6,
    ...paddingStyles.pb6,
    ...marginStyles.mr4,
  },
  activeDateContainer: {
    backgroundColor: holidayColors.primaryBlue,
  },
  dayText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    opacity: 0.47,
  },
  dateText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  labelContainer: {
    height: 46,
    width: widthPixel(28),
    justifyContent: 'center',
    alignItems: 'center',
    ...holidayBorderRadius.borderRadius8,
    borderTopEndRadius: 0,
    borderBottomEndRadius: 0,
  },
  labelText: {
    color: holidayColors.white,
    ...fontStyles.labelSmallBold,
    transform: [{ rotate: '270deg' }],
  },
});
export default Calendar;