import React from 'react';
import {Image} from 'react-native';
import PropTypes from 'prop-types';
import promoBackground from '@mmt/legacy-assets/src/no_package_default.webp';
import {createImagePath} from '../../utils/HolidayUtils';


class ImageTileView extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      imageLoadFailed: false,
    };
  }

  render() {
    const {imageObj} = this.props;
    return (
      <Image
        style={this.props.imageStyle}
        source={
          !this.state.imageLoadFailed ?
            {
              uri:
                createImagePath(
                  imageObj.name,
                  imageObj.path,
                  this.props.imageSize,
                  imageObj.fullPath
                ),
            } : promoBackground}
        onError={this._onImageLoadError}
      />
    );
  }

  _onImageLoadError = () => {
    this.setState({imageLoadFailed: true});
  };
}

ImageTileView.propTypes = {
  imageObj: PropTypes.object.isRequired,
  imageSize: PropTypes.string.isRequired,
  imageStyle: PropTypes.number.isRequired,
};
export default ImageTileView;
