import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import FeatureCard from './FeatureCard';
import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import { shapeTypes } from '@mmt/legacy-commons/Common/Components/CoachMarks';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { getShowNewActivityDetail } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { holidayNavigationPush } from '../../Utils/DetailPageNavigationUtils';
import { Overlay } from '../DetailOverlays/OverlayConstants';
import { capitalizeText } from '../../../../src/utils/textTransformUtil';

const title = {
  'EDIT':'EDIT',
  'VIEW_DETAILS':'VIEW DETAILS',
  'FEATURES':'Features',
  'PACKAGE_FEATURES':'Package Features',
};
const DEFAULT_INDEX = 0;
export default class FeatureList extends BasePage {

  constructor (props) {
    super(props);
    this.state = {
      activeIndex: -1,
    };
  }
  componentDidUpdate(prevprops){
    if (prevprops.isOverlay != this.props.isOverlay){
      this.setState({
        activeIndex: this.props.activeIndex,
      });
    }
  }


  handlePress = (activeIndex) => {
    const { editable = true, packageFeatures = [] } = this.props || {};
    if (!editable) {
      return;
    }
    if (this.props.isOverlay){
      this.props.swipeCarousal(activeIndex);
      this.setState({
        activeIndex: activeIndex,
      });
      if (this.refFL) {
        this.refFL?.scrollToIndex({ animated: true, index: activeIndex });
      }
    }
    else {
      this.setState({
        activeIndex: activeIndex,
      });
      const showNewActivityDetail = getShowNewActivityDetail();
      if (showNewActivityDetail) {
        holidayNavigationPush({
          pageKey: HOLIDAY_ROUTE_KEYS.PACKAGE_ADD_ONS,
          overlayKey: Overlay.PACKAGE_ADD_ONS,
          props: {
            activeIndex,
            packageFeature: packageFeatures[activeIndex],
            hideOverlay: this.props.hideOverlays,
          },
          showOverlay: this.props.showOverlay,
          hideOverlays: this.props.hideOverlays,
        });
        // HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.PACKAGE_ADD_ONS, {
        //   activeIndex,
        //   packageFeature: packageFeatures[activeIndex],
        // });
      } else {
        this.props.toggleFDFeatureBottomSheet(true, activeIndex);
      }
    }

  }

  render () {
    let { packageFeatures = [], isOverlay, editable = true, activeIndex } = this.props || {};
    const coachMarkShapeObject = {
      right: -10,
      left: null,
      bottom: null,
      type: shapeTypes.circle,
      radius: 35,
    };
    const initialindex = this.state.activeIndex > -1 ? this.state.activeIndex : activeIndex;
   return <View >
        {!this.props?.isReview && <View style={!isOverlay ? styles.headingContainer : styles.headingContainer1}>
          <Text style={!isOverlay ? styles.heading : styles.heading1}>
            {!isOverlay ?  title.FEATURES : ''}
          </Text>
          {!isOverlay && editable && <TouchableOpacity onPress={() => {this.props.toggleFDFeatureBottomSheet(true,DEFAULT_INDEX);}}>
          <DynamicCoachMark
            cueStepKey="variantFDSelection"
            shapeType="circle"
            isSetCustomShape
            offsetHeight = {30}
            offsetWidth = {30}
            shapeObject={coachMarkShapeObject}
          >
            <Text style={styles.link}>{capitalizeText(title.EDIT)}</Text>
        </DynamicCoachMark>
          </TouchableOpacity>}
        </View>
        }
        {this.props?.isReview && <View style={!isOverlay ? styles.headingContainerReview : styles.headingContainer1}>
          <Text style={[!isOverlay ? styles.heading : styles.heading1,styles.headingReview]}>
            {!isOverlay ? (title.PACKAGE_FEATURES) : ''}
          </Text>
        </View>}

    <FlatList
      ref={(c) => {
        this.refFL = c;
      }}
      data={packageFeatures}
      style={styles.boxContainer}
      // initialScrollIndex={initialindex > 0 ? initialindex : 0}   //@to-do to check if needed
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={
        this.props?.isReview
          ? { paddingHorizontal: 2 }
          : isOverlay
          ? styles.featureListOverlayContainer
          : styles.featureListContainer
      }
      onScrollToIndexFailed={({
        index,
        averageItemLength,
      }) => {
        this.refFL.current?.scrollToOffset({
          offset: index * averageItemLength,
          animated: true,
        });
      }}
      renderItem={({item, index}) => (
        <FeatureCard
          disableOnPress={!isOverlay}
          item={item}
          index={index}
          handlePress={()=> this.handlePress(index)}
          editMode={isOverlay}
          key={index}
          editable={editable}
          activeIndex={this.state.activeIndex > -1 ? this.state.activeIndex  : activeIndex }
          packageFeaturesLength={packageFeatures.length}
        />
      )}
    />
    </View>;
  }
}

const styles = StyleSheet.create({
  boxContainer: {
    flexDirection: 'row',
  },
  container: {
    marginTop: 18,
    marginBottom: 16,
  },
  heading: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  headingReview:{
    fontWeight: 'bold',
    flex:2,
  },
  heading1: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
    lineHeight: 18,
  },
  link: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
    textAlign:'right',
  },
  headingContainer: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    marginHorizontal: 16,

  },
  headingContainerReview:{
    marginTop: 10,
    flexDirection: 'row',
    display:'flex',
    marginBottom: 15,
    marginHorizontal: 2,
  },
  headingContainer1: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  featuresListReviewContainer: {
    paddingHorizontal: 2,
  },
  featureListContainer: {
    paddingHorizontal: 15,
  },
  featureListOverlayContainer: {

  },
});
