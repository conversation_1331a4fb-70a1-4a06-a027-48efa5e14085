import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import iconArrowDown from '../images/ic_downArrow.png';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
const MIN_CARD_COUNT = 3;

export default class FeatureCard extends BasePage {
  constructor(props) {
    super(props);
  }

  render() {
    const { item, index, activeIndex, disableOnPress, editMode, packageFeaturesLength } =
      this.props;
    const containerStyle = [styles.featureContainer];
    if (editMode && index === activeIndex) {
      // @ts-ignore
      containerStyle.push(styles.activeFeature);
    }
    return (
      <View style={styles.mr10}>
        <TouchableOpacity style={containerStyle} activeOpacity={0.8} onPress={this.handlePress}>
          <View style={styles.iconContainer}>
            <HolidayImageHolder style={styles.icon} imageUrl={item.imageUrl} />
          </View>
          <View style={styles.featureTextContainer}>
            {!!item.title && <Text style={styles.title}>{item.title}</Text>}
            {!editMode && (
              <HolidayImageHolder style={styles.iconArrowDown} defaultImage={iconArrowDown} />
            )}
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  handlePress = () => {
    this.props.handlePress(this.props.item, this.props.index);
  };
}

const styles = StyleSheet.create({
  boxContainer: {
    flexDirection: 'row',
  },
  icon: {
    width: 20,
    height: 20,
  },
  iconContainer: {
    ...paddingStyles.pa4,
    borderRadius: 100,
  },
  title: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    textAlign: 'center',
    ...paddingStyles.pv2,
  },
  mr10: { ...marginStyles.mr10, alignContent: 'center',},
  activeFeature: {
    borderWidth: 2,
    borderColor: holidayColors.primaryBlue,
  },
  featureContainer: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    borderColor: holidayColors.disableGrayBg,
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius8,
    minHeight: 36,
    width: '100%',
    ...paddingStyles.pl8,
    ...paddingStyles.pr10,
  },
  subHeading: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
    textAlign: 'center',
    width: 74,
    ...paddingStyles.pb4,
  },
  activeSubHeading: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  featureTextContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  iconArrowDown: {
    width: 16,
    height: 16,
    ...marginStyles.ml8,
  },
});
