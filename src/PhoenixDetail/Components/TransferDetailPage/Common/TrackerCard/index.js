import React, {useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {connect} from 'react-redux';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const getSightSeeingOptions = carItineraryDetail => {
    // Handle fallback
    if (!carItineraryDetail
        || !carItineraryDetail.carContents
        || carItineraryDetail.carContents.length === 0
        || !carItineraryDetail.carContents[0].carDayWiseContents
    ) {
        return [];
    }

    // Handle data
    const sightSeeingMapping = [];
    if (carItineraryDetail.carContents) {
        let carItineraryDestinations = carItineraryDetail.carContents[0].carDayWiseContents;
        if (carItineraryDestinations.length > 0) {
            carItineraryDestinations.map(options => {
                options.carSightseeingDetails.map(sightSeeingOption => {
                    const {cityName, name} = sightSeeingOption;
                    if (cityName) {
                        if (sightSeeingMapping.some(item => item.city === cityName)) {
                            if (!sightSeeingMapping.find(item => item.city === cityName).subCity) {
                                sightSeeingMapping.find(item => item.city === cityName).subCity = [name];
                            } else {
                                sightSeeingMapping.find(item => item.city === cityName).subCity.push(name);
                            }
                        } else {
                            sightSeeingMapping.push({'city': cityName, subCity: [name]});
                        }
                    }
                });
            });
        }
    }
    return sightSeeingMapping;
};

const TrackerCard = (props) => {
    const [listToShow, setListToShow] = useState(100);
    const [subListToShow, setsubListToShow] = useState(100);

    const {packageContent} = props || {};
    const {carItineraryContent} = packageContent || {};

    if (!carItineraryContent) {
        return [];
    }
    const data = getSightSeeingOptions(carItineraryContent);
    if (!data || data.length === 0) {
        return [];
    }

    const handleList = () => {
        if (listToShow === 2) {
            setListToShow(data.length);
        } else {
            setListToShow(2);
        }
    };
    const handleSubList = (list) => {
        //alert(list)
        if (subListToShow === 2) {
            setsubListToShow(list.length);
        } else {
            setsubListToShow(2);
        }
    };
    let count = 0;
    data.map((item) => {
        if (item) {
            count = count + item.subCity.length;
        }
    });

    const ViewMoreLessText = listToShow === 2 ? `${data.length - 2} + More` : 'View Less';
    return (
        <View>
            <View style={styles.container}>
                <View style={AtomicCss.marginBottom20}>
                    <Text style={styles.headingText}>Sightseeing points : {count}</Text>
                </View>
                {data.map((item, index) => {
                    const ViewMoreLessSubText = subListToShow === 2 ? `${item.subCity.length - 2}+ More` : 'View Less';

                    while (index < listToShow) {
                        return (
                        <View style={((index < (data.length - 1)) ? styles.trackerRow : styles.trackerLastRow)}>
                                <View style={styles.trackerCircle}/>
                                <View style={styles.heading}><Text
                                    style={styles.cityText}>{item.city}</Text></View>
                                <View style={styles.subHeading}>
                                    {item.subCity.map((list, index) => {
                                        while ((index < 2) || subListToShow !== 2) {
                                            return (
                                                <Text key={index} style={styles.listItem}>{list}</Text>
                                            );
                                        }
                                    })}

                                </View>
                            </View>
                        );
                    }

                })}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: holidayColors.white,
        ...marginStyles.mb20,
    },
    headingText: {
        ...fontStyles.labelMediumBlack,
        color: holidayColors.black,
    },
    trackerLastRow: {
        flexDirection: 'row',
        marginLeft: 6,
        paddingLeft: 5,
        paddingBottom: 20,
    },
    trackerRow: {
        borderLeftWidth: 1,
        borderColor: holidayColors.lightGray,
        flexDirection: 'row',
        marginLeft: 5,
        paddingLeft: 5,
        paddingBottom: 20,
    },
    trackerRowViewMore: {
        borderLeftWidth: 1,
        borderColor: 'transparent',
        flexDirection: 'row',
        marginLeft: 5,
        paddingLeft: 5,
    },
    trackerCircle: {
        borderRadius: 100,
        width: 10,
        height: 10,
        backgroundColor: holidayColors.lightGray,
        marginLeft: -10,
    },
    heading: {
        marginLeft: 15,
        width: 80,
        marginTop: -3,
    },
    cityText: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.gray,
    },
    subHeading: {
        marginLeft: 15,
    },
    listItem: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.lightGray,
        ...marginStyles.mb6,
    },
    trackerCircleBlue: {
        borderRadius: 100,
        width: 10,
        height: 10,
        backgroundColor: holidayColors.primaryBlue,
        marginLeft: -10,
    }, separator: {
        borderTopWidth: 10,
        borderColor: '#f2f2f2',
        marginBottom: 9,
    },
});

const mapStateToProps = (state) => {
    const {packageContent} = state.holidaysDetail || {};
    return {packageContent};
};


export default connect(mapStateToProps, null)(TrackerCard);
