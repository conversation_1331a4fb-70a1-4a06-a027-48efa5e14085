import React from 'react';
import { Text, View, TouchableOpacity, Image, StyleSheet } from 'react-native';
import { IMAGE_CDN } from '../../../Common/Components/HolidayImageUrls';
import cross from '@mmt/legacy-assets/src/holidays/iconCross.webp';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
const transferpriceConstants = {
  priceIncrease: 1,
  priceDrop: -1,
};

const HolidayPriceChangeMessage = (props) => {
  const { closePriceMessage, airPortTransferMessage } = props || {};
  const { transferPriceChange = 0, displayMessage = '' } = airPortTransferMessage || {};
  let backgroundcolorMessage, iconPrice;
  switch (transferPriceChange) {
    case transferpriceConstants.priceIncrease:
      backgroundcolorMessage = holidayColors.fadedYellow;
      iconPrice = `${IMAGE_CDN}Flight Terminal Handling_Price Increase.png`;
      break;
    case transferpriceConstants.priceDrop:
      backgroundcolorMessage = holidayColors.fadedGreen;
      iconPrice = `${IMAGE_CDN}Flight Terminal Handling _Price Drop.png`;
      break;

    default:
      iconPrice = undefined;
      break;
  }
  const conatinerStyle = [styles.container, {backgroundColor: backgroundcolorMessage}];
  return (
    transferPriceChange !== 0 && (
      <View style={conatinerStyle}>
        <View style={styles.iconTextStyles}>
          <Image source={{ uri: iconPrice }} style={styles.icon} />
          <Text style={styles.displayMessage}>{displayMessage}</Text>
        </View>
          <TouchableOpacity onPress={closePriceMessage}>
            <Image source={cross} style={styles.cross} />
          </TouchableOpacity>
      </View>
    )
  );
};

const styles = StyleSheet.create({
  iconTextStyles: {
    flexDirection: 'row',
    width: '95%',
  },
  icon: {
    ...marginStyles.mr14,
    height: 20,
    width: 20,
  },
  displayMessage: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    flex: 1,
  },
  cross: {
    height: 20,
    width: 20,
    tintColor: holidayColors.lightGray,
  },
  container: {
    minHeight: 40,
    flexDirection: 'row',
    alignItems: 'flex-start',
    ...paddingStyles.pl16,
    ...paddingStyles.pr16,
    ...paddingStyles.pv10,
  },
});
export default HolidayPriceChangeMessage;
