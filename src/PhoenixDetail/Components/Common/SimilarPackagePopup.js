import React from 'react';
import { StyleSheet, Text, View, Image } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

const SimilarPackagePopup = ({ name, icon }) => {
    return (
        <View style={styles.wrapper}>
            <View style={styles.circle}>
                <Image source={icon} style={styles.icon} />
            </View>
            <View style={styles.textWrapper}>
                <Text style={styles.textStyle}>
                    There are no more similar {name} available for this package
                </Text>
            </View>

        </View>

    );
};
const styles = StyleSheet.create({
    wrapper: {
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
        borderRadius: 4,
        padding: 12,
        alignItems: 'center',
        display: 'flex',
        marginTop: 10,
    },
    circle: {
        height: 130,
        width: 130,
        borderRadius: 75,
        backgroundColor: holidayColors.lightBlueBg,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    },
    icon: {
        width: 55,
        height: 55,
    },
    textStyle: {
        ...fontStyles.labelMediumBold,
        color: holidayColors.lightGray,
        textAlign:'center',
    },
    textWrapper:{
        paddingHorizontal:'2%',
        marginTop:10,
    },

});

export default SimilarPackagePopup;
