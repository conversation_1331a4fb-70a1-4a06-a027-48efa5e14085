import React from 'react';
import {View, ScrollView} from 'react-native';
import PropTypes from 'prop-types';
import {viewTypes} from '../../Listing/ListingConstants';
import HolidayFunctionalPersuasion from '../../Common/Components/HolidayFunctionalPersuasion';
import HolidayBlackPersuasion from '../../Common/Components/HolidayBlackPersuasion';
import HolidayCashbackPersuasion from '../../Common/Components/HolidayCashbackPersuasion';
import HolidayWpmPersuasion from '../../Common/Components/HolidayWpmPersuasion';
import HolidayPricePersuasion from '../../Common/Components/HolidayPricePersuasion';
import HolidayEmiPersuasion from '../../Common/Components/HolidayEmiPersuasion';
import HolidayMagicStrip from '../../Common/Components/HolidayMagicStrip';
import AtomicCSS from '@mmt/legacy-commons/Styles/AtomicCss';
import HolidayCostPersuasion from '../../Common/Components/HolidayCostPersuasion';

class DetailPersuasion extends React.Component {
  render() {
    const elemToShow = this.fetchPersuasionElem(this.props.item);
    return (
      <View>
        {
          elemToShow
        }
      </View>
    );
  }

  fetchPersuasionElem = (item) => {
    let returnElem = {};
    switch (this.props.item.type) {
      case viewTypes.LISTING_EMI_PERSUASION:
        returnElem = (<HolidayEmiPersuasion
          emiPersuasionData={item.item.emiDetail}
          key="EMI"
        />);
        break;
      case viewTypes.LISTING_FUNCTIONAL_PERSUASION:
        returnElem = (<HolidayFunctionalPersuasion
          functionalPersuasionData={item.item}
          style={detailPersuasionStyle}
          key="FUNC"
        />);
        break;
      case viewTypes.LISTING_BLACK_PERSUASION:
        returnElem = (<HolidayBlackPersuasion
          blackPersuasionData={item.item.mmtBlackDetail}
          style={detailPersuasionStyle}
          key="BLACK"
        />);
        break;
      case viewTypes.LISTING_COST_PERSUASION:
        returnElem = (<HolidayCostPersuasion
          wircPersuasionData={item.item}
          key="COST"
          togglePopup={this.props.togglePopup}
        />);
        break;
      case viewTypes.LISTING_CASHBACK_PERSUASION:
        returnElem = (<HolidayCashbackPersuasion
          key="CASHBACK"
          cashbackPersuasionData={item.item}
          style={detailPersuasionStyle}
        />);
        break;
      case viewTypes.LISTING_WPM_PERSUASION:
        returnElem = (<HolidayWpmPersuasion
          key="WPM"
          wpmDetail={item.item.wpmDetail}
          style={detailPersuasionStyle}
        />);
        break;
      case viewTypes.LISTING_PRICE_PERSUASION:
        returnElem = (<HolidayPricePersuasion
          key="PRICE"
          pricePersuasion={item.item}
          style={datePersuasionStyle}
          togglePopup={this.props.togglePopup}
        />);
        break;
      case viewTypes.LISTING_MAGIC_STRIP_PERSUASION:
        if (this.props.onMagicStripClicked && item && item.item && item.item.cards && item.item.cards.length) {
          returnElem = (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={[AtomicCSS.marginTop12, AtomicCSS.paddingRight16, AtomicCSS.paddingLeft16]}
            >
              {item.item.cards.map((card, index) => (
                <HolidayMagicStrip
                  compact
                  fixedWidth
                  key={index}
                  {...card}
                  onMagicStripClicked={() => card.clickUrl && this.props.onMagicStripClicked(card.clickUrl, card.type)}
                />
                ))}
            </ScrollView>
          );
        } else {
          returnElem = null;
        }
        break;
      default:
        returnElem = null;
    }
    return returnElem;
  };
}

DetailPersuasion.propTypes = {
  item: PropTypes.object.isRequired,
  togglePopup: PropTypes.func.isRequired,
  onMagicStripClicked: PropTypes.func,
};

const detailPersuasionStyle = {
  paddingTop: 14,
  paddingBottom: 14,
  backgroundColor: '#fff',
  marginBottom: 0,
};

const datePersuasionStyle = {
  marginBottom: 14,
};

export default DetailPersuasion;

