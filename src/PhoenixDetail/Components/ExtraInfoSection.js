import {Text, View, StyleSheet} from 'react-native';
import React from 'react';
import PropTypes from 'prop-types';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {removeHTMLTags, unescapeHTML} from '../../utils/HolidayUtils';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { capitalizeText } from '../../utils/textTransformUtil';
class ExtraInfoSection extends React.Component {
  render() {
    return (
      <View style={AtomicCss.marginBottom20}>
        <Text style={styles.generalInfoHeading}>{capitalizeText(this.props.title)}</Text>
        {this.props.items.map((item, index) => (
          item && item !== '' && removeHTMLTags(unescapeHTML(item)) !== '' ?
            <View key={index} style={[AtomicCss.flexRow]}>
              <View style={styles.Bullet}/>
              <Text style={styles.generalInfo}>
                {removeHTMLTags(unescapeHTML(item))}
              </Text>
            </View> : []
        ))}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  Bullet: {
    height: 6,
    width: 6,
    borderRadius: 8,
    backgroundColor: holidayColors.lightGray,
    marginRight: 11,
    ...marginStyles.mt6,
  },
  generalInfoHeading: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
    marginBottom: 10,
  },
  generalInfo: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    lineHeight: 19,
    flex: 1,
  },
});

ExtraInfoSection.propTypes = {
  items: PropTypes.array.isRequired,
  title: PropTypes.string.isRequired,
};

export default ExtraInfoSection;
