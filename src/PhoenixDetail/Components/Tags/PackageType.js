import React, {useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, Image, View} from 'react-native';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import BottomSheetOverlay from '../../../Common/Components/BottomSheetOverlay';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const PackageType = ({ packageType, trackLocalClickEvent }) => {
  const [modalVisibility, setModalVisibility] = useState(false);

  const openPackageTypeBottomSheet = () => {
    trackLocalClickEvent('read_type_', `${packageType.title}`);
    setModalVisibility(true);
  };

  if (!packageType) {
    return [];
  }

  const { imageUrl = '', title, highlights = [] } = packageType;

  return (
      <View>
        <TouchableOpacity style={styles.container} onPress={openPackageTypeBottomSheet}>
          <Image style={styles.icon} source={{ uri: imageUrl }} />
          <Text style={styles.text}>{title}</Text>
        </TouchableOpacity>
        {modalVisibility && highlights.length > 0 && (
            <BottomSheetOverlay
                title={title}
                isCloseBtnVisible
                toggleModal={() => setModalVisibility(!modalVisibility)}
                visible={modalVisibility && highlights.length > 0}
                containerStyles={styles.containerStyles}
            >
              <PackageTypeOverlay packageType={packageType} />
            </BottomSheetOverlay>
        )}
      </View>
  );
};


const PackageTypeOverlay = ({packageType}) => {
  const {highlights} = packageType;
  const packageSubHeading = `The package you've selected is a ${packageType.title}, here are some highlights.`;
  return (
    <View style={styles.overlayContainer}>
    <Text style={styles.overlaySubHeading}>
      {packageSubHeading}
    </Text>
    {highlights &&
      highlights.length > 0 &&
      highlights.map((item, index) => (
        <View style={styles.itemContainer}>
          <Image
            source={require('@mmt/legacy-assets/src/green_tick.webp')}
            style={styles.itemIcon}
          />
          <Text style={styles.overlayItem}>{item}</Text>
        </View>
      ))}
  </View>
  );
};

const styles = StyleSheet.create({
  containerStyles: {
    ...paddingStyles.pa16,
  },
  container: {
    height: 22,
    flexDirection: 'row',
    backgroundColor: '#1f1c2c',
    borderRadius: 3,
    paddingHorizontal: 8,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.white,
    marginTop:-2,
  },
  icon: {
    width: 20,
    height: 20,
    marginRight: 4,
  },
  overlayContainer: {
    justifyContent: 'center',
  },
  overlaySubHeading: {
    ...fontStyles.labelMediumRegular,
    color: holidayColors.gray,
    lineHeight: 22,
    marginBottom: 20,
  },
  itemIcon: {
    height: 12,
    width: 12,
    marginRight: 6,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 8,
  },
  overlayItem: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
});

export default PackageType;
