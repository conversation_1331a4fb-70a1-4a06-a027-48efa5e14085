import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  Animated,
  Easing, StatusBar,
  Image, Platform, TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import LinearGradient from 'react-native-linear-gradient';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {
  animateOverlay,
  fetchPricePersDateText,
  findPricePersuasion,
} from '../Utils/HolidayDetailUtils';
import {rupeeFormatter} from '@mmt/legacy-commons/Helpers/currencyUtils';
import { holidayColors } from '../../Styles/holidayColors';

const iconCalendar = require('@mmt/legacy-assets/src/iconCalendar.webp');

class PricePersuasionOverlay extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      overlayPosition: new Animated.Value(0),
    };
  }

  static navigationOptions = {header: null};

  componentDidMount() {
    animateOverlay(this, 400);
  }

  startAnimate(bottom, duration, delay) {
    Animated.timing(this.state.overlayPosition, {
      toValue: bottom,
      easing: Easing.easeInOut,
      duration,
      delay,
    })
      .start();
  }

  render() {
    const {
      togglePopup, persuasionData, departureDate, updateDepDate,
    } = this.props;
    const {cheaperDate, priceDifference} = findPricePersuasion(persuasionData);
    return (
      <View style={styles.overlayContainer}>
        <TouchableOpacity onPress={() => togglePopup('')} style={styles.overlayBg}/>
        <Animated.View style={[styles.overlayContent, {bottom: this.state.overlayPosition}]}>
          <View>
            <Image style={styles.iconCal} source={iconCalendar}/>
            <Text style={[styles.subHeading, styles.textGrey]}>Flexible with dates?</Text>
            <Text style={styles.heading}>
              Start your jouney on
              <Text style={[styles.textGreen, styles.bold]}>
                {` ${fetchPricePersDateText(cheaperDate)} `}
              </Text>
              instead of {` ${fetchPricePersDateText(departureDate)}`} and save additional
              <Text style={[styles.textGreen, styles.bold]}>
                {(priceDifference && priceDifference > 0) ?
                  ` ₹ ${rupeeFormatter(priceDifference)}!` : ' money!'}
              </Text>
            </Text>
            <Text style={styles.subHeading}>Why drop in price? </Text>
            <Text style={styles.priceInfo}> Package pricing depends on the collective cost of
              flights, hotels, &amp; other inclusions. The price of your package is reduced due to
              drop in flight rates on that date.
            </Text>
          </View>
          <View style={styles.btnSection}>
            <TouchableOpacity activeOpacity={0.7} onPress={() => togglePopup('')}>
              <Text style={styles.btn}>NO THANKS</Text>
            </TouchableOpacity>
            <TouchableOpacity
              activeOpacity={0.7}
              style={AtomicCss.pushRight}
              onPress={() => updateDepDate(cheaperDate)}
            >
              <LinearGradient
                start={{
                  x: 1.0,
                  y: 0.0,
                }}
                end={{
                  x: 0.0,
                  y: 1.0,
                }}
                colors={['#065af3', '#53b2fe']}
                style={styles.CapsuleBtnFill}
              >
                <Text style={styles.btnText}>CHANGE DATES</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  overlayContainer: {
    justifyContent: 'flex-end',
    position: 'absolute',
    flex: 1,
    height: '100%',
    width: '100%',
    zIndex: 12,
    elevation: 12,
  },
  overlayBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 3,
    elevation: 3,
  },
  overlayContent: {
    backgroundColor: '#fff',
    paddingVertical: 25,
    paddingHorizontal: 30,
    paddingBottom: 35,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 4,
    zIndex: 4,
    position: 'absolute',
    bottom: 0,
    marginBottom: -400,
    width: '100%',
    shadowOffset: {
      width: 1,
      height: 0,
    },
  },
  iconCal: {
    height: 100,
    width: 100,
    marginLeft: -15,
  },
  textGrey: {
    color: '#9b9b9b',
  },
  heading: {
    fontSize: 22,
    marginBottom: 20,
    color: '#000',
    fontFamily: 'Lato-Regular',
    lineHeight: 27,
  },
  textGreen: {
    color: '#1a7971',
  },
  subHeading: {
    fontSize: 14,
    fontFamily: 'Lato-Bold',
    color: '#4a4a4a',
    marginBottom: 4,
  },
  priceInfo: {
    fontSize: 14,
    fontFamily: 'Lato-Regular',
    color: '#9b9b9b',
    marginBottom: 55,
    lineHeight: 17,
  },
  bold: {
    fontFamily: 'Lato-Bold',
  },
  btn: {
    fontFamily: 'Lato-Regular',
    color: '#008cff',
    fontSize: 16,
  },
  btnText: {
    color: '#fff',
    fontSize: 16,
    textAlign: 'center',
    fontFamily: 'Lato-Bold',
    ...Platform.select({
      ios: {
        backgroundColor: holidayColors.transparent,
      },
    }),
  },
  CapsuleBtnFill: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 22,
    height: 44,
    width: 188,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    elevation: 4,
    zIndex: 4,
  },
  btnSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

PricePersuasionOverlay.propTypes = {
  persuasionData: PropTypes.array.isRequired,
  togglePopup: PropTypes.func.isRequired,
  departureDate: PropTypes.string.isRequired,
  updateDepDate: PropTypes.func.isRequired,
};
export default PricePersuasionOverlay;
