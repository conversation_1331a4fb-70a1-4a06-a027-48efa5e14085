import React, { useState, useEffect } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, Image } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import CloseIcon from '@mmt/legacy-assets/src/ic_cross__gray.webp';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { paddingStyles } from '../../Styles/Spacing';

const Child = ({ unmountMe, type, message, center, hideClose }) => {
  const statusStyles =
    type === 'error'
      ? {
          containerStyle: styles.containerError,
          textColor: styles.textError,
          iconStyle: styles.closeIconError,
        }
      : type === 'success'
      ? {
          containerStyle: styles.containerSuccess,
          textColor: styles.textSuccess,
          iconStyle: styles.closeIconSuccess,
        }
      : {};
  return (
    <View style={[styles.container, statusStyles.containerStyle]}>
      <Text style={[styles.textMessage, center && AtomicCss.alignCenter, statusStyles.textColor]}>
        {message}
      </Text>
      {!hideClose ? (
        <TouchableOpacity onPress={() => unmountMe()} style={styles.closeBtn}>
          <Image
            source={CloseIcon}
            style={[
              styles.closeIcon,
              statusStyles.iconStyle,
            ]}
          />
        </TouchableOpacity>
      ) : null}
    </View>
  );
};
const NotificationMessage = (props) => {
  const [renderChild, handleChildUnmount] = useState(true);
  useEffect(() => {
    setTimeout(() => handleChildUnmount(false),  props.timer || 10000);
  });
  return renderChild ? <Child {...props} unmountMe={() => handleChildUnmount(false)} /> : null;
};
const styles = StyleSheet.create({
  container: {
    ...paddingStyles.pa16,

    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  containerError: {
    backgroundColor: holidayColors.fadedRed,
  },
  containerSuccess: {
    backgroundColor: holidayColors.fadedGreen,
  },
  textMessage: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    flex: 1,
  },
  closeBtn: {
    marginTop: 5,
  },
  closeIcon: {
    height: 8,
    width: 8,
  },
  closeIconError: {
    tintColor: holidayColors.red,
  },
  closeIconSuccess: {
    tintColor: holidayColors.green,
  },
  textError: {
    color: holidayColors.red,
  },
  textSuccess: {
    color: holidayColors.green,
  },
});
export default NotificationMessage;
