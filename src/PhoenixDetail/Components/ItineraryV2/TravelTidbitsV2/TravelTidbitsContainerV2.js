import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import {
  HOLIDAYS_ACTIVITY_OVERLAY_DETAIL,
  HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
  VIEW_STATE,
} from '../../../Utils/PheonixDetailPageConstants';
import TravelTidbits from './TravelTidbits';
import TidbitsErrorView from './TidbitsErrorView';
import TidbitsProgressView from './TidbitsProgressView';
import {
  attachVersionInRequest,
  fetchActivityFilterV2,
  fetchActivityListingResponseForTidbitsV2,
} from '../../../../utils/HolidayNetworkUtils';
import {
  createBlackStripData,
  createStaySeqData,
  getPreSelectedActivityWithRecheckKeyFromDetailResponse,
  getProductType,
  getSelectedActivitiesData,
  getSelectedActivitiesPayload,
  logAndTrackUpdatePress,
  updateActivityWithNewRecheckKey,
  updateSelectedActivitiesInState,
  validateResponses,
  cleanProductMetadata,
} from './TidBitsUtils';
import { DEFAULT_SELECTED_STAY_SEQUENCE } from '../../ActivityOverlay/ActivityListing/ActivityListingPageNew';
import { componentImageTypes, packageActions } from '../../../DetailConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { PAGE_TYPES_FOR_API } from '../../../../HolidayConstants';
import { getPackagePrice } from '../../../Utils/ActivityOverlayUtils';
import { connect } from 'react-redux';
import {
  resetPageState,
  setBlackStripData,
  setHolidayActivitiesListingState,
  setSelectedActivitiesStripData,
} from '../../../Actions/HolidayTidbitsAction';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { debounce, isEmpty } from 'lodash';

/**
 * TravelTidbitsContainerV2 component.
 * This component is responsible for managing the state and behavior of the TravelTidbits feature.
 * It fetches data, handles user interactions, and renders the appropriate subcomponents based on the current state.
 *
 * @param {Object} props - The properties passed to the component.
 */
const defaultFilter = {
  activity: { filter: [], sorter: [] },
  transfer: { filter: [], sorter: [] },
  meal: { filter: [], sorter: [] },
};

const typeToKey = {
  ACTIVITY: 'activity',
  TRANSFER: 'transfer',
  MEAL: 'meal',
};

// Helper to create initial cursor state - optimized version
const INITIAL_CURSOR_STATE = {
  activity: { cursor: {} },
  transfer: { cursor: {} },
  meal: { cursor: {} },
};

const TravelTidbitsContainerV2 = (props) => {
  const {
    dynamicId,
    day,
    activityReqParams,
    pricingDetail,
    packageDetailDTO,
    roomDetails,
    onComponentChange,
    lastPage,
    currentActivePlanOnItinerary,
    activityProductType = '',
    viewState,
    setViewState,
    resetPageState,
    setBlackStripData,
    setSelectedActivitiesStripData,
    blackStripData,
  } = props;

  const [response, setResponse] = useState(null);
  //const [childLoader, setChildLoader] = useState(VIEW_STATE.LOADING);

  const [tabIndex, setTabIndex] = useState(0);
  const preSelectedActivities = getPreSelectedActivityWithRecheckKeyFromDetailResponse(activityReqParams);
  const [selectedActivities, setSelectedActivities] = useState(preSelectedActivities);

  // Replace single pagination state with per-tab pagination state
  const [paginationLoadingStates, setPaginationLoadingStates] = useState({
    ACTIVITY: false,
    TRANSFER: false,
    MEAL: false,
  });

  // Add search loading state per tab
  const [searchLoadingStates, setSearchLoadingStates] = useState({
    ACTIVITY: false,
    TRANSFER: false,
    MEAL: false,
  });

  const staySequences = createStaySeqData(activityReqParams);
  const [activeDaySequence, setActiveDaySequence] = useState(staySequences ? staySequences[0].staySequence : DEFAULT_SELECTED_STAY_SEQUENCE);
  const productType = activityProductType ? [activityProductType] : getProductType(currentActivePlanOnItinerary);
  const [filterListData, setFilterListData] = useState();
  const [appliedFilters, setAppliedFilters] = useState(defaultFilter);
  // Store search text per tab (activity, transfer, meal)
  const [searchedText, setSearchedText] = useState({
    activity: '',
    transfer: '',
    meal: '',
  });
  const [activeTab, setActiveTab] = useState(productType);
  const [cursorData, setCursorData] = useState(INITIAL_CURSOR_STATE);

  useEffect(() => () => resetPageState(), [resetPageState]);



  // Helper function to get current tab's pagination state
  const getCurrentTabPaginationState = () => {
    return paginationLoadingStates[activeTab[0]] || false;
  };

  // Helper function to get current tab's search text
  const getCurrentTabSearchText = () => {
    const currentTabKey = typeToKey[activeTab[0]];
    const currentSearchText = searchedText[currentTabKey] || '';
    return currentSearchText;
  };

  // Helper function to update specific tab's pagination state
  const updateTabPaginationState = (tabType, isLoading) => {
    setPaginationLoadingStates(prev => ({
      ...prev,
      [tabType]: isLoading,
    }));
  };

  // Helper function to get current tab's search loading state
  const getCurrentTabSearchLoadingState = () => {
    return searchLoadingStates[activeTab[0]] || false;
  };

  // Helper function to update specific tab's search loading state
  const updateTabSearchLoadingState = (tabType, isLoading) => {
    setSearchLoadingStates(prev => ({
      ...prev,
      [tabType]: isLoading,
    }));
  };

  const selectActivity = (activityCode, reCheckKey, name, acmeType, acmeSubType) => {
    // Reset all pagination states on user interaction
    setPaginationLoadingStates({
      ACTIVITY: false,
      TRANSFER: false,
      MEAL: false,
    });
    updateSelectedActivitiesInState(name, activityCode, reCheckKey, acmeType, acmeSubType, setSelectedActivities);
  };

  const removeActivity = activityCode => {
    setSelectedActivities((currentCodesWithRecheckKey) =>
      currentCodesWithRecheckKey.filter((codeWithRecheckKey) => codeWithRecheckKey.code !== activityCode),
    );
  };


  // Effect hook for updating the pricing data when the selected activities change.
  useEffect(() => {
    const blackStripData = createBlackStripData(response, staySequences, activeDaySequence , pricingDetail, selectedActivities , preSelectedActivities, day);
    const selectedActivitiesData = getSelectedActivitiesData(response, activeDaySequence, selectedActivities, preSelectedActivities);
    setBlackStripData(blackStripData);
    setSelectedActivitiesStripData(selectedActivitiesData);
  }, [response, selectedActivities, activeDaySequence]);

  const createActionData = (selectedActivities) => {
    return {
      action: packageActions.MODIFY,
      dynamicPackageId: dynamicId,
      selectedActivities,
      day,
    };
  };

  const handleComponentChange = (actionData) =>
    onComponentChange(actionData, componentImageTypes.ACTIVITY);

  const navigateToLastPage = () => HolidayNavigation.navigate(lastPage);

  /* parametes are only needed when function called activity detail page */
  const onUpdatePress = ({ recheckKey = '', code = '', name = '', acmeType = '', acmeSubType = '' } = {}) => {
    const updatedSelectedActivities = [...selectedActivities, { code, recheckKey }].filter(Boolean);
    const selectedActivitiesPayload = getSelectedActivitiesPayload( response, updatedSelectedActivities, staySequences );

    if (code && recheckKey) {
      updateActivityWithNewRecheckKey(selectedActivitiesPayload, code, recheckKey, name, acmeType, acmeSubType);
    }

    const actionData = createActionData(selectedActivitiesPayload);
    handleComponentChange(actionData);
    navigateToLastPage();
    logAndTrackUpdatePress(blackStripData, staySequences);
  };

  const captureClickEvents = ({ eventName = '', suffix = '', prop1 = '' }) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value : value.replace(/_/g , '|'),
      subPageName: prop1 || HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: prop1 || HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
    });

  };

 const modifyActivityDetail = async (add, activityCode, recheckKey, name) => {
  if (add) {
    selectActivity(activityCode, recheckKey, name);
  } else {
    removeActivity(activityCode);
  }

  captureClickEvents({
    eventName: add ? 'add_' : 'remove_',
    suffix: activityCode,
    prop1: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL,
  });
};

  const openActivityDetailPage = ({ code, safe, openSlotOverlay = false, scrollToRatePlan = false, activityType = '', city = '' } = {}) => {
    const activity = selectedActivities.find((activity) => activity.code === code);
    let selected = false;
    if (activity) {
      selected = !activity;
    }

    captureClickEvents({
      eventName: openSlotOverlay ? 'ChangetimeSlot_' : scrollToRatePlan ?  'MoreOptions_' : 'view_',
      suffix: `${activityType || 'ACTIVITY'}_${city}_${day}`,
    });

    const navigationParams = {
      blackStripData: {
        ...blackStripData,
        addonPrice: pricingDetail?.categoryPrices?.[0]?.addonsPrice ?? 0,
      },
      packagePrice: getPackagePrice(pricingDetail),
      dynamicPackageId: dynamicId,
      staySequence: activeDaySequence,
      day,
      activityCode: code,
      selectedRecheckKey: activity?.recheckKey,
      modifyActivityDetail: modifyActivityDetail,
      modifyActivityTrue: true,
      onUpdatePress: onUpdatePress,
      selected,
      subtitleData: props.subtitleData,
      branch: props.branch,
      packageDetailDTO: packageDetailDTO,
      roomDetails: roomDetails,
      setActivityList: ()=> {},
      openSlotOverlay,
      scrollToRatePlan,
    };
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_V2, navigationParams);
  };

 /* const updateAPIStatus = (status)=>{
    if (activeTab.length === productType.length && !loadMoreRef.current){
      setViewState(status);
      setChildLoader(VIEW_STATE.SUCCESS);
    }
    else if (!loadMoreRef.current){
      setChildLoader(status);
    }
  };*/
  function areAllListingActivitiesEmpty(responses) {
    if (!Array.isArray(responses)) {return false;}
    return responses.every(
      node => Array.isArray(node?.listingActivities) && node.listingActivities.length === 0
    );
  }

    const fetchData = async () => {
    // Only show main loading view if not pagination loading AND not search loading
    const isPaginationLoading = getCurrentTabPaginationState();
    const isSearchLoading = getCurrentTabSearchLoadingState();

    if (!isPaginationLoading && !isSearchLoading) {
      setViewState(VIEW_STATE.LOADING);
    }

    try {
      const responses = await fetchResponses();
      if (!validateResponses(responses) || areAllListingActivitiesEmpty(responses)) {
        setViewState(VIEW_STATE.ERROR);
        return;
      }
      updateResponses(responses);
      setViewState(VIEW_STATE.SUCCESS);
    } catch (error) {
      setViewState(VIEW_STATE.ERROR);
    }
  };

  useEffect(() => {
    fetchData();
  }, [day, selectedActivities, appliedFilters]);

  // Update search text for specific tab - each tab maintains its own search state
  const updateSearchText = debounce((filteredData, type, text) => {
    let fData = filteredData;
    const MIN_SEARCH_TEXT_LENGTH = 3;
    const processedText = text?.trim() || '';
    const tabKey = typeToKey[type];

    // Always update search text for the specific tab (activity, transfer, or meal)
    setSearchedText(prev => ({
      ...prev,
      [tabKey]: processedText,
    }));

    if (!isEmpty(processedText) && processedText.length >= MIN_SEARCH_TEXT_LENGTH) {
      // Set search loading state to true when starting search
      updateTabSearchLoadingState(type, true);
      fData = { ...filteredData, searchText: processedText };
      applyFilter(fData, type, false);
    } else if (processedText.length === 0) {
      // Clear search - keep loading state active until API response
      updateTabSearchLoadingState(type, true); // Keep loading state active
      fData = { ...filteredData, searchText: '' };
      applyFilter(fData, type, false);
    }
  }, 700);

  // Function for fetching the responses for all stay sequences.
  const fetchResponses = async () => {
    return Promise.all(staySequences.map(fetchSequenceResponse));
  };
  const createFetchPayload = (sequence) => {
    const { staySequence } = sequence || {};

    const isPaginationActive = Object.values(paginationLoadingStates).some(Boolean);

    // Optimized productMetadata creation - exclude empty searchText fields
    const productMetadata = isPaginationActive
      ? cleanProductMetadata(Object.entries(appliedFilters).reduce((acc, [key, value]) => {
          acc[key] = { ...value, ...cursorData[key] };
          return acc;
        }, {}))
      : cleanProductMetadata(appliedFilters);

    const payload = {
      dynamicPackageId: dynamicId,
      staySequence,
      day: day,
      //@todo ASHISH remove this once monika change's is live
      selectedActivities:selectedActivities.map((activity) => {
        return {
          code: activity.code,
          recheckKey: activity.recheckKey,
        };
      }),
      activityCodes: selectedActivities.map((activity) => {
        return activity.code;
      }),
      v2: true,
      productType:activeTab,
      isListingV2: true,
      productMetadata,
    };

    return attachVersionInRequest(payload, PAGE_TYPES_FOR_API.DETAIL);
  };

  const fetchSequenceResponse = async (sequence) => {
    const payload = createFetchPayload(sequence);
    const responseBody = await fetchActivityListingResponseForTidbitsV2(payload);
    const activeType = {
      'ACTIVITY': 'activityListingData',
      'TRANSFER': 'transferListingData',
      'MEAL': 'mealListingData',
    };
    const responseKey = activeType[activeTab[0]];
    const currentResponse = response?.[sequence.staySequence]?.[responseKey];

    if (currentResponse) {
      const updatedResponse = {
        ...response[sequence.staySequence],
        [responseKey]: {
          ...responseBody[responseKey],
          listingActivities: getCurrentTabPaginationState()
            ? [
                ...currentResponse.listingActivities,
                ...responseBody[responseKey].listingActivities,
              ]
            : responseBody[responseKey].listingActivities,
            packagePriceMap: {
              ...currentResponse.packagePriceMap,
              ...responseBody[responseKey].packagePriceMap,
            },
            discountedFactor:responseBody[responseKey].discountedFactor || currentResponse?.discountedFactor,
        },
      };

      // Optimized cursor update for current tab only
      const cursorKey = typeToKey[activeTab[0]];
      const nextCursor = responseBody[responseKey].nextCursor;
      if (nextCursor) {
        setCursorData(prev => ({
          ...prev,
          [cursorKey]: { cursor: nextCursor },
        }));
      }

      updateTabPaginationState(activeTab[0], false);
      // Clear search loading state when response is received
      updateTabSearchLoadingState(activeTab[0], false);
      return [sequence.staySequence, updatedResponse];
    }

    // Optimized cursor data update for all types
    const newCursorData = {};
    Object.entries(typeToKey).forEach(([type, key]) => {
      const dataKey = `${key}ListingData`;
      const nextCursor = responseBody?.[dataKey]?.nextCursor;
      if (nextCursor) {
        newCursorData[key] = { cursor: nextCursor };
      }
    });

    if (Object.keys(newCursorData).length > 0) {
      setCursorData(prev => ({ ...prev, ...newCursorData }));
    }

    // Clear search loading state when response is received (for initial load)
    updateTabSearchLoadingState(activeTab[0], false);
    return [sequence.staySequence, responseBody];
  };

  const updateResponses = (responses) => {
    const responseMap = Object.fromEntries(responses);
    setResponse(responseMap);
  };
  const applyFilter = (appliedFilterData, type ,loadMore = false) => {
    updateTabPaginationState(type, loadMore);
    const key = typeToKey[type];
    if (key) {
      setAppliedFilters((prev)=>{
        return {
        ...prev,
          [key]: appliedFilterData,
        };
      });
      const hasSearchText = appliedFilterData.hasOwnProperty('searchText');
      // Update searchedText state when searchText is provided in filter data
      // This ensures the ActivityPageHeader's searchText stays in sync
      if (hasSearchText) {
        setSearchedText(prev => ({
          ...prev,
          [key]: appliedFilterData.searchText || '',
        }));
      }
    }
  };

const getActivityFilterData = async() => {
  const resObj = {
    dynamicPackageId: dynamicId,
    day: day,
    staySequence: activeDaySequence,
    cityCode:staySequences[0]?.data.cityCode,
  };
 const response = await fetchActivityFilterV2(resObj);
 if (response.success){
 setFilterListData(response);
 }

};
  useEffect(() => {
    getActivityFilterData();
    setActiveTab([productType[tabIndex]]);
  }, []);

  const onEndReached = (filterData, type) => {
    setActiveTab([type]);
    if (paginationLoadingStates[type]) {
      return;
    }
    applyFilter(filterData, type, true);
  };

  const updateActiveTab = (type) => {
    // Clear search loading state for the new tab when just switching tabs
    // This prevents stuck loading states from previous search operations
    if (searchLoadingStates[type]) {
      updateTabSearchLoadingState(type, false);
    }

    // Don't reset pagination state when switching tabs - let background loading continue
    setActiveTab([type]);
  };

  return (
    <View style={{ flex: 1 }}>
      {viewState === VIEW_STATE.SUCCESS  && (
        <TravelTidbits
          {...props}
          activeDaySequence={activeDaySequence}
          response={response}
          setActiveDaySequence={setActiveDaySequence}
          selectActivity={selectActivity}
          removeActivity={removeActivity}
          onUpdatePress={onUpdatePress}
          selectedActivities={selectedActivities}
          preSelectedActivities={preSelectedActivities}
          blackStripData={blackStripData}
          openActivityDetailPage={openActivityDetailPage}
          tabIndex={tabIndex}
          setTabIndex={setTabIndex}
          filterListData={filterListData || []}
          appliedFilters={appliedFilters}
          applyFilter={applyFilter}
          onEndReached={onEndReached}
          updateActiveTab={updateActiveTab}
          searchedText={getCurrentTabSearchText()}
          updateSearchText={updateSearchText}
          isLoadMore={getCurrentTabPaginationState()}
          isSearchLoading={getCurrentTabSearchLoadingState()}
         // childLoader={childLoader}
          productType={activeTab}
        />
      )}
      {viewState === VIEW_STATE.ERROR && (
        <TidbitsErrorView
          productType={productType}
          departureDetail={props?.departureDetail || {}}
          day={day}
        />
      )}
      {viewState === VIEW_STATE.LOADING && !getCurrentTabPaginationState() && !getCurrentTabSearchLoadingState() && <TidbitsProgressView />}
    </View>
  );
};

const mapDispatchToProps = (dispatch) => {
  return {
    setViewState: state => dispatch(setHolidayActivitiesListingState(state)),
    setBlackStripData: data => dispatch(setBlackStripData(data)),
    resetPageState: () => dispatch(resetPageState()),
    setSelectedActivitiesStripData : data => dispatch(setSelectedActivitiesStripData(data)),
  };
};
const  mapStateToProps = state => {
  const { viewState, blackStripData } = state.travelTidbitsReducer;
  return { viewState, blackStripData };
};

export default connect(mapStateToProps, mapDispatchToProps)(TravelTidbitsContainerV2);
