import React, { useEffect, useRef, useState } from 'react';
import { Animated, StyleSheet, Text, TouchableOpacity, View, Platform } from 'react-native';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { borderRadiusValues } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import Divider from '../../../../Common/Components/Divider';
import RadioGroupButton from '../../../../Common/Components/RadioButtonGroup';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../../Common/Components/HolidayImageUrls';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';

const CitySelectionPopup = (props) => {
  const { staySequences, activeDaySequence, onChangeCity, selectCityPopupVisibility, headingText = 'Select City', hideSelectCityPopupVisibility } = props || {};
  const ANIMATION_DURATION = 200;
  const BACKGROUND_TRANSPARENCY = 0.3;
  const [renderComponent, setRenderComponent] = useState(false);
  const defaultSelectedSequenceIndex = staySequences.find(ss => ss.staySequence === activeDaySequence);
  const [selectedDaySequence, setSelectedDaySequence] = useState(defaultSelectedSequenceIndex?.staySequence);
  const slideAnim = useRef(new Animated.Value(ANIMATION_DURATION)).current;
  const backgroundColorAnim = useRef(new Animated.Value(selectCityPopupVisibility ? BACKGROUND_TRANSPARENCY : 0)).current;
  const onclick = (id) => id !== selectedDaySequence && setSelectedDaySequence(id);

  const animateSequence = (slideToValue, backgroundColorToValue, callback) => {
    Animated.sequence([
      Animated.timing(slideAnim, {
        toValue: slideToValue,
        duration: ANIMATION_DURATION,
        useNativeDriver: false,
      }),
      Animated.timing(backgroundColorAnim, {
        toValue: backgroundColorToValue,
        duration: ANIMATION_DURATION,
        useNativeDriver: false,
      }),
    ]).start(callback);
  };

  const slideUp = () => {
    animateSequence(0, selectCityPopupVisibility ? BACKGROUND_TRANSPARENCY : 0);
  };

  const slideDown = () => {
    animateSequence(300, selectCityPopupVisibility ? BACKGROUND_TRANSPARENCY : 0, () => {
      setRenderComponent(false);
    });
  };

  const onApplyClicked = () => selectedDaySequence !== activeDaySequence && onChangeCity(selectedDaySequence);

  useEffect(() => {
    setRenderComponent(true);

    if (selectCityPopupVisibility) {
      slideUp();
    } else {
      slideDown();
    }
  }, [selectCityPopupVisibility]);

  const PopupContent = ({ headingText, onclick }) => {
    return (
      <View style={styles.popContent}>
        <View style={styles.header}>
          <TouchableOpacity onPress={hideSelectCityPopupVisibility}>
          <HolidayImageHolder imageUrl={getImageUrl(IMAGE_ICON_KEYS.CROSS_GREY)} style={styles.cross} />
          </TouchableOpacity>
            <Text style={styles.headingText}>{headingText}</Text>
        </View>
        <Divider style={styles.divider} />
        <View style={styles.cityContainer}>
          {staySequences.map(({ name, staySequence }, index) => (
            <RadioGroupButton
              optionText={name}
              key={index + name}
              selected={selectedDaySequence === staySequence}
              btnContainerStyles={styles.buttonContainer}
              handleClick={() => onclick(staySequence)}
            />
          ))}
        </View>
        <PrimaryButton
          buttonText={'APPLY'}
          handleClick={onApplyClicked}
          isDisabled={selectedDaySequence === activeDaySequence}
          btnContainerStyles={styles.updateButton}
        />
      </View>
    );
  };

  const interpolatedBackgroundColor = backgroundColorAnim.interpolate({
    inputRange: [0, BACKGROUND_TRANSPARENCY],
    outputRange: ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0.3)'],
  });

  const animatedStyles = {
    transform: [{ translateY: slideAnim }],
    backgroundColor: interpolatedBackgroundColor,
  };

  return (
    <>
      {renderComponent && (
        <Animated.View style={[styles.popOverlay, animatedStyles]}>
          <PopupContent headingText={headingText} onclick={onclick} />
        </Animated.View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  popOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'flex-end',
    zIndex: 5,
  },
  popContent: {
    backgroundColor: holidayColors.white,
    borderTopLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
    ...paddingStyles.pb10
  },
  headingText: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
  },
  updateButton: {
    ...marginStyles.ml20,
    ...marginStyles.mr20,
    borderRadius: borderRadiusValues.br8,
  },
  buttonContainer: {
    ...marginStyles.mb10,
  },
  cross: {
    width: 10,
    height: 10,
    tintColor: holidayColors.gray,
    padding: 10,
    ...marginStyles.mr10
  },
  header:{
    flexDirection: 'row',
    ...paddingStyles.ph16,
    ...paddingStyles.pv16,
  },
  cityContainer: {
    ...marginStyles.mt6,
    ...paddingStyles.ph16,
    ...paddingStyles.pv16,
  },

});

export default CitySelectionPopup;
