import React, { useState } from 'react';
import { FlatList, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import {
  getAllInclusions,
  getHotelData,
  getHotelObject,
  getIconForItineraryUnitType,
} from '../../../../Utils/PhoenixDetailUtils';
import { HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE } from '../../../../Utils/PheonixDetailPageConstants';
import { openChangeHotelFromPhoenixPage } from '../../../../Utils/HolidayDetailUtils';
import { itineraryUnitSubTypes, itineraryUnitTypes } from '../../../../DetailConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../../Navigation';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { actionStyle } from '../../../DayPlan/dayPlanStyles';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { marginStyles } from '../../../../../Styles/Spacing';
import { RESIZE_MODE_IMAGE } from '../../../../../HolidayConstants';
import { restructureInclusionData } from '../../ItineraryV2Utils';

/* Components */
import DayPlanRowHOC from '../../Common/DayPlanRowHOC';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import BottomSheet from '../../../BottomSheet/BottomSheet';
import UserRatingCommon from '../../../../../Common/Components/UserRatingCommon';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import { HotelBasicDetail, HotelImageComponent, HotelRatePlan } from './Components';
import BottomSheetOverlay from '../../../../../Common/Components/BottomSheetOverlay';
import ItineraryUnitInformation from '../../Common/ItineraryUnitInformation';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { holidayNavigationPush } from '../../../../Utils/DetailPageNavigationUtils';
import { Overlay } from '../../../DetailOverlays/OverlayConstants';

const MAX_INCLUSION_TO_SHOW = 2;

const HotelRow = ({
  failedHotels = [],
  day,
  city,
  bundled,
  hotelDetail,
  roomDetails,
  hideBorder = false,
  itineraryUnit,
  accessRestriction,
  onViewDetailPress,
  packageDetailDTO,
  onComponentChange,
  destinationName,
  lastPageName,
  detailData,
  fromPresales = false,
  packageDetail,
  defaultCollapsedState = '',
  viewDetailAccessRestriction = null,
  showOverlay,
  hideOverlays,
  fromPage = '',
}) => {
  const [modalVisibility, setModalVisibility] = useState(false);
  const { text, hotel, itineraryUnitType, itineraryUnitSubType, address = '' } = itineraryUnit;
  const hotelSellableId =
    hotel && hotel.hotelSellableId === undefined ? '' : hotel ? hotel.hotelSellableId : '';
  const changeHotelRestricted = accessRestriction ? accessRestriction.changeHotelRestricted : false;
  const hotelObject = getHotelObject(hotelDetail, hotelSellableId);
  const { roomTypes = [], cardHeaderTexts = [] } = hotelObject || {};
  const { roomInformation = {} } = roomTypes?.[0] || {};
  const { inclusions = [], inclusionsHighlighted = [] } = roomInformation || {};
  const inclusionList = getAllInclusions(inclusionsHighlighted, inclusions);
  let isHotelUnavailable = failedHotels?.some((hotel) => hotel.sellableId === hotelSellableId);

  const captureClickEvents = ({ eventName = '', value = '', suffix = ''}) => {
    const eventValue = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value: value || eventValue.replace(/_/g, '|'),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1: `base:${itineraryUnitTypes.HOTEL}`,
      suffix,
    })
  }

  const openHotelListing = (hotelObject) => {
    captureClickEvents({
      eventName: 'change_',
      suffix: `${itineraryUnitTypes.HOTEL}_${destinationName}_${day}`,
    });
    openChangeHotelFromPhoenixPage(
      hotelObject,
      packageDetailDTO,
      roomDetails,
      onComponentChange,
      lastPageName,
      '',
      isHotelUnavailable ? hotelSellableId : null,
      showOverlay,
      hideOverlays,
      detailData,
    );
  };

  const onViewAllInclusionsClicked = () => {
    setModalVisibility(true);
    captureClickEvents({
      eventName: 'View_All_Inclusions',
      value: 'View|All_Inclusions',
    });
  };

  const onViewDetail = () => {
    openHotelDetailPage(hotelObject, false);
  };

  const openHotelDetailPage = (hotelObject, changeRoomPress = false) => {
    changeRoomPress
      ? captureClickEvents({
          eventName: 'change_room_',
          suffix: `${day}_${destinationName}`,
        })
      : captureClickEvents({
          eventName: 'view_',
          suffix: `${itineraryUnitTypes.HOTEL}_${destinationName}_${day}`,
        });
    const navigationParams = {
        hotel: hotelObject,
        roomDetails,
        fromPage: HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.BASE_DETAIL_PAGE,
        onComponentChange,
        changeHotelRestricted,
        scrollToRoomType: changeRoomPress,

        /*This prop is required to handle a case when user lands from hotel listing to detail page by selecting a different
    hotel. The room type code of new hotel room will be compared with the room type code of preselected hotel in order to
    show update button on price bar. In case when user lands to this page from detail or overlay page, both the props
    hotel and preselected hotel can be same.*/
        preSelectedHotel: hotelObject,
        lastPageName: lastPageName,
        packageDetailDTO: packageDetailDTO,
        bundled: hotelObject?.bundled,
        failedHotelSellableId: isHotelUnavailable ? hotelSellableId : null,
        detailData: detailData,
      }

    holidayNavigationPush({
      props: navigationParams,
      pageKey: HOLIDAY_ROUTE_KEYS.HOTEL_DETAIL,
      overlayKey: Overlay.HOTEL_DETAIL_PAGE_FULL_SCREEN,
      showOverlay,
      hideOverlays,
      navigationFunction: HolidayNavigation.push,
    })
  };

  const showRenderFooter =
    !changeHotelRestricted &&
    (!viewDetailAccessRestriction || viewDetailAccessRestriction.viewDetailHotelRestriction);
  const renderHotelCard = () => {
    if (hotelObject) {
      const { ratePlanInfo = {} } = hotelObject || {};

      return (
        <View style={styles.hotelDetailContainer}>
          {/* zIndex has been added to put the premium tool tip on the top*/}
          <View style={[styles.hotelDetails, { zIndex: 2 }]}>
            <HotelBasicDetail hotelObject={hotelObject} />
            <HotelImageComponent hotelObject={hotelObject} />
          </View>
          {/*Don't Remove zIndex, else tooltip for premium hotels will break.*/}
          <View style={[showRenderFooter ? styles.footer : {}, { zIndex: 1 }]}>
            {!changeHotelRestricted && (
              <TouchableOpacity onPress={() => openHotelListing(hotelObject)}>
                <DynamicCoachMark
                  cueStepKey="changeOrRemove"
                  offsetHeight={70}
                  offsetWidth={70}
                  extraInfo={{ from: 'hotelRow', day }}
                >
                  <Text style={actionStyle}>Change Hotel</Text>
                </DynamicCoachMark>
              </TouchableOpacity>
            )}
            {/* ignore condition if viewDetailAccessRestriction does not exist else check value */}
            {!viewDetailAccessRestriction || !viewDetailAccessRestriction.hotelRestricted ? (
              <TouchableOpacity
                style={{ marginLeft: 'auto' }}
                disabled={isHotelUnavailable}
                onPress={onViewDetail}
              >
                <Text style={isHotelUnavailable ? styles.anchorTextUnavailable : actionStyle}>
                  {isHotelUnavailable ? 'Currently Unavailable' : 'View Details'}
                </Text>
              </TouchableOpacity>
            ) : null}
          </View>
          <HotelRatePlan
            fromPage={fromPage}
            hotelObject={hotelObject}
            openDetailPage={() => openHotelDetailPage(hotelObject, true)}
            isHotelUnavailable={isHotelUnavailable}
            changeHotelRestricted={changeHotelRestricted}
            onViewAllInclusionsClicked={onViewAllInclusionsClicked}
          />
          {modalVisibility && inclusionList?.length > 0 ? (
            <BottomSheetOverlay
              title={'Room Inclusions'}
              showCross
              toggleModal={() => setModalVisibility(!modalVisibility)}
              visible={modalVisibility}
              containerStyles={styles.containerStyles}
              headingContainerStyles={styles.headingContainerStyles}
            >
              <ItineraryUnitInformation info={restructureInclusionData({ data: inclusionList })} />
            </BottomSheetOverlay>
          ) : null}
        </View>
      );
    }
    return [];
  };


  return (
    <DayPlanRowHOC
      hideBorder={hideBorder}
      day={day}
      city={city}
      unit={itineraryUnit}
      cardHeaderTexts={
        itineraryUnitSubType === itineraryUnitSubTypes.CHECKOUT
          ? [{ text: 'HOTEL CHECKOUT', isEmphasized: true }, { text: address, isEmphasized: false }]
          : cardHeaderTexts
      }
      hideRowSeperator={itineraryUnitSubType === itineraryUnitSubTypes.CHECKOUT}
      defaultCollapsedState={defaultCollapsedState}
    >
      {renderHotelCard()}
    </DayPlanRowHOC>
  );
};
const styles = StyleSheet.create({
  hotelDetailContainer: {
    flex: 1,
  },
  hotelDetails: {
    flexDirection: 'row',
    flex: 1,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...marginStyles.mt12,
  },
  anchorTextUnavailable: {
    ...actionStyle,
    color: holidayColors.red,
  },
  containerStyles: {
    ...paddingStyles.pa16,
    ...paddingStyles.pb40,
  },
  headingContainerStyles: {
    ...marginStyles.mb12
  }
});

const inclusionStyles = StyleSheet.create({
  inclusionIcon: {
    width: 20,
    height: 20,
    ...marginStyles.mr10,
    ...marginStyles.mt6,
    tintColor: holidayColors.gray,
  },
  inclusions: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    marginTop: 5,
    marginRight: 15,
  },

  inclusionsContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default HotelRow;
