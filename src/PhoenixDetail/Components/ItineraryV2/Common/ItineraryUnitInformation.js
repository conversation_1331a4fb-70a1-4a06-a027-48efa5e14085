import React, { useState } from 'react';
import { isArray, isEmpty } from 'lodash';
import { Text, View, TouchableOpacity, StyleSheet, FlatList } from 'react-native';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import { RESIZE_MODE_IMAGE } from '../../../../HolidayConstants';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { DotSeperator } from './Seperators';
import { htmlTextBaseStyles } from '../dayPlanV2Styles';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

export const INFO_SIZE = {
  SMALL: 'SMALL',
  BASE: 'BASE',
};
export const INFO_SEPERATOR = {
  DOT: 'DOT',
  LINE_BREAK: 'LINE_BREAK',
};

const ItineraryUnitInformation = ({
  info = [],
  size = INFO_SIZE.SMALL,
  infoLimit = null,
  seperator = INFO_SEPERATOR.LINE_BREAK,
  clickEventParams = {}, //Expected { eventName }
}) => {
  if (info?.length === 0) {
    return [];
  }
  const [infoList, setInfoList] = useState(infoLimit ? info?.slice(0, infoLimit) : info);
  const [showReadMore, setShowReadMore] = useState(false);

  const renderInfoItem = ({ item, index }) => {
    const { iconUrl, texts = [], text = '', textColorCode = null } = item || {};
  //if there no text present in the info item, return empty array
    if(isEmpty(texts?.filter(String) || []) && !text) return []; 
    
    let textStyle = {};
    const iconStyles = {
      ...(size === INFO_SIZE.BASE && {
        width: 24,
        height: 20,
        ...marginStyles.mr4,
      }),
      ...(size === INFO_SIZE.SMALL && {
        width: 14,
        height: 14,
        ...marginStyles.mr4,
        ...marginStyles.mt4,
      }),
      ...(textColorCode && { tintColor: textColorCode }),
    };

    if (size === INFO_SIZE.BASE) {
      textStyle = {
        ...fontStyles.labelBaseRegular,
        color: textColorCode || holidayColors.gray,
        flex: 1,
      };
    } else {
      textStyle = {
        ...fontStyles.labelSmallRegular,
        color: textColorCode || holidayColors.gray,
        flex: 1,
      };
    }

    const renderInfoTextItems = ({ item, index }) => {
      return (
        <Text style={textStyle}>
          {item}
        </Text>
      );
    };

    return (
      <View style={styles.infoContainer}>
        {iconUrl ? (
          <HolidayImageHolder
            imageUrl={iconUrl}
            style={iconStyles}
            resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
          />
        ) : (
          <DotSeperator style={[marginStyles.mt8, marginStyles.mr6]} />
        )}
        <View style={{ flex: 1 }}>
          {isArray(texts) && texts?.length > 0 ? (
            seperator === INFO_SEPERATOR.DOT ? (
              <Text>
                {texts.map((text, index) => {
                  const isLastElement = texts.length - 1 === index;
                  return (
                    <Text
                      style={{
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      {renderInfoTextItems({ item: text, index })}
                      <View style={[marginStyles.mt4, paddingStyles.ph6]}>
                        {!isLastElement && <DotSeperator />}
                      </View>
                    </Text>
                  );
                })}
              </Text>
            ) : (
              <FlatList
                data={texts}
                renderItem={renderInfoTextItems}
                keyExtractor={(item, index) => `texts-${Math.random()}-${index}`}
              />
            )
          ) : (
            renderInfoTextItems({ item: text, index })
          )}
        </View>
      </View>
    );
  };

  const captureClickEvents = ({ eventName = '', prop1 = '' }) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
      subPageName: prop1,
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1,
    });
  };

  const handleReadMore = () => {
    setInfoList(info);
    setShowReadMore(true);
    if (!isEmpty(clickEventParams)) {
      const { component = '', sectionName = '', prop1 = ''} = clickEventParams || {};
      captureClickEvents({
        eventName : `readMore_${component}_${sectionName}`,
        prop1,
      })
    }
  };

  const handleReadLess = () => {
    setInfoList(infoLimit ? info?.slice(0, infoLimit) : info);
    setShowReadMore(false);
  };
  return (
    <View>
      <FlatList
        data={infoList}
        renderItem={renderInfoItem}
        keyExtractor={(item, index) => `texts-${index}`}
      />

      {infoLimit && info?.length > infoLimit && (
        <View style={paddingStyles.pl10}>
          {showReadMore ? (
            <>
              <TouchableOpacity onPress={handleReadLess} style={marginStyles.mt4}>
                <Text style={htmlTextBaseStyles.actionStyle}>Read Less</Text>
              </TouchableOpacity>
            </>
          ) : (
            <TouchableOpacity onPress={handleReadMore} style={marginStyles.mt4}>
              <Text style={htmlTextBaseStyles.actionStyle}>Read More</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );
};

export default ItineraryUnitInformation;

const styles = StyleSheet.create({
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    ...paddingStyles.pv2,
    flex: 1,
  },
  style: {
    flex: 1,
  },
  infoText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
});
