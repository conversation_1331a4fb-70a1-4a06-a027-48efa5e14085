import React, { useState } from 'react';
import { isEmpty } from 'lodash';
import { Text, View, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { RESIZE_MODE_IMAGE } from '../../../../HolidayConstants';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { sectionHeaderSpacing } from '../../../../Styles/holidaySpacing';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import {
  dayPlanHeaderBorder,
  dayPlanV2RowContainerStyle,
  dotSeperator,
  headerSeperator,
} from '../dayPlanV2Styles';
import { ITINERARY_COLLAPSED_STATE } from '../constants';

// ICONS
import DropDownIcon from '@mmt/legacy-assets/src/ic_arrow_dropdown_copy.webp';

/* Components */
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import { DashedSeperator } from './Seperators';
import DottedLine from 'mobile-holidays-react-native/src/Common/Components/DottedLine';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { getIconForItineraryUnitType } from '../../../Utils/PhoenixDetailUtils';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

export const COLLPASED_STATE = {
  [ITINERARY_COLLAPSED_STATE.COLLAPSIBLE_EXPANDED]: 1, // state will be expanded with icon to toggle
  [ITINERARY_COLLAPSED_STATE.COLLAPISBLE_COLLAPSED]: 0, // state will be closed with icon to toggle
  [ITINERARY_COLLAPSED_STATE.NON_COLLAPSIBLE]: 1, // state will be expanded without icon to toggle
};
const DayPlanRowHOC = ({
  unit = {},
  cardHeaderTexts = [],
  children,
  day = '',
  city = '',
  defaultCollapsedState = ITINERARY_COLLAPSED_STATE.COLLAPISBLE_COLLAPSED,
  cardSubHeader = null,
  renderHeaderDetails = () => {},
  isFromCommute = false,
  showCommuteBorder = false,
  hideBorder = false,
  hideRowSeperator = false,
}) => {
  const { itineraryUnitType = '', itineraryUnitSubType = '' } = unit || {};
  const imageIcon =
    itineraryUnitType
      ? getIconForItineraryUnitType(itineraryUnitType, itineraryUnitSubType)
      : '';
  const [expanded, setExpanded] = useState(COLLPASED_STATE[defaultCollapsedState]);
  const isCollapsible = defaultCollapsedState !== ITINERARY_COLLAPSED_STATE.NON_COLLAPSIBLE;
  const captureClickEvents = ({ eventName = '', value = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value,
    })
    trackPhoenixDetailLocalClickEvent({eventName});
  }
  const handleCollapseClick = () => {
    const expantState = expanded ? 'Collapse' : 'Expand' ;
      captureClickEvents({
        eventName: `${expantState}_${itineraryUnitType}_${day}_${city}`,
        value: `${expantState}|${itineraryUnitType}|${day}|${city}`,
      });
    setExpanded(!expanded);
  };

  const renderCollapseIcon = () => {
    const arrowStyles = {
      ...styles.arrowIcon,
      ...(expanded ? styles.upIcon : styles.downIcon),
    };
    return (
      isCollapsible && (
        <TouchableOpacity
          style={styles.arrowIconContainer}
          activeOpacity={0.7}
          onPress={handleCollapseClick}
        >
          <HolidayImageHolder
            defaultImage={DropDownIcon}
            style={arrowStyles}
            resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
          />
        </TouchableOpacity>
      )
    );
  };

  const renderCardTextItem = ({ item, index }) => {
    const { text = '' } = item;
    if (isEmpty(text)) {
      return null;
    }
    return (
      <View style={styles.rowHeaderTextContainer}>
        <Text style={item?.isEmphasized ? styles.rowHeaderTextBold : styles.rowHeaderText}>
          {item.text}
        </Text>
      </View>
    );
  };

  const renderCommuteBorder = () => {
    return (
      showCommuteBorder && (
        <View
          style={{
            width: 0.5,
            position: 'asbolute',
            left: 20,
            top: '5%',
          }}
        >
          <DottedLine height="110%" strokeWidth={2} orientation="vertical" />
        </View>
      )
    );
  };

  return (
    <View
      style={[
        styles.rowContainer,
        isFromCommute || hideBorder
          ? { borderBottomWidth: 0, paddingBottom: 8, paddingTop: 0 }
          : {},
      ]}
    >
      {renderCommuteBorder()}
      <View style={[styles.rowHeading]}>
        <View style={dayPlanHeaderBorder} />
        {imageIcon ? (
          <HolidayImageHolder
            defaultImage={imageIcon}
            style={styles.iconImage}
            resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
          />
        ) : <View style={styles.iconImage} />}
      </View>
      <View style={styles.rowDetails}>
        <View style={styles.rowDetailContainer}>
          <View style={{ flex: 1 }}>
            <FlatList
              horizontal
              scrollEnabled={false}
              renderItem={renderCardTextItem}
              data={cardHeaderTexts}
              ItemSeparatorComponent={() => <View style={[dotSeperator, marginStyles.mt8]} />}
              contentContainerStyle={{
                // flexWrap: 'wrap',
                width: '100%',
                flex: 1,
              }}
            />
            {!!cardSubHeader && (
              <Text style={styles.cardSubHeader} numberOfLines={1}>
                {cardSubHeader}
              </Text>
            )}
          </View>
          <View style={styles.rowHeaderDetailContainer}>
            {renderHeaderDetails()}
            {renderCollapseIcon()}
          </View>
        </View>
        {Boolean(expanded) && (
          <View style={{ flex: 1 }}>
            {!hideRowSeperator && <View style={headerSeperator} />}
            {children}
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  iconImage: {
    width: 20,
    height: 20,
    marginEnd: 10,
  },
  rowDetailContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  rowContainer: {
    ...dayPlanV2RowContainerStyle,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  rowHeading: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 0,
  },
  rowDetails: {
    flexDirection: 'column',
    flexGrow: 1,
    ...paddingStyles.pr30,
    ...marginStyles.mt4,
    flex: 1,
  },
  rowHeaderDetailContainer: {
    flexDirection: 'row',
    marginLeft: 'auto',
  },
  rowHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // maxWidth: 500,
    wordWrap: 'break-word',
    flexWrap: 'wrap',
  },
  rowHeaderTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // flex: 1,
  },
  rowHeaderText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  rowHeaderTextBold: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  cardSubHeader: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    flex: 1,
  },
  arrowIconContainer: {
    marginLeft: 'auto',
    width: 20,
    height: 20,
    alignItems: 'center',
  },
  arrowIcon: {
    width: 12,
    height: 12,
    tintColor: holidayColors.gray,
  },
  upIcon: {
    transform: [{ rotate: '-90deg' }],
  },
  downIcon: {
    transform: [{ rotate: '90deg' }],
  },
});
export default DayPlanRowHOC;
