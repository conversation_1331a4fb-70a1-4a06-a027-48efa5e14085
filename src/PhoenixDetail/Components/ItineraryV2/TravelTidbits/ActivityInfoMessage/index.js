import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import React, { useState } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View, LayoutAnimation, Platform, UIManager, Animated } from 'react-native';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { getOpitimsedImageUrl, IMAGE_ICON_KEYS } from '../../../../../Common/Components/HolidayImageUrls';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import { isEmpty } from 'lodash';
import iconMeal from '../../../images/forkIcon.png';
import iconTransfer from '../../../images/ic_cabs.png';
import iconActivity from '../../../images/ic_sightSeeing.png';
import iconArrow from '../../../images/ic_upArrow.png';
import { setSelectedActivitiesInfoStripState } from '../../../../Actions/HolidayTidbitsAction';
import { connect } from 'react-redux';
import { SELECTED_ACTIVITIES_INFO_STRIP_STATE } from '../../../../Reducers/HolidayTidbitsReducer';

const ActivityInfoMessage = (props) => {
  const {
    showTooManyActivitiesSelected = false,
    selectedActivitiesStripData = {},
    setSelectedActivitiesInfoStripState,
    selectedActivitiesInfoStripState,
  } = props || {};

  if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
  }

  const {activity, transfers, meals} = selectedActivitiesStripData || {};

  if (isEmpty(selectedActivitiesStripData) && !showTooManyActivitiesSelected) {
    return [];
  }

  const convertToNewFormat = json => {
    const newArray = [];

    for (const category in json) {
      if (json.hasOwnProperty(category)) {
        const type = category.toLowerCase();
        json[category].names.forEach(name => {
          newArray.push({ name: name, type: type });
        });
      }
    }

    return newArray;
  }

  const generateText = (category, categoryName, categoryNamePlural) => category?.count > 0
    ? category.count === 1
      ? `${category.count} ${categoryName}`
      : `${category.count} ${categoryNamePlural}`
    : null;

  const categoriesCountText = [
    generateText(activity, 'Activity', 'Activities'),
    generateText(transfers, 'Transfer', 'Transfers'),
    generateText(meals, 'Meal', 'Meals'),
  ].filter(Boolean).join(' | ');


  const getActivityIcon = (type) => {
    switch (type) {
      case 'activity':
        return iconActivity
      case 'transfers':
        return iconTransfer
      case 'meals':
        return iconMeal
      default:
        return iconActivity;
    }
  };

  const ActivityList = () => {
    const data = convertToNewFormat(selectedActivitiesStripData);
    return (
      <View style={{flexDirection:'column', ...marginStyles.mt10, ...marginStyles.mr30}}>
        {data.map(item => (
          <View style={{flexDirection:'row', ...marginStyles.mb6}}>
            <Image source={getActivityIcon(item.type)} style={styles.icon} />
            <Text style={styles.descriptionText} numberOfLines={1}>{item.name}</Text>
          </View>
        ))}
      </View>
    );
  };

  const onArrowClicked = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);

    const nextState = selectedActivitiesInfoStripState === SELECTED_ACTIVITIES_INFO_STRIP_STATE.EXPANDED
      ? SELECTED_ACTIVITIES_INFO_STRIP_STATE.COLLAPSED
      : SELECTED_ACTIVITIES_INFO_STRIP_STATE.EXPANDED;

    setSelectedActivitiesInfoStripState(nextState);
  }

  if (showTooManyActivitiesSelected){
    return (
      <View style={styles.infoContainer}>
        <HolidayImageHolder imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.ALERT_TRIANGLE_ICON)} style={styles.alertTriangleStyle} />
        <View>
          <Text style={styles.titleText}>Too many activities selected</Text>
          <Text style={styles.descriptionText}>Try removing activities or add them to different day.</Text>
        </View>
      </View>
    );
  }

const showNameList = selectedActivitiesInfoStripState === SELECTED_ACTIVITIES_INFO_STRIP_STATE.EXPANDED;
  return (
    <TouchableOpacity activeOpacity={1} onPress={onArrowClicked} style={styles.rootContainer}>
        <View style={[styles.infoContainer, styles.infoContainerAlt]}>
          <View style={styles.circle}>
          <HolidayImageHolder imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.WHITE_TICK)} style={styles.tick} />
          </View>
            <View style={styles.container}>
              <Text style={styles.titleText}>{categoriesCountText}</Text>
            </View>

          <Image source={iconArrow} style={[styles.arrow, {transform: showNameList ? [{ rotate: '180deg'}] : [{ rotate: '0deg'}]}]} />

        </View>
      {showNameList && <View style={{ backgroundColor: holidayColors.fadedYellow, ...paddingStyles.ph12, ...styles.infoContainerAlt, }}>
        <ActivityList />
      </View>}
    </TouchableOpacity>
    );
  };

const styles = StyleSheet.create({
  rootContainer: {
    position: 'absolute',
    bottom: 70,
    width: '100%',
  },
  infoContainer: {
    backgroundColor: holidayColors.fadedYellow,
    ...paddingStyles.ph12,
    ...paddingStyles.pv8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoContainerAlt: {
    backgroundColor: '#EAF5FF',
  },
  alertTriangleStyle: {
    width: 24,
    height: 24,
    marginRight: 12,
  },
  titleText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.gray,
    lineHeight: 16,
  },
  descriptionText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 16,
  },
  circle: {
    width: 14,
    height: 14,
    borderRadius: 12,
    backgroundColor: holidayColors.darkBlue5,
    justifyContent: 'center',
    alignItems: 'center',
    ...marginStyles.mr8,
  },
  tick: {
    width: 8,
    height: 8,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    ...marginStyles.mb2,
  },
  container: {
    flex: 1,
  },
  part: {
    flex: 1,
    justifyContent: 'center',
  },
  containert: {
    flex: 1,
    padding: 20,
  },
  category: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  icon: {
    width: 12,
    height: 12,
    marginRight: 10,
  },
  arrow: {
    width: 18,
    height: 18,
    marginRight: 10,
  },
});


const mapDispatchToProps = (dispatch) => {
  return {
    setSelectedActivitiesInfoStripState : data => dispatch(setSelectedActivitiesInfoStripState(data)),
  }
}
const  mapStateToProps = state => {
  const { selectedActivitiesInfoStripState } = state.travelTidbitsReducer
  return { selectedActivitiesInfoStripState };
}

export default connect(mapStateToProps, mapDispatchToProps)(ActivityInfoMessage);
