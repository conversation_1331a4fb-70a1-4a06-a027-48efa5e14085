import React, { useEffect } from 'react';
import { Image, Platform, StatusBar, StyleSheet, Text, View, BackHandler } from 'react-native';
import errorImageIcon from '@mmt/legacy-assets/src/visa_error_image.webp';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { getProductTypeErrorIcon, getProductTypeText, getTidbitErrorPageHeader } from './TidBitsUtils';
import { HolidayNavigation } from '../../../../Navigation';
import { SUB_HEADER_DATE_FORMAT } from '../../Header';
import { getNewDate } from '@mmt/legacy-commons/Common/utils/DateUtils';
import fecha from 'fecha';
import { addDays } from '@mmt/legacy-commons/Helpers/dateTimehelpers';

/* Components */
import PrimaryButton from '../../../../Common/Components/Buttons/PrimaryButton';
import PageHeader from '../../../../Common/Components/PageHeader';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import useBackHandler from '../../../../hooks/useBackHandler';

const TidbitsErrorView = (props) => {
  const { productType, departureDetail, day ,isHeaderShown=true } = props || {};
  const date = fecha.format(
    addDays(getNewDate(departureDetail?.departureDate), day - 1 < 0 ? 0 : day - 1),
    SUB_HEADER_DATE_FORMAT,
  );

  const onBackPressed = () => {
    HolidayNavigation.pop();
    return true;
  };

  // Effect hook for handling the hardware back button press.
  useBackHandler(onBackPressed);

  const productTypeText = getProductTypeText(productType, false);
  const productTypeIcon = getProductTypeErrorIcon(productType[0]);

  return (
    <View style={styles.container}>
      {isHeaderShown && <PageHeader
        showBackBtn
        showShadow
        title={getTidbitErrorPageHeader(productType)}
        onBackPressed={onBackPressed}
        containerStyles={styles.header}
      />}
      <View style={styles.errorDetails}>
        <View style={styles.errorDetailsText}>
          <HolidayImageHolder
            style={styles.errorImage1}
            defaultImage={errorImageIcon}
            imageUrl={productTypeIcon}
          />
          <Text style={styles.title}>{productTypeText} not available</Text>
          <Text style={styles.subTitle}>
            No {productTypeText} available to be added for this package on {date}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    backgroundColor: holidayColors.white,
    width: '100%',
  },
  header: {
  },
  errorDetails: {
    alignItems: 'center',
    paddingVertical: 15,
    flex: 1,
    width: '100%',
  },
  errorDetailsText: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    ...paddingStyles.pa16,
  },
  errorImage1: {
    width: 72,
    height: 72,
    ...marginStyles.mb30,
  },
  title: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    ...marginStyles.mb10,
    textAlign: 'center',
  },
  subTitle: {
    ...fontStyles.labelLargeRegular,
    color: holidayColors.gray,
    ...marginStyles.mb70,
    lineHeight: 20,
    textAlign: 'center',
  },
  button: {
    paddingHorizontal: 80,
  },
});

export default TidbitsErrorView;
