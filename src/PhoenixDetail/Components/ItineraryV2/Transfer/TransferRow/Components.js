import React from 'react';
import { Text, View, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { isEmpty } from 'lodash';
import { RESIZE_MODE_IMAGE } from 'mobile-holidays-react-native/src/HolidayConstants';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { sectionHeaderSpacing } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { actionSeperator, actionStyle, dayPlanRowImage } from '../../../DayPlan/dayPlanStyles';
import { dayPlanRowHeadingV2Styles, dayPlanV2RowHeadingStyles } from '../../dayPlanV2Styles';

/* Components */
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import ItineraryUnitImageWrapper from '../../Common/ItineraryUnitImage';
import { capitalizeText } from 'mobile-holidays-react-native/src/utils/textTransformUtil';

const TransferFooter = ({
  isTransferActivity,
  removeTransferCardConfirmation,
  openTransferListing,
  removeTransferRestricted,
  changeTransferRestricted,
  openActivityDetailIfATHTransfer,
  onViewDetail = () => {},
  day,
  unavailable,
}) => {
  const handleTransferActivityChange = () => {  
    if (isTransferActivity) {
      openActivityDetailIfATHTransfer();
    } else {
      openTransferListing();
    }
  }

  const handleViewDetail = () => {
    if(isTransferActivity) {
      openActivityDetailIfATHTransfer();
    } else {
      onViewDetail();
    }
  }
  return (
    <View style={transferFooterStyles.footer}>
      <View style={{ alignItems: 'center' }}>
        {(!removeTransferRestricted || !changeTransferRestricted) && (
          <DynamicCoachMark
            cueStepKey="changeOrRemove"
            offsetHeight={70}
            offsetWidth={70}
            extraInfo={{ from: 'transferRow', day }}
          >
            <View style={{ flexDirection: 'row' }}>
              {!removeTransferRestricted && !unavailable && (
                <TouchableOpacity onPress={removeTransferCardConfirmation}>
                  <Text style={actionStyle}>Remove</Text>
                </TouchableOpacity>
              )}
              {!removeTransferRestricted && !changeTransferRestricted && !unavailable && (
                <Text style={actionSeperator}>|</Text>
              )}
              {!changeTransferRestricted && (
                <TouchableOpacity onPress={handleTransferActivityChange}>
                  <Text style={actionStyle}>{isTransferActivity ? 'Modify' : 'Change'}</Text>
                </TouchableOpacity>
              )}
            </View>
          </DynamicCoachMark>
        )}
      </View>
      {unavailable ? (
            <Text style={transferDetailStyles.unavailableText}>Unavailable</Text>
          ) : (
        <TouchableOpacity onPress={handleViewDetail}>
          <Text style={actionStyle}>View Details</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const TransferDetails = ({
  privateText,
  itineraryUnit,
  imageUrl,
  transferDetailsList,
  borderColor,
}) => {
  const {
    itineraryUnitType,
    itineraryUnitSubType,
    text,
    shortText,
    shortTextnew,
    cityId,
    car = {},
    isLandOnly,
    landOnlyDescription,
  } = itineraryUnit || {};

  const renderTransferDetails = ({ item, index }) => {
    const { iconUrl, text } = item || {};
    if (isEmpty(text)) {
      return [];
    }
    return (
      <View style={transferDetailStyles.transferDetailTextContainer}>
        <HolidayImageHolder
          imageUrl={item.iconUrl}
          style={{ width: 15, height: 15, ...marginStyles.mr8, ...marginStyles.mt2 }}
          resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
        />
        <Text style={transferDetailStyles.transferDetailText}>{item?.text}</Text>
      </View>
    );
  };
  return (
    <View style={{ flexDirection: 'row' }}>
      <View style={[sectionHeaderSpacing, { flex: 1, borderColor: borderColor }]}>
        <Text style={dayPlanV2RowHeadingStyles.heading}>
          {privateText ? `${capitalizeText(privateText)} Transfer` : ''}
          {/* {!!vchlModel && (
        <Text style={styles.subHeadingSm}>
          {privateText && vchlModel ? <Text style={actionSeperator}> | </Text> : ''}
          {vchlModel}
          <Text style={{ textTransform: 'lowercase' }}> or similar</Text>
        </Text>
      )} */}
        </Text>
        {isLandOnly && !isEmpty(landOnlyDescription) && (
          <Text style={transferDetailStyles.subHeadingSm}>{landOnlyDescription}</Text>
        )}
        <View style={marginStyles.mr8}>
          <FlatList data={transferDetailsList} renderItem={renderTransferDetails} />
        </View>
      </View>
      <View style={{ marginLeft: 'auto' }}>
        <ItineraryUnitImageWrapper imageUrl={imageUrl} resizeMode={RESIZE_MODE_IMAGE.CONTAIN} />
      </View>
    </View>
  );
};

export { TransferDetails, TransferFooter };

const transferFooterStyles = StyleSheet.create({
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});

const transferDetailStyles = StyleSheet.create({
  subHeading: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
  },
  unavailableText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.red,
    marginTop: 'auto',
  },
  subHeadingSm: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  transferIcon: {
    width: 85,
    height: 89,
    marginRight: 10,
    resizeMode: 'contain',
    ...dayPlanRowImage,
    borderWidth: 0.5,
    borderColor: holidayColors.grayBorder,
  },
  transferDetailTextContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    ...marginStyles.mv2,
  },
  transferDetailText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    flex: 1,
  },
});
