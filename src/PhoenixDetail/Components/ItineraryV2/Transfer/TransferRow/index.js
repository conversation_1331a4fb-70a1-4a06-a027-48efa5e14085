import React, { useState } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import CrossIcon from '@mmt/legacy-assets/src/ic_cross__gray.webp';
import {
  getCarObject,
  getIconForItineraryUnitType,
  getPackageDestinations,
  getSellableIdDataForTransfers,
} from '../../../../Utils/PhoenixDetailUtils';
import {
  HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE,
  OVERLAY_CAROUSAL_POSITION,
} from '../../../../Utils/PheonixDetailPageConstants';
import { has, isArray, isEmpty } from 'lodash';
import {
  componentImageTypes,
  itineraryUnitTypes,
  packageActionComponent,
  packageActions,
} from '../../../../DetailConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import CabIcon from '@mmt/legacy-assets/src/holidays/cab.webp';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../../Navigation';
import { getEnableCarouselViewDetail } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';
import {
  actionSeperator,
  actionStyle,
  dayPlanRowContainerStyle,
  dayPlanRowHeadingStyles,
  dayPlanRowImage,
} from '../../../DayPlan/dayPlanStyles';
import { sectionHeaderSpacing, smallHeightSeperator } from '../../../../../Styles/holidaySpacing';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { dayPlanRowHeadingV2Styles } from '../../dayPlanV2Styles';
import { RESIZE_MODE_IMAGE } from 'mobile-holidays-react-native/src/HolidayConstants';
import { IMAGE_ICON_KEYS, getOpitimsedImageUrl } from '../../../../../Common/Components/HolidayImageUrls';

/* Components */
import { TransferDetails, TransferFooter } from './Components';
import DayPlanRowHOC from '../../Common/DayPlanRowHOC';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import RemovalConfirmation from '../../../DayPlan/RemovalConfirmation';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { holidayNavigationPush } from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/DetailPageNavigationUtils';
import { Overlay } from '../../../DetailOverlays/OverlayConstants';

const TransferRow = (props) => {
  const {
    day,
    city='',
    packageDetail,
    itineraryUnit,
    accessRestriction,
    onViewDetailPress,
    onComponentChange,
    roomDetails,
    packageDetailDTO,
    destinationName,
    lastPageName,
    fromPresales = false,
    detailData,
    showOverlay,
    hideOverlays,
    showBottomDivider,
    isFromCommute,
    defaultCollapsedState = '',
    showCommuteBorder = false,
    openTransferAthAsActivityDetail = () => {}
  } = props || {};

  let imageUrl = '';
  let vchlModel = '';
  let privateText = '';
  let vchlName = '';
  let seatCpcty = 0;
  let category = '';
  let facilities = [];
  let sellableId = '';
  let transferUnavailable = false;
  let transferBannerNotification = {};

  const [isModalVisisble, setModalVisible] = useState(false);
  const [showNotification, setShowNotification] = useState(true);
  const transferRemoval = {
    title: 'Removing this Transfer?',
    content: 'All road transfers including sightseeing will be removed from this package.',
    icon: CabIcon,
    onContinue: 'YES, REMOVE',
    cancel: "DON'T REMOVE",
  };
  const { carItineraryDetail, transferDetail } = packageDetail || {};
  const {
    itineraryUnitType,
    itineraryUnitSubType,
    text,
    shortText,
    shortTextnew,
    cityId,
    car = {},
    isLandOnly,
    landOnlyDescription,
  } = itineraryUnit || {};
  const {
    sellableId: slbleId,
    journey,
    privateCar,
    onlyTransfer,
    seating,
    carType,
    inclusionText,
  } = car || {};

  sellableId = slbleId;
  const { changeTransferRestricted = false, removeTransferRestricted = false } =
    accessRestriction || {};

  const transferItemDetail = getSellableIdDataForTransfers(car, carItineraryDetail, transferDetail);
  const isTransferActivity = transferItemDetail?.type === itineraryUnitTypes.ACTIVITY
  if (!transferItemDetail) {
    return [];
  } else {
    transferItemDetail.day = day;
  }

  const populateDataFromVehicleInfo = (vehicleInfo, imageDetail) => {
    const {
      model,
      privateOrShared,
      vehicleName,
      maxPaxCapacity,
      vehicleCategory,
      facilities: fcltes,
      imageUrl: imgUrl,
    } = vehicleInfo || {};
    vchlModel = model;
    privateText = privateOrShared;
    vchlName = vehicleName;
    seatCpcty = maxPaxCapacity;
    category = vehicleCategory;
    facilities = fcltes;

    if (!isEmpty(imgUrl)) {
      imageUrl = imgUrl;
    } else if (imageDetail && has(imageDetail, 'images')) {
      //Handle fallback
      const { images } = imageDetail || [];
      if (isArray(images) && images.length > 0 && !isEmpty(images[0].path)) {
        imageUrl = images[0].path;
      }
    }
  };

  if (onlyTransfer) {
    const { privateTransfer, defaultSelection, groupTransfer } = transferItemDetail || {};
    if (defaultSelection === 'SHARED' && groupTransfer) {
      const { vehicleInfo, sellableId: slbleId, imageDetail } = groupTransfer || {};
      sellableId = slbleId;
      populateDataFromVehicleInfo(vehicleInfo, imageDetail);
    } else if (privateTransfer) {
      const { vehicleInfo, sellableId: slbleId, imageDetail, unavailable = false, bannerNotification } = privateTransfer || {};
      sellableId = slbleId;
      transferUnavailable = unavailable;
      transferBannerNotification = bannerNotification;
      populateDataFromVehicleInfo(vehicleInfo, imageDetail);
    }
    privateText = defaultSelection;
  } else {
    const { vehicleInfo } = transferItemDetail || {};
    populateDataFromVehicleInfo(vehicleInfo);
  }

  const captureClickEvents = ({eventName = '', suffix  = ''}) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: `base:${itineraryUnitTypes.TRANSFERS}`,
    })
  }

  const onViewDetail = () => {
    const { onlyTransfer } = car || {};
    const temp = getCarObject(carItineraryDetail, transferDetail, slbleId, onlyTransfer);
    const data = {
      data: temp,
      day,
      text,
      cityId,
      shortText,
      type: itineraryUnitType,
      extraData: car,
      ...car,
    };
    data.data.sellableId = slbleId;
    if (onlyTransfer) {
      data.transferObj = { ...temp.privateTransfer };
    } else {
      data.commute = { ...temp };
    }
    const showCarouselView = fromPresales ? false : getEnableCarouselViewDetail();
    const fromPage = HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.BASE_DETAIL_PAGE;
    openTransferDetailPage(
          data,
          transferItemDetail.day,
          fromPage,
          lastPageName,
          `view_${itineraryUnitTypes.TRANSFERS}_${destinationName}_${day}`,
    );
  };

  const openTransferDetailPage = (transferItemDetail, day, fromPage, lastPageName, eventName) => {
    captureClickEvents({ eventName });
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.TRANSFER_DETAIL,
      overlayKey: Overlay.TRANSFER_DETAIL_PAGE,
      showOverlay,
      hideOverlays,
      props: {
        item: getDataForListing(),
        transferItemDetail,
        index: 0,
        packageDetailDTO,
        roomDetails,
        onComponentChange,
        accessRestriction,
        day,
        fromPage,
        lastPageName,
      },
    });

  };

  
  /**
   * This function will be called instead of openTransferListing if transfer type === ACTIVITY.
   * It opens the activity detail for the transfer item.
   */
  const openActivityDetailIfATHTransfer = () => { 
    openTransferAthAsActivityDetail({ item: transferItemDetail, unit: itineraryUnit })
  }
  const openTransferListing = () => {
    const carSellableId = car ? car.sellableId : '';
    const onlyTransfer = car ? car.onlyTransfer : false;

    captureClickEvents({
      eventName: 'change_',
      suffix: `${itineraryUnitTypes.TRANSFERS}_${destinationName}_${day}`,
    });
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.TRANSFER_LISTING,
      overlayKey: Overlay.TRANSFER_LISTING_PAGE,
      showOverlay,
      hideOverlays,
      props: {
        item: getDataForListing(),
        roomDetails,
        packageDetailDTO,
        onComponentChange,
        accessRestriction,
        lastPageName: HOLIDAY_ROUTE_KEYS.DETAIL,
        fromPage: HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.BASE_DETAIL_PAGE,
        detailData,
      },
    });
  };


  const removeTransferCard = () => {
    captureClickEvents({
      eventName: 'remove_',
      suffix: `${itineraryUnitTypes.TRANSFERS}_${day}_confirm_${destinationName}`,
    });
    removeTransfer(transferItemDetail, onlyTransfer, onComponentChange);
  };

  const removeTransferCardConfirmation = () => {
    setModalVisible(true);
    captureClickEvents({
      eventName: 'remove_',
      suffix: `${itineraryUnitTypes.TRANSFERS}_${day}_click_${destinationName}`,
    });
  };

  const hideTransferModal = () => {
    setModalVisible(false);
  };

  const getDataForListing = () => {
    const carSellableId = car ? car.sellableId : '';
    const onlyTransfer = car ? car.onlyTransfer : false;
    const transferItemDetail = {
      day,
      text,
      cityId,
      shortText,
      carSellableId,
      onlyTransfer,
      data: {
        ...getCarObject(carItineraryDetail, transferDetail, carSellableId, onlyTransfer),
        sellableId: carSellableId,
      },
      extraData: car ? car : null,
    };
    return transferItemDetail;
  };

  const removeTransfer = (transferItemDetail, onlyTransfer, onComponentChange) => {
    const actionData = {};
    //onComponentChange
    if (onlyTransfer) {
      actionData.transferSelectionType = 'NONE';
      actionData.transferSequence = transferItemDetail.transferSequence;
      actionData.packageComponent = packageActionComponent.TRANSFER;
      actionData.action = packageActions.CHANGE;
    } else {
      actionData.sellableId = transferItemDetail.sellableId;
      actionData.startDay = transferItemDetail.startDay;
      actionData.resultSellableId = transferItemDetail.sellableId;
      actionData.packageComponent = packageActionComponent.CAR;
      actionData.action = packageActions.TOGGLE;
    }
    onComponentChange(actionData, componentImageTypes.TRANSFERS);
  };

  const borderColor = showBottomDivider ? holidayColors.grayBorder : holidayColors.white;
  const containerStyle = isFromCommute ? smallHeightSeperator : '';
  const transferDetailsList = [
    {
      iconUrl: getOpitimsedImageUrl(IMAGE_ICON_KEYS.PAX),
      text: facilities?.map((transferItemDetail) => transferItemDetail.title).join(', '),
    },
    {
      iconUrl: getOpitimsedImageUrl(IMAGE_ICON_KEYS.BOLT),
      text: inclusionText,
    },
  ];

  const cardSubHeaderText = !isEmpty(shortText)
    ? isLandOnly
      ? shortText
      : `Transfer From ${shortText}`
    : !text
    ? text
    : '';
  return (
    <DayPlanRowHOC
      day={day}
      city={city}
      unit={itineraryUnit}
      cardHeaderTexts={[{ text: 'TRANSFER', isEmphasized: true }]}
      cardSubHeader={cardSubHeaderText}
      isFromCommute={isFromCommute}
      defaultCollapsedState={defaultCollapsedState}
      showCommuteBorder={showCommuteBorder}
    >
      <View style={[containerStyle, styles.transferRow]}>
        <View style={transferUnavailable && styles.opacityReduced}>
        <TransferDetails
          privateText={privateText}
          itineraryUnit={itineraryUnit}
          imageUrl={imageUrl}
          transferDetailsList={transferDetailsList}
          borderColor={borderColor}      
        />
        </View>
        <TransferFooter
          isTransferActivity={isTransferActivity}
          removeTransferCardConfirmation={removeTransferCardConfirmation}
          openTransferListing={openTransferListing}
          openActivityDetailIfATHTransfer={openActivityDetailIfATHTransfer}
          removeTransferRestricted={removeTransferRestricted}
          changeTransferRestricted={changeTransferRestricted}
          onViewDetail={onViewDetail}
          day={day}
          unavailable={transferUnavailable}
        />
        {transferUnavailable && transferBannerNotification && showNotification && (
          <View style={[styles.bannerContainer, { backgroundColor: transferBannerNotification?.bannerConfig?.backgroundColor }]}>
            <View style={styles.bannerContent}>
              <View style={styles.bannerHeader}>
                <View style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
                  <View style={[styles.iconContainer]}>
                    <Image
                      source={{ uri: transferBannerNotification?.content?.icon?.iconUrl }}
                      style={[styles.iconText]}
                    />
                  </View>
                  <Text style={[styles.notificationType, { color: transferBannerNotification?.content?.title?.color }]}>
                    {transferBannerNotification?.content?.title?.text}
                  </Text>
                </View>
                <TouchableOpacity style={styles.crossIconContainer} onPress={() => setShowNotification(false)}>
                  <Image source={CrossIcon} style={[styles.crossIcon, { tintColor: transferBannerNotification?.bannerConfig?.backgroundColor }]} />
                </TouchableOpacity>
              </View>
              <Text style={[styles.bannerDescription, { color: transferBannerNotification?.content?.description?.color, marginHorizontal: 32 }]}>
                {transferBannerNotification?.content?.description?.text}
              </Text>
            </View>
          </View>
        )}
        {isModalVisisble && (
          <RemovalConfirmation
            hideModal={hideTransferModal}
            removeTransferCard={removeTransferCard}
            {...transferRemoval}
          />
        )}
      </View>
    </DayPlanRowHOC>
  );
};

const styles = StyleSheet.create({
  transferRow: {
    flex: 1,
    ...paddingStyles.pb12,
  },
  opacityReduced: {
    opacity: 0.5,
  },
  bannerContainer: {
    marginTop: 12,
    borderRadius: 8,
    padding: 12,
  },
  bannerContent: {
    flexDirection: 'column',
  },
  bannerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  iconText: {
    width: 24,
    height: 24,
  },
  notificationType: {
    ...fontStyles.labelBaseBold,
  },
  bannerDescription: {
    ...fontStyles.bodySmall,
    lineHeight: 18,
  },
  crossIcon: {
    height: 12,
    width: 12,
    marginVertical: 4,
  },
  crossIconContainer: {
    backgroundColor: holidayColors.lightGray,
    width: 20,
    height: 20,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default TransferRow;
