import React, { useState } from 'react';
import { Text, StyleSheet, View, Image, TouchableOpacity } from 'react-native';
import Proptypes from 'prop-types';
import { getFlightDate } from '../../../../Utils/FlightUtils';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { flightRemovalPrompt } from '../../constants';

/* Icons */
import FlightIcon from '@mmt/legacy-assets/src/iconFlight.webp';

/* Components */
import RemovalConfirmation from '../../../DayPlan/RemovalConfirmation';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import ItineraryUnitExtraInfoMessages from '../../../ItineraryUnitExtraInfoMessages';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../../../Common/Components/HolidayImageUrls';

const dummyFlightCardDefaultPropAccessRestriction = {};

const DummyFlightCard = ({
  flightData = {},
  accessRestriction = dummyFlightCardDefaultPropAccessRestriction,
  day = 0,
  removeFlights = null,
  isReviewPage = false,
  flightExtraInfo = {},
}) => {
  const { departure, fromAirport, toAirport, arrival } = flightData;
  const from = fromAirport.airportCity;
  const to = toAirport.airportCity;

  const removeFlightRestricted = accessRestriction
    ? accessRestriction.removeFlightRestricted
    : false;

  const [isModalVisisble, setModalVisible] = useState(false);
  const removeTransferCardConfirmation = () => {
    trackPhoenixDetailLocalClickEvent({
      eventName: 'remove',
      suffix: `${day}_flights_click`,
      prop1: 'base:flights',
    });
    setModalVisible(true);
  };
  const hideTransferModal = () => {
    setModalVisible(false);
  };

  const renderFlightDetail = ({ location, time }) => {
    return (
      <View>
        <Text style={styles.flightLocation}>{location}</Text>
        <Text style={styles.flightDate}>{getFlightDate(time)}</Text>
      </View>
    );
  };

  const getFlightRouteView = () => {
    return (
      <View style={{ flex: 1 }}>
        <View style={styles.flightRoute}>
          <View style={styles.flightRouteLine} />
          <View />
          <HolidayImageHolder
            imageUrl={getImageUrl(IMAGE_ICON_KEYS.AIRPLANE_ICON)}
            style={styles.airplaneIcon}
          />
          <View />
        </View>
      </View>
    );
  };
  return (
    <View>
      <View style={styles.flightDetails}>
        <View style={styles.flightTimings}>
          {renderFlightDetail({ location: from, time: departure })}
          {getFlightRouteView()}
          {renderFlightDetail({ location: to, time: arrival })}
        </View>
        <View style={styles.airlineDetails}>
          <HolidayImageHolder
            defaultImage={FlightIcon}
            style={styles.airlineIcon}
            resizeMode={'contain'}
          />
          <Text style={styles.airlineCode}>Tentative Flight</Text>
        </View>
      </View>
      <ItineraryUnitExtraInfoMessages extraInfo={flightExtraInfo} />
      {isReviewPage || !removeFlights ? null : (
        <View style={styles.footer}>
          {!removeFlightRestricted && (
            <DynamicCoachMark
              cueStepKey="changeOrRemove"
              offsetHeight={70}
              offsetWidth={70}
              extraInfo={{ from: 'flightsRow', day }}
            >
              <View style={{ flexDirection: 'row' }}>
                {!removeFlightRestricted && (
                  <TouchableOpacity onPress={removeTransferCardConfirmation}>
                    <Text style={styles.anchorText}>REMOVE</Text>
                  </TouchableOpacity>
                )}
              </View>
            </DynamicCoachMark>
          )}
        </View>
      )}
      {isModalVisisble && (
        <RemovalConfirmation
          hideModal={hideTransferModal}
          removeTransferCard={removeFlights}
          {...flightRemovalPrompt}
        />
      )}
    </View>
  );
};

DummyFlightCard.propTypes = {
  flightData: Proptypes.object.isRequired,
  accessRestriction: Proptypes.object,
  day: Proptypes.number,
  removeFlights: Proptypes.func,
  isReviewPage: Proptypes.bool,
};

const DummyFlightCards = ({ flightLegs, ...rest }) => {
  if (flightLegs) {
    return (
      <View>
        {flightLegs.map((item, index) => (
          <DummyFlightCard key={index} flightData={item} index={index} {...rest} />
        ))}
      </View>
    );
  } else {
    return null;
  }
};

const styles = StyleSheet.create({
  flightDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  airlineDetails: {
    alignItems: 'center',
    ...marginStyles.ml10,
    maxWidth: 43,
  },
  airlineIcon: {
    height: 36,
    width: 36,
    backgroundColor: holidayColors.lightBlueBg,
    borderRadius: 18,
  },
  airlineCode: {
    ...fontStyles.labelSmallRegular,
    fontSize: 10,
    color: holidayColors.gray,
    marginTop: 3,
    textAlign: 'center',
  },
  flightTimings: {
    flex: 1,
    flexDirection: 'row',
  },
  flightLocation: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  flightDate: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  flightRoute: {
    position: 'relative',
    marginHorizontal: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  flightRouteLine: {
    position: 'absolute',
    width: '100%',
    height: 1,
    backgroundColor: holidayColors.grayBorder,
    top: 7,
  },
  airplaneIcon: {
    height: 15,
    width: 15,
    tintColor: holidayColors.primaryBlue,
  },
  footer: {
    marginTop: 12,
  },
  anchorText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
});

export default DummyFlightCards;
