import React, { useState } from 'react';
import { View } from 'react-native';
import { trackPhoenixDetailLocalClickEvent } from '../../../../../Utils/PhoenixDetailTracking';
import { flightRemovalPrompt } from '../../../constants';

/* Components */
import { AirlineDetails, FlightFooter, FlightTimings, Layovers } from './Components';
import RemovalConfirmation from '../../../../DayPlan/RemovalConfirmation';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';


const FlightCard = ({ flightData, flightCardProps, index, viewDetailAccessRestriction }) => {
  const {
    ifFlightGroupFailed,
    day,
    flightLegs,
    stops,
    accessRestriction,
    onViewDetailPress,
    sellableId,
    openFlightListingPage,
    removeFlights,
  } = flightCardProps || {};
  const { airlineCode, flightId, departure, fromAirport, toAirport, arrival, via, viaList } =
    flightData;
  const from = fromAirport.airportCity;
  const to = toAirport.airportCity;
  const toAirportCode = toAirport.airportCode;

  const captureClickEvents = ({ eventName = '', suffix = '', prop1 = '' }) => {
    const value = eventName + '_' + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
    });
    trackPhoenixDetailLocalClickEvent({ eventName, suffix, prop1 });
  };

  const [isModalVisible, setModalVisible] = useState(false);
  const removeTransferCardConfirmation = () => {
    captureClickEvents({
      eventName: 'remove',
      suffix: `${day}_flights_click`,
      prop1: 'base:flights',
    });
    setModalVisible(true);
  };
  const hideTransferModal = () => {
    setModalVisible(false);
  };

  return (
    <View>
      <View style={{ flexDirection: 'row' }}>
        <FlightTimings departure={departure} arrival={arrival} from={from} to={to} stops={stops} />
        <AirlineDetails airlineCode={airlineCode} flightId={flightId} />
      </View>
      <FlightFooter
        ifFlightGroupFailed={ifFlightGroupFailed}
        accessRestriction={accessRestriction}
        viewDetailAccessRestriction={viewDetailAccessRestriction}
        removeTransferCardConfirmation={removeTransferCardConfirmation}
        openFlightListingPage={openFlightListingPage}
        onViewDetailPress={onViewDetailPress}
        sellableId={sellableId}
        day={day}
      />
      <Layovers
        via={via}
        viaList={viaList}
        stops={stops}
        index={index}
        flightLegs={flightLegs}
        to={to}
        toAirportCode={toAirportCode}
      />
      {isModalVisible && (
        <RemovalConfirmation
          hideModal={hideTransferModal}
          removeTransferCard={removeFlights}
          {...flightRemovalPrompt}
        />
      )}
    </View>
  );
};
export default FlightCard;
