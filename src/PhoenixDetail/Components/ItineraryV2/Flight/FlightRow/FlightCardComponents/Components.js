import React from 'react';
import { Text, View, Image, TouchableOpacity, FlatList, StyleSheet } from 'react-native';
import { getAirlineIconUrl } from '../../../../FlightDetailPage/FlightListing/FlightsUtils';
import { getFlightDate, parseFlightDate } from '../../../../../Utils/FlightUtils';
import { actionSeperator, actionStyle } from '../../../../DayPlan/dayPlanStyles';
import { OVERLAY_CAROUSAL_POSITION } from '../../../../../Utils/PheonixDetailPageConstants';
import { HOLIDAY_ROUTE_KEYS } from '../../../../../../Navigation';
import { itineraryUnitTypes } from '../../../../../DetailConstants';
import { getFlightDuration, getFlightTime } from '../../../../../Utils/PhoenixDetailUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

/* Components */
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import { holidayBorderRadius } from '../../../../../../Styles/holidayBorderRadius';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../../../../Common/Components/HolidayImageUrls';
import { RESIZE_MODE_IMAGE } from 'mobile-holidays-react-native/src/HolidayConstants';

const AirlineDetails = ({ airlineCode, flightId }) => (
  <View style={airlineDetailStyles.airlineDetails}>
    <HolidayImageHolder
      imageUrl={getAirlineIconUrl(airlineCode)}
      style={airlineDetailStyles.airlineIcon}
      resizeMode={RESIZE_MODE_IMAGE.COVER}
    />
    <Text style={airlineDetailStyles.airlineCode}>{flightId}</Text>
  </View>
);

const FlightTimings = ({ departure, arrival, from, to }) => {
  const depTime = getFlightTime(parseFlightDate(departure));
  const arrTime = getFlightTime(parseFlightDate(arrival));

  const getFlightRouteView = () => {
    return (
      <View style={{ flex: 0.3 }}>
        <View style={flightTimingStyles.flightRoute}>
          <View style={flightTimingStyles.flightRouteLine} />
          <View />
          <HolidayImageHolder
            imageUrl={getImageUrl(IMAGE_ICON_KEYS.AIRPLANE_ICON)}
            style={flightTimingStyles.airplaneIcon}
          />
          <View />
        </View>
      </View>
    );
  };
  const getFlightTimeDetails = ({ time, date, city }) => {
    return (
      <View style={{ flex: 0.28 }}>
        <Text style={flightTimingStyles.flightTime}>{time}</Text>
        <Text style={flightTimingStyles.flightDate}>{getFlightDate(date)}</Text>
        <Text style={flightTimingStyles.flightLocation} numberOfLines={1}>
          {city}
        </Text>
      </View>
    );
  };
  return (
    <View style={flightTimingStyles.flightTimings}>
      {getFlightTimeDetails({ time: depTime, date: departure, city: from })}
      <View style={{ flex: 0.04 }}></View>
      {getFlightRouteView()}
      <View style={{ flex: 0.05 }}></View>
      {getFlightTimeDetails({ time: arrTime, date: arrival, city: to })}
    </View>
  );
};

const FlightFooter = (props) => {
  const {
    ifFlightGroupFailed,
    removeTransferCardConfirmation,
    openFlightListingPage,
    onViewDetailPress,
    sellableId,
    accessRestriction,
    viewDetailAccessRestriction,
    day,
  } = props || {};

  const changeFlightRestricted = accessRestriction?.changeFlightRestricted ?? false;
  const removeFlightRestricted = accessRestriction?.removeFlightRestricted ?? false;
  const availabilityText = !ifFlightGroupFailed ? 'View Details' : 'Currently Unavailable';
  const availabilityTextStyle = ifFlightGroupFailed ? styles.anchorTextUnavailable : actionStyle;

  const onApplyClick = () => {
    openFlightListingPage({ replace: false });
  };

  const onViewDetailsClicked = () => {
    onViewDetailPress(
      OVERLAY_CAROUSAL_POSITION.FLIGHT,
      sellableId,
      day,
      HOLIDAY_ROUTE_KEYS.DETAIL,
      itineraryUnitTypes.FLIGHT,
    );
  };

  return (
    <View style={styles.footer}>
      <View style={AtomicCss.alignCenter}>
        {(!removeFlightRestricted || !changeFlightRestricted) && (
          <DynamicCoachMark
            cueStepKey="changeOrRemove"
            offsetHeight={70}
            offsetWidth={70}
            extraInfo={{ from: 'flightsRow', day }}
          >
            <View style={AtomicCss.flexRow}>
              {!removeFlightRestricted && (
                <TouchableOpacity onPress={removeTransferCardConfirmation}>
                  <Text style={actionStyle}>Remove</Text>
                </TouchableOpacity>
              )}

              {!changeFlightRestricted && !removeFlightRestricted && (
                <Text style={actionSeperator}>|</Text>
              )}

              {!changeFlightRestricted && (
                <TouchableOpacity onPress={onApplyClick}>
                  <Text style={actionStyle}>Change</Text>
                </TouchableOpacity>
              )}
            </View>
          </DynamicCoachMark>
        )}
      </View>
      {!viewDetailAccessRestriction || !viewDetailAccessRestriction.flightRestricted ? (
        <TouchableOpacity disabled={ifFlightGroupFailed} onPress={onViewDetailsClicked}>
          <Text style={availabilityTextStyle}>{availabilityText}</Text>
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

const FlightLayover = ({ duration, airport, flightChange, airportCode, style }) => {
  if (flightChange) {
    return (
      <View style={[layoverStyles.layover, style]}>
        <Text>Change flight | {airport}</Text>
      </View>
    );
  } else {
    const layoverText = airportCode ? `${airportCode}, ${airport}` : airport;
    return (
      <View style={[layoverStyles.layover, style]}>
        <Text style={layoverStyles.layoverText2}>
          <Text style={layoverStyles.layoverText1}>{getFlightDuration(duration)} </Text>
          Layover in {layoverText}
        </Text>
      </View>
    );
  }
};

const Layovers = ({ via, viaList, stops, index, flightLegs, to, toAirportCode }) => {
  const renderItem = ({ item, index }) => (
    <FlightLayover
      key={index}
      duration={item.duration}
      airport={item.airport}
      flightChange={item.flightChange}
    />
  );

  if (via && viaList) {
    return (
      <FlatList
        data={viaList}
        renderItem={renderItem}
        keyExtractor={(_, index) => index.toString()}
      />
    );
  }

  if (stops > 0 && !via && index < flightLegs.length - 1) {
    return (
      <FlightLayover
        duration={flightLegs[index + 1].layoverDuration}
        airport={to}
        airportCode={toAirportCode}
        flightChange={false}
      />
    );
  }

  return [];
};

export { AirlineDetails, FlightTimings, FlightFooter, Layovers };

const airlineDetailStyles = StyleSheet.create({
  airlineDetails: {
    alignItems: 'center',
    marginRight: 10,
    maxWidth: 54,
  },
  airlineIcon: {
    height: 42,
    width: 42,
    ...holidayBorderRadius.borderRadius4,
  },
  airlineCode: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    marginTop: 3,
  },
});

const flightTimingStyles = StyleSheet.create({
  flightTimings: {
    flex: 1,
    flexDirection: 'row',
  },
  flightTime: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  flightLocation: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  flightDate: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  flightRoute: {
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  flightRouteLine: {
    position: 'absolute',
    width: '100%',
    height: 3,
    backgroundColor: holidayColors.grayBorder,
    top: 6,
  },
  airplaneIcon: {
    height: 15,
    width: 15,
    tintColor: holidayColors.black,
  },
});

const layoverStyles = StyleSheet.create({
  layover: {
    height: 27,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: holidayColors.lightGray2,
    marginVertical: 19,
    borderRadius: 5,
  },
  layoverText1: {
    ...fontStyles.labelSmallBold,
    letterSpacing: 0,
    color: holidayColors.gray,
  },
  layoverText2: {
    ...fontStyles.labelSmallRegular,
    letterSpacing: 0,
    color: holidayColors.black,
  },
});
const styles = StyleSheet.create({
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },
  anchorTextUnavailable: {
    ...actionStyle,
    color: holidayColors.red,
  },
});
