import React from 'react';
import { FlatList, StyleSheet, Text, View } from 'react-native';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';

/* Components */
import ImageCarousal from 'mobile-holidays-react-native/src/Common/Components/ImageCarousal';
import { holidayColors } from '../../../../../Styles/holidayColors';
import Tag from '../ActivityListingPage/Tag';

const ActivityDetailPageHeader = (props) => {
  const { activity = {}, packageDetailDTO = {} } = props;
  const { metaData = {}, imageDetail } = activity;
  const { images = [] } = imageDetail || {};
  const {
    locked = false,
    freebie,
    name = '',
    userRating = '',
    cityName = '',
    duration = '',
    labels = [],
  } = metaData || {};

  const updatedImages = images.map((image) => {
    return {
      ...image,
      fullPath: image?.path || '',
    };
  });

  const renderTags = ({ item = {} }) => {
    const { black } = holidayColors || {};
    const { texts = [''], textColorCode = black, borderColorCode = black } = item;
    const tagText = Array.isArray(texts) && texts.length > 0 ? texts[0] : '';

    return (
      <Tag
        textColor={borderColorCode}
        borderColor={textColorCode}
        labelText={tagText}
      />
    );
  };

  const renderSeparator = () => <View style={styles.tagsSeparator} />;

  return (
    <>
      <View style={marginStyles.mb16}>
        <ImageCarousal cardImages={updatedImages} activity={true} />
      </View>
      <View style={[{ alignSelf: 'flex-end' }, marginStyles.mb4]}></View>
      <View style={styles.tagContainer}>
        <FlatList
          data={labels}
          renderItem={renderTags}
          keyExtractor={(item, index) => index.toString()}
          horizontal={true}
          ItemSeparatorComponent={renderSeparator}
        />
      </View>
      <View style={paddingStyles.pb16}>
        <Text style={[fontStyles.headingMedium, { color: holidayColors.black }]}>{name}</Text>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  actionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  separator: {
    ...marginStyles.mh6,
    backgroundColor: holidayColors.grayBorder,
    width: 1,
    height: 10,
  },
  tagsSeparator: {
    width: 6,
  },
  tagContainer: {
    ...marginStyles.mb6
  },
});

export default ActivityDetailPageHeader;
