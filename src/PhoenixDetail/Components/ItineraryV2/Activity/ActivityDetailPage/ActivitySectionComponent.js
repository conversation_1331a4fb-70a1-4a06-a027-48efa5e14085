import React from 'react';
import { isEmpty } from 'lodash';
import { View, Text, Image, StyleSheet } from 'react-native';
import { holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';

import ItineraryUnitInformation, {
  INFO_SEPERATOR,
  INFO_SIZE,
} from '../../Common/ItineraryUnitInformation';
import { htmlTextBaseStyles } from '../../dayPlanV2Styles';

/* Components */
import ReadMoreReadLessHTMLText from '../../../../../Common/Components/ReadMoreLessHTMLText';
import { BaseCard, ActivityPhotos } from './Components';
import ActivityOptions from './ActivityOptions';
import ActivityLocation from './ActivityLocation';
import RatePlanPoints from './ActivityOptionRatePlan/RatePlanPoints';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../../../utils/HolidayPDTConstants';
import { SUB_PAGE_NAMES } from '../../../../../../src/HolidayConstants';
import { getSubPageName } from '../../../ActivityOverlay/ActivityUtils';

export const ACTIVITY_SECTION_TYPES = {
  GENERIC: 'GENERIC',
  ACTIVITY_OPTIONS: 'ACTIVITY_OPTIONS',
  LOCATION: 'LOCATION',
  RATE_PLAN_POINTS: 'RATE_PLAN_POINTS',
};

const ActivitySectionComponent = ({ day = '', data, activity = {}, clickEventParams = {}, ...rest }) => {
  const {
    title = '',
    info = [],
    images = [],
    subTitle = '',
    description = '',
    shouldShow = true,
    backgroundColor = '',
    titleTextColor = '',
    sectionType = '',
    infoLimit = null,
    infoSeperator = '',
  } = data || {};

  const { cityName = '', acmeType = '' } = activity?.metaData || {}
  const { packagePrice } = rest || {};
  if (!shouldShow || isEmpty(sectionType)) {
    return [];
  }
  const captureClickEvents = ({eventName = '', prop1 = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
      subPageName: getSubPageName(acmeType),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName, 
      prop1,
    })
  }
  

  const renderGenericSection = () => {
    return (
      <View style={{ flex: 1}}>
        {info?.length > 0 && (
          <ItineraryUnitInformation
            info={info}
            size={INFO_SIZE.BASE}
            seperator={infoSeperator ? infoSeperator : null}
            infoLimit={infoLimit}
            clickEventParams={clickEventParams}
          />
        )}
        {!!description && (
          <View style={marginStyles.mt6}>
            <ReadMoreReadLessHTMLText
              textValue={description}
              limit={2}
              anchorStyle={htmlTextBaseStyles.actionStyle}
              textStyle={htmlTextBaseStyles}
              captureClickEvents={captureClickEvents}
              clickEventParams={clickEventParams}
            />
          </View>
        )}
        {images?.length > 0 && <ActivityPhotos data={images} />}
      </View>
    );
  };

  const renderActivityOptions = () => {
    return (
      <ActivityOptions
        day={day}
        city={cityName}
        activity={activity}
        packagePrice={packagePrice}
        acmeType={acmeType}
        {...rest}
      />
    );
  };

  const renderLocation = () => {
    const { detailExtraInfo } = activity || {};
    const { venueDetails } = detailExtraInfo || {};
    return <ActivityLocation venueDetails={venueDetails} />;
  };

  const renderRatePlanPoints = () => {
    const { info = [] } = data || {};
    return <RatePlanPoints data={info} clickEventParams={clickEventParams} />;
  };
  const renderSection = ({ sectionType }) => {
    switch (sectionType) {
      case ACTIVITY_SECTION_TYPES.GENERIC:
        return renderGenericSection();
      case ACTIVITY_SECTION_TYPES.ACTIVITY_OPTIONS:
        return renderActivityOptions();
      case ACTIVITY_SECTION_TYPES.LOCATION:
        return renderLocation();
      case ACTIVITY_SECTION_TYPES.RATE_PLAN_POINTS:
        return renderRatePlanPoints();
      default:
        return [];
    }
  };

  return (
    <BaseCard
      headerText={title}
      subHeaderText={subTitle}
      bgcolor={backgroundColor}
      textColor={titleTextColor}
    >
      {renderSection({ sectionType })}
    </BaseCard>
  );
};
export default ActivitySectionComponent;
