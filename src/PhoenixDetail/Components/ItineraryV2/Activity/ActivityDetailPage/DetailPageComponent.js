import React, { useState, useRef, useMemo } from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  FlatList,
  Platform,
  StatusBar,
} from 'react-native';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { fetchActivityDetailsResponse } from '../../../../../utils/HolidayNetworkUtils';
import { HTML_CODES, MONTH_ARR_CAMEL } from '../../../../../HolidayConstants';
import { getNewDate } from '@mmt/legacy-commons/Common/utils/DateUtils';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { createActivityData } from '../../ItineraryV2Utils';

/* Components */
import ActivitySectionComponent from './ActivitySectionComponent';
import ImageCarousal from 'mobile-holidays-react-native/src/Common/Components/ImageCarousal';
import PageHeader from '../../../../../Common/Components/PageHeader';
import PriceFooter from './PriceFooter';
import ActivityDetailPageHeader from './ActivityDetailPageHeader';
import PhoenixHeader from '../../../PhoenixHeader';
import { connect } from 'react-redux';
import FilterLoader from '../../../../../SearchWidget/Components/FilterLoader';
import { HOLIDAYS_ACTIVITY_OVERLAY_DETAIL, VIEW_STATE } from '../../../../Utils/PheonixDetailPageConstants';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { checkAndChangePriceToZero } from '../../../../Utils/PhoenixDetailUtils';
import { getPriceDiff } from '../../../../../utils/HolidayUtils';
import { HolidayNavigation } from '../../../../../Navigation';
import { PRESALES_MIMA_DETAIL_PAGE } from '../../../../../MimaPreSales/utils/PreSalesMimaConstants';
import ConfirmationPopup from '../../../ConfirmationPopup/ConfirmationPopup';
import { holidayNavigationPop } from '../../../../Utils/DetailPageNavigationUtils';
import { Overlay } from '../../../DetailOverlays/OverlayConstants';

const ActivityDetailPageComponent = (props) => {
  const {
    activity = {},
    pageHeader = '',
    onBackPress,
    addActivityRestricted = false,
    ratePlanRestricted = true,
    onUpdatePress,
    subtitleData = '',
    showHorizontalLoader,
    day = '',
    hideOverlays,
    ...rest
  } = props;
  const {
    blackStripData: blackStrip,
    selectedRecheckKeyState,
    scrollToRatePlan = false,
    packageDetailDTO = {},
    selected: selectedState,
  } = rest || {};
  const [blackStripData, setBlackStripData] = useState(blackStrip);
  const [newActivityOptionRecheckKey, setNewActivityOptionRecheckKey] = useState(null);
  const [scrollToRatePlanView, setScrollToRatePlanView] = useState(scrollToRatePlan);
  const { metaData = {}, imageDetail, additionalData = {} } = activity;
  const { name = '', code, acmeType, acmeSubType } = metaData || {};
  const { images = [] } = imageDetail || {};
  const flatListRef = useRef(null);
  const [confirmDialogVisibility, setConfirmDialogVisibility] = useState(false);

  const activitySectionData = createActivityData({
    activity,
    addActivityRestricted,
    ratePlanRestricted,
  });

  const updateBlackStripData = (change, activityPrice2, newReCheckKey) => {
    const {
      day,
      packagePrice,
      numberOfActivities,
      activityPrice,
      showUpdate,
      discountedFactor = null,
    } = blackStrip;
    setBlackStripData({
      day: day,
      packagePrice: packagePrice,
      numberOfActivities: numberOfActivities + change,
      activityPrice: activityPrice + activityPrice2,
      showUpdate: selectedRecheckKeyState !== newReCheckKey || showUpdate,
      discountedFactor,
    });
    setNewActivityOptionRecheckKey(newReCheckKey);
  };

  const blackStripUpdatePress = () => {
    onUpdatePress({ recheckKey: selectedRecheckKeyState, code, name, acmeType, acmeSubType });
  };

  const handleContentSizeChange = () => {
    if (scrollToRatePlanView) {
      flatListRef.current.scrollToIndex({ index: 2, animated: true, offset: 50 });
    }

    setScrollToRatePlanView(false);
  };
  const renderListHeaderItem = () => {
    return <ActivityDetailPageHeader {...props} />;
  };

  const onRemoveCtaPress = () => {
    const { locked, freebie } = metaData || {};

    const restriction = packageDetailDTO?.pageName === PRESALES_MIMA_DETAIL_PAGE ? true : false;
    const selected = selectedState || selectedRecheckKeyState;
    if (locked || freebie || !selected || restriction) {
      return;
    }
    setConfirmDialogVisibility(true);
  };

  const removeActivtyHandle = () => {
    setConfirmDialogVisibility(false);
    const { onRemovePress, modifyActivityDetail, packagePrice } = props;
    const { locked, freebie } = metaData || {};

    const restriction = packageDetailDTO?.pageName === PRESALES_MIMA_DETAIL_PAGE ? true : false;
    const selected = selectedState || selectedRecheckKeyState;
    if (locked || freebie || !selected || restriction) {
      return;
    }
    if (onRemovePress) {
      onRemovePress();
    } else {
      const { metaData, packagePriceMap, discountedFactor, recheckKey } = activity || {};
      const { code, name } = metaData || {};
      let price = null;
      if (selectedRecheckKeyState) {
        price = checkAndChangePriceToZero(
          getPriceDiff(packagePrice, packagePriceMap[selectedRecheckKeyState], discountedFactor),
        );
      } else {
        price = checkAndChangePriceToZero(
          getPriceDiff(packagePrice, packagePriceMap[recheckKey], discountedFactor),
        );
      }
      modifyActivityDetail(false, code, null, name, price);
    }
    holidayNavigationPop({
      overlayKeys: [Overlay.ACTIVITY_DETAIL_V2],
      hideOverlays,
    })
  };

  const isActivityOptionSelected = useMemo(() => {
    return activity?.activityOptions?.some((option) => {
      const isSlotChange = option?.slotDetails?.slots.some((slot) => {
        return (
          slot?.slotRecheckKey === selectedRecheckKeyState ||
          slot?.slotRecheckKey === newActivityOptionRecheckKey
        );
      });
      const isRatePlanChange =
        option.recheckKey === selectedRecheckKeyState ||
        option.recheckKey === newActivityOptionRecheckKey;

      return isSlotChange || isRatePlanChange;
    });
  }, [selectedRecheckKeyState, newActivityOptionRecheckKey]);
  
  const renderActivitySectionItem = ({ item, index }) => {
    const {acmeType = 'ACTIVITY'} = activity?.metaData || {};
    const clickEventParams = { sectionName: item?.title, component: acmeType, prop1: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL };
    return (
      <ActivitySectionComponent
        day={day}
        data={item}
        activity={activity}
        updateBlackStripData={updateBlackStripData}
        blackStripData={blackStripData}
        ratePlanRestricted={ratePlanRestricted}
        subtitleData={subtitleData}
        onRemoveCtaPress={onRemoveCtaPress}
        clickEventParams={clickEventParams}
        hideOverlays={hideOverlays}
        {...rest}
      />
    );
  };

  return (
    <View style={styles.activityContainer}>
      <FilterLoader
        showCenterLoader={true}
        loadingFirstTime={false}
        show={showHorizontalLoader}
        parentStyle={styles.horizontalLoader}
      />
      <PhoenixHeader
        title={activity?.metaData?.name || ''}
        subtitleData={subtitleData}
        handleClose={onBackPress}
      />
      <FlatList
        ref={flatListRef}
        data={activitySectionData}
        ListHeaderComponent={renderListHeaderItem}
        onContentSizeChange={handleContentSizeChange}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={paddingStyles.pa16}
        onScrollToIndexFailed={(info) => {
          const wait = new Promise((resolve) => setTimeout(resolve, 500));
          wait.then(() => {
            flatListRef.current?.scrollToIndex({ index: info.index, animated: true });
          });
        }}
        renderItem={renderActivitySectionItem}
        keyExtractor={(item, index) => `section-${item?.sectionType || ''}-${index}`}
      />
      {!addActivityRestricted && (
        <PriceFooter
          {...blackStripData}
          isActivityOptionSelected={isActivityOptionSelected}
          onUpdatePress={blackStripUpdatePress}
          selectedRechekKeyState={selectedRecheckKeyState}
          addonPrice={blackStrip?.addonPrice}
        />
      )}
      {confirmDialogVisibility && (
        <ConfirmationPopup
          headingText={'You have done some changes in the Holidays package.'}
          primaryButtonText={'Yes, Remove'}
          confirmDialogVisibility={confirmDialogVisibility}
          onUpdatePackageClickFromPopup={removeActivtyHandle}
          onNotNowClicked={() => {
            setConfirmDialogVisibility(false);
          }}
          showMeButtonVisibility={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  activityContainer: {
    backgroundColor: holidayColors.lightGray2,
    height: '100%',
    width: '100%',
  },
  horizontalLoader: {
    width: '100%',
    position: 'absolute',
    zIndex: 20,
    elevation: 3,
  },
});

const mapStateToProps = (state, newProps) => {
  const { viewState: activityListingViewState } = state.travelTidbitsReducer || {};
  const { isActivityDetailFirstPage } = newProps || {};
  const showHorizontalLoader =
    activityListingViewState === VIEW_STATE.LOADING && !isActivityDetailFirstPage;
  return { showHorizontalLoader };
};

export default connect(mapStateToProps)(ActivityDetailPageComponent);
