import React from 'react';
import { isEmpty } from 'lodash';
import { Text, View, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { RESIZE_MODE_IMAGE } from '../../../../../HolidayConstants';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { actionStyle } from '../../../DayPlan/dayPlanStyles';
import { htmlTextBaseStyles, htmlTextSmallStyles } from '../../dayPlanV2Styles';
import {
  IMAGE_ICON_KEYS,
  getOpitimsedImageUrl,
} from '../../../../../Common/Components/HolidayImageUrls';
import { holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';

/*  Components */
import ItineraryUnitInformation from '../../Common/ItineraryUnitInformation';
import { DashedSeperator, DotSeperator } from '../../Common/Seperators';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import ReadMoreReadLessHTMLText from 'mobile-holidays-react-native/src/Common/Components/ReadMoreLessHTMLText';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { PRESALES_MIMA_DETAIL_PAGE } from 'mobile-holidays-react-native/src/MimaPreSales/utils/PreSalesMimaConstants';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../../../utils/HolidayPDTConstants';

function calculateHourDifference(startTime, endTime) {
  const start = new Date(`2000-01-01 ${startTime}`);
  const end = new Date(`2000-01-01 ${endTime}`);
  const diff = Math.abs(end - start);
  const hours = Math.floor(diff / 1000 / 60 / 60);
  return hours;
}

export const getTimeSlotDuration = (startTime = '', endtime = '') => {
  const startTimeArr = startTime.split(':');
  const EndTimeArr = endtime.split(':');
  const minutesCalc = Number(EndTimeArr[1]) - Number(startTimeArr[1]);
  const hoursCalc = Number(EndTimeArr[0]) - Number(startTimeArr[0]);
  return (minutesCalc + hoursCalc * 60) / 60 > 1
    ? ((minutesCalc + hoursCalc * 60) / 60).toFixed(1)
    : (minutesCalc + hoursCalc * 60) / 60;
};
export const ActivityBasicDetail = ({ name, shortDescription, onViewDetail = () => {}, info }) => {

  const captureClickEvents = ({eventName = '', prop1 = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName, 
      prop1,
    })
  }

  return (
    <View style={activityBasicDetailStyles.activityMajorDetails}>
      <Text style={activityBasicDetailStyles.activityName} numberOfLines={2}>
        {name}
      </Text>
        <ReadMoreReadLessHTMLText
          textValue={shortDescription}
          textStyle={htmlTextSmallStyles}
          limit={2}
          anchorStyle={htmlTextSmallStyles.actionStyle}
          containerStyle={activityBasicDetailStyles.descriptionText}
          handleOnClick={onViewDetail}
          captureClickEvents={captureClickEvents}
        />
      <ItineraryUnitInformation info={info} />
    </View>
  );
};

/* Generic Component being used in Base Detail, Activity Detail */

export const ActivityRatePlanSlotInfo = ({
  slotDetails,
  onSlotChangeClick,
  selectedSlotKey,
  day = '',
  city = '',
  component = '',
  packageDetailDTO = {},
}) => {
  if (isEmpty(slotDetails)) {
    return [];
  }

  const { changeCtaText = '', textDetails = {}, slots = [] } = slotDetails || {};
  const selectedTimeSlot = selectedSlotKey
    ? slots?.filter((slot) => {
        return slot.slotRecheckKey === selectedSlotKey;
      })?.[0] || {}
    : slotDetails?.timeslot || {};

  const {
    startTime,
    endTime,
    slotDurationHour = getTimeSlotDuration(startTime, endTime),
  } = selectedTimeSlot || {};

  const {
    text = '',
    backgroundColorCode = holidayColors.fadedGreen,
    textColor = holidayColors.green,
  } = textDetails || {};

  const handleChangeClick = () => {
    onSlotChangeClick();
  };
  return (
    <View style={activityRatePlanInfoStyles.container}>
      <View style={activityRatePlanInfoStyles.slotContainer}>
        <HolidayImageHolder
          imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.CLOCK)}
          style={activityRatePlanInfoStyles.iconStyle}
        />
        <View>
          <View style={activityRatePlanInfoStyles.slotTextContainer}>
            <Text style={activityRatePlanInfoStyles.slotTimeText}>Time Slot</Text>
            <DotSeperator style={activityRatePlanInfoStyles.dotStyle} />
            {!!startTime && !!endTime && (
              <Text style={activityRatePlanInfoStyles.slotTimeTextTime} numberOfLines={1}>
                {`${startTime} - ${endTime} ${
                  slotDurationHour ? `(${slotDurationHour} hour)` : ''
                }`}
              </Text>
            )}
          </View>
          {!!text && (
            <View
              style={[
                activityRatePlanInfoStyles.slotsAvailableContainer,
                { backgroundColor: holidayColors.fadedGreen },
              ]}
            >
              <Text style={[activityRatePlanInfoStyles.slotsAvailableText, , { color: textColor }]}>
                {text}
              </Text>
            </View>
          )}
        </View>
        {packageDetailDTO?.pageName !== PRESALES_MIMA_DETAIL_PAGE && (
          <TouchableOpacity onPress={handleChangeClick} style={{ marginLeft: 'auto' }}>
            <Text style={activityRatePlanInfoStyles.changeTextCta}>{changeCtaText}</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export const ActivityRatePlanInfo = ({
  ratePlanInfo = {},
  onViewDetail = () => {},
  onSlotChangeClick,
  day = '',
  city = '',
  component = '',
  packageDetailDTO = {},
  unavailable = false,
}) => {
  const {
    name = '',
    slotDetails = {},
    otherInfos = [],
    moreOptionsCtaText = '',
  } = ratePlanInfo || {};
  const handleMoreOptions = () => {
    onViewDetail(true);
  };
  return (
    <View>
        <View style={[activityBasicDetailStyles.opacityContainer, unavailable && activityBasicDetailStyles.opacityReduced]}>
        {!!name && (
        <Text style={activityRatePlanInfoStyles.name} numberOfLines={2}>
          {name}
        </Text>
      )}
      <ActivityRatePlanSlotInfo
        slotDetails={slotDetails}
        onSlotChangeClick={onSlotChangeClick}
        day={day}
        city={city}
        component={component}
        packageDetailDTO = {packageDetailDTO}
      />
      {!isEmpty(slotDetails) && !isEmpty(otherInfos) && (
        <DashedSeperator marginSpacing={[marginStyles.mt8, marginStyles.mb4]} />
      )}
      <ItineraryUnitInformation info={otherInfos} />
      </View>
      {!!moreOptionsCtaText && packageDetailDTO?.pageName !== PRESALES_MIMA_DETAIL_PAGE && (
        <TouchableOpacity onPress={handleMoreOptions}>
          <Text style={activityRatePlanInfoStyles.moreOptionsTextCta}>{moreOptionsCtaText}</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const activityRatePlanInfoStyles = StyleSheet.create({
  container: {
    ...marginStyles.mt4,
  },
  name: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  iconStyle: {
    width: 14,
    height: 14,
    ...marginStyles.mr4,
    ...marginStyles.mt2,
    tintColor: holidayColors.gray,
  },
  slotContainer: {
    flexDirection: 'row',
    ...marginStyles.mt6,
    alignItems: 'base-line',
  },
  slotTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  slotTimeText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    alignItems: 'center',
  },
  slotTimeTextTime: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    alignItems: 'center',
    width: '65%',
    // flex: 1,
  },
  dotStyle: {
    ...marginStyles.mh6,
    ...marginStyles.mt2,
  },
  changeTextCta: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    marginLeft: 'auto',
  },
  moreOptionsTextCta: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  slotsAvailableContainer: {
    ...paddingStyles.pa6,
    ...marginStyles.mt2,
    alignSelf: 'flex-start',
    ...holidayBorderRadius.borderRadius4,
  },
  slotsAvailableText: {
    ...fontStyles.labelSmallBold,
  },
});

const activityBasicDetailStyles = StyleSheet.create({
  activityName: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
    marginBottom: 8,
    lineHeight: 22,
  },
  activityMajorDetails: {
    flex: 1,
    ...marginStyles.mr16,
  },
  descriptionText: {
    ...marginStyles.mb4,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pv2,
  },
  infoIconUrl: {
    width: 14,
    height: 14,
    ...marginStyles.mr4,
  },
  infoText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  opacityContainer: {
    opacity: 1,
  },
  opacityReduced: {
    opacity: 0.5,
  },
});
