import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import { sightSeeingCities } from '../../Utils/PhoenixDetailUtils';
import {OVERLAY_CAROUSAL_POSITION} from '../../Utils/PheonixDetailPageConstants';
import {
  getGoogleAPIKeyForAllPlarforms,
  getStaticMapUriForCoordinatesList,
  isRawClient,
} from '../../../utils/HolidayUtils';
import { isNumber, isEmpty } from 'lodash';
import { itineraryUnitTypes } from '../../DetailConstants';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { getEnableCarouselViewDetail } from '../../../utils/HolidaysPokusUtils';
import { actionStyle, dayPlanRowContainerStyle, dayPlanRowHeadingStyles, dayPlanRowImage } from './dayPlanStyles';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { sectionHeaderSpacing } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';
import { holidayNavigationPush } from '../../Utils/DetailPageNavigationUtils';
import { Overlay } from '../DetailOverlays/OverlayConstants';
import ItineraryUnitExtraInfoMessages from '../ItineraryUnitExtraInfoMessages';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';


export default class SightSeeingRow extends BasePage {

  constructor (props) {
    super(props);
    this.state = {
      googleAPIKey: '',
    };
  }

  async componentDidMount() {
    let googleAPIKey = await getGoogleAPIKeyForAllPlarforms();
    this.setState({
      googleAPIKey: googleAPIKey,
    });
  }

  onViewDetail = () => {
    const {sellableId} =  this.props.itineraryUnit || {};
    const showCarouselView = this?.props?.fromPresales ?  false : getEnableCarouselViewDetail();
    showCarouselView ? this.props.onViewDetailPress(OVERLAY_CAROUSAL_POSITION.SIGHTSEEING, sellableId, this.props.day, HOLIDAY_ROUTE_KEYS.DETAIL, itineraryUnitTypes.ACTIVITY) :
      this.openSightSeeingDetail({data: this.props.itineraryUnit}, this.props.subtitleData, this.props.packageDetailDTO);
  }

  captureClickEvents = ({eventName = '', suffix = ''}) =>{
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: `base:${itineraryUnitTypes.ACTIVITY}`,
    })
  }

  openSightSeeingDetail = (item, subtitleData, packageDetailDTO) => {
    const { carExtraInfo } =  this.props.itineraryUnit || {};
     this.captureClickEvents({
      eventName: 'view_',
      suffix: `${itineraryUnitTypes.ACTIVITY}_${this.props.destinationName}_${this.props.day}`,
    });
    holidayNavigationPush(
      {
        pageKey:HOLIDAY_ROUTE_KEYS.SIGHTSEEING_DETAIL,
        overlayKey:Overlay.SIGHTSEEING_DETAIL_PAGE,
        props: {item, subtitleData, packageDetailDTO, carExtraInfo},
        showOverlay:this.props.showOverlay,
        hideOverlays:this.props.hideOverlays,
      } );
  }

  render() {
    let duration = 0;
    let coordinateList = [];
    let locationName = [];
    const { locationImageUrl, carExtraInfo } =  this.props.itineraryUnit || {};
    const citySet = new Set();
    let markers = [];
    let imageSight = '';
    let imageLocationAvailable = !isEmpty(locationImageUrl);
    this.props.itineraryUnit.locations.forEach((item, index) => {
      duration += item.durationInHours;
      if (isNumber(item.latitude) && isNumber(item.longitude)) {
        coordinateList.push({lat: item.latitude,lon: item.longitude});
        markers.push( {
          title: item.name,
          coordinates: {
            latitude: parseFloat(item.latitude),
            longitude: parseFloat(item.longitude),
          },
        });
      } else if (isEmpty(imageSight) && item?.images?.length > 0) {
        imageSight = item?.images[0].path;
      }

      locationName.push('\u2022  ' + item.name);
      citySet.add(item.cityName);
    });

    const handlePress = () => {
      if(isRawClient())return ;
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.PHOENIX_DETAIL_MAP_PAGE, {
        markers,
        name: 'Sightseeing in ' + sightSeeingCities(citySet),
      });
    };
    const imageUrl = imageLocationAvailable
      ? locationImageUrl
      : getStaticMapUriForCoordinatesList(coordinateList, false, this.state.googleAPIKey);
    return (
      <View style={styles.activityRow}>
        <Text style={[dayPlanRowHeadingStyles.heading, sectionHeaderSpacing]}>
          Sightseeing in { sightSeeingCities(citySet) }
          <Text style={dayPlanRowHeadingStyles.bold}> {duration} {duration > 1 ? 'Hours' : 'Hour'}</Text>
        </Text>
        <View style={styles.activityDetails}>
        {imageLocationAvailable &&
              <TouchableOpacity>
                <View style={styles.thumbWrapper}>
                  <Image source={{uri: locationImageUrl}}
                         style={styles.activityImg}/>
                </View>
              </TouchableOpacity>
          }
          {!imageLocationAvailable && coordinateList && coordinateList.length > 0 && (
            <TouchableOpacity onPress={handlePress}>
              <View style={styles.thumbWrapper}>
                <Image
                  source={{
                    uri: imageUrl,
                  }}
                  style={styles.activityImg}
                />
              </View>
            </TouchableOpacity>
          )}
          {/* If no co-ordinates available set image on our own */}
          {!imageLocationAvailable && !(coordinateList && coordinateList.length > 0)  && !isEmpty(imageSight) &&
            <View style={styles.thumbWrapper}>
              <Image source={{uri: imageSight}}
                     style={styles.activityImg}/>
            </View>
          }
          <View style={styles.columnView}>
            {locationName.slice(0, 5).map((item, index) => {
                return <Text style={styles.locationStyle} key={index}>{item}</Text>;
            })}
            {locationName.length > 5 &&
            <TouchableOpacity
              onPress={() => this.onViewDetail()}>
              <Text style={styles.anchorTextSm}>+ {locationName.length - 5} More</Text>
            </TouchableOpacity>
            }
          </View>
        </View>
        <ItineraryUnitExtraInfoMessages extraInfo={carExtraInfo}/>
        <View style={styles.footer}>
          <TouchableOpacity
            onPress={() => this.onViewDetail()}>
            <Text style={actionStyle}>View Details</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  activityRow: {
    ...dayPlanRowContainerStyle,
  },
  heading: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: '#4a4a4a',
    marginBottom: 10,
  },
  bold: {
    fontFamily: fonts.bold,
    color: '#000000',
  },
  ratingTag: {
    height: 18,
    borderRadius: 4,
    paddingHorizontal: 5,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 4,
  },
  rating: {
    fontSize: 12,
    fontFamily: fonts.black,
    color: '#ffffff',
  },
  ratingSmText: {
    fontSize: 10,
    fontFamily: fonts.regular,
    color: '#ffffff',
  },
  activityTag: {
    backgroundColor: '#d7fae2',
    height: 18,
    borderRadius: 2,
    paddingHorizontal: 10,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-start',
    marginBottom: 7,
  },
  activityTagText: {
    fontSize: 11,
    fontFamily: fonts.bold,
    color: '#219393',
  },
  subHeading: {
    fontSize: 14,
    fontFamily: fonts.black,
    color: '#000000',
    marginBottom: 8,
  },
  subHeadingSm: {
    fontFamily: fonts.bold,
    color: '#4a4a4a',
    fontSize: 12,
  },
  activityDetails: {
    flexDirection: 'row',
  },
  thumbWrapper: {
    width: 100,
    height: 68,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
    borderRadius: 4,
    overflow: 'hidden',
  },
  activityImg: {
    width: 100,
    height: 68,
    ...dayPlanRowImage,
  },
  activityMajorDetails: {
    flex: 1,
    flexDirection: 'row',
  },
  dateText: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: '#4a4a4a',
    marginBottom: 4,
  },
  labelText: {
    fontSize: 12,
    fontFamily: fonts.regular,
    color: '#9b9b9b',
    marginBottom: 4,
    minWidth: 90,
  },
  valueText: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: '#4a4a4a',
    marginBottom: 4,
  },
  valueTextSm: {
    fontSize: 10,
    fontFamily: fonts.regular,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 12,
  },
  anchorTextSm: {
    marginTop: 10,
    fontSize: 12,
    fontFamily: fonts.bold,
    color: '#008cff',
  },
  columnView: {
    flex: 1,
    flexDirection: 'column',
  },

  locationStyle : {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
    marginTop: 5,
  },
});
