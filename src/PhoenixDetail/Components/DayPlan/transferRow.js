
import React, { useState } from 'react';
import { Image, StyleSheet, Text,  View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import {getCarObject, getPackageDestinations, getSellableIdDataForTransfers,} from '../../Utils/PhoenixDetailUtils';
import {
  HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE,
  OVERLAY_CAROUSAL_POSITION,
} from '../../Utils/PheonixDetailPageConstants';
import { has, isArray, isEmpty } from 'lodash';
import {Overlay} from '../DetailOverlays/OverlayConstants';
import {
  componentImageTypes,
  itineraryUnitTypes,
  packageActionComponent,
  packageActions,
} from '../../DetailConstants';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import RemovalConfirmation from './RemovalConfirmation';
import CabIcon from '@mmt/legacy-assets/src/holidays/cab.webp';
import {HOLIDAY_ROUTE_KEYS, HolidayNavigation} from '../../../Navigation';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import { getEnableCarouselViewDetail } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';
import { actionSeperator, actionStyle, dayPlanRowContainerStyle, dayPlanRowHeadingStyles, dayPlanRowImage } from './dayPlanStyles';
import { sectionHeaderSpacing } from '../../../Styles/holidaySpacing';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { marginStyles } from "../../../Styles/Spacing";
import { isMobileClient } from '../../../utils/HolidayUtils';
import { holidayNavigationPush } from '../../Utils/DetailPageNavigationUtils';import TextButton from '../../../Common/Components/Buttons/TextButton';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { capitalizeText } from '../../../../src/utils/textTransformUtil';

const TransferRow = (props) => {
  const {
    day,
    packageDetail,
    itineraryUnit,
    accessRestriction,
    onViewDetailPress,
    onComponentChange,
    roomDetails,
    packageDetailDTO,
    destinationName,
    showOverlay,
    lastPageName,
    fromPresales = false,
    detailData,
    showBottomDivider = true,
  } = props || {};

  let imageUrl = '';
  let vchlModel = '';
  let privateText = '';
  let vchlName = '';
  let seatCpcty = 0;
  let category = '';
  let facilities = [];
  let sellableId = '';

  const [isModalVisisble, setModalVisible] = useState(false);
  const transferRemoval = {
    title:'Removing this Transfer?',
    content:'All road transfers including sightseeing will be removed from this package.',
    icon:CabIcon,
    onContinue:'YES, REMOVE',
    cancel:'DON\'T REMOVE',
  };
  const { carItineraryDetail, transferDetail } = packageDetail || {};
  const { itineraryUnitType, itineraryUnitSubType, text, shortText,shortTextnew, cityId, car = {}, isLandOnly, landOnlyDescription} = itineraryUnit || {};
  const { sellableId: slbleId, journey, privateCar, onlyTransfer, seating, carType, inclusionText } = car || {};

  sellableId = slbleId;
  const { changeTransferRestricted = false, removeTransferRestricted = false } = accessRestriction || {};

  const item = getSellableIdDataForTransfers(car, carItineraryDetail, transferDetail);
  if (!item) {
    return [];
  } else {
    item.day = day;
  }

  const populateDataFromVehicleInfo = (vehicleInfo, imageDetail) => {
    const { model, privateOrShared, vehicleName, maxPaxCapacity, vehicleCategory, facilities: fcltes, imageUrl: imgUrl } = vehicleInfo || {};
    vchlModel = model;
    privateText = privateOrShared;
    vchlName = vehicleName;
    seatCpcty = maxPaxCapacity;
    category = vehicleCategory;
    facilities = fcltes;

    if (!isEmpty(imgUrl)) {
      imageUrl = imgUrl;
    } else if (imageDetail && has(imageDetail, 'images')) {
      //Handle fallback
      const { images } = imageDetail || [];
      if (isArray(images) && images.length > 0 && !isEmpty(images[0].path)) {
        imageUrl = images[0].path;
      }
    }
  };

  if (onlyTransfer) {
    const { privateTransfer, defaultSelection, groupTransfer } = item || {};
    if (defaultSelection === 'SHARED' && groupTransfer) {
      const { vehicleInfo, sellableId: slbleId, imageDetail } = groupTransfer || {};
      sellableId = slbleId;
      populateDataFromVehicleInfo(vehicleInfo, imageDetail);
    } else if (privateTransfer) {
      const { vehicleInfo, sellableId: slbleId, imageDetail } = privateTransfer || {};
      sellableId = slbleId;
      populateDataFromVehicleInfo(vehicleInfo, imageDetail);
    }
    privateText = defaultSelection;
  } else {
    const { vehicleInfo } = item || {};
    populateDataFromVehicleInfo(vehicleInfo);
  }

  const getCityCountryName = (code, destinations) => {
    const city = destinations.find(destination => destination.cityId === code);
    return city ? city.name : false;
  };

  const onViewDetail = () => {
    const {onlyTransfer} = car || {};
    const temp = getCarObject(carItineraryDetail,transferDetail, slbleId, onlyTransfer);
    const data = {
      data: temp,
      day,
      text,
      cityId,
      shortText,
      type: itineraryUnitType,
      extraData: car,
      ...car,
    };
    data.data.sellableId = slbleId;
    if (onlyTransfer) {
      data.transferObj = {...temp.privateTransfer};
    } else {
      data.commute = {...temp};
    }
    const showCarouselView = fromPresales ? false : getEnableCarouselViewDetail();
    const fromPage = HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.BASE_DETAIL_PAGE;
    showCarouselView ? onViewDetailPress(OVERLAY_CAROUSAL_POSITION.TRANSFERS, sellableId, day, lastPageName, itineraryUnitTypes.TRANSFERS) :
      openTransferDetailPage(data, item.day, fromPage, lastPageName, `view_${itineraryUnitTypes.TRANSFERS}_${destinationName}_${day}`);
  };

  const captureClickEvents = ({ eventName = '', suffix = ''}) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g,'|'),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: `base:${itineraryUnitTypes.TRANSFERS}`,
    })
  }

  const openTransferDetailPage = (item, day, fromPage, lastPageName, eventName) => {
    captureClickEvents({ eventName });

    const params = {
      item,
      index: 0,
      packageDetailDTO,
      roomDetails,
      onComponentChange,
      accessRestriction,
      day,
      fromPage,
      lastPageName,
    };
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.TRANSFER_DETAIL,
      overlayKey: Overlay.TRANSFER_DETAIL_PAGE,
      props: params,
      hideOverlays: props.hideOverlays,
      showOverlay,
    });
  };

  const openTransferListing = () => {
    const item = getDataForListing();
    captureClickEvents({
      eventName: 'change_',
      suffix: `${itineraryUnitTypes.TRANSFERS}_${destinationName}_${day}`,
    });
    const transferListingPageProps = {
      item,
      roomDetails,
      packageDetailDTO,
      onComponentChange,
      accessRestriction,
      lastPageName: HOLIDAY_ROUTE_KEYS.DETAIL,
      fromPage: HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.BASE_DETAIL_PAGE,
      detailData,
    };
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.TRANSFER_LISTING,
      overlayKey: Overlay.TRANSFER_LISTING_PAGE,
      props: transferListingPageProps,
      hideOverlays: props.hideOverlays,
      showOverlay,
    });
  };

  const removeTransferCard = () => {
    captureClickEvents({
      eventName: 'remove_',
      suffix: `${itineraryUnitTypes.TRANSFERS}_${day}_confirm_${destinationName}`,
    });
    removeTransfer(item, onlyTransfer, onComponentChange);
  };
  const removeTransferCardConfirmation = () => {
    setModalVisible(true);
    captureClickEvents({
      eventName: 'remove_',
      suffix: `${itineraryUnitTypes.TRANSFERS}_${day}_click_${destinationName}`,
    });
  };
  const hideTransferModal = () =>{
    setModalVisible(false);
  };
  const getDataForListing = () => {
    let obj = {};
    obj.day = day;
    obj.text = text;
    obj.cityId = cityId;
    obj.shortText = shortText;
    const carSellableId = car ? car.sellableId : '';
    const onlyTransfer = car ? car.onlyTransfer : false;
    obj.data = getCarObject(carItineraryDetail, transferDetail, carSellableId, onlyTransfer);
    obj.data.sellableId = carSellableId;
    obj.extraData = car ? car : null;
    return obj;
  };

  const cityName = getCityCountryName(cityId, getPackageDestinations(packageDetail));
  const borderColor = showBottomDivider ? holidayColors.grayBorder : holidayColors.white;
  return (
    <View style={{width:'100%'}}>
      <View style={[styles.transferRow, {borderColor,width:'100%'}]}>
        <View style={sectionHeaderSpacing}>
          {!isEmpty(shortText) && !isLandOnly && (
            <Text style={dayPlanRowHeadingStyles.heading}>
              Transfer from
              <Text style={dayPlanRowHeadingStyles.bold}> {shortText} </Text>
            </Text>
          )}
          {!isEmpty(shortText) && isLandOnly && (
            <Text style={dayPlanRowHeadingStyles.heading}>
              <Text style={dayPlanRowHeadingStyles.bold}> {shortText} </Text>
            </Text>
          )}
          {isEmpty(shortText) && !isEmpty(text) && <Text style={dayPlanRowHeadingStyles.bold}>{text}</Text>}

          <Text style={styles.subHeading}>
            {privateText ? `${capitalizeText(privateText)} Transfer` : ''}
            {!!vchlModel && (
              <Text style={styles.subHeadingSm}>
                {privateText && vchlModel ? <Text style={actionSeperator}> | </Text> : ''}
                {capitalizeText(vchlModel)}
                <Text> or similar</Text>
              </Text>
            )}
          </Text>
        </View>
        <View style={styles.transferDetails}>
          <Image source={{uri: imageUrl}} style={styles.transferIcon} />
          <Facilities facilities={facilities} inclusionText={inclusionText} />
        </View>
        {isLandOnly && !isEmpty(landOnlyDescription) && (
          <Text style={styles.subHeadingSm}>*{landOnlyDescription}</Text>
        )}
        <View style={styles.footer}>
          <View style={AtomicCss.alignCenter}>
        {(!removeTransferRestricted || !changeTransferRestricted) && <DynamicCoachMark cueStepKey="changeOrRemove" offsetHeight = {70} offsetWidth = {70} extraInfo={{from: 'transferRow', day}}>
          <View style={AtomicCss.flexRow}>
            {!removeTransferRestricted &&
              <TextButton
                buttonText="Remove"
                handleClick={() => removeTransferCardConfirmation()}
                btnTextStyle={actionStyle}
              />
              }
              {!removeTransferRestricted && !changeTransferRestricted && (
                    <Text style={actionSeperator}>|</Text>
              )}
            {!changeTransferRestricted &&
              <TextButton
                buttonText="Change"
                handleClick={() => openTransferListing()}
                btnTextStyle={actionStyle}
              />
              }
          </View>
          </DynamicCoachMark>}
          </View>
          <TextButton
            buttonText="View Details"
            handleClick={() => onViewDetail()}
            btnTextStyle={actionStyle}
          />
        </View>
      </View>
      {isModalVisisble && <RemovalConfirmation hideModal={hideTransferModal} removeTransferCard={removeTransferCard} {...transferRemoval}/>
      }
    </View>
  );
};

const removeTransfer = (item, onlyTransfer, onComponentChange) => {
  const actionData = {};
  //onComponentChange
  if (onlyTransfer) {
    actionData.transferSelectionType = 'NONE';
    actionData.transferSequence = item.transferSequence;
    actionData.packageComponent = packageActionComponent.TRANSFER;
    actionData.action = packageActions.CHANGE;

  } else {
    actionData.sellableId = item.sellableId;
    actionData.startDay = item.startDay;
    actionData.resultSellableId = item.sellableId;
    actionData.packageComponent = packageActionComponent.CAR;
    actionData.action = packageActions.TOGGLE;
  }
  onComponentChange(actionData, componentImageTypes.TRANSFERS);
};

const Facilities = ({ facilities, inclusionText }) => {
  const MAX_FACILITY_COUNT = 3;
  if (!facilities || facilities.length === 0) {
    return [];
  }

  const facilityView = [];
  facilities.forEach((facility, index) => {
    const { title } = facility || {};  // type will ve used to fetch ICON
    if (facilityView.length < MAX_FACILITY_COUNT && title) {
      facilityView.push(
        <Text style={styles.detailText}>
          {title}
          {facilityView.length === MAX_FACILITY_COUNT - 1 || facilities.length - 1 === index ? (
            ''
          ) : (
            <Text style={actionSeperator}> | </Text>
          )}
        </Text>,
      );
    }
  });

  return (
    <View style={styles.transferMajorDetails}>
      <View style={{justifyContent:'center', alignContent:'center', flex:1}}>
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>{facilityView}</View>
        {!isEmpty(inclusionText) && (
          <Text style={[styles.detailText, marginStyles.mt4]}>
            <Text style={{...fontStyles.labelSmallRegular}}>{inclusionText}</Text>
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  transferRow: {
    ...dayPlanRowContainerStyle,
  },
  subHeading: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
  },
  subHeadingSm: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
  },
  transferDetails: {
    flexDirection: 'row',
  },
  thumbWrapper: {
    width: 100,
    height: 58,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  transferIcon: {
    width: 85,
    height: 89,
    marginRight: 10,
    resizeMode: 'contain',
    ...dayPlanRowImage,
    borderWidth:0.5,
    borderColor:holidayColors.grayBorder,
  },
  transferMajorDetails: {
    flex: 1,
    flexDirection: 'row',
  },
  detailText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
    marginBottom: 4,
  },
  detailTextSm: {
    fontSize: 10,
    fontFamily: fonts.regular,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },
});

export default TransferRow;
