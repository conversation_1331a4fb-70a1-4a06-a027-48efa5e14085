import React, { useState } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  ItineraryUnit,
  HotelDetail,
  HotelRowCardData, ComponentAccessRestriction,
} from '../../Types/PackageDetailApiTypes';
import { getAllInclusions, getHotelData, getHotelObject, getIconForItineraryUnitType } from '../../Utils/PhoenixDetailUtils';
import { HtmlHeading } from './HtmlHeading';
import blackStar from '@mmt/legacy-assets/src/star.webp';
import taLogo from '@mmt/legacy-assets/src/tripAdvisorLogo.webp';
import greyStar from '@mmt/legacy-assets/src/greyStar.webp';
import mySafeImage from '../images/my-safety.png';
import {
  HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE,
  MAX_STAR_COUNT,
  OVERLAY_CAROUSAL_POSITION,
} from '../../Utils/PheonixDetailPageConstants';
import { openChangeHotelFromPhoenixPage } from '../../Utils/HolidayDetailUtils';
import { itineraryUnitTypes } from '../../DetailConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import BottomSheetOverlay from '../../../Common/Components/BottomSheetOverlay';
import { getImagePath } from '@mmt/legacy-commons/Common/utils/postSalesCommonUtils';
import { isEmpty } from '@mmt/core/helpers/dataHelpers';
import { getEnableCarouselViewDetail } from '../../../utils/HolidaysPokusUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { actionStyle, dayPlanRowContainerStyle, dayPlanRowHeadingStyles, dayPlanRowImage } from './dayPlanStyles';
import { sectionHeaderSpacing } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { isMobileClient } from '../../../utils/HolidayUtils';
import {  getScreenWidth  } from '../../../utils/HolidayUtils';

/* Components */
import { Overlay } from '../DetailOverlays/OverlayConstants';
import HtmlHeadingV2 from 'mobile-holidays-react-native/src/Common/Components/HTML/V2';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import BottomSheet from '../BottomSheet/BottomSheet';
import UserRatingCommon from '../../../Common/Components/UserRatingCommon';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const WHITE_TICK_ICON = getImagePath('checkmark.webp');

interface HotelRowProps {
  hotelDetail: Map<string, HotelDetail>;
  itineraryUnit: ItineraryUnit;
  accessRestriction: ComponentAccessRestriction | null | undefined;
  roomDetails: any;
  packageDetailDTO: any;
  onComponentChange: any;
  onViewDetailPress: (pos: number, packageDetailDTO: any, day: any, lastPageName: string, lob: string) => void;
  failedHotels: any
  destinationName: string | null;
  lastPageName: string;
  showOverlay: (key: string, data: any) => {},
  hideOverlays: () => {}
}

const HotelRow = ({ failedHotels, day, bundled, hotelDetail, roomDetails, itineraryUnit, accessRestriction, onViewDetailPress, packageDetailDTO,
  onComponentChange, destinationName, showOverlay,hideOverlays,lastPageName, detailData, fromPresales = false, packageDetail }: HotelRowProps) => {
  const [modalVisibility, setModalVisibility] = useState(false);
  const { text, hotel, itineraryUnitType, itineraryUnitSubType }: ItineraryUnit = itineraryUnit;
  const hotelSellableId: string = (hotel && hotel.hotelSellableId === undefined) ? '' : hotel ? hotel.hotelSellableId : '';
  const changeHotelRestricted: boolean = accessRestriction ? accessRestriction.changeHotelRestricted : false;
  const icon = getIconForItineraryUnitType(itineraryUnitType, itineraryUnitSubType);

  let isHotelUnavailable = false;
  for (let i = 0; i < failedHotels.length; i++) {
    if (failedHotels[i].sellableId === hotelSellableId) {
      isHotelUnavailable = true;
      break;
    }
  }

  const captureClickEvents = ({ eventName = '', suffix = '', prop1 = '', value = '' }) => {
    const finalValue = eventName + suffix ;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value || finalValue.replace(/_/g, '|'),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1: `base:${itineraryUnitTypes.HOTEL}`,
      suffix,
    })

  }
  const openHotelListing = (hotelObject: HotelDetail | null | undefined) => {
    captureClickEvents({
      eventName: 'change_',
      suffix: `${itineraryUnitTypes.HOTEL}_${destinationName}_${day}`,
    });
    openChangeHotelFromPhoenixPage(
        hotelObject,
        packageDetailDTO,
        roomDetails,
        onComponentChange,
        lastPageName,
        '',
        isHotelUnavailable ? hotelSellableId : null,
        showOverlay,
        hideOverlays,
        detailData,
    );
  };

  const onViewAllInclusionsClicked = () => {
    setModalVisibility(true);
    captureClickEvents({ eventName: 'View_All_Inclusions', value : 'View|All_Inclusions' });
  };

  const onViewDetail = (hotelObject: HotelDetail | null | undefined) => {
    const showCarouselView = fromPresales ? false : getEnableCarouselViewDetail();
    showCarouselView ?
      onViewDetailPress(OVERLAY_CAROUSAL_POSITION.HOTEL, hotelSellableId, day, lastPageName, itineraryUnitTypes.HOTEL)
      : openHotelDetailPage(hotelObject, false);
  };

  const openHotelDetailPage = (hotelObject: HotelDetail | null | undefined, changeRoomPress = false) => {
//todo ashish handle omniture tracking
    /*    changeRoomPress
      ? captureClickEvents({
        eventName: 'change_room_',
        suffix: `${day}_${destinationName}`,
      })
      : captureClickEvents({
        eventName: 'view_',
        suffix: `${itineraryUnitTypes.HOTEL}_${destinationName}_${day}`,
      });*/

    const changeRoomProps = {
      hotel: hotelObject,
      roomDetails,
      fromPage: HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.BASE_DETAIL_PAGE,
      onComponentChange,
      changeHotelRestricted,
      scrollToRoomType: changeRoomPress,

      /*This prop is required to handle a case when user lands from hotel listing to detail page by selecting a different
hotel. The room type code of new hotel room will be compared with the room type code of preselected hotel in order to
show update button on price bar. In case when user lands to this page from detail or overlay page, both the props
hotel and preselected hotel can be same.*/
      preSelectedHotel: hotelObject,
      lastPageName: lastPageName,
      packageDetailDTO: packageDetailDTO,
      bundled: hotelObject?.bundled,
      failedHotelSellableId: isHotelUnavailable ? hotelSellableId : null,
      detailData: detailData,
      hideOverlays,
      showOverlay,
    };

    isMobileClient()
        ? HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_DETAIL, changeRoomProps)
        : showOverlay(Overlay.HOTEL_DETAIL_PAGE_FULL_SCREEN,changeRoomProps);
  };

  const getOrSimilarTextView = () => {
    return (<Text style={styles.OrSimilar}>{' (or Similar)'}</Text>);
  };

  const renderHotelCard = (hotelObject) => {
    if (hotelObject) {
      const NO_MEAL = 'NO_MEAL';
      const hotelData: HotelRowCardData = getHotelData(hotelObject, false);
      const { metadataDetail } = packageDetail || {};
      const { safe } = metadataDetail || {};
      const { similarHotels, bundled, roomInformation, hotel, ratePlan } = hotelData || {};
      const { mealName, mealsIncluded } = ratePlan || {};
      const mealType = mealsIncluded?.[0] || '';
      const { locationInfo, taInfo, mmtRatingInfo, ratingType } = hotel || {};
      let userRatinginfo = mmtRatingInfo || {};
      let isMMTRating = true;
      if (!!ratingType && ratingType === 'TA' && !isEmpty(taInfo)) {
        userRatinginfo = taInfo;
        isMMTRating = false;
      }
      const { userRating } = userRatinginfo;


      const MAX_USER_RATING = 5;
      const { areaName, pointOfInterest } = locationInfo || {};
      const { inclusions, inclusionsHighlighted } = roomInformation || {};
      const allInclusions = getAllInclusions(inclusionsHighlighted, inclusions);
      const starImages = [];
      const MAX_INCLUSION_TO_SHOW = 2;
      for (let i = 0; i < MAX_STAR_COUNT; i++) {
        starImages.push(i < hotelData.rating ? blackStar : greyStar);
      }
      return (
        <>
          {safe && (
            <View style={styles.hotelSubHeadingContainer}>
              <Image style={styles.safeImageIcon} source={mySafeImage} />
            </View>
          )}
          <View style={styles.hotelDetails}>
            <View style={styles.thumbWrapper}>
              <PlaceholderImageView url={hotelData?.images[0]?.fullPath} style={styles.hotelImg} />
              <View style={styles.ratingContainerStyle}>
                {!!userRating && userRating > 0 ? (
                  isMMTRating ? (
                    <UserRatingCommon
                      ratingValue={userRating}
                      containerStyle={styles.ratingContainer}
                    />
                  ) : (
                    <View style={styles.taContainer}>
                      <Image source={taLogo} style={styles.taLogo} />
                      <Text style={[styles.hotelTagText]}>
                        {userRating}/{' '}
                        <Text style={styles.maxHotelTagRating}>{MAX_USER_RATING}</Text>
                      </Text>
                    </View>
                  )
                ) : null}
              </View>
            </View>
            <View style={styles.hotelMajorDetails}>
              <View style={AtomicCss.flex1}>
                {hotelData.rating > 0 && (
                  <View style={styles.ratingStarWrapper}>
                    {starImages.map((item, index) => (
                      <Image key={index} style={styles.starRating} source={item} />
                    ))}
                  </View>
                )}
                <View style={styles.hotelNameContainer}>
                  <Text numberOfLines={2} style={styles.hotelName}>
                    {hotelData.name}{' '}
                    {((similarHotels && similarHotels.length > 0) || bundled) &&
                      getOrSimilarTextView()
                    }
                  </Text>
                  <Text style={styles.hotelAddress}>{areaName}</Text>
                  {pointOfInterest && <Text style={styles.poi}>{pointOfInterest}</Text>}
                  <View style={styles.dateContainer}>
                    <Text style={[styles.dateText]}>
                      {hotelData.chkInDate} - {hotelData.chkOutDate}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.footer}>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.flex1]}>
              {!changeHotelRestricted &&
                <TouchableOpacity onPress={() => openHotelListing(hotelObject)}>
                  <DynamicCoachMark cueStepKey="changeOrRemove" offsetHeight={70} offsetWidth={70} extraInfo={{ from: 'hotelRow', day }}>
                    <Text style={actionStyle}>Change</Text>
                  </DynamicCoachMark>
                </TouchableOpacity>}
              <TextButton
                buttonText={isHotelUnavailable ? 'Currently Unavailable ' : 'View Details'}
                handleClick={() => onViewDetail(hotelObject)}
                btnTextStyle={isHotelUnavailable ? styles.anchorTextUnavailable : actionStyle}
                disabled={isHotelUnavailable}
                btnWrapperStyle={AtomicCss.pushRight}
              />
            </View>
          </View>

          {!bundled && (
            <View style={styles.infoBox}>
              <Text numberOfLines={1} style={styles.infoTitle}>
                {hotelData.roomType}
              </Text>
              {mealName && (
                <Text numberOfLines={1} style={styles.mealPlan}>
                  <Text>Meal Plan: </Text>
                  {mealType === NO_MEAL ? 'No Meals' : mealName}
                </Text>
              )}

              {allInclusions && allInclusions.length > 0 && (
                <Text style={styles.roomRates}>Room Inclusions :</Text>
              )}
              {allInclusions &&
                allInclusions.length > 0 &&
                allInclusions.map((item, index) => {
                  if (index > MAX_INCLUSION_TO_SHOW - 1) {
                    return [];
                  }
                  const { description, color } = item || {};
                  return (
                    <View style={styles.inclusionsContainer} key={index}>
                      <Image
                        style={[styles.greenIconStyle, { tintColor: color }]}
                        source={WHITE_TICK_ICON}
                      />
                      <Text style={[styles.inclusions, { color: color }]}>{description}</Text>
                    </View>
                  );
                })}

              <View style={{ marginTop: 10, flexDirection: 'row', flex: 1 }}>
                {!isHotelUnavailable && !bundled && !changeHotelRestricted && (
                  <TextButton
                    buttonText="Change Room"
                    handleClick={() => openHotelDetailPage(hotelObject, true)}
                    btnTextStyle={actionStyle}
                  />
                )}

                {allInclusions && allInclusions.length > 2 && (
                  <TouchableOpacity
                    onPress={onViewAllInclusionsClicked}
                    style={{ marginLeft: 'auto' }}
                  >
                    <DynamicCoachMark
                      cueStepKey="changeOrRemove"
                      offsetHeight={70}
                      offsetWidth={70}
                      extraInfo={{ from: 'hotelRow', day }}
                    >
                      <Text style={actionStyle}>View All Inclusions</Text>
                    </DynamicCoachMark>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          )}

          {(modalVisibility && inclusions && inclusions.length > 0) ?
            <BottomSheetOverlay
              title="Room Inclusions"
              containerStyles={styles.containerStyles}
              headingContainerStyles={styles.headingContainerStyles}
              visible={modalVisibility}
              toggleModal={() => setModalVisibility(!modalVisibility)}
            >
              <PackageInclusionOverlay inclusions={allInclusions} />
            </BottomSheetOverlay> : null
          }

        </>
      );
    }
    return [];
  };

  const hotelObject: HotelDetail | null | undefined = getHotelObject(hotelDetail, hotelSellableId);
  return (
    <View style={styles.hotelRow}>
      <View style={[styles.hotelHeading, isEmpty(hotelObject) ? {} : sectionHeaderSpacing]}>
        {/* <Image source={icon} style={styles.iconImage}/> */}
        <HtmlHeadingV2
        htmlText={text}
        style={dayPlanRowHeadingStyles}
        mWebStyle={StyleSheet.flatten(dayPlanRowHeadingStyles.heading)}
      />
      </View>
      {renderHotelCard(hotelObject)}
    </View>
  );
};

const PackageInclusionOverlay = ({ inclusions }) => {
  return (
    <View style={{ justifyContent: 'center', paddingHorizontal: 15, marginBottom: 10 }}>
      {inclusions && inclusions.length > 0 && inclusions.map((item, index) => {
        const { description, color } = item || {};
        return (
          <View style={{ flexDirection: 'row', paddingBottom: 8 }} key={index}>
            <Image style={[styles.greenIconStyle, { tintColor: color, marginTop: 5 }]} source={WHITE_TICK_ICON} />
            <Text style={[styles.inclusionText, { color }]}>{description}</Text>
          </View>
        );
      },
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  hotelRow: {
    ...dayPlanRowContainerStyle,
  },
  hotelHeading: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hotelSubHeadingContainer: {
    ...paddingStyles.pb10,
    flex: 1,
    flexDirection: 'row',
  },
  iconImage: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginEnd: 10,
  },
  hotelTagText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
    marginLeft: 3,
  },
  maxHotelTagRating: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    marginStart: getScreenWidth() / 3,
  },
  anchorTextUnavailable: {
    ...actionStyle,
    color: holidayColors.red,
  },
  OrSimilar: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.lightGray,
  },
  infoBox: {
    marginTop: 10,
    padding: 15,
    backgroundColor: holidayColors.lightGray2,
    ...holidayBorderRadius.borderRadius16,
  },
  infoTitle: {
    ...fontStyles.labelMediumBold,
    lineHeight: 19,
    color: holidayColors.black,
    marginBottom: 4,
  },
  textStyle: {
    color: '#008bff',
    fontSize: 11,
    fontWeight: 'bold',
  },
  greenIconStyle: {
    width: 9,
    height: 9,
    marginRight: 10,
    marginTop: 10,
    tintColor: '#4a4a4a',
  },
  roomRates: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  inclusions: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    marginTop: 5,
    marginRight: 15,
  },

  /* HOTEL DETAILS */
  hotelNameContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  hotelName: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    marginRight: 13,
    textAlignVertical: 'center',
  },
  hotelAddress: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    marginTop: 5,
  },
  poi: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    marginTop: 3,
  },
  hotelImg: {
    width: '100%',
    height: '100%',
    ...dayPlanRowImage,
  },
  hotelMajorDetails: {
    flex: 1,
    flexDirection: 'row',
  },
  ratingStarWrapper: {
    flexDirection: 'row',
    marginVertical: 3,
  },
  starRating: {
    width: 10,
    height: 10,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 'auto',
  },
  hotelDetails: {
    flexDirection: 'row',
  },
  thumbWrapper: {
    width: 100,
    height: 108,
    marginRight: 10,
    borderRadius: 4,
    overflow: 'hidden',
    position: 'relative',
    marginLeft: 30,
  },
  ratingContainerStyle: {
    position: 'relative',
  },
  ratingContainer: {
    ...paddingStyles.ph8,
    bottom: 0,
    position: 'absolute',
    zIndex: 1,
    borderTopLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  taContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#34E0A1',
    height: 24,
    alignItems: 'center',
    bottom: 0,
    position: 'absolute',
    zIndex: 1,
    borderTopLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderRadius: 4,
    ...paddingStyles.ph6,
  },
  taLogo: {
    width: 14,
    height: 9,
  },
  safeImageIcon: {
    width: 80,
    height: 20,
  },
  dateText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
    marginTop: 4,
  },
  /* HOTEL DETAILS */
  inclusionsContainer: { flexDirection: 'row' },
  nights: { fontSize: 11, fontFamily: fonts.bold, color: 'black' },
  inclusionText: { fontFamily: fonts.regular, fontSize: 14, color: '#4a4a4a' },
  mealPlan: { ...fontStyles.labelBaseRegular, color: holidayColors.black, marginBottom: 10, },
  containerStyles: {
    ...paddingStyles.pa16,
    ...paddingStyles.pb40,
  },
  headingContainerStyles: {
    ...marginStyles.mb12
  }
});

export default HotelRow;
