import React, { useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import entities from 'entities';
import {
  staticDataFDInterface,
  StaticItinerary,
  Image,
  Destination,
} from '../../Types/PackageDetailApiTypes';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import BottomSheet from '../BottomSheet/BottomSheet';
import { dayPlanContentStyles, dayPlanRowContainerStyle } from './dayPlanStyles';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { isMobileClient } from '../../../utils/HolidayUtils';
import HtmlHeadingV2 from 'mobile-holidays-react-native/src/Common/Components/HTML/V2';
import { capitalizeText } from '../../../../src/utils/textTransformUtil';


interface StaticItineraryComponentProps {
  staticData: staticDataFDInterface;
  isOverlay: boolean;
  destinationMap: Map<number, Destination>;
}

const StaticItineraryComponent = ({
  staticData,
  isOverlay,
  destinationMap,
}: StaticItineraryComponentProps) => {
  const [modalVisibility, setModalVisibility] = useState(false);
  const { data = {}, images, day }: staticDataFDInterface = staticData || {};
  const { description }: StaticItinerary = data || {};
  const tempImages: Image[] = images && images.length > 2 ? images.slice(0, 2) : images;
  const destination: Destination | undefined =
    destinationMap && destinationMap.has(day) ? destinationMap.get(day) : undefined;
  const destinationName = destination ? destination.name : null;
  const HTMLView = isMobileClient()
    ? require('react-native-htmlview').default
    : require('../../../Common/Components/HTML').default;

  const renderImages = (item: Image, index: number) => {
    const { fullPath, title } = item || {};
    return (
      <>
        <View style={style.imageContainer}>
          <HolidayImageHolder imageUrl={fullPath} style={style.image} resizeMode={'contain'}/>
          {!!title &&
          <LinearGradient
            start={{x: 0.0, y: 0.0}}
            end={{x: 0.0, y: 1.0}}
            colors={['rgba(0,0,0,0)', 'rgba(0,0,0,0.8)']}
            style={style.imageTextContainer}
          >
            <Text style={style.imageText}>{capitalizeText(title)}</Text>
          </LinearGradient>}
        </View>
      </>
    );
  };


  return (
    <View style={[style.container, !isOverlay ? {} : { borderBottomWidth: 0 }]}>
      {isOverlay && (
        <View style={style.dayTextContainer}>
          <Text style={style.dayText}>Day {day}</Text>
          {!!destinationName && (
            <>
              <Text style={{ marginHorizontal: 5 }}>{'\u2022'}</Text>
              <Text style={style.destText}>{destinationName}</Text>
            </>
          )}
        </View>
      )}
      <View style={style.descriptionContainer}>
        <HtmlHeadingV2
          htmlText={description}
          style={dayPlanContentStyles}
          mWebStyle={StyleSheet.flatten(dayPlanContentStyles.heading)}
        />
      </View>
      <View style={style.imagesArrayContainer}>
        {tempImages &&
          tempImages.length > 0 &&
          tempImages.map((item, index) => renderImages(item, index))}
      </View>
      {modalVisibility ? (
        <BottomSheet isCloseBtnVisible onBackPressed={() => setModalVisibility(false)}>
          <StaticItineraryComponent
            staticData={staticData}
            isOverlay={true}
            destinationMap={destinationMap}
          />
        </BottomSheet>
      ) : null}
    </View>
  );
};

const style = StyleSheet.create({
  container: {
    ...dayPlanRowContainerStyle,
  },
  descriptionContainer: { marginBottom: 10 },
  paragraph: {
    ...fontStyles.labelBaseRegular,
    lineHeight: 19,
    color: holidayColors.gray,
  },
  bold: {
    ...fontStyles.labelBaseBold,
    lineHeight: 19,
    color: holidayColors.gray,
  },
  readMore: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  imagesArrayContainer: {
    justifyContent: 'flex-start',
    flexDirection: 'row',
  },
  imageContainer: {
    width: '50%',
    paddingHorizontal: 2.5,
  },
  image: {
    height: 110,
    ...holidayBorderRadius.borderRadius16,
  },
  imageTextContainer: {
    position: 'absolute',
    bottom: 0,
    height: 22,
    width: '94%',
    marginHorizontal: 8,
    justifyContent: 'center',
  },
  imageText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
    textAlign: 'center',
    marginBottom: 2,
  },
  dayTextContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 13,
    flexDirection: 'row',
  },
  dayText: {
    fontFamily: fonts.bold,
    fontSize: 16,
    color: '#000000',
  },
  destText: {
    fontFamily: fonts.regular,
    fontSize: 14,
    color: '#9b9b9b',
  },
});

export default StaticItineraryComponent;
