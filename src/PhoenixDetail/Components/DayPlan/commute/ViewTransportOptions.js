import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { holidayColors } from '../../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const ViewTransportOptionsNew = ({ commuteCtaObj }) => {
  const { commuteCta = '', commuteHeader = '', handleCtaClick = () => {} } = commuteCtaObj || {};
  // useEffect(() => {
  //   trackPhoenixDetailLocalClickEvent({ eventName: 'View_transport_options_yes' });
  // }, []);
  const captureClickEvents = () => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.pageRenedered,
      value: 'View|transport_options|yes',
      shouldTrackToAdobe:false
    })
    trackPhoenixDetailLocalClickEvent({ eventName: 'View_transport_options_yes' });
  }
  useEffect(() => {
    captureClickEvents();
  }, []);
  return (
    <View style={styles.box}>
      <Text style={styles.title}>{commuteHeader}</Text>
      <TextButton
        buttonText={commuteCta?.toUpperCase()}
        handleClick={handleCtaClick}
        btnTextStyle={styles.subtitle}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  box: {
    width: '90%',
    justifyContent: 'center',
    backgroundColor: holidayColors.lightBlueBg,
    height: 60,
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.ml16,
    ...marginStyles.mb10,
    ...paddingStyles.pa10,
  },
  title: {
    ...fontStyles.labelBaseRegular,
    marginBottom: 4,
  },
  subtitle: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
  },
});

export default ViewTransportOptionsNew;
