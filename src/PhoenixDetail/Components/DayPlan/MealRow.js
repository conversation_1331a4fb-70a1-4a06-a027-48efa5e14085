import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import iconSpoon from '@mmt/legacy-assets/src/holidays/ic_spoon.webp';
import iconfork from '@mmt/legacy-assets/src/holidays/ic_fork.webp';
import { dayPlanContentStyles, dayPlanRowContainerStyle, dayPlanRowHeadingStyles } from './dayPlanStyles';
import { sectionHeaderSpacing } from '../../../Styles/holidaySpacing';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

/* Components */
import HtmlHeadingV2 from 'mobile-holidays-react-native/src/Common/Components/HTML/V2';

const MealRow = ({itineraryUnit}) => {

  const {text} = itineraryUnit || {};

  return (
    <View style={styles.mealRow}>
      <View style={styles.headingContainer}>
        <View>
          <Image source={iconSpoon} style={styles.iconspoon} />
        </View>
        <View>
          <Image source={iconfork} style={styles.iconfork} />
        </View>
        <Text style={styles.title}>Day Meals</Text>
      </View>
      <View>
        <HtmlHeadingV2
          htmlText={text}
          style={dayPlanContentStyles}
          mWebStyle={StyleSheet.flatten(dayPlanContentStyles.heading)}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  mealRow: {
    ...dayPlanRowContainerStyle,
  },
  headingContainer: {
    ...AtomicCss.flexRow,
    ...AtomicCss.alignCenter,
    ...paddingStyles.pb8,
  },
  title:{
    ...dayPlanRowHeadingStyles.heading,
    paddingLeft: 10,
  },
  iconspoon: {
    width: 6,
    height: 14,
    resizeMode: 'cover',
  },
  iconfork: {
    width: 10,
    height: 14,
    marginHorizontal:1,
    resizeMode: 'cover',
  },
});

export default MealRow;
