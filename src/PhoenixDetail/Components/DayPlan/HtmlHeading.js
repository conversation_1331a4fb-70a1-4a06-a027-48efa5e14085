import React from 'react';
import { Text } from 'react-native';
import HTMLView from 'mobile-holidays-react-native/src/Common/Components/HTML';
import entities from 'entities';
import { isRawClient } from '../../../utils/HolidayUtils';

const htmlStyleForWeb = {
  fontSize: 12,
  color: '#000000',
  fontFamily: 'Lato-Regular',
};

export const HtmlHeading = ({ htmlText, style, styleSheetForWeb = {} }) => {
  const stylesheet = isRawClient() ? { ...htmlStyleForWeb, ...styleSheetForWeb } : style;
  const renderNode = (node, index, siblings, parent, defaultRenderer) => {
    if (node.type === 'tag') {
      if (node.name === 'p') {
        return <Text style={style.heading}>{defaultRenderer(node.children, parent)}</Text>;
      } else if (node.name === 'b') {
        return <Text style={style.bold}>{defaultRenderer(node.children, parent)}</Text>;
      }
    } else if (node.type === 'text') {
      return entities.decodeHTML(node.data);
    }
    return undefined;
  };

  return (
    <HTMLView
      value={`<p>${htmlText}</p>`}
      style={style}
      stylesheet={stylesheet}
      renderNode={renderNode}
    />
  );
};
