import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { smallHeightSeperator } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';

export const dayPlanRowHeadingStyles = {
  heading: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
   },
   bold: {
     ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
   },
  };

export const dayPlanContentStyles = {
  heading: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  title:{
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  bold: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  paragraph: {
    ...fontStyles.labelBaseRegular,
    lineHeight: 19,
    color: holidayColors.gray,
  },
};

export const dayPlanRowContainerStyle = {
  paddingHorizontal: 16,
  paddingVertical: 20,
  ...smallHeightSeperator,
  backgroundColor: holidayColors.white,
};

export const actionStyle = {
  ...fontStyles.labelBaseBold,
  color: holidayColors.primaryBlue,
};

export const actionSeperator = {
  ...fontStyles.labelBaseBold,
  color: holidayColors.grayBorder,
  marginHorizontal: 5,
};

export const dayPlanRowImage = {
  ...holidayBorderRadius.borderRadius16,
}
