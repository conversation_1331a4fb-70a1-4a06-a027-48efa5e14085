import React, { Component } from 'react';
import {
  BackH<PERSON>ler,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  Image,
  Text,
  ScrollView,
} from 'react-native';
import CrossIcon from '../images/cross.png';
import {HARDWARE_BACK_PRESS} from '../../../SearchWidget/SearchWidgetConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import withBackHandler from '../../../hooks/withBackHandler';

/**
 * Usage
 *  <BottomModal children={[<SiddhiBanner/> <SomeOtherBanner/>]} toggleBannerState={this.toggleBannerState}/>}
 *  fixChild prop to disable content scroll, by default its scrollable.
 *  Supports multiple children JSX
 */

class BottomSheet extends Component {
  constructor(props) {
    super(props);
  }

  onBackClick = ()=> {
    return this.handleBackPress();
}

  captureClickEvents = () => {
    const eventName = 'collapse_' + this.props.title;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix: this.props.title,
    });
  };

  handleBackPress = () => {
    const {trackBackPressed} = this.props || {};
    if (trackBackPressed) {
      trackBackPressed();
    } else {
      this.captureClickEvents();
    }
    this.props.onBackPressed();
  };

  renderHeader = () => <View style={[styles.header, this.props.headerStyle]}>

    {this.props.isCloseBtnVisible ?
    <TouchableOpacity onPress={this.handleBackPress}>
      <Image source={CrossIcon} style={[styles.crossIcon, this.props.crossButtonStyle]} />
    </TouchableOpacity> : []
    }

    {this.props.title ? <Text style={[styles.title, this.props.titleStyle]}>{this.props.title}</Text> : null}
  </View>

  render() {
    const { isCloseBtnVisible, title, containerStyle, fixChild, topIcon } = this.props;
    if (!this.props.children) {
      log.error('BottomModal requires at least one child to render.');
      return null;
    }
    return (
      <Modal
        animationType={'fade'}
        transparent={true}
        onRequestClose={this.handleBackPress}>
        <TouchableOpacity
          activeOpacity={1}
          style={styles.overlay}
          onPressOut={this.handleBackPress}>
          <TouchableWithoutFeedback>
            <View style={[styles.container, containerStyle]}>
              {topIcon && <View>{topIcon}</View>}
              {(isCloseBtnVisible || title) ? this.renderHeader() : null}
              {fixChild
                  ? <View>{this.props.children}</View>
                  : <ScrollView showsVerticalScrollIndicator={false}>{this.props.children}</ScrollView>
              }
            </View>
          </TouchableWithoutFeedback>
        </TouchableOpacity>
      </Modal>
    );
  }
}

const styles = StyleSheet.create({
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    flex: 1,
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: 'white',
    borderTopRightRadius: 12,
    borderTopLeftRadius: 12,
    padding: 13,
    maxHeight: '85%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    marginBottom: 15,
    minHeight: 40,
  },
  crossBtn: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  crossIcon: {
    height: 24,
    width: 24,
    marginVertical: 4,
  },
  title: {
    fontSize: 18,
    lineHeight: 18,
    fontFamily: 'lato',
    fontWeight: 'bold',
    color: '#000000',
    flex: 1,
    textAlign: 'center',
    paddingTop: 8,
  },
});
export default withBackHandler(BottomSheet);
