import React from 'react';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss'
import { paddingStyles } from '../../Styles/Spacing';
import { actionStyle } from './DayPlan/dayPlanStyles';
import TextButton from '../../Common/Components/Buttons/TextButton';

const SelectText = ({ onPress, textStyles = {}, containerStyles = {} }) => (
  <TextButton
      buttonText="Select"
      handleClick={onPress}
      btnTextStyle={[textStyles, actionStyle]}
      btnWrapperStyle={[paddingStyles.pv6, containerStyles, AtomicCss.alignSelfFlexEnd]}
  />
);

export default SelectText;
