import React from 'react';
import { FlatList, StyleSheet, Text, View } from 'react-native';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import PageHeader from 'mobile-holidays-react-native/src/Common/Components/PageHeader';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { DotSeperator } from '../ItineraryV2/Common/Seperators';
import { capitalizeText } from '../../../../src/utils/textTransformUtil';

const PhoenixHeader = (props) => {
  const { subtitleData = [], title = '', handleClose } = props;

  const getSubtitleViews = () => {
    return (
      <FlatList
        horizontal
        data={subtitleData}
        renderItem={({ item, index }) => <Text style={styles.subtitle}>{item}</Text>}
        ItemSeparatorComponent={<DotSeperator style={styles.dot} />}
        contentContainerStyle={[paddingStyles.pt2, { alignItems: 'center' }]}
      />
    );
  };

  const TitleAndSubTitleView = () => {
    return (
      <View style={styles.titleStyles}>
        <Text style={styles.title}>{title}</Text>
        {capitalizeText(getSubtitleViews())}
      </View>
    );
  };

  return (
    <PageHeader
      showBackBtn
      showShadow
      onBackPressed={handleClose}
      containerStyles={styles.header}
      numberOfLines={2}
      leftComponent={<TitleAndSubTitleView />}
    />
  );
};

const styles = StyleSheet.create({
  titleStyles: {
    flex: 1,
  },
  title: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
  },
  subtitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  dot: {
    height: 4,
    width: 4,
    backgroundColor: holidayColors.green,
    borderRadius: 50,
    ...marginStyles.mt8,
    ...marginStyles.mh6,
  },
});

export default PhoenixHeader;
