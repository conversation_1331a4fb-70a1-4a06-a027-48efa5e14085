import React from 'react';
import PropTypes from 'prop-types';
import {Image, StyleSheet, Text, TouchableOpacity, View, Dimensions, Platform} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import icon from '../images/customize.webp';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import { shapeTypes } from '@mmt/legacy-commons/Common/Components/CoachMarks';
import {isIosClient} from "../../../utils/HolidayUtils";
import { isGIdisplayPoweredbyMMTHeader } from '../../../utils/HolidayUtils';
import { getAffiliate } from '../../../theme';
import { getdeviceType } from '../../../utils/HolidayDevicesTypes';


const FloatingWidget = ({handleClick, count, addDynamicCuesStep}) => {
    const coachMarkShapeObject = {
        type: shapeTypes.rect,
        width: 195,
        height: 32,
    };
    const extraHeight = isGIdisplayPoweredbyMMTHeader(getAffiliate(), getdeviceType()) ? 38 : 0
    if (count > 0) {
        return (
        <View 
            style={styles.container}
            shadowOffset={{height: 3}}
            shadowColor="black"
            shadowOpacity={0.3}
        >
            <DynamicCoachMark
                isSetCustomShape
                shapeObject= {coachMarkShapeObject}
                cueStepKey="customizationWidget"
                offsetHeight = {3+extraHeight}
                offsetWidth={isIosClient() ? 3 : 0}
                addDynamicCuesStep={addDynamicCuesStep}
            >
            <View style={[styles.widget, AtomicCss.flexRow]}>
                    <TouchableOpacity style={[AtomicCss.flexRow]} onPress={handleClick}>
                        <View style={styles.filterWrapper}>
                            <Image style={styles.iconFilter} source={icon}/>
                        </View>
                        <Text style={{color:'#37E1D2',fontSize: 6}}>{'\u2B24'}</Text>
                        <Text style={styles.text}>View
                            <Text style={{
                                fontFamily: 'Lato-Bold',
                                fontSize: 13,
                            }}> {count}</Text> {count > 1 ? ' Customizations' : ' Customization'}</Text>
                    </TouchableOpacity>
                </View>
            </DynamicCoachMark>
      </View>
    );
    } else {
        return [];
    }
};

const styles = StyleSheet.create({
    widget: {
        backgroundColor: '#4a4a4a',
        height: 28,
        borderRadius: 14,
        paddingVertical: 9,
        paddingHorizontal: 20,
        alignItems: 'center',
        justifyContent: 'center',
        alignContent: 'center',
    },
    container: {
        width: '100%',
        position: 'absolute',
        bottom: 90,
        alignItems: 'center',
        justifyContent: 'center',

    },
    text: {
        opacity: 0.9,
        fontFamily: fonts.bold,
        fontSize: 11,
        marginTop: 2,
        textAlignVertical: 'center',
        fontWeight: '500',
        fontStyle: 'normal',
        marginLeft: 8,
        color: '#dbdbdb',
    },
    iconFilter: {
        height: 24,
        width: 24,
    },
    filterNo: {
        color: '#fff',
        fontSize: 10,
        fontFamily: 'Lato-Bold',
    },
    filterNoWrapper: {
        backgroundColor: '#eb2026',
        borderRadius: 15,
        alignItems: 'center',
        justifyContent: 'center',
        height: 15,
        width: 15,
        position: 'absolute',
        right: -2,
        top: 0,
    },
    filterWrapper: {
        width: 24,
    },
    iconSorter: {
        height: 20,
        width: 20,
        marginRight: 12,
    },
});

FloatingWidget.propTypes = {
    handleClick: PropTypes.func.isRequired,
    count: PropTypes.number,
};

export default FloatingWidget;
