import React from 'react';
import {connect} from 'react-redux';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import rupeeIcon from '../../../ListingHotelNew/images/rupeeIcon.webp';
import starIcon from '../../../ListingHotelNew/images/starIcon.webp';
import locationIcon from '../../../ListingHotelNew/images/locationIcon.webp';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { HOLIDAYS_HOTEL_OVERLAY_LISTING } from '../../Utils/PheonixDetailPageConstants';
import { isEmpty } from 'lodash';
import { fontStyles } from '../../../Styles/holidayFonts';
import BottomTabs from '@Frontend_Ui_Lib_App/BottomTabs'; 
import iconFilter from '../images/ic_filter.png';
import iconSort from '../images/ic_sort.png';
import {holidayBorderRadius} from '../../../Styles/holidayBorderRadius'
import { paddingStyles } from '../../../Styles/Spacing';
import { logPhoenixDetailPDTEvents } from '../../../utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';
import { holidayNavigationPush } from '../../Utils/DetailPageNavigationUtils';
import { Overlay } from '../DetailOverlays/OverlayConstants';
import { hideOverlays, showOverlay } from '../DetailOverlays/Redux/DetailOverlaysActions';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors'
class BottomFilters extends BasePage {

  componentDidMount() {
    super.componentDidMount();
  }

  captureClickEvents = ({eventName = '', suffix = ''}) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
      subPageName: HOLIDAYS_HOTEL_OVERLAY_LISTING,
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: HOLIDAYS_HOTEL_OVERLAY_LISTING,
    });
  }
  openWholeFilterSection = () => {
    this.captureClickEvents({
      eventName: 'sort_filter_clicked',
      prop1: HOLIDAYS_HOTEL_OVERLAY_LISTING,
    });

    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.HOTEL_FILTER_LIST,
      overlayKey: Overlay.FILTER_LIST,
      showOverlay: this.props.showOverlay,
      hideOverlays: this.props.hideOverlays,
      props: {
        isOpenedDirectly: true,
        filterData: this.props.filterData,
        days: this.props.days,
        back: () => this.props.hideOverlays([Overlay.FILTER_LIST]),  // todo - not needed
      },
    });

  };

  openPriceFilterSection = () => {
    const {
      priceList,
    } = this.props.filterData;
    const fullPriceRange = {
      min: priceList[0],
      max: priceList[priceList.length - 1],
    };
    this.captureClickEvents({
      eventName: 'price_filter_clicked',
      prop1: HOLIDAYS_HOTEL_OVERLAY_LISTING,
    });

    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.HOTEL_PRICE_RANGE,
      overlayKey: Overlay.PRICE_RANGE,
      showOverlay: this.props.showOverlay,
      hideOverlays: this.props.hideOverlays,
      props: {
          isOpenedDirectly: true,
          fullPriceRange,
          appliedPriceRange: this.props.appliedFilterData.appliedPriceRange,
          days: this.props.days,
          back: () => this.props.hideOverlays([Overlay.PRICE_RANGE]), // todo - not needed
      },
    });
  }

  openPopularityFilterSection = () => {
    const {
      popularList,
    } = this.props.filterData;
    this.captureClickEvents({
      eventName: 'popularity_filter_clicked',
      prop1: HOLIDAYS_HOTEL_OVERLAY_LISTING,
    });
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.HOTEL_POPULAR_SECTION,
      overlayKey: Overlay.POPULAR_SECTION,
      showOverlay: this.props.showOverlay,
      hideOverlays: this.props.hideOverlays,
      props: {
          isOpenedDirectly: true,
          popularList,
          appliedPriceRange: this.props.appliedFilterData.appliedPriceRange,
          back: () => this.props.hideOverlays([Overlay.POPULAR_SECTION]), // todo - not needed
      },
    });
  }

  openPropertyFilterSection = () => {
    const {
      propertyTypeList,
    } = this.props.filterData;
    const {
      starRatingList,
    } = this.props.filterData;
    this.captureClickEvents({
      eventName: 'property_filter_clicked',
      prop1: HOLIDAYS_HOTEL_OVERLAY_LISTING,
    });
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.HOTEL_PROPERTY_TYPE,
      overlayKey: Overlay.PROPERTY_TYPE,
      showOverlay: this.props.showOverlay,
      hideOverlays: this.props.hideOverlays,
      props: {
          appliedPropertyTypeList: this.props.appliedFilterData.appliedPropertyTypeList,
          appliedStarRatingList: this.props.appliedFilterData.appliedStarRatingList,
          propertyTypeList,
          starRatingList,
          back: () => this.props.hideOverlays([Overlay.PROPERTY_TYPE]), // todo - not needed
      },
    });
  }
  render() {
    const {
      popularList,
    } = this.props.filterData;
    return (
      <View style={styles.bottomFiltersWrapper}>
        <BottomTabs
          customStyles={styles.customStyles}
          data={[
            {
              icon: iconSort,
              label: 'Sort',
              onPress: this.openWholeFilterSection,
            },
            {
              icon: iconFilter,
              label: 'Filter',
              onPress: this.openWholeFilterSection,
            }
          ]}
        />
      </View>
    );
  }
}
const styles = StyleSheet.create({
  rupeeIcon: { width: 23, height: 23, marginBottom: 5 },
  starIcon: { width: 23, height: 23, marginBottom: 5 },
  locationIcon: { width: 17, height: 22, marginBottom: 5 },
  bottomFiltersWrapper: {
    position: 'absolute',
    alignItems: 'center',
    bottom: 0,
    width: '100%',
  },
  customStyles: {
    wrapperStyle: {
      width: '100%',
      ...paddingStyles.pv10,
      ...holidayBorderRadius.borderRadius4,
    }
  },
  filterText: {
    ...fontStyles.labelSmallRegular,
  },
});

const mapDispatchToProps = dispatch => ({
  showOverlay: (key, data) => dispatch(showOverlay(key, data)),
  hideOverlays: (keys) => dispatch(hideOverlays(keys)),
});

const mapStateToProps = (state) => {
  const {appliedFilterData} = state.holidayHotelListingReducer;
  return {appliedFilterData};
};

export default connect(mapStateToProps, mapDispatchToProps)(BottomFilters);
