import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

const StarRating = (props) => {
  const { selectedStarRating } = props;
  return (
    <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
      {[...Array(5)].map((star, index) => {
        const ratingValue = index + 1;
        return (
          <Text
            style={[
              fontStyles.labelSmallRegular,
              ratingValue <= selectedStarRating ? styles.fullStar : styles.emptyStar,
            ]}
          >
            &#9733;
          </Text>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
    fullStar: {
        color: holidayColors.gray,
    },
    emptyStar: {
        color: holidayColors.lightGray2,
    },
});

export default StarRating;
