import React, {useState} from 'react';
import {ScrollView, StyleSheet, Text, View} from 'react-native';
import Header from '../../Header';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import HotelListCard from './HotelListCard';
import SelectPriceCard from '../../SelectPriceCard';

const similarHotelData = [
  {
    propertyType: 'Hotel',
    userRating: 4.5,
    propertyName: 'Munnar Hill Resort',
    address: 'Munnar, Kerala',
    landmark: '400m from Tea Museum',
    date: 'Sun, 03 Jan 2021 - Tue 05 Jan 2021',
    starRating: 4,
    roomType: 'Standard Twin room',
    additionalFacililities: [
      'Breakfast', 'Pay at Hotel',
    ],
    price: 329.99,
  },
  {
    propertyType: 'Hotel',
    userRating: 4.5,
    propertyName: 'Munnar Hill Resort',
    address: 'Munnar, Kerala',
    landmark: '400m from Tea Museum',
    date: 'Sun, 03 Jan 2021 - Tue 05 Jan 2021',
    starRating: 4,
    roomType: 'Standard Twin room',
    additionalFacililities: [
      'Breakfast', 'Pay at Hotel',
    ],
    price: 329.99,
  },
];

const HotelListingPage = props => {
  const [selectCard, setSelectCard] = useState();
  const toggleSelect = (index) => {
        setSelectCard(index === selectCard ? null : index);};
  return (
    <View style={styles.container}>
      <Header
        title="Exquisite Kerala"
        subTitle="Jan 3 . 2 Adults"
      />
      <ScrollView>
      <SelectPriceCard />

      {/* <PriceInfo
        isVisible={true}
        paxList={"2 Travellers"}
        carType="Resort Rio"
        totalPrice="+₹ 26,909"
      /> */}

      <View>
        <View style={styles.listingText}>
          <View style={[AtomicCss.marginBottom10]}><Text style={[AtomicCss.font16, AtomicCss.blackText, AtomicCss.blackFont]}>Similar Hotels</Text></View>
          <View style={[AtomicCss.marginBottom10]}>
            <Text style={[AtomicCss.font11, AtomicCss.defaultText, AtomicCss.regularFont]}>* Listing Similar Hotels -You may get any hotel from this list with <Text style={AtomicCss.blackFont}>similar star rating and amenities.</Text> </Text>
          </View>
          <View><Text style={[AtomicCss.font11, AtomicCss.defaultText, AtomicCss.regularFont]}>Showing <Text style={[AtomicCss.boldFont]}>5 Similar Hotels*</Text> You may get any one in your package</Text></View>
        </View>
        {similarHotelData.map((item, index) =>
          <View>
            <HotelListCard
              key={index}
              propertyType={item.propertyType}
              userRating={item.userRating}
              propertyName={item.propertyName}
              address={item.address}
              landmark={item.landmark}
              date={item.date}
              starRating={item.starRating}
              roomType={item.roomType}
              price={item.price}
              showPrice={false}
              selectCard={selectCard}
              index={index}
              handleCardSelect = {() => toggleSelect(index)}
            />
            {index + 1 == similarHotelData.length ? null : (
              <View style={styles.cardDivider}>
                <View style={styles.orTag}><Text style={[AtomicCss.font10, AtomicCss.boldFont, AtomicCss.greyText]}>OR</Text></View>
              </View>
            )}

          </View>
        )}
      </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
    ...getPlatformElevation(0),
  },
  overallRatingText: {
    fontFamily: fonts.black,
    fontSize: 18,
    color: '#ffffff',
  },
  cardDivider: {
    borderTopWidth: 1,
    borderColor: '#e5e5e5',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginTop: 20,
  },
  orTag: {
    width: 25,
    height: 25,
    borderRadius: 25,
    borderColor: '#e5e5e5',
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    top: -15,
    backgroundColor: 'white',
  },
  listingText: {
    padding: 15,
  },
});

export default HotelListingPage;
