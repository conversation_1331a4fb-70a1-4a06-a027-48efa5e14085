import React from 'react';
import {Platform} from 'react-native';
import WebVideoPlayer from '@mmt/legacy-commons/Common/Components/VideoPlayer/web';
import WebDashVideoPlayer from '@mmt/legacy-commons/Common/Components/VideoPlayer/dash';
import HolidaysAppVideoPlayer from './HolidaysAppVideoPlayer';
import {isRawClient} from "../../../utils/HolidayUtils";

const VideoPlayer = props => (isRawClient() ?
    (props.dash ? <WebDashVideoPlayer {...props} /> : <WebVideoPlayer {...props} />)
    : <HolidaysAppVideoPlayer {...props} />);

export default VideoPlayer;
