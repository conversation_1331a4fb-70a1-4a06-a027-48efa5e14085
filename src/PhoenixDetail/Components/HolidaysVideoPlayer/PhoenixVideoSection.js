import React from 'react';
import {StyleSheet, View} from 'react-native';
import VideoPlayer from './index';
import VideoSection from './VideoSection';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import PropTypes from 'prop-types';

export default class PhoenixVideoSection extends VideoSection {

  updateVideoPausedState = (pausedState) => {
    this.setState({
      paused: pausedState,
    });
    const suffix = pausedState ? 'pause' : 'play';
    trackPhoenixDetailLocalClickEvent({
      eventName: 'video_',
      suffix,
    });
  }

  render() {
    const { containerStyle = {}, mediaStyle = {} ,repeat = false, width, height } = this.props || {};
    return (
      <View style={[styles.container,containerStyle,mediaStyle]}>
        <View style={width ? {width, height} :  styles.media}>
          <VideoPlayer
            repeat={repeat}
            autoPlay={true}
            source={{uri: this.props.videoUrl}}
            resizeMode="stretch"
            disableBack
            disableFullscreen={true}
            disablePlayPause={this.props.disablePlayPause ? this.props.disablePlayPause : false}
            muted={true}
            showControls={false}
            paused={this.state.paused}
            posterResizeMode="stretch"
            style={styles.media}
            updateVideoPausedState={this.updateVideoPausedState}
            dash
            showGallery={this.props.showGallery}
            openGallery={() => this.props.openGallery()}
            disableTimer={this.props.disableTimer ? this.props.disableTimer : false}
            disableVolume={this.props.disableVolume ? this.props.disableVolume : false}
            disableSeekbar = {this.props.disableSeekbar ? this.props.disableSeekbar : false}
            width={width}
            height={height}
          />
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    justifyContent:'center',
    alignItems: 'center',
    zIndex: 10,
    backgroundColor: '#000',
    flex:1,
  },
  media: {
    width: 100,
    height: 100,
  },
});

PhoenixVideoSection.propTypes = {
  videoUrl: PropTypes.string.isRequired,
  videoPaused: PropTypes.bool.isRequired,
};
