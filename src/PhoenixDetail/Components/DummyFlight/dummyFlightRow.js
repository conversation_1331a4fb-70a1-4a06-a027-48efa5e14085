import React from 'react';
import { StyleSheet, View } from 'react-native';
import PropTypes from 'prop-types';
import AlertIcon from '@mmt/legacy-assets/src/alert_cream.webp';
import { isEmpty } from 'lodash';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import ItineraryUnitExtraInfoMessages from '../ItineraryUnitExtraInfoMessages';

/* Components */
import HtmlHeadingV2 from 'mobile-holidays-react-native/src/Common/Components/HTML/V2';
import DummyFlightCards from './dummyFlightCard';
import HolidaysMessageStrip from '../../../Common/Components/HolidaysMessageStrip';

const DummyFlightRow = ({
  day = 0,
  flightObject,
  itineraryUnit,
  removeFlights = null,
  isReviewPage = false,
}) => {
  const { shortText = '' } = itineraryUnit || {};
  const { flightMetadataDetail = {}, flightLegs = [], stops } = flightObject || {};
  const { dummyFlightMessage = '', flightExtraInfo = [] } = flightMetadataDetail || {};

  return (
    <View style={[styles.flightRow, isReviewPage ? {} : styles.borderStyle]}>
      <View style={styles.headingContainer}>
        <HtmlHeadingV2
          htmlText={shortText}
          style={styles}
          mWebStyle={StyleSheet.flatten(styles.heading)}
        />
      </View>
      <DummyFlightCards
        flightLegs={flightLegs}
        stops={stops}
        day={day}
        removeFlights={removeFlights}
        isReviewPage={isReviewPage}
      />
      <ItineraryUnitExtraInfoMessages extraInfo={flightExtraInfo} />
    </View>
  );
};

DummyFlightRow.propTypes = {
  day: PropTypes.number,
  flightObject: PropTypes.object.isRequired,
  itineraryUnit: PropTypes.object.isRequired,
  removeFlights: PropTypes.func,
  isReviewPage: PropTypes.bool,
};
const styles = StyleSheet.create({
  flightRow: {
    marginBottom: 12,
    ...paddingStyles.pa16,
    backgroundColor: holidayColors.white,
  },
  borderStyle: {
    borderBottomColor: holidayColors.lightGray2,
    borderBottomWidth: 1,
  },
  headingContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
  },
  heading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  upgradeFlightContainer: {
    marginLeft: 'auto',
  },
  quickUpgradeText: {
    fontFamily: 'Lato',
    fontSize: 10,
    fontWeight: '900',
    color: holidayColors.primaryBlue,
  },
});

export default DummyFlightRow;
