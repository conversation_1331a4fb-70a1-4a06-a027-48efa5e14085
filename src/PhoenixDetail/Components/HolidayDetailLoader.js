import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import LottieView from 'lottie-react-native';
import { createLoaderDate, createTravellersText } from '../Utils/HolidayDetailUtils';
import { NO_DEPARTURE_CITY, USER_DEFAULT_CITY } from '../../HolidayConstants';
import { getDepartureCity, isRawClient } from '../../utils/HolidayUtils';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { isEmpty } from 'lodash';

const jsonFile = require('@mmt/legacy-assets/src/holidays_detail_loader/makemytrip.json');

export default class HolidayDetailLoader extends React.Component {
  static navigationOptions = {
    header: null,
  };

  constructor(props) {
    super(props);
    // Create a reference to the animation object
    this.animation = React.createRef();
  }

  async componentDidMount() {
    this.defaultUserDepartureCity = await getDepartureCity();
    !isRawClient() && this.animation?.current?.play();
  }

  renderFilterViews = () => {
    const { departureCity, destinationCity, departureDate, campaign } = this.props;
    const isData = !!departureCity && !!destinationCity && !!departureCity;
    return (
      <>
        {isData && <View style={styles.seprator} />}
        <View style={styles.filterViewContainer}>
          <FilterRow
            title="STARTING FROM"
            value={departureCity || this.defaultUserDepartureCity || USER_DEFAULT_CITY}
          />
          {destinationCity?.length > 0 ? (
            <FilterRow title="GOING TO" value={destinationCity} />
          ) : campaign?.length > 0 ? (
            <FilterRow title="PACKAGES FOR" value={campaign} />
          ) : null}
          {departureDate?.length > 0 ? <FilterRow title="TRAVEL DATE" value={departureDate} /> : null}
        </View>
        {isData && <View style={styles.seprator} />}
      </>
    );
  }

  render() {
    const {
      departureCity, departureDate, duration, travellerObj, openingSavedPackage, showDateText,
      changeAction, loadingText, showFilters,
    } = this.props;
    const durationText = duration ? `(${duration}N . ${duration + 1}D)` : ' ';
    const isData = !!departureCity && !isEmpty(departureDate) && !isEmpty(travellerObj);
    return (
      <View style={styles.laoderWrapper}>
        <View>
          <LottieView
            ref={this.animation} // Assign the reference to the Lottie component
            style={styles.lottie}
            source={jsonFile}
          />
        </View>
        {showFilters && this.renderFilterViews()}
        {!openingSavedPackage &&
        <>
          {isData && <View style={styles.seprator} />}
          <View style={styles.loaderDtls}>
            {departureCity && departureCity !== NO_DEPARTURE_CITY &&
              <View style={[styles.filterRowContainer]}>
                <Text style={[styles.filterRowTitle]}>FROM</Text>
                <Text style={[styles.filterRowValue]}>{departureCity}</Text>
              </View>
            }
            {departureDate &&
              <View style={styles.filterRowContainer}>
                <Text style={[styles.filterRowTitle]}>ON</Text>
                <Text style={[styles.filterRowValue]}>
                  {createLoaderDate(departureDate)}{'\n'}
                  {durationText}
                </Text>
              </View>
            }
            {travellerObj &&
              <View style={styles.filterRowContainer}>
                <Text style={[styles.filterRowTitle]}>FOR </Text>
                <Text style={[styles.filterRowValue]}>
                  {createTravellersText(travellerObj)}
                </Text>
              </View>}
          </View>
          {isData && <View style={styles.seprator} />}
        </>
        }
        {showDateText &&
          <TouchableOpacity style={styles.loaderBtmLink}>
            <Text style={styles.loaderBtmLinkTxt}>Updating your travel dates</Text>
          </TouchableOpacity>
        }
        {changeAction &&
          <TouchableOpacity style={styles.loaderBtmLink}>
            <Text style={styles.loaderBtmLinkTxt}>{loadingText}</Text>
          </TouchableOpacity>
        }
      </View>
    );
  }
}
const FilterRow = ({ title, value }) => (
  <View style={styles.filterRowContainer}>
    <Text style={styles.filterRowTitle}>{title}</Text>
    <Text style={styles.filterRowValue}>{value}</Text>
  </View>
);

const styles = StyleSheet.create({
  laoderWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: holidayColors.white,
  },
  loaderDtls: {
    width: '95%',
    flexDirection: 'column',
    justifyContent: 'space-around',
  },
  loaderDtlsTxt: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  loaderBtmLink: {
    position: 'absolute',
    bottom: 30,
  },
  loaderBtmLinkTxt: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelBaseBold,
  },
  filterViewContainer: {
    flexDirection: 'column',
    justifyContent: 'space-around',
  },
  filterRowContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    ...paddingStyles.pb16,
  },
  filterRowTitle: {
    color: holidayColors.lightGray,
    ...fontStyles.labelSmallRegular,
    textAlign:'center',
  },
  filterRowValue: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.gray,
    lineHeight: 20,
    marginTop: 2,
    textAlign:'center',
  },
  seprator: {
    height: 1,
    width: '95%',
    backgroundColor: holidayColors.grayBorder,
    ...marginStyles.mb16,
  },
  lottie: {
    width: 150,
    height: 150,
  },
});
