import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';

const StarRating = (props) => {
  const {selectedStarRating} = props;
  return (
    <View style={[cStyles.flexRow, cStyles.alignCenter]}>
      {[...Array(5)].map((star, index) => {
        const ratingValue = index + 1;
        return <Text
            key={index}
            style={[{fontSize: 9, marginBottom:16, marginTop: 8}, (ratingValue <= selectedStarRating) ? {color: 'black'} : {color: '#d0d0d0'}]}>&#9733;</Text>;
      })}
    </View>
  );
};

const styles = StyleSheet.create({});

export default StarRating;
