import React, {useState} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import StarRating from './StarRating';
import UserRatingCommon from '../../../../Common/Components/UserRatingCommon';
import {HOLIDAYS_HOTEL_OVERLAY_DETAIL, HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE} from '../../../Utils/PheonixDetailPageConstants';
import {getListOfImageUrls} from '../../../Utils/PhoenixDetailUtils';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import {findDaysBetweenDates} from '@mmt/legacy-commons/Common/utils/DateUtils';
import LinearGradient from 'react-native-linear-gradient';
import arrow from './arrow.webp';
import {isEmpty} from 'lodash';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import taLogo from '@mmt/legacy-assets/src/tripAdvisorLogo.webp';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { borderRadiusValues, holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';
import {connect} from 'react-redux';
import {hideOverlays, showOverlay} from '../../DetailOverlays/Redux/DetailOverlaysActions';
import {Overlay} from '../../DetailOverlays/OverlayConstants';
import {isMobileClient, isRawClient} from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';
import IMAGE_NOT_FOUND from '@mmt/legacy-assets/src/Images/no_image_default.webp';
import { isIosClient } from '../../../../utils/HolidayUtils';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { SUB_PAGE_NAMES } from '../../../../../src/HolidayConstants';

const HotelGalleryCard = ({hotelDetailData, fromPage = '', fromPresales = false, changeHotelRestricted = false, openHotelListingPage, packageDetailDTO, trackOmniture, bundled, showOverlay,hideOverlays}) => {
  const [viewWidth, setViewWidth] = useState({width: ''});

  const onLayout = (e) => {
    setViewWidth({width: e.nativeEvent.layout.width - 30});
  };

  const {hotel} =  hotelDetailData  || {};
  const {starRating,checkInDate, checkOutDate, name : hotelName, hotelInformation, imageInfo, mmtRatingInfo, locationInfo, safe, name,  ratingType, taInfo} = hotel || {};
  const {type, propertyType, mmtAssured = false} = hotelInformation || {};
  const {imageDataList} =  imageInfo || {};
  const {latLong, cityName, pointOfInterest} = locationInfo || {};
  let markers = [];
  const MAX_USER_RATING = 5;
  const imageUrlList = getListOfImageUrls(imageDataList);
  const numberOfNights = findDaysBetweenDates(checkInDate, checkOutDate);
  const mainImageUrl = imageUrlList.length >= 1 ? imageUrlList[0] : '';
  const galleryData = [
    imageUrlList.length >= 2 ? imageUrlList[1] : '',
    imageUrlList.length >= 3 ? imageUrlList[2] : '',
    imageUrlList.length >= 4 ? imageUrlList[3] : '',
  ];
  const imageCount = imageUrlList.length;
  let userRatingInfo = mmtRatingInfo || {};
  let isMMTRating = true;
  if (!!ratingType && ratingType === 'TA' && !isEmpty(taInfo)) {
    userRatingInfo = taInfo;
    isMMTRating = false;
  }
  const {userRating, reviewCount} = userRatingInfo;

  // Hide this section of address of lat long is empty.
  if (!isEmpty(latLong)) {
    //Sample latLong = 34.06753,74.83062
    let data = latLong.split(',');
    if (data && data.length > 0)
    {markers.push( {
      coordinates: {
        latitude: parseFloat(data[0]),
        longitude: parseFloat(data[1]),
      },
    });}
  }

  const captureClickEvents = ({ eventName = '', value = '' }) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value : value || eventName.replace('_','|'),
      subPageName:SUB_PAGE_NAMES.HOTEL_DETAIL
    })
    trackOmniture(eventName);
  };

  const showImageGallery = () => {
    if (imageUrlList.length > 0) {
      const params = {imageList: imageUrlList, name: hotelName};
      isMobileClient()
          ? HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY_FULL_PAGE, params)
          : showOverlay(Overlay.FULL_PAGE_GALLERY, params);
    }
  };

  const handleChange = () => {
    openHotelListingPage();
    captureClickEvents({
      eventName: 'change_hotel_' +
        cityName +
        '_' +
        (findDaysBetweenDates(packageDetailDTO.departureDate, checkInDate) + 1),
    });
  };

  const handleSimilar = () => {
    openHotelListingPage();
    captureClickEvents({
      eventName: 'similar_hotel_' +
        cityName +
        '_' +
        (findDaysBetweenDates(packageDetailDTO.departureDate, checkInDate) + 1),
    });
  };

  const handleReadReviews = () => {
    isMobileClient()
        ? HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_REVIEWS_PAGE, {
          hotelDetailsData: {hotel},
        })
        : showOverlay(Overlay.SHOW_HOLIDAY_HOTEL_REVIEWS, {
          'hotelDetailsData': {hotel},
          back: () => hideOverlays([Overlay.SHOW_HOLIDAY_HOTEL_REVIEWS]),
        });
    captureClickEvents({
      eventName: 'read_reviews',
    })
  };

  const handleViewOnMap = () => {
    if(isRawClient()) return;
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.PHOENIX_DETAIL_MAP_PAGE, {
      name,
      markers,
      pageName: HOLIDAYS_HOTEL_OVERLAY_DETAIL,
    });
    captureClickEvents({ eventName: 'view_map' });
  };

  const { width } = viewWidth || {};
  const smallPhotoWidth = (width - 20) / 4;
  const smallPhotoHeight = (width - 8) / 4;
  const smallPhotoStyles = {
      width: smallPhotoWidth,
      height: smallPhotoHeight,
      margin: 2,
  };
  const largePhotoStyles = {
      width:width / 2,
      height: width / 2,
      paddingRight: 2.5,
      ...marginStyles.mt2,
  };

  const renderImage = (item, i, style) => {
    const imageStyle = [styles.thumbnailImg, i === 1 && styles.borderTopRightRadius];
    return (
      <View key={i} style={style}>
        {isEmpty(item)
          ? <Image source={IMAGE_NOT_FOUND} style={imageStyle} />
          : (isIosClient()
            ? <PlaceholderImageView style={imageStyle} url={item} />
            : <Image source={{ uri: item }} style={imageStyle} />)
        }
      </View>
    );
  };

  return (
    <View style={styles.gallaryWrap} onLayout={onLayout}>
      <View style={styles.gallaryHdr}>
        <View style={cStyles.flex1}>
          <View style={[cStyles.flexRow, marginStyles.mb6, cStyles.spaceBetween]}>
            <View style={styles.hotelDetails}>
              <Text style={styles.hotelLocation}>Hotel in {cityName} </Text>
              <Text style={styles.noOfNights}>{numberOfNights} Nights</Text>
            </View>
            {/*Hide Change button if user has landed to this page from hotels listing or overlay pages.*/}
            {!bundled &&
              !changeHotelRestricted &&
              fromPage !== HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.HOTEL_LISTING_PAGE &&
              fromPage !== HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE && (
                <View>
                  <TextButton
                    buttonText="Change"
                    handleClick={handleChange}
                    btnTextStyle={styles.action}
                  />
                </View>
              )}
          </View>
          <View style={cStyles.flex1}>
            <Text style={styles.hotelName}>{hotelName}</Text>
          </View>
          <View style={[cStyles.flexRow, marginStyles.mt6]}>
            {bundled &&
              fromPage !== HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.HOTEL_LISTING_PAGE &&
              !fromPresales && (
                <View style={{ marginEnd: 5 }}>
                  <TouchableOpacity onPress={handleSimilar}>
                    <View style={[cStyles.flexRow, cStyles.alignCenter]}>
                      <Text style={styles.orSimilar}>Or Similar</Text>
                      <Image style={styles.arrowIcon} source={arrow} />
                    </View>
                  </TouchableOpacity>
                </View>
              )}
          <View style={{ marginTop: -6 }}>
              <StarRating selectedStarRating={starRating} />
            </View>
          </View>
        </View>
      </View>

      <TouchableOpacity
        onPress={() => {
          if (fromPresales) {
            return '';
          }
          showImageGallery();
          captureClickEvents({eventName :'gallery_open'});
        }}
        activeOpacity={fromPresales ? 1 :  0.5}
      >
      <View style={styles.imageGallary}>
          <View style={largePhotoStyles}>
            {!isEmpty(mainImageUrl) && <PlaceholderImageView url={mainImageUrl} style={[styles.tileImg]} />}
          </View>
        <View style={[styles.imageWrap, { width: viewWidth.width / 2 }]}>
          {galleryData.map((item, i) => renderImage(item, i, smallPhotoStyles))}
            <View
              style={[
                styles.moreImages,
                styles.thumbnailImg,
                smallPhotoStyles,
              ]}
            >
              <Text style={[styles.imageCount]}>
                {imageCount}
                {imageCount > 1 ? '+' : ''}{' '}
              </Text>
              <Text style={[styles.photosText]}>{imageCount > 1 ? 'Photos' : 'Photo'}</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
      <View style={styles.hotelInfo}>
      {safe && <View style={{marginRight: 5}}>
          <View style={[cStyles.flexRow]}>
            <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={['#ffffff', '#ffeeed']} style={holidayBorderRadius.borderRadius4}>
            <View style={[styles.container]}>
              <Image style={styles.icon} source={require('../../images/mysafe_small.webp')}/>
            </View>
            </LinearGradient>
          </View>
        </View>}
        {mmtAssured && (
          <View style={styles.mmtAssuredTag}>
            <Image source={require('../../images/mmt_assured.webp')} style={styles.iconMMTSafe} />
            <View style={[marginStyles.ml6]}>
              <Text style={styles.mmtAssuredText}>
                MMT Assured
              </Text>
            </View>
          </View>
        )}
        {isMMTRating && !!userRating && userRating > 0 && (
          <View style={styles.ratingContainer}>
            <UserRatingCommon ratingValue={userRating} containerStyle={paddingStyles.pa8} />
          </View>
        )}
        {!isMMTRating && !!userRating && (
          <View style={styles.taContainer}>
            <Image source={taLogo} style={styles.taIcon} />
            <Text style={[styles.hotelTagText, styles.hotelTagTaRating]}>
              {userRating}/<Text style={styles.hotelTagTaOutOfRating}>{MAX_USER_RATING}</Text>
            </Text>
          </View>
        )}

        {isMMTRating && reviewCount > 0 && !fromPresales && (
          <TextButton
            buttonText={`Read ${reviewCount} Reviews`}
            handleClick={handleReadReviews}
            btnTextStyle={styles.readReviews}
          />
        )}
      </View>

      <View style={styles.cityInfo}>
        <View style={styles.cityDetails}>
          <Text style={styles.cityName}>{cityName}</Text>
          {isMobileClient() && !fromPresales && (
            <TextButton
              buttonText="View on Map"
              handleClick={handleViewOnMap}
              btnTextStyle={styles.viewOnMap}
            />
          )}
        </View>
        {!isEmpty(pointOfInterest) && (
        <View style={[cStyles.flexRow, cStyles.alignCenter, marginStyles.mt4]}>
          <Text style={styles.pointOfInterest}>
            {pointOfInterest}
          </Text>
        </View>
      )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  gallaryWrap: {
    ...paddingStyles.pa16,
    width: '100%',
  },
  tileImg: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
    borderTopLeftRadius:borderRadiusValues.br16,
    borderBottomLeftRadius:borderRadiusValues.br16,
    borderTopRightRadius:0,
    borderBottomRightRadius:0,
    ...marginStyles.mr10,
  },
  imageCount:{
    ...fontStyles.labelBaseBlack,
    color:holidayColors.white,
    alignSelf:'center',
  },
  photosText:{
    ...fontStyles.labelSmallRegular,
    color:holidayColors.white,
    alignSelf:'center',
  },
  thumbnailImg: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageWrap: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  imageGallary: {
    flexDirection: 'row',
  },
  moreImages: {
    backgroundColor: holidayColors.primaryBlue,
    borderRadius: 0,
    justifyContent: 'center',
    shadowColor: '#000',
    ...paddingStyles.pa10,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    borderBottomRightRadius: borderRadiusValues.br16,
  },
  textContainer: {
    justifyContent: 'center',
    ...marginStyles.mr4,
    ...paddingStyles.pr6,
  },
  gallaryHdr: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  hotelDetails: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  hotelLocation: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  noOfNights: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  action : {
    ...fontStyles.labelBaseBold,
     color: holidayColors.primaryBlue,
  },
  hotelName: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  orSimilar: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    textDecorationLine: 'underline',
  },
  hotelInfo: {
    ...cStyles.flexRow,
    ...cStyles.alignCenter,
    flexWrap: 'wrap',
    ...paddingStyles.pv6,
  },
  container: {
    flexDirection: 'row',
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
  },
  icon: {
    ...marginStyles.ma4,
    height: 16,
    width: 60,
    ...holidayBorderRadius.borderRadius8,
  },
  iconMMTSafe: {
    width: 18,
    height: 18,
    resizeMode: 'cover',
  },
  mmtAssuredTag: {
    borderWidth: 1,
    borderColor: '#d7d5d5',
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.pa2,
    ...paddingStyles.pr6,
    flexDirection: 'row',
    alignItems: 'center',
    ...marginStyles.mr6,
    alignSelf: 'flex-start',
    ...marginStyles.mt10,
  },
  mmtAssuredText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.black,
  },
  arrowIcon: {
    height: 10,
    width: 10,
    ...marginStyles.mt2,
    marginStart: 5,
    tintColor: holidayColors.primaryBlue,
  },
  taContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#34E0A1',
    height: 24,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.ph6,
    ...marginStyles.mb8,
    alignItems: 'center',
  },
  taIcon : {
    width: 14,
    height: 9,
  },
  hotelTagTaRating: {
    ...marginStyles.ml4,
    color: holidayColors.gray,
  },
  hotelTagTaOutOfRating: {
    ...fontStyles.labelSmallRegular,
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...marginStyles.mb8,
  },
  readReviews: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    ...marginStyles.mt12
  },
  cityInfo: {
    ...paddingStyles.pt16,
    borderTopWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  cityDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cityName: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
  },
  viewOnMap: {
    ...fontStyles.labelBaseBold,
    color:holidayColors.primaryBlue,
    ...marginStyles.ml6
  },
  pointOfInterest: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
});

const mapDispatchToProps = dispatch => ({
  hideOverlays: (keys) => dispatch(hideOverlays(keys)),
  showOverlay: (key, data) => dispatch(showOverlay(key, data)),
});
export default connect(null, mapDispatchToProps)(HotelGalleryCard);
