import React, {useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import HolidaySafeBannerDetail from '../../../../Common/Components/CovidSafety/HolidaySafeBannerDetail';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { SUB_PAGE_NAMES } from '../../../../../src/HolidayConstants';

const AboutHotel = ({hotelDetailData, branch, fromOverlay = false, onOverlayReadMoreClicked = {}, trackOmniture}) => {
  const MAX_TRUNCATED_STRING_LENGTH = 300;
  const ELLIPSIZE_STRING = '...';
  const {hotel} =  hotelDetailData  || {};
  const {detailedInfo, hotelInformation, safe, description: descriptionOverlay} = hotel || {};
  const {checkInTime, checkOutTime} = hotelInformation || {};
  const {description = 'let this be a samele stirng for tesintg'}  =  detailedInfo || {};
  let formattedDescription = '';

  // Long description response text contains HTML.
  // We need to remove html tags so that we can slice text for short and long format to be displayed on UI.
  if (description && typeof description === 'string') {
    formattedDescription = description
      // Replace all the <br > with /n
      .replace(/<br \/>/g, '\n')
      // Remove all the other html tags. replace with empty space.
      .replace(/<[^>]*>/g, '');
  } else if (descriptionOverlay && typeof descriptionOverlay === 'string') {
    formattedDescription = descriptionOverlay
      // Replace all the <br > with /n
      .replace(/<br \/>/g, '\n')
      // Remove all the other html tags. replace with empty space.
      .replace(/<[^>]*>/g, '');
  } else {
    return [];
  }

  const captureClickEvents = (eventName = '') => {
    logPhoenixDetailPDTEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace('_','|'),
      subPageName:SUB_PAGE_NAMES.ACTIVITY_DETAIL
    })
    trackOmniture('read_details');
  }

  const [trunketString, setTrunketString] = useState(true);
  const trunketStingTxt = trunketString
    ? formattedDescription.slice(0, MAX_TRUNCATED_STRING_LENGTH) + ELLIPSIZE_STRING
    : formattedDescription;
  const handleMoreTxt = () => {
    setTrunketString(!trunketString);
    if (trunketString){
      captureClickEvents('read_details');
    }
  };
  return (
      <View style={styles.blkWrapper}>
      <View style={marginStyles.mt4}>
      <Text style={styles.hotelTitle}>About the Hotel</Text>
      </View>
      <View style={[styles.checkInWrap]}>
        <View style={[cStyles.flexRow, cStyles.alignCenter]}>
          <Text style={[styles.checkIn]}>Check In</Text>
          <Text style={[styles.checkInDetails]}>{checkInTime}</Text>
        </View>
        <View style={[cStyles.flexRow, cStyles.alignCenter, marginStyles.ml10]}>
          <Text style={[styles.checkIn]}>Check Out</Text>
          <Text style={[styles.checkInDetails]}>{checkOutTime}</Text>
        </View>
      </View>

      {!fromOverlay && <View style={[marginStyles.mt10]}>
        <Text style={styles.description} onPress={handleMoreTxt}>{trunketStingTxt}
          <Text onPress={handleMoreTxt} style={[marginStyles.mt10, styles.readMoreText]}>{trunketString ? '  Read More' : 'Show Less'}</Text>
        </Text>
      </View>}

      {fromOverlay &&
      <View style={[marginStyles.mt10]}>
        <TouchableOpacity onPress={()=> {
          onOverlayReadMoreClicked();
          captureClickEvents('read_details');
        }}>
          <Text style={styles.description}>{descriptionOverlay}</Text>
          <Text style={[styles.readMoreText]}>{'Read More'}</Text></TouchableOpacity>
      </View>}

      {!fromOverlay && safe && <View style={marginStyles.mt12}>
        <HolidaySafeBannerDetail branch={branch} trackOmniture={trackOmniture}/></View>}
    </View>
  );
};

const styles = StyleSheet.create({
  checkInWrap: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  description: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    lineHeight: 19,
  },
  blkWrapper: {
    ...paddingStyles.ph16,
    ...paddingStyles.pb16,
  },
  hotelTitle: {
    ...fontStyles.labelLargeBlack,
    color:holidayColors.black,
  },
  readMoreText:{
    color:holidayColors.primaryBlue,
    ...fontStyles.labelBaseBold,
  },
  checkIn:{
    ...fontStyles.labelBaseRegular,
    color:holidayColors.lightGray,
    ...marginStyles.mr6,
  },
  checkInDetails:{
    ...fontStyles.labelBaseBlack,
    color:holidayColors.black,
  },
});

export default AboutHotel;
