import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {isEmpty} from 'lodash';
import {
    getGoogleAPIKeyForAllPlarforms,
    getStaticMapUriForCoordinatesList,
    isMobileClient
} from '../../../../utils/HolidayUtils';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import { HOLIDAYS_HOTEL_OVERLAY_DETAIL } from '../../../Utils/PheonixDetailPageConstants';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';

const Location = ({hotelDetailData}) => {
    const {hotel} =  hotelDetailData  || {};
    const {locationInfo, name} = hotel || {};
    const {latLong, address} = locationInfo || {};
    const [googleAPiKey, setGoogleAPiKey] = useState('');
    let coordinateList = [];
    let markers = [];

    useEffect(() => {
        (async () => {
            let googleAPIKey = await getGoogleAPIKeyForAllPlarforms();
            setGoogleAPiKey(googleAPIKey);
        })();
    }, []);

    // Hide this section of address of lat long is empty.
    if (isEmpty(address) || isEmpty(latLong)) {
        return [];
    } else {
        //Sample latLong = 34.06753,74.83062
        let data = latLong.split(',');
        if (data && data.length > 0)
        {coordinateList.push({lat: data[0], lon: data[1]});}
        markers.push( {
            coordinates: {
                latitude: parseFloat(data[0]),
                longitude: parseFloat(data[1]),
            },
        });
    }

    const handleClick = () => {
        if (isMobileClient()) {
            HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.PHOENIX_DETAIL_MAP_PAGE, {
                name,
                markers,
                pageName: HOLIDAYS_HOTEL_OVERLAY_DETAIL
            })
        }
    }

    return (
        <View style={paddingStyles.ph16}>
            <View style={[ marginStyles.mb10]}>
                <Text style={[styles.locationText]}>Location</Text>
            </View>
        <View style={styles.locationWrap}>
            <View style={styles.locInnerWrap}>
                <Text style={styles.address}>{address}</Text>
            </View>
            <TouchableOpacity onPress={handleClick}>
            <View style={styles.locInnerWrap}>
                <Image source={{uri: getStaticMapUriForCoordinatesList(coordinateList, false, googleAPiKey)}} style={styles.activityImg}/>
            </View>
            </TouchableOpacity>
        </View>
        </View>
    );
};

const styles = StyleSheet.create({
    address: {
        ...fontStyles.labelBaseRegular,
        color:holidayColors.gray,
        lineHeight: 19,
    },
    locationWrap: {
        borderColor: holidayColors.grayBorder,
        flexDirection: 'row',
    },
    locInnerWrap: {
        width: '58%',
    },
    mapImage: {
        height: 93,
        resizeMode: 'cover',
        backgroundColor: holidayColors.lightGray,
        ...holidayBorderRadius.borderRadius8,
    },
    activityImg: {
        width: 150,
        height: 68,
    },
    locationText:{
        ...fontStyles.labelLargeBlack,
        color:holidayColors.gray,
    },
});

export default Location;
