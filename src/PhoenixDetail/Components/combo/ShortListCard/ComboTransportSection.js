import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { TRANSPORT_TYPE } from '../ComboConstats';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../../Common/Components/HolidayImageUrls';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { marginStyles } from '../../../../Styles/Spacing';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';

const ComboTransportSection = (props) => {
  const { sectionTitle, time, imageUrl = '', type, isDummy } = props || {};
  const imageSource =
    type === TRANSPORT_TYPE.FLIGHT ? imageUrl : getImageUrl(IMAGE_ICON_KEYS.CAB_ICON_WHITE);


  return (
    <View style={styles.section}>
      <Text style={styles.topText}>{sectionTitle}</Text>
      <View style={styles.bottomSection}>
        <HolidayImageHolder
          imageUrl={isDummy ? getImageUrl(IMAGE_ICON_KEYS.AIRPLANE_ICON) : imageSource}
          style={styles.image}
        />
        <View style={styles.textContainer}>
          <Text
            style={[fontStyles.labelSmallRegular, { color: holidayColors.gray }]}
            numberOfLines={1}
          >
            {isDummy ? 'Tentative Flight' : time}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default ComboTransportSection;

const styles = StyleSheet.create({
  section: {
    flex: 1,
    paddingLeft: 16,
    paddingEnd: 8,
    paddingTop: 8,
    paddingBottom: 16,
  },
  topText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
    ...marginStyles.mb6,
    ...marginStyles.mt6,
    ...marginStyles.ml2,
  },
  bottomSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  image: {
    width: 24,
    height: 24,
    resizeMode: 'cover',
    borderRadius: 5,
    marginHorizontal: 2,
    marginRight: 10,
  },
  textContainer: {
    flex: 1,
  },
});
