import {Dimensions, FlatList, Image, StyleSheet, View, Text, StatusBar, Platform, BackHandler} from 'react-native';
import React, {useEffect, useState} from 'react';
import FlightCombo from './FlightCombo';
import CabCombo from './CabCombo';
import {HOLIDAY_ROUTE_KEYS, HolidayNavigation} from '../../../Navigation';
import PhoenixHeader from '../PhoenixHeader';
import ComboShortListCard from './ShortListCard/ComboShortListCard';
import {fetchCommuteChangeListing} from '../../../utils/HolidayNetworkUtils';
import {isNetworkAvailable} from '@mmt/legacy-commons/Common/utils/AppUtils';
import FullPageError from '../ReviewRating/components/FullPageError';
import HolidayDetailLoader from '../HolidayDetailLoader';
import {ERROR_MESSAGES, ERROR_TYPE, TIPS, TRANSPORT_TYPE, VIEW_STATE} from './ComboConstats';
import {
    checkCombo,
    getDefaultSelectedItem,
    getTripReturnDateFromDestinationDetail,
    isSelectedItemDefault,
    getSelectedItemIndex,
} from './Utils/ComboUtils';
import FlightPlusCabCombo from './FlightPlusCabCombo';
import {componentImageTypes, packageActionComponent, packageActions} from '../../DetailConstants';
import {holidayColors} from '../../../Styles/holidayColors';
import {getImageUrl, IMAGE_ICON_KEYS} from '../../../Common/Components/HolidayImageUrls';
import BottomMessage from '../../../Common/Components/BottomMessage';
import ConfirmationPopup from '../ConfirmationPopup/ConfirmationPopup';
import {fontStyles} from '../../../Styles/holidayFonts';
import {marginStyles, paddingStyles} from '../../../Styles/Spacing';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { isEmpty } from 'lodash';
import { getAndroidBottomBarHeight } from '../../../utils/HolidayUtils';
import { holidayNavigationPop, holidayNavigationPush } from '../../Utils/DetailPageNavigationUtils';
import { Overlay } from '../DetailOverlays/OverlayConstants';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import useBackHandler from '../../../hooks/useBackHandler';
import { on } from 'events';

const ComboPage = (props) => {
    const [viewState, setViewState] = useState(VIEW_STATE.LOADING);
    const [data, setData] = useState(null);
    const {
        dynamicPackageId = '',
        departureDetail,
        destinationDetail,
        onComponentChange,
        hideOverlays,
        showOverlay,
      } = props || {};
    const [selectedItem, setSelectedItem] = useState(null);
    const [confirmDialogVisibility, setConfirmDialogVisibility] = useState(false);

    const {cityName: sourceCity = ''} = departureDetail || {};
    const [destination] = destinationDetail.destinations;
    const {name: DestinationCity = ''} = destination || {};
   const returnDate = getTripReturnDateFromDestinationDetail(destinationDetail);
    const packageSrcDestData = {sourceCity, DestinationCity, departureDate : departureDetail?.departureDate, returnDate };
    const  subtitleData = [ `${sourceCity} - ${DestinationCity} |  ${DestinationCity} - ${sourceCity} `];
    const windowHeight = Dimensions.get('window').height;

    const showUpdate = !isSelectedItemDefault(selectedItem, data);

    const captureClickEvents = ({ eventName = '', prop1 = '', actionType = {}, value = ''}) => {
        logPhoenixDetailPDTEvents({
            actionType: !isEmpty(actionType) ? actionType : PDT_EVENT_TYPES.buttonClicked,
            value: value || eventName.replace(/_/g, '|'),
            subPageName: prop1,
        })
        trackPhoenixDetailLocalClickEvent({
            eventName,
            prop1
        });
    }

    const backHandlerCallback = React.useCallback(() => {
        onBackPressed();
        return true;
      }, [showUpdate]);
    useBackHandler(backHandlerCallback);

    //TODO - Ask ashish use of this and check in mweb
    // useEffect(() => {
    //     HolidayNavigation.getNavigationRef().setOptions({
    //         onComponentChange: () => {
    //         },
    //     });
    // }, [HolidayNavigation.getNavigationObj()]);
    const onUpdateClick = () => {
        const actionData = {
            action: packageActions.CHANGE,
            dynamicPackageId: dynamicPackageId,
            variantId: selectedItem.variantId,
            packageComponent: packageActionComponent.COMMUTE,
        };
        const selectedItemIndex = getSelectedItemIndex({ selectedItem, data: data?.variants });
        captureClickEvents({
          eventName: `update_option_${selectedItemIndex}_${selectedItem?.variantType}`,
          value: `update|option|${selectedItemIndex}|${selectedItem?.variantType}`,
          prop1: 'details:moretransportoptions',
        });
        onComponentChange(actionData, componentImageTypes.COMMUTE);
        holidayNavigationPop({
            overlayKeys: [Overlay.COMMUTE_VIEW_OPTIONS],
            hideOverlays,
          });
    };

    const fetchDataAndUpdateState = async (packageId) => {
        /*const hasNetwork = await isNetworkAvailable();
        if (!hasNetwork) {
            setViewState(VIEW_STATE.NO_NETWORK);
            return;
        }*/

        const response = await fetchCommuteChangeListing(packageId);
        if (response && response.success && response?.variants?.length > 0) {
            setViewState(VIEW_STATE.SUCCESS);
            setData(response);
            setSelectedItem(getDefaultSelectedItem(response));
        } else {
            setViewState(VIEW_STATE.ERROR);
            setData(null);
        }
    };

    useEffect(() => {
        fetchDataAndUpdateState(dynamicPackageId);
    }, [dynamicPackageId]);

    useEffect(() => {
        if (!isEmpty(data)) {
            const selectedVariant = data?.variants?.filter((variant)=>variant.selected)?.[0];
            const isAnyVariantSelected = Number(Boolean(selectedVariant));
            captureClickEvents({
                eventName: `Selected_${isAnyVariantSelected}_Total_${data?.variants?.length || 0}`,
                actionType: PDT_EVENT_TYPES.pageRenedered,
            });
            if (!isEmpty(selectedVariant)) {
                captureClickEvents({
                  eventName: `options_1_${selectedVariant?.variantType}`,
                  value: `options|1|${selectedVariant?.variantType}`,
                  actionType: PDT_EVENT_TYPES.pageRenedered,
                });
            }
        }
      }, [data]);
    const onBackPressed = () => {
        if (showUpdate && !confirmDialogVisibility) {
            setConfirmDialogVisibility(true);
        } else {
            captureClickEvents({
                eventName: 'back',
                prop1: 'details:moretransportoptions',
            });
            holidayNavigationPop({
                overlayKeys: [Overlay.COMMUTE_VIEW_OPTIONS],
                hideOverlays,
              });
        }
    };

    const onViewDetailsClick = ({item, index}) => {
        captureClickEvents({
            eventName: `View_details_${index}_${item?.variantType}`,
            value: `View_details|${index}|${item?.variantType}`,
            prop1: 'details:moreTransportOptions',
          });

          holidayNavigationPush({
              overlayKey: Overlay.COMMUTE_DETAIL_PAGE,
              pageKey: HOLIDAY_ROUTE_KEYS.COMMUTE_DETAIL_PAGE,
              props: {
                item,
                data,
                subtitleData,
                packageSrcDestData,
                dynamicPackageId,
              },
              showOverlay,
              hideOverlays,
          });
      };

    const onRefreshPressed = () => {
        setViewState(VIEW_STATE.LOADING);
        fetchDataAndUpdateState(dynamicPackageId);
    };

    const renderStickyHeader = () => {
        return (<PhoenixHeader
            title={'Change Mode of Transport'}
            subtitleData={subtitleData}
            handleClose={onBackPressed}
        />);
    };

    const renderErrorView = (errorType) => {
        const error = ERROR_MESSAGES[errorType];
        return (
            <FullPageError
                title={error.TITLE}
                subTitle={error.SUB_TITLE}
                suggestion={error.SUGGESTION}
                onRefreshPressed={onRefreshPressed}
                renderStickyHeader={renderStickyHeader}
                onBackPressed={onBackPressed}
            />
        );
    };

    const renderProgressView = () =>
        <HolidayDetailLoader
            openingSavedPackage
            showDateText={false}
            changeAction
            loadingText="Loading commute data ..."
        />;


    const renderListItem = ({ item, index }) => {
        const type = checkCombo(item);

        const handleViewDetailClick = (item) => {
            onViewDetailsClick({ item, index });
        };
        const handleSelectItem = (item) => {
            captureClickEvents({
                eventName: `change_${index}_${item?.variantType}`,
                prop1: 'details:moretransportoptions',
            });
            setSelectedItem(item);
        };
        switch (type) {
            case TRANSPORT_TYPE.FLIGHT:
            return (
                <FlightCombo
                item={item}
                response={data}
                setSelectedItem={handleSelectItem}
                selectedItem={selectedItem}
                onViewDetailsClick={handleViewDetailClick}
                />
            );
            case TRANSPORT_TYPE.CABS:
            case TRANSPORT_TYPE.NMC:
            return (
                <CabCombo
                item={item}
                response={data}
                setSelectedItem={handleSelectItem}
                selectedItem={selectedItem}
                onViewDetailsClick={handleViewDetailClick}
                />
            );
            case TRANSPORT_TYPE.COMBO:
            case TRANSPORT_TYPE.NONE:
            return (
                <FlightPlusCabCombo
                item={item}
                response={data}
                setSelectedItem={handleSelectItem}
                selectedItem={selectedItem}
                onViewDetailsClick={handleViewDetailClick}
                />
            );
            default:
            return []; // or return some fallback component
        }
        };

    const renderListItems = () => {
        const { variants = [] } = data || {};
        return (
                <FlatList
                    data={Object.values(variants)}
                    renderItem={renderListItem}
                    keyExtractor={(item, index) => 'key-' + index}
                    contentContainerStyle={styles.container}
                />
        );
    };

    const Info = () => {
        return (
            <View style={styles.tipContainer}>
                <Image source={{uri: getImageUrl(IMAGE_ICON_KEYS.TIPS_UPDATE_OUTLINE)}} style={styles.tipIcon}/>
                <Text style={styles.tipText}>{TIPS}</Text>
            </View>
        );
    };

    const hideConfirmationPopup = () => setConfirmDialogVisibility(false);

    const handleCloseMessageStrip = () => {
        captureClickEvents({
          eventName: 'Close_Package_Customized',
          prop1: 'details:moretransportoptions',
        });
    };
    const renderSuccess = () => (
        <View style={styles.parent}>
          {renderStickyHeader()}
          <ComboShortListCard
            selectedItem={selectedItem}
            DestinationCity={DestinationCity}
            sourceCity={sourceCity}
            onUpdateClick={onUpdateClick}
            showUpdate={showUpdate}
          />
          <Info />
          {renderListItems()}
          <BottomMessage
            message={data?.customisedVariantDetails?.message}
            handleCloseMessage={handleCloseMessageStrip}
          />
          {confirmDialogVisibility && (
            <ConfirmationPopup
              confirmDialogVisibility={confirmDialogVisibility}
              onUpdatePackageClickFromPopup={onUpdateClick}
              onNotNowClicked={onBackPressed}
              onShowMeClicked={hideConfirmationPopup}
            />
          )}
        </View>
      );

    switch (viewState) {
        case VIEW_STATE.LOADING:
            return renderProgressView();
        case VIEW_STATE.NO_NETWORK:
            return renderErrorView(ERROR_TYPE.NO_NETWORK);
        case VIEW_STATE.ERROR:
            return renderErrorView(ERROR_TYPE.GENERIC_ERROR);
        case VIEW_STATE.SUCCESS:
            return renderSuccess();
        default:
            return [];
    }

};

const styles = StyleSheet.create({
    parent:{
        height: '100%',
        backgroundColor:holidayColors.lightGray2,
    },
    container: {
        backgroundColor: holidayColors.lightGray2,
        ...Platform.select({
            ios: {},
            android: {
                paddingBottom: getAndroidBottomBarHeight() + 10,
            },
            web : {
                flex: 1 ,
                paddingBottom: 50,

            },
        }),
        paddingBottom: 50,
    },
    tipText:{
        ...fontStyles.labelSmallBold,
        color: holidayColors.lightGray,
        ...marginStyles.mr20,
        ...marginStyles.ml6,
    },
    tipIcon :{
        height: 16,
        width: 16,
       ...marginStyles.ml16,
        ...marginStyles.mt2,
    },
    tipContainer:{
        flexDirection: 'row',
        paddingRight: 26,
        backgroundColor: holidayColors.lightGray2,
        ...paddingStyles.pt12,
        ...paddingStyles.pb12,
    },
    pageWrap: {
        flex: 1,
        paddingTop: 0,
    },
});


export default ComboPage;
