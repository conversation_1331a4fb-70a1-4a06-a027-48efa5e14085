import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconClose from './Components/images/ic_cross.webp';
import iconUndo from './Components/images/ic_undo.png';
import iconFlight from './Components/images/ic_flight.png';
import iconHotels from './Components/images/ic_hotel.png';
import iconVisa from '@mmt/legacy-assets/src/Visa/ic_documents.webp';
import iconActivity from './Components/images/ic_sightSeeing.png';
import iconTransfers from './Components/images/ic_cabs.png';
import BottomSheetOverlay from '../Common/Components/BottomSheetOverlay';
import { packageActions } from './DetailConstants';
import { getDiffPackagePriceLabel } from './Utils/HolidayUtils';
import { fontStyles } from '../Styles/holidayFonts';
import { holidayColors } from '../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../Styles/Spacing';
import { smallHeightSeperator } from '../Styles/holidaySpacing';
const activityMsg = {
  single: 'Activity Removed',
  multiple: 'Activities Removed',
};

const CustomizationPopup = (props) => {
  const { handlePopup, toggleUndoBottomSheet, undoStack, popFromStack } = props;
  const stackUndo = [...undoStack];
  stackUndo.reverse();
  return (
    <BottomSheetOverlay
      title={`View your ${stackUndo.length} ${stackUndo > 1 ? 'Customizations' : 'Customization'}`}
      toggleModal={() => toggleUndoBottomSheet(false)}
      visible={undoStack}
      containerStyles={styles.containerStyles}
    >
      <View>
        <View style={styles.popContent}>
          {stackUndo.map((item, index) => {

            // Handle Action
            let action = 'Changed';
            if (item.action === packageActions.REMOVE || item.action === packageActions.TOGGLE) {
              action = 'Removed';
            } else if (item.action === packageActions.ADD) {
              action = 'Added';
            }

            // Handle LOB icon and name.
            let lobName = item.lob;
            let icon = iconFlight;
            if (item.lob === 'Hotel') {
              icon = iconHotels;
            } else if (item.lob === 'Activity') {
              icon = iconActivity;
            } else if (item.lob === 'Transfer' || item.lob === 'CAR_ITINERARY') {
              icon = iconTransfers;
              lobName = 'Transfer';
            } else if (item.lob === 'Flight' || item.lob === 'FLIGHT') {
              lobName = 'Flight';
            } else if (item.lob === 'Visa' || item.lob === 'VISA') {
              icon = iconVisa;
              lobName = 'Visa';
            }

            return (
              <View
                key={index}
                style={[
                  styles.contentRow,
                  stackUndo.length - 1 !== index ? smallHeightSeperator : {},
                ]}
              >
                <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                  <View style={AtomicCss.marginRight15}><Image source={icon} style={styles.iconImage} /></View>
                  <View>
                    <View>
                      <Text style={styles.lobName}>
                        {lobName} {action}
                        {item.activityCount &&
                          ` + ${item.activityCount} ${item.activityCount < 2 ? activityMsg.single : activityMsg.multiple
                          }`}
                      </Text>
                    </View>
                    <View style={[styles.lobValueChangeContainer]}>
                      <Text
                        style={[
                          styles.lobValueChange,
                          { color: item.priceChange >= 0 ? '#eb2026' : '#1a7971' },
                        ]}
                      >
                        {getDiffPackagePriceLabel(item.priceChange)}
                      </Text>
                      <Text style={[styles.lobValueChange]}>/person</Text>
                    </View>
                  </View>
                </View>
                {index === 0 && (
                  <TouchableOpacity
                    onPress={popFromStack}
                    style={[AtomicCss.flexRow, AtomicCss.alignCenter]}
                  >
                    <View style={[AtomicCss.marginRight5]}>
                      <Image source={iconUndo} style={styles.iconImage} />
                    </View>
                    <View>
                      <Text style={styles.undoText}>
                        Undo
                      </Text>
                    </View>
                  </TouchableOpacity>
                )}
              </View>);
          }
          )}
        </View>
      </View>
    </BottomSheetOverlay>
  );
};

const styles = StyleSheet.create({
  iconClose: {
    width: 18,
    height: 18,
    resizeMode: 'cover',
    marginRight: 10,
  },
  popContent: {
    ...paddingStyles.pa16,
  },
  iconImage: {
    width: 20,
    height: 20,
    resizeMode: 'cover',
  },
  contentRow: {
    ...paddingStyles.pv16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  lobName: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.gray,
  },
  lobValueChangeContainer: {
    ...AtomicCss.flexRow,
    ...AtomicCss.alignCenter,
    ...marginStyles.mt6,
  },
  lobValueChange: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  undoText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
  },
  containerStyles: {
    padding: 16
  }
});

export default CustomizationPopup;
