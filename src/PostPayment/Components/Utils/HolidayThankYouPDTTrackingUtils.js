import {
  getComponentsForPDT,
  getLobCategory,
  initializeCampaignDetails,
  initializeSearchContext,
  logHolidaysEventToPDT,
  populateExperimentalDetails,
  populatePageContext,
  populateSearchContext,
} from 'mobile-holidays-react-native/src/utils/HolidayPDTTrackingV3';
import { THANKYOU_PDT_PAGE_NAME } from '../HolidayThankyouUtils';
import { getThankYouPDTObj, updateThankYouPDTObj } from './HolidayThankYouPDTDataHolder';
import { DOM_BRANCH } from 'mobile-holidays-react-native/src/HolidayConstants';
import {
  createChildAgeArrayFromApi,
  createRoomDataFromRoomDetailsPhoenix,
} from 'mobile-holidays-react-native/src/utils/RoomPaxUtils';
import { getPaxDetails } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import { isEmpty } from 'lodash';
import fecha from 'fecha';
import { getSelectedCoupon } from 'mobile-holidays-react-native/src/Review/Utils/HolidayReviewUtils';

const getBookingInfo = ({ thankYouData, reviewDetail }) => {
  const {
    totalAmount,
    depDate = '',
    endDate = '',
    tagDestName = '',
    depCityName = '',
    paymentReferenceId = '',
  } = thankYouData || {};
  const { roomDetail = {}, pricingDetail = {}, dealDetail = {} } = reviewDetail || {};
  const { currencyCode = '', serviceTax } = pricingDetail || {};
  const { rooms = [] } = roomDetail || {};

  const bookingInfo = {
    currency: currencyCode,
    price: totalAmount,
    tax: serviceTax,
    applied_coupon: getSelectedCoupon(dealDetail).couponCode,
    from_date_time: new Date(depDate).getTime(),
    to_date_time: new Date(endDate).getTime(),
    number_of_rooms: rooms.length,
    origin: depCityName,
    destination: tagDestName,
    booking_id: paymentReferenceId,
    booking_date: fecha.format(new Date(),('YYYY-MM-DD')),
  };
  return bookingInfo;
};

export const getThankYouEventDetail = ({ thankYouData, reviewDetail }) => {
  const { isPremiumPackage = false } = thankYouData || {};
  const category = isPremiumPackage ? 'Premium' : '';
  return {
    components: {
      product_list: [
        {
          id: reviewDetail.id || "null",
          dynamic_id: reviewDetail.dynamicId,
          ...(isPremiumPackage ? {category: [category]} : {}),
        },
      ],
    },
};
};

const cleanApplicableCoupons = jsonData => {
  try {
    // Validate input
    if (!jsonData || typeof jsonData !== 'object') {
      console.error('Invalid input: jsonData must be an object');
    }

    // Create a deep copy to avoid mutating the original data
    const cleanedData = JSON.parse(JSON.stringify(jsonData));

    // Navigate to the product list and filter applicable coupons
    // Handle both structures: event_detail.components and direct components
    let productList = null;

    if (cleanedData.event_detail &&
      cleanedData.event_detail.components &&
      cleanedData.event_detail.components.product_list) {
      productList = cleanedData.event_detail.components.product_list;
    } else if (cleanedData.components && cleanedData.components.product_list) {
      productList = cleanedData.components.product_list;
    }

    if (productList) {
      productList.forEach(product => {
        if (product.price &&
          product.price.discounts &&
          product.price.discounts.applicable_coupons) {

          // Filter out coupons where is_applied is false
          product.price.discounts.applicable_coupons =
            product.price.discounts.applicable_coupons.filter(coupon => coupon.is_applied === true);
        }
      });
    }

    return cleanedData;

  } catch (error) {
    console.error('Error cleaning applicable coupons:', error.message);
    // Return original data if cleaning fails
    return jsonData;
  }
};

export const logThankYouPDTEvents = ({ actionType, value, thankYouData, reviewDetail, event_detail = {} ,shouldTrackToAdobe=true}) => {
  let pdtObj = {
    ...getThankYouPDTObj(),
    event_detail : cleanApplicableCoupons(event_detail),
  };
  logHolidaysEventToPDT({
    pdtObj,
    value,
    actionType,
    bookingInfo: getBookingInfo({ thankYouData, reviewDetail }),
    shouldTrackToAdobe
  });
};

export const initThankYouPDTObj = ({ thankYouData, reviewDetail }) => {
  const branch = thankYouData?.branch || DOM_BRANCH;
  const pdtObj = initializeThankYouPDTObj({ branch, reviewDetail });
  updateThankYouPDTObj({ pdtObj });
};

const setThankYouPageContext = ({ branch }) => {
  const lobCategory = getLobCategory({ branch });
  return populatePageContext({
    funnelStep: THANKYOU_PDT_PAGE_NAME,
    pageName: THANKYOU_PDT_PAGE_NAME,
    lobCategory,
  });
};

export const initializeThankYouPDTObj = ({ branch, reviewDetail }) => {
  let pdtObj = getThankYouPDTObj();
  return {
    ...JSON.parse(JSON.stringify(pdtObj)),
    experiment_details: populateExperimentalDetails(),
    page_context: setThankYouPageContext({ branch }),
    search_context: populateThankYouSearchContext({ reviewDetail }),
    campaign_details: initializeCampaignDetails(),
  };
};

export const populateThankYouSearchContext = ({ reviewDetail }) => {
  const { departureDetail = {}, tagDestination = {}, roomDetail = {} } = reviewDetail || {};
  const { rooms = [] } = roomDetail;
  const {
    cityId = '',
    cityName = '',
    locusDetails: depLocusDetails = {},
    departureDate = '',
  } = departureDetail || {};
  const { id = '', name = '', locusDetails: destLocusDetails = {} } = tagDestination;
  const deptCity = {
    id: cityId,
    name: cityName,
    locusId: depLocusDetails?.locusCode,
    type: depLocusDetails?.locusType,
  };
  const destCity = {
    id,
    name,
    locusId: destLocusDetails?.locusCode,
    type: depLocusDetails?.locusType,
  };
  const pdtObj = getThankYouPDTObj();
  const { search_context = {} } = pdtObj;
  let childAgeArray = [];
  if (rooms) {
    childAgeArray = createChildAgeArrayFromApi(rooms);
  }
  const paxDetails = {
    ...getPaxDetails({ roomDetails: rooms }),
    roomData: createRoomDataFromRoomDetailsPhoenix(rooms, childAgeArray),
  };
  const prevSearchContext = isEmpty(search_context) ? initializeSearchContext() : search_context;
  return populateSearchContext({
    paxDetails,
    destCity,
    deptCity,
    packageDate: departureDate,
    prevSearchContext,
  });
};
