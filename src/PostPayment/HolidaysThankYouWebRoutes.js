import React from 'react';
import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import {connect} from 'react-redux';
import HolidayThankYou from './Components/HolidayThankYou';
import {withRouterState} from 'web/WebRouter';
import url from 'url';
import {PAYMENT_STATUS} from '../HolidayConstants';
import HolidayDeeplinkParser from '../utils/HolidayDeeplinkParser';

class HolidaysThankYouWeb extends React.Component {
    constructor(props) {
        super(props);
        const urlObj = url.parse(window.location.href, window.location.search);
        const {query} = urlObj;
        const aff = HolidayDeeplinkParser.getAffiliateFromUrl(window.location.href);
        if (query && query.PayId) {
            this.state = {
                payId: query.PayId,
                aff
            };
        } else {
            this.state = {
                paymentResponse: PAYMENT_STATUS.FAILURE,
                aff
            };
        }
    }

    render() {
        return (
            <HolidayThankYou {...this.state}/>
        );
    }
}

const mapStateToProps = state => ({
    ...state,
});

export const HolidaysThankYouContainer = connect(mapStateToProps, null)(HolidaysThankYouWeb);

const HolidaysThankYouRoutes = () => (
    <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
        <Switch>
            <Route exact path="/holidays/onlineBookingPaymentThankyouAction"
                   component={withRouterState(HolidaysThankYouContainer)}/>
        </Switch>
    </View>
);

export default withRouter(HolidaysThankYouRoutes);
