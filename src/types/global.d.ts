/**
 * Comprehensive Global TypeScript Declaration File
 *
 * This file provides automatic type declarations for all JavaScript modules
 * in the project using modern TypeScript patterns and wildcard declarations.
 *
 * Key Features:
 * - Automatic handling of all .js files
 * - Wildcard patterns for common file types
 * - React Native specific declarations
 * - Fallback types for unknown modules
 * - Path mapping for different directory structures
 */

// =============================================================================
// UNIVERSAL JAVASCRIPT MODULE DECLARATIONS
// =============================================================================

/**
 * Universal wildcard declaration for ALL JavaScript files
 * This automatically provides type declarations for any .js file in the project
 */
declare module "*.js" {
  const content: any;
  export = content;
}

/**
 * Universal wildcard declaration for JSX files
 * Provides React component types for .jsx files
 */
declare module "*.jsx" {
  import { ComponentType } from 'react';
  const Component: ComponentType<any>;
  export default Component;
  export const [key: string]: any;
}

// =============================================================================
// DIRECTORY-BASED WILDCARD PATTERNS
// =============================================================================

/**
 * Utils directory - All utility JavaScript files
 * Provides common utility function signatures
 */
declare module "*/utils/*" {
  export const [key: string]: any;
  export default any;
}

/**
 * Components directory - All component JavaScript files
 * Provides React component types
 */
declare module "*/Components/*" {
  import { ComponentType } from 'react';
  const Component: ComponentType<any>;
  export default Component;
  export const [key: string]: any;
}

/**
 * Constants files - All constant JavaScript files
 * Provides object/constant exports
 */
declare module "*Constants*" {
  export const [key: string]: any;
  export default Record<string, any>;
}

/**
 * Styles directory - All style JavaScript files
 * Provides style object types
 */
declare module "*/Styles/*" {
  export const [key: string]: Record<string, any>;
  export default Record<string, any>;
}

// =============================================================================
// REACT NATIVE SPECIFIC PATTERNS
// =============================================================================

/**
 * React Native module pattern
 * Handles modules with react-native in the path
 */
declare module "*react-native*" {
  export const [key: string]: any;
  export default any;
}

/**
 * Mobile holidays react native specific modules
 */
declare module "mobile-holidays-react-native/*" {
  export const [key: string]: any;
  export default any;
}

declare module '*/HolidayUtils' {
  export function getFabT2QconfigDefaultData(): Record<string, any>;
  export function getExperimentValue(key: string, defaultValue: any): any;
  export function getExperimentValueWithLob(params: {
    lob: string;
    key: string;
    defaultValue: any;
  }): any;
  export function isRawClient(): boolean;
  export function isLuxeFunnel(): boolean;
  export function getSubFunnelName(): string;
  export function getDepartureCity(): Promise<string>;
  export function isUserLoggedIn(): boolean;
  export function getUserDetails(): Promise<any>;
  export function isAndroidClient(): boolean;
  export function isIosClient(): boolean;
  export function isAppPlatform(): boolean;
  export function getdeviceType(): string;
  export function getPlatformIdentifier(): string;
  export function createCtaOpts(showCall: boolean, showQuery: boolean, showChat: boolean): string;
  export function getDaysDiff(startTime: Date, endTime: Date): number;
  export function getThemeFilterName(): string;
  export function getPaxConfig(pageDataMap: any): string;
  export function getGoogleAPIKeyForAllPlarforms(): Promise<string>;
  export function getCurrentPosition(): Promise<any>;
  export function getAcmeXSellConfig(): any;
  export function getLocationPermissionStatus(): Promise<boolean>;
  export function getStoragePermissionStatus(): Promise<boolean>;
  export function getSearchTravelDate(date: any, metaData: any): any;
  export function padToKDigits(num: number, k: number): string;
  export function getAndroidBottomBarHeight(): number;
  export function isNonHeaderAffiliate(aff: string): boolean;
  export function isSummaryTabDefaultOpen(params: { fromPreSales?: boolean }): boolean;
  export function openGenericDeeplink(params: { url?: string }): void;
}

// Generic declaration for any JavaScript module that doesn't have specific types
declare module '*.js' {
  const content: any;
  export = content;
}

// Declaration for constants files
declare module '*/HolidayConstants' {
  export const FAB_PULSE_ANIMATION_DEFAULT_CONFIG: Record<string, any>;
  export const FabT2QConfigDefaultData: Record<string, any>;
  export const LuxFabT2QConfigDefaultData: Record<string, any>;
  export const RAW_PLATFORM_NAME: string;
  export const variantEnum: Record<string, any>;
  export const AFFILIATES: Record<string, string>;
  export const FUNNELS: Record<string, string>;
  export const SUB_FUNNELS_TYPES: Record<string, string>;
  export const DOM_BRANCH: string;
  export const USER_DEFAULT_CITY: string;
  // Add other constants as needed
}

// Declaration for tracking constants
declare module '*/HolidayTrackingConstants' {
  const content: Record<string, any>;
  export = content;
}

// Declaration for web routes
declare module '*/HolidayWebRoute' {
  const content: Record<string, any>;
  export = content;
}

// Declaration for reducers and store
declare module '*/holidaysReducers' {
  const content: any;
  export = content;
}

declare module '*/holidaysStore' {
  const content: any;
  export = content;
}

// Declaration for JSX components without TypeScript
declare module '*.jsx' {
  import { ComponentType } from 'react';
  const Component: ComponentType<any>;
  export default Component;
}

// Declaration for other utility modules that might be imported
declare module '*/HolidayNetworkUtils' {
  export function fetchPackageDetailShareUrl(...args: any[]): Promise<any>;
  export function fetchQueueIdentifier(...args: any[]): Promise<any>;
  export function getRequestHeaders(...args: any[]): any;
  export function fetchFabCta(...args: any[]): Promise<any>;
  // Add other network utility functions as needed
}

// Declaration for style modules
declare module '*/holidayFonts' {
  export const fontStyles: Record<string, any>;
}

declare module '*/holidayColors' {
  export const holidayColors: Record<string, any>;
}

declare module '*/holidayBorderRadius' {
  export const holidayBorderRadius: Record<string, any>;
}

declare module '*/Spacing' {
  export const paddingStyles: Record<string, any>;
  export const marginStyles: Record<string, any>;
}

// Declaration for utility modules
declare module '*/CtaUtils' {
  export const NEW_CTA_GRADIENT_COLORS: any;
  export function getCtaGradientColors(...args: any[]): any;
  export function getFabBorderStyle(...args: any[]): any;
  export function getFabsIconUrls(...args: any[]): any;
  export const LUXE_CTA_BORDER_GRADIENT_COLORS: any;
}

declare module '*/HolidayImageUrls' {
  export function getImageUrl(...args: any[]): string;
  export const IMAGE_ICON_KEYS: Record<string, any>;
}

declare module '*/HolidayPDTConstants' {
  export const PDT_EVENT_TYPES: Record<string, any>;
}

declare module '*/textTransformUtil' {
  export function capitalizeText(text: string): string;
}

// Declaration for component modules
declare module '*/CommonOverlay' {
  export const COMMON_OVERLAYS: Record<string, any>;
}

// Declaration for modules with mobile-holidays-react-native prefix
declare module 'mobile-holidays-react-native/src/utils/HolidayUtils' {
  export * from '*/HolidayUtils';
}

declare module 'mobile-holidays-react-native/src/Styles/holidayFonts' {
  export * from '*/holidayFonts';
}

declare module 'mobile-holidays-react-native/src/Styles/holidayColors' {
  export * from '*/holidayColors';
}

declare module 'mobile-holidays-react-native/src/Styles/Spacing' {
  export * from '*/Spacing';
}

declare module 'mobile-holidays-react-native/src/utils/HolidayPDTConstants' {
  export * from '*/HolidayPDTConstants';
}

declare module 'mobile-holidays-react-native/src/utils/textTransformUtil' {
  export * from '*/textTransformUtil';
}

declare module 'mobile-holidays-react-native/src/Common/Components/CommonOverlay' {
  export * from '*/CommonOverlay';
}

// Global type augmentations for common patterns
declare global {
  interface Window {
    // Add any global window properties if needed
  }

  // Add NodeJS namespace for timeout types
  namespace NodeJS {
    interface Timeout {}
  }

  // Add JSX namespace for React components
  namespace JSX {
    interface Element {}
  }
}

// Export empty object to make this a module
export {};
