// Global TypeScript declarations for JavaScript modules in the project

declare module '*/HolidaysPokusUtils' {
  export function getPokusConfigKey(key: string): string;
  export function getPokusForNewDetailContent(fromPresales?: boolean): boolean;
  export function getPokusForPackageHighlights(): boolean;
  export function getPokusForGalleryV2(): boolean;
  export function getPokusForMandatoryPaxDate(): boolean;
  export function getPokusforTIEntrySection(): string;
  export function getPokusforReviewUpdateSection(): boolean;
  export function getPokusforReviewTcsV2Section(): boolean;
  export function getMyraChatIconPokus(): boolean;
  export function getOfferTimerMobile(): boolean;
  export function getCuesConfig(): Record<string, any>;
  export function getIsSearchFilter(): {
    searchV2: boolean;
    menuList: boolean;
    filter: boolean;
  };
  export function getEnableGeoLoc(): boolean;
  export function getLandingSupresedSections(): string;
  export function getPhoenixVariantType(): string;
  export function getHolidayCovidConent(): boolean;
  export function getFabPulseAnimConfig(): Record<string, any>;
  export function getHotelCollectionCard(): boolean;
  export function getActivityCollectionCard(): boolean;
  export function getGalleryWidget(): boolean;
  export function getHolRecentSearchExpireDays(): number;
  export function getEnableGroupingSection(): string;
  export function getShow6EGroupPackages(): boolean;
  export function getShowFeedbackPresales(): boolean;
  export function getHolShowStoryMob(): boolean;
  export function getMaxUndoAllowed(): number;
  export function getShowHECardonDetail(): boolean;
  export function getShowQuotesCompare(): boolean;
  export function getEnableCarouselViewDetail(): boolean;
  export function getShowOvernightsFilter(): boolean;
  export function getHolQueryFormDefaultTravelDaysCount(): number;
  export function getPhoenixReviewSectionsExpanded(): boolean;
  export function getPhoenixReviewSectionsOrder(sections: Record<string, any>): number[];
  export function getShowBudgetGraphHol(): boolean;
  export function getHolStoryExpiryInDaysMob(): number;
  export function getHolidaysCtaData(): any[];
  export function getExcludedInterventionTypes(): string;
  export function getDisableUserGroupHol(): boolean;
  export function getApiPokusListing(): string;
  export function getOpenIndigoPackageoutApp(): boolean;
  export function getGICustomerCareNum(): string;
  export function getIGOCustomerCareNum(): string;
  export function getHDFCCustomerCareNum(): string;
  export function getAIXCustomerCareNum(): string;
  export function getShowFreebie(): boolean;
  export function getCoachMarkDaysDelayHol(): number;
  export function getShowTCSBanner(): boolean;
  export function getTpPostSalesQueryNumHol(): string;
  export function getShowWGBannerLandingHol(): boolean;
  export function getShowPhoenixGroupingV2(): boolean;
  export function showRNESection(): boolean;
  export function removeLastPage(): boolean;
  export function enableReferToContacts(): boolean;
  export function getRneContactApiBatchSize(): number;
  export function showNewRVSSection(): boolean;
  export function showNewCYSSection(): boolean;
  export function getShowNewActivityDetail(): boolean;
  export function showInsuranceSection(): boolean;
  export function getFabCtaConfig(): Record<string, any>;
  export function getT2QFabFlags(): {
    isNewFabHeader: boolean;
    showPrivacyPolicy: boolean;
    isSingleStepSubmission: boolean;
  };
  export function getT2QFabIconFlag(): boolean;
  export function showNewRoomAndGuest(): boolean;
  export function enableSearchByImage(): {
    landingEntryPoint: boolean;
    searchDestEntryPoint: boolean;
    landingSearchDest: boolean;
    showNewSearchByImage: boolean;
  };
  export function getSearchByImageWidth(): {
    maxWidth: number;
    maxHeight: number;
  };
  export function showFabAnimationExtended(): boolean;
  export function getDetailDefaultTabDayPlan(): {
    detailsV3: string;
    details_V3_presales: string;
  };
  export function getNewFabAnimationData(): {
    isNewFabCta: boolean;
    fabCtaText: string;
    isShimmerEnabled: boolean;
    isRotatingEnabled: boolean;
  };
  export function getReviewpagePopupInterval(): number;
  export function showMMTBlack(): boolean;
  export function showMMTPersonalization(): boolean;
  export function showMMTPersonalizationV2(): boolean;
  export function showTravelTidbitsV2(): boolean;
  export function getTrackPDTCTAHandler(): boolean;
  export function showTravelPlex(): boolean;
  export function travelPlexRollbackEnabled(): boolean;
  export function showHolAgentOnLandingAndListingPage(): boolean;
  export function showHolAgentOnDetailAndReviewPage(): boolean;
  export function showVPPAndInsuranceOnDetails(): boolean;
  export function getMyraText(): string;
}

declare module '*/HolidayUtils' {
  export function getFabT2QconfigDefaultData(): Record<string, any>;
  export function getExperimentValue(key: string, defaultValue: any): any;
  export function getExperimentValueWithLob(params: {
    lob: string;
    key: string;
    defaultValue: any;
  }): any;
  export function isRawClient(): boolean;
  export function isLuxeFunnel(): boolean;
  export function getSubFunnelName(): string;
  export function getDepartureCity(): Promise<string>;
  export function isUserLoggedIn(): boolean;
  export function getUserDetails(): Promise<any>;
  export function isAndroidClient(): boolean;
  export function isIosClient(): boolean;
  export function isAppPlatform(): boolean;
  export function getdeviceType(): string;
  export function getPlatformIdentifier(): string;
  export function createCtaOpts(showCall: boolean, showQuery: boolean, showChat: boolean): string;
  export function getDaysDiff(startTime: Date, endTime: Date): number;
  export function getThemeFilterName(): string;
  export function getPaxConfig(pageDataMap: any): string;
  export function getGoogleAPIKeyForAllPlarforms(): Promise<string>;
  export function getCurrentPosition(): Promise<any>;
  export function getAcmeXSellConfig(): any;
  export function getLocationPermissionStatus(): Promise<boolean>;
  export function getStoragePermissionStatus(): Promise<boolean>;
  export function getSearchTravelDate(date: any, metaData: any): any;
  export function padToKDigits(num: number, k: number): string;
  export function getAndroidBottomBarHeight(): number;
  export function isNonHeaderAffiliate(aff: string): boolean;
  export function isSummaryTabDefaultOpen(params: { fromPreSales?: boolean }): boolean;
  export function openGenericDeeplink(params: { url?: string }): void;
}

// Generic declaration for any JavaScript module that doesn't have specific types
declare module '*.js' {
  const content: any;
  export = content;
}

// Declaration for constants files
declare module '*/HolidayConstants' {
  export const FAB_PULSE_ANIMATION_DEFAULT_CONFIG: Record<string, any>;
  export const FabT2QConfigDefaultData: Record<string, any>;
  export const LuxFabT2QConfigDefaultData: Record<string, any>;
  export const RAW_PLATFORM_NAME: string;
  export const variantEnum: Record<string, any>;
  export const AFFILIATES: Record<string, string>;
  export const FUNNELS: Record<string, string>;
  export const SUB_FUNNELS_TYPES: Record<string, string>;
  export const DOM_BRANCH: string;
  export const USER_DEFAULT_CITY: string;
  // Add other constants as needed
}

// Declaration for tracking constants
declare module '*/HolidayTrackingConstants' {
  const content: Record<string, any>;
  export = content;
}

// Declaration for web routes
declare module '*/HolidayWebRoute' {
  const content: Record<string, any>;
  export = content;
}

// Declaration for reducers and store
declare module '*/holidaysReducers' {
  const content: any;
  export = content;
}

declare module '*/holidaysStore' {
  const content: any;
  export = content;
}

// Declaration for JSX components without TypeScript
declare module '*.jsx' {
  import { ComponentType } from 'react';
  const Component: ComponentType<any>;
  export default Component;
}

// Declaration for other utility modules that might be imported
declare module '*/HolidayNetworkUtils' {
  export function fetchPackageDetailShareUrl(...args: any[]): Promise<any>;
  export function fetchQueueIdentifier(...args: any[]): Promise<any>;
  export function getRequestHeaders(...args: any[]): any;
  export function fetchFabCta(...args: any[]): Promise<any>;
  // Add other network utility functions as needed
}

// Declaration for style modules
declare module '*/holidayFonts' {
  export const fontStyles: Record<string, any>;
}

declare module '*/holidayColors' {
  export const holidayColors: Record<string, any>;
}

declare module '*/holidayBorderRadius' {
  export const holidayBorderRadius: Record<string, any>;
}

declare module '*/Spacing' {
  export const paddingStyles: Record<string, any>;
  export const marginStyles: Record<string, any>;
}

// Declaration for utility modules
declare module '*/CtaUtils' {
  export const NEW_CTA_GRADIENT_COLORS: any;
  export function getCtaGradientColors(...args: any[]): any;
  export function getFabBorderStyle(...args: any[]): any;
  export function getFabsIconUrls(...args: any[]): any;
  export const LUXE_CTA_BORDER_GRADIENT_COLORS: any;
}

declare module '*/HolidayImageUrls' {
  export function getImageUrl(...args: any[]): string;
  export const IMAGE_ICON_KEYS: Record<string, any>;
}

declare module '*/HolidayPDTConstants' {
  export const PDT_EVENT_TYPES: Record<string, any>;
}

declare module '*/textTransformUtil' {
  export function capitalizeText(text: string): string;
}

// Declaration for component modules
declare module '*/CommonOverlay' {
  export const COMMON_OVERLAYS: Record<string, any>;
}

// Declaration for modules with mobile-holidays-react-native prefix
declare module 'mobile-holidays-react-native/src/utils/HolidayUtils' {
  export * from '*/HolidayUtils';
}

declare module 'mobile-holidays-react-native/src/Styles/holidayFonts' {
  export * from '*/holidayFonts';
}

declare module 'mobile-holidays-react-native/src/Styles/holidayColors' {
  export * from '*/holidayColors';
}

declare module 'mobile-holidays-react-native/src/Styles/Spacing' {
  export * from '*/Spacing';
}

declare module 'mobile-holidays-react-native/src/utils/HolidayPDTConstants' {
  export * from '*/HolidayPDTConstants';
}

declare module 'mobile-holidays-react-native/src/utils/textTransformUtil' {
  export * from '*/textTransformUtil';
}

declare module 'mobile-holidays-react-native/src/Common/Components/CommonOverlay' {
  export * from '*/CommonOverlay';
}

// Global type augmentations for common patterns
declare global {
  interface Window {
    // Add any global window properties if needed
  }

  // Add NodeJS namespace for timeout types
  namespace NodeJS {
    interface Timeout {}
  }

  // Add JSX namespace for React components
  namespace JSX {
    interface Element {}
  }
}

// Export empty object to make this a module
export {};
