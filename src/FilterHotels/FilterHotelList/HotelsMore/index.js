import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import HotelsMoreTabs from './HotelsMoreTabs';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import FilterSectionHeader from '../FilterSectionHeader';
import filterStyles from '../FilterHotelStyles';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

class ImportantInfo extends BasePage {
  constructor(props) {
    super(props);
    this.state = {
      defaultShow: 4,
      showMore: false,
      appliedPropertyTypeList: props.appliedPropertyTypeList,
    };
  }

  componentWillReceiveProps(nextProps): void {
    const { appliedPropertyTypeList: newPriceRange = [] } = nextProps;
    const { appliedPropertyTypeList = [] } = this.props;
    if (JSON.stringify(newPriceRange) !== JSON.stringify(appliedPropertyTypeList)) {
      this.setState({
        ...this.state,
        appliedPropertyTypeList: newPriceRange,
      });
    }
  }

  handleShowMore = () => {
    if (!this.state.showMore) {
      this.setState({
        showMore: !this.state.showMore,
        defaultShow: Object.keys(this.props.propertyTypeList).length,
      });
    } else {
      this.setState({ showMore: !this.state.showMore, defaultShow: 4 });
    }
  };
  render() {
    const { appliedPropertyTypeList } = this.state;
    if (!appliedPropertyTypeList || appliedPropertyTypeList.length === 0) {
      return [];
    }
    return (
      <View style={filterStyles.cardTabs}>
        <FilterSectionHeader title={'Hotels and More'} />
        <View style={styles.tabContainer}>
          {Object.keys(this.props.propertyTypeList).map((key, index) => {
            while (index < this.state.defaultShow) {
              return (
                <HotelsMoreTabs
                  updateAppliedPropertyTypeFilterData={
                    this.props.updateAppliedPropertyTypeFilterData
                  }
                  index={index}
                  item={this.props.propertyTypeList[key]}
                  appliedPropertyTypeList={appliedPropertyTypeList}
                />
              );
            }
            return {};
          })}
        </View>
        {Object.keys(this.props.propertyTypeList).length > this.state.defaultShow && (
          <TouchableOpacity onPress={this.handleShowMore} activeOpacity={0.6}>
            <Text style={styles.linkText}>
              {!this.state.showMore
                ? `${Object.keys(this.props.propertyTypeList).length - this.state.defaultShow} ` +
                  'MORE PROPERTY TYPE'
                : 'SHOW LESS PROPERTY TYPE'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  tabContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    ...marginStyles.mb10,
  },
  linkText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.primaryBlue,
  },
});

export default ImportantInfo;
