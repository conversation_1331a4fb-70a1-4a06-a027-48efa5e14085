import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import {
  PageHeaderBackButton,
  PageHeaderTitle,
  shadowStyle,
} from 'mobile-holidays-react-native/src/Common/Components/PageHeader';
import crossIcon from '@mmt/legacy-assets/src/holidays/iconCross.webp';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import Header from '@Frontend_Ui_Lib_App/Header';

const FilterHeader = (props) => {

  const leftIcon = {
    icon: crossIcon,
    onPress: props.closeFilterScreen
  };
  const actionText = {
    text: 'Clear All',
    onPress: props.clearAppliedFilter
  };

  return(
    <View style={[styles.header]}>
    <Header
      actionText={actionText}
      customStyles={{
        bodyContainerStyle: styles.bodyContainerStyle,
        rightWrapperStyle: styles.clearAll,
        titleStyle: styles.titleStyle,
        wrapperStyle: styles.wrapperStyle,
      }}
      leftIcon={leftIcon}
      title={props.title}
    />
  </View>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 1,
    backgroundColor: 'red',
  },
  bodyContainerStyle: {
    flexDirection: 'row', alignItems: 'center'
  },
  titleStyle: {
    flex: 1
  },
  wrapperStyle: {
    width: '100%'
  },
  bodyContainerStyle: {
    flexDirection: 'row', alignItems: 'center'
  },
  titleStyle: {
    flex: 1
  },
  wrapperStyle: {
    width: '100%'
  },
  title: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    letterSpacing: 0.3,
    marginLeft: 5,
  },
  clearAll: {
    ...fontStyles.labelBaseBold,
    letterSpacing: 0.23,
    color: holidayColors.primaryBlue,
  },
});

export default FilterHeader;
