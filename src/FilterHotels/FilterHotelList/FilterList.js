import React from 'react';
import {connect} from 'react-redux';
import {ScrollView, StyleSheet, View} from 'react-native';
import FilterHeader from './FilterHeader';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss.js';
import SortingSection from './SortingSection';
import PopularSection from './PopularSection';
import StarRating from './StarRating';
import HotelsMore from './HotelsMore';
import UserRating from './UserRating';
import Amenities from './Amenities';
import Button from './Button';
import Locations from './Locations';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import withBackHandler from '../../hooks/withBackHandler';

import PriceRangeList from './PriceRange/PriceRangeList';
import { FILTER_UPDATE } from '../../ListingHotelNew/HolidaysHotelListingConstants';
import { SORTING_FILTER } from '../../HolidayConstants';
import { trackAppliedFilterAndSorter } from '../../PhoenixDetail/Components/PhoenixHotelsListing/PhoenixHotelslListingUtils';
import { holidayNavigationPop } from '../../PhoenixDetail/Utils/DetailPageNavigationUtils';
class FilterList extends BasePage {
  static navigationOptions={header: null}

  constructor(props) {
    super();
    this.state = {
      appliedFilterData: { ...props.appliedFilterData },
      copyAppliedFilterData: {
        appliedSortingList: 'Popularity',
        appliedLocationList: [],
        appliedStarRatingList: [],
        appliedPopularList: [],
        appliedPropertyTypeList: [],
        appliedAmenitiesList: [],
        appliedURating: '',
        appliedPriceRange: {},
      },
    };
  }

  componentDidMount() {
    super.componentDidMount();
  }
  onBackClick = () => {
    HolidayNavigation.pop();
  };

  render() {
    const { filterData } = this.props;
    const {
      sortingList,
      locationList,
      starRatingList,
      popularList,
      propertyTypeList,
      amenitiesList,
      uRating,
      priceList,
    } = filterData;
    const fullPriceRange = {
      min: priceList[0],
      max: priceList[priceList.length - 1],
    };

    return (
      <View style={AtomicCss.flex1}>
        <View style={styles.container}>
          <FilterHeader
            closeFilterScreen={this.closeFilterScreen}
            clearAppliedFilter={this.clearAppliedFilter}
            title="Sort &amp; Filters"
          />
          <ScrollView>
            <SortingSection
              updateAppliedSortingFilterData={this.updateAppliedSortingFilterData}
              appliedFilterData={this.state.appliedFilterData}
              sortingList={sortingList}
            />
            <PriceRangeList
              updateAppliedPriceRangeFilterData={this.updateAppliedPriceRangeFilterData}
              isOpenedDirectly={false}
              fullPriceRange={fullPriceRange}
              appliedPriceRange={this.state.appliedFilterData.appliedPriceRange}
              days={this.props.days}
            />
            <Locations
              updateAppliedLocationFilterData={this.updateAppliedLocationFilterData}
              appliedFilterData={this.state.appliedFilterData}
              locationList={locationList}
            />
            <StarRating
              updateAppliedStarRatingFilterData={this.updateAppliedStarRatingFilterData}
              appliedStarRatingList={this.state.appliedFilterData.appliedStarRatingList}
              starRatingList={starRatingList}
            />
            <PopularSection
              updateAppliedPopularFilterData={this.updateAppliedPopularFilterData}
              isOpenedDirectly={false}
              appliedPopularList={this.state.appliedFilterData.appliedPopularList}
              popularList={popularList}
            />
            <HotelsMore
              updateAppliedPropertyTypeFilterData={this.updateAppliedPropertyTypeFilterData}
              appliedPropertyTypeList={this.state.appliedFilterData.appliedPropertyTypeList}
              propertyTypeList={propertyTypeList}
            />
            <Amenities
              updateAppliedAmenitiesFilterData={this.updateAppliedAmenitiesFilterData}
              appliedFilterData={this.state.appliedFilterData}
              amenitiesList={amenitiesList}
            />
            <UserRating
              updateAppliedUserRatingFilterData={this.updateAppliedUserRatingFilterData}
              appliedFilterData={this.state.appliedFilterData}
              uRating={uRating}
            />
          </ScrollView>
          <View style={{ paddingHorizontal: 11, paddingVertical: 11 }}>
            <Button
              btnTxt="APPLY FILTERS"
              btnBg="gradientBtn"
              btnType="flat"
              handleClick={this.applyFilter}
            />
          </View>
        </View>
      </View>
    );
  }


  applyFilter = () => {
    this.props.updateFilterData(this.state.appliedFilterData);
    trackAppliedFilterAndSorter(this.state.appliedFilterData);
    this.closeFilterScreen();
  };
  closeFilterScreen = () => {
    holidayNavigationPop({
      overlayKeys: [this.props.overlayKey],
      hideOverlays: this.props.hideOverlays,
    });
  };

  updateAppliedSortingFilterData = (item = {}) => {
    const { appliedFilterData } = this.state;
    const { appliedSortingList } = appliedFilterData;
    let setThis = 'Popularity';
    if (appliedSortingList === item && item !== setThis) {
      setThis = 'Popularity';
    } else {
      setThis = item;
    }
    this.setState({
      appliedFilterData: {
        ...appliedFilterData,
        appliedSortingList: setThis,
      },
    });
  };

  updateAppliedLocationFilterData = (item = {}) => {
    const { appliedFilterData } = this.state;
    const { appliedLocationList = [] } = appliedFilterData;
    const myLocationList = [...appliedLocationList];
    if (myLocationList.includes(item)) {
      myLocationList.splice(myLocationList.indexOf(item), 1);
    } else {
      myLocationList.push(item);
    }

    this.setState({
      appliedFilterData: {
        ...appliedFilterData,
        appliedLocationList: myLocationList,
      },
    });
  };

  updateAppliedStarRatingFilterData = (item = {}) => {
    const { appliedFilterData } = this.state;
    const { appliedStarRatingList = [] } = appliedFilterData;
    const myStarList = [...appliedStarRatingList];
    if (myStarList.includes(item)) {
      myStarList.splice(myStarList.indexOf(item), 1);
    } else {
      myStarList.push(item);
    }
    this.setState({
      appliedFilterData: {
        ...appliedFilterData,
        appliedStarRatingList: myStarList,
      },
    });
  };

  updateAppliedPopularFilterData = (item = {}) => {
    const { appliedFilterData } = this.state;
    const { appliedPopularList = [] } = appliedFilterData;
    const mySortList = [...appliedPopularList];
    if (mySortList.includes(item)) {
      mySortList.splice(mySortList.indexOf(item), 1);
    } else {
      mySortList.push(item);
    }

    this.setState({
      appliedFilterData: {
        ...appliedFilterData,
        appliedPopularList: mySortList,
      },
    });
  };

  updateAppliedPropertyTypeFilterData = (item = {}) => {
    const { appliedFilterData } = this.state;
    const { appliedPropertyTypeList = [] } = appliedFilterData;
    const mySortList = [...appliedPropertyTypeList];
    if (mySortList.includes(item)) {
      mySortList.splice(mySortList.indexOf(item), 1);
    } else {
      mySortList.push(item);
    }

    this.setState({
      appliedFilterData: {
        ...appliedFilterData,
        appliedPropertyTypeList: mySortList,
      },
    });
  };

  updateAppliedAmenitiesFilterData = (item = {}) => {
    const { appliedFilterData } = this.state;
    const { appliedAmenitiesList = [] } = appliedFilterData;
    const mySortList = [...appliedAmenitiesList];
    if (mySortList.includes(item)) {
      mySortList.splice(mySortList.indexOf(item), 1);
    } else {
      mySortList.push(item);
    }

    this.setState({
      appliedFilterData: {
        ...appliedFilterData,
        appliedAmenitiesList: mySortList,
      },
    });
  };

  updateAppliedUserRatingFilterData = (item = {}) => {
    const { appliedFilterData } = this.state;
    const { appliedURating = '' } = appliedFilterData;
    let setThis = '';
    if (appliedURating === item) {
      setThis = '';
    } else {
      setThis = item;
    }
    this.setState({
      appliedFilterData: {
        ...appliedFilterData,
        appliedURating: setThis,
      },
    });
  };

  updateAppliedPriceRangeFilterData = (item = []) => {
    const { appliedFilterData } = this.state;
    const myPriceRange = {
      min: item[0],
      max: item[1],
    };
    this.setState({
      appliedFilterData: {
        ...appliedFilterData,
        appliedPriceRange: myPriceRange,
      },
    });
  };

  removeSpecificFilterFromAppliedSection = (item, type) => {
    const { appliedFilterData } = this.state;
    switch (type) {
      case SORTING_FILTER: {
        const { appliedSortingList } = appliedFilterData;

        break;
      }
      default:
    }
  };

  clearAppliedFilter = () => {
    const { copyAppliedFilterData } = this.state;
    this.setState({
      appliedFilterData: { ...copyAppliedFilterData },
      copyAppliedFilterData: { ...copyAppliedFilterData },
    });
  }
  componentWillUnmount() {
    super.componentWillUnmount();
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f2f2f2',
    justifyContent: 'space-between',
    position: 'relative',
  },
});

const mapStateToProps = (state) => {
  const { appliedFilterData } = state.holidayHotelListingReducer;
  return { appliedFilterData };
};

const mapDispatchToProps = (dispatch) => ({
  updateFilterData: (defaultFilterData) => {
    dispatch({ type: FILTER_UPDATE, appliedFilterData: defaultFilterData });
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(withBackHandler(FilterList));
