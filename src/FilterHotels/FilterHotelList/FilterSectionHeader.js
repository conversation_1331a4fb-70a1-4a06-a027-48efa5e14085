import React from 'react';
import { Text, StyleSheet } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const FilterSectionHeader = ({title}) => {
  return <Text style={styles.headingText}>{title}</Text>;
};

const styles = StyleSheet.create({
  headingText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    ...marginStyles.mb18,
  },
});
export default FilterSectionHeader;
