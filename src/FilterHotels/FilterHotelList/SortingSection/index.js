import React from 'react';
import {View, StyleSheet, Text} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import SortingTabs from './SortingTabs';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

class SortingSection extends BasePage {
  render() {
    return (
      <View style={styles.sortingCard}>
        <Text style={styles.headingText}>SORTING</Text>
        <View style={[AtomicCss.flexRow, styles.flexWrap]}>
          {Object.keys(this.props.sortingList).map((item, index) => (
            <SortingTabs
              updateAppliedSortingFilterData={this.props.updateAppliedSortingFilterData}
              index={index}
              item={this.props.sortingList[item]}
              appliedSortingList={this.props.appliedFilterData.appliedSortingList}
            />
          ))}
        </View>
      </View>
    );
  }
  updateAppliedSortingFilterData = (sortingItem = {}) => {
    this.props.updateAppliedSortingFilterData(sortingItem);
  };
}

const styles = StyleSheet.create({
  sortingCard: {
    backgroundColor: holidayColors.lightGray2,
    ...paddingStyles.pa16,
  },
  headingText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
    ...marginStyles.mb12,
  },
  flexWrap: { flexWrap: 'wrap' },
});

export default SortingSection;
