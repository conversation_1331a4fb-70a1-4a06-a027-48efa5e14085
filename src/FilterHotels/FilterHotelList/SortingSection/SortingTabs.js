import React, { Component, Fragment } from 'react';
import { StyleSheet, Text, Image, TouchableOpacity } from 'react-native';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { widthPixel } from 'mobile-holidays-react-native/src/Styles/holidayNormaliseSize';
import filterStyles from '../FilterHotelStyles';
class SortingSection extends Component {
  handleActiveTab = () => {
    const { item } = this.props;
    this.props.updateAppliedSortingFilterData(item.text === 'Price' ? item.subText : item.text);
  };

  render() {
    const { item } = this.props;
    const { text, Icon, subText, type } = item;
    const { appliedSortingList = 'Popularity' } = this.props;
    let active = false;
    if (text === 'Price') {
      active = appliedSortingList === subText;
    } else {
      active = appliedSortingList === text;
    }
    return (
      <Fragment>
        <TouchableOpacity
          onPress={this.handleActiveTab}
          activeOpacity={0.6}
          style={[filterStyles.tab, styles.sortingTabs, active ? filterStyles.activeTab : {}]}
        >
          <Image style={styles[`${type}Icon`]} source={Icon} />
          <Text style={[filterStyles.text, appliedSortingList === text && filterStyles.activeText]}>
            {text}
          </Text>
          <Text style={[styles.subText]}>{subText}</Text>
        </TouchableOpacity>
      </Fragment>
    );
  }
}

const styles = StyleSheet.create({
  sortingTabs: {
    width: widthPixel(79),
    height: 98,
  },
  subText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  starIcon: {width: 26, height: 26, marginBottom: 8},
  priceLowIcon: {width: 22, height: 18, marginBottom: 10},
  priceHighIcon: {width: 23, height: 18, marginBottom: 10},
  userRatingIcon: {width: 25, height: 26, marginBottom: 10},
});

export default SortingSection;
