import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity, ScrollView } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import PopularTabs from './PopularTabs';
import FilterHeader from '../FilterHeader';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import {
  POPULAR_FILTER_UPDATE,
} from '../../../ListingHotelNew/HolidaysHotelListingConstants';
import Button from '../PriceRange/PriceRangeList/Button';
import FilterSection from '../FilterSection';
import BudgetFilter from '../PriceRange/PriceRangeList/BudgetFilter';
import { connect } from 'react-redux';
import { HolidayNavigation } from '../../../Navigation';
import FilterSectionHeader from '../FilterSectionHeader';
import filterStyles from '../FilterHotelStyles';
import { holidayNavigationPop } from '../../../PhoenixDetail/Utils/DetailPageNavigationUtils';
import withBackHandler from '../../../hooks/withBackHandler';

class PopularSection extends BasePage {
  constructor(props) {
    super(props);
    this.state = {
      appliedPopularList: props.appliedPopularList,
    };
  }
  onBackClick = () => {
    HolidayNavigation.pop();
    return true;
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  componentWillReceiveProps(nextProps): void {
    const {appliedPopularList: newPriceRange = []} = nextProps;
    const {appliedPopularList = []} = this.props;
    if (JSON.stringify(newPriceRange) !== JSON.stringify(appliedPopularList)) {
      this.state = {
        appliedPopularList: newPriceRange,
      };
    }
  }

  updateAppliedPopularFilterData = (item = {}) => {
    const {appliedPopularList = []} = this.state;
    const mySortList = [...appliedPopularList];
    if (mySortList.includes(item)) {
      mySortList.splice(mySortList.indexOf(item), 1);
    } else {
      mySortList.push(item);
    }
    this.setState({
      appliedPopularList: mySortList,
    });
  }

  popScreenAndSetParentState = () => {
    this.setPropularList();
    this.closeFilterScreen();
  }
  closeFilterScreen = () => {
    holidayNavigationPop({
      overlayKeys: [this.props.overlayKey],
      hideOverlays: this.props.hideOverlays,
    });
  }
  clearAppliedFilter = () => {
    this.setState({
      appliedPopularList: [],
    });
  }
  setPropularList = () => {
    this.props.dispatchPopularList(this.state.appliedPopularList);
  }

  render() {
    const {isOpenedDirectly = false} = this.props;
    const {appliedPopularList} = this.state;
    if (isOpenedDirectly) {
      return (
        <View style={styles.container}>
          <FilterHeader
            closeFilterScreen={this.closeFilterScreen}
            title="Popular"
            clearAppliedFilter={this.clearAppliedFilter}
          />
            <View style={{flex: 1, flexDirection: 'row', padding: 20, flexWrap: 'wrap'}}>
              {Object.keys(this.props.popularList).map((item, index) => (
                <PopularTabs
                  updateAppliedPopularFilterData={this.updateAppliedPopularFilterData}
                  index={index}
                  appliedPopularList={appliedPopularList}
                  item={this.props.popularList[item]}
                />
            ))}
            </View>
          <TouchableOpacity
            style={{ paddingHorizontal: 11, paddingVertical: 11, backgroundColor: '#fff', justifyCenter: 'bottom' }}
          >
            <Button btnTxt="DONE" btnBg="gradientBtn" btnType="flat" handleClick={this.popScreenAndSetParentState}/>
          </TouchableOpacity>
        </View>
      );
    }
    return (
      <View style={filterStyles.cardTabs}>
        <FilterSectionHeader title={'Popular Amongst Indians'} />
        <View style={[AtomicCss.flexRow, AtomicCss.flexWrap]}>
          {Object.keys(this.props.popularList).map((item, index) => (
            <PopularTabs
              updateAppliedPopularFilterData={this.props.updateAppliedPopularFilterData}
              index={index}
              appliedPopularList={appliedPopularList}
              item={this.props.popularList[item]}
            />
          ))}
        </View>
      </View>);
  }
}


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    justifyContent: 'space-between',
    position: 'relative',
  },
});

const mapDispatchToProps = dispatch => ({
  dispatchPopularList: (appliedPopularList) => {
    dispatch({type: POPULAR_FILTER_UPDATE, appliedPopularList});
  },
});

export default connect(null, mapDispatchToProps)(withBackHandler(PopularSection));
