import {
  initializeCampaignDetails,
  initializeSearchContext,
  logHolidaysEventToPDT,
  populateExperimentalDetails,
  populatePageContext,
  populateSearchContext,
} from '../../utils/HolidayPDTTrackingV3';
import { LANDING_PAGE_NAME } from '../LandingConstants';
import {
  getLandingPDTObj,
  updateLandingPDTObj,
} from './HolidayLandingPDTDataHolder';
import {
  getDataFromStorage,
  KEY_USER_DEST_CITY,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import { getPageSectionVisitResponse, sectionTrackingPageNames } from '../../utils/SectionVisitTracking';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { PDT_PAGE_EXIT_EVENT } from 'mobile-holidays-react-native/src/HolidayConstants';
import { isEmpty } from 'lodash';


export const logHolidaysLandingPDTEvents = ({ value, actionType, sectionData = null ,queryDetail={},shouldTrackToAdobe=true}) => {
  let pdtObj = getLandingPDTObj();
    // Ensure we're not losing the components data from event_detail
    if (pdtObj.event_detail && !actionType.components && pdtObj.event_detail.components) {
      actionType = {
        ...actionType,
        components: pdtObj.event_detail.components,
      };
    }
  
    // Add section data to content_details if provided
    if (sectionData && Array.isArray(sectionData)) {
      pdtObj = {
        ...pdtObj,
        event_detail: {
          ...pdtObj.event_detail || {},
          components: {
            ...(pdtObj.event_detail?.components || {}),
            content_details: sectionData,
          },
        },
      };
    }

  logHolidaysEventToPDT({
    pdtObj ,
    value,
    actionType,
    queryDetail,
    shouldTrackToAdobe
  });
};

export const setSearchContextData = ({
  depCityData = {},
  destCityData = {},
  paxDetails = {},
  tagDetails = {},
}) => {
  let pdtObj = getLandingPDTObj();
  const { search_context } = pdtObj || {};
  return populateSearchContext({
    deptCity : depCityData,
    destCity : destCityData,
    paxDetails,
    tagDetails,
    prevSearchContext: search_context,
  });
};


export const initializeLandingPDTObj = () => {
  let pdtObj = getLandingPDTObj();
  return ({
    ...JSON.parse(JSON.stringify(pdtObj)),
    experiment_details: populateExperimentalDetails(),
    page_context: setPageContextData(),
    search_context: initializeSearchContext(),
    campaign_details : initializeCampaignDetails(),
  });
};

export const setPageContextData = () => {
  const pageName = LANDING_PAGE_NAME;
  const funnelStep = LANDING_PAGE_NAME;
  return populatePageContext({ funnelStep, pageName });
};

export const initLandingPdtObj = () => {
  const initialLandingPDTObj = initializeLandingPDTObj();
  updateLandingPDTObj({ pdtObj : initialLandingPDTObj });
};

export const setDefaultDestinationData = async() => {
    // Try to get the saved destination city data from storage first
  const destCity = await getDataFromStorage(KEY_USER_DEST_CITY);
  const destinationCity = destCity?.destinationCity || '';
  const destCityData = {
    id: 'holidays',
    countryName: 'INDIA',
    locusId: 'holidays',
    name: isEmpty(destinationCity) ? 'holidays' : destinationCity,
  };
  let pdtObj = {
    search_context: setSearchContextData({ destCityData }),
  };
  updateLandingPDTObj({ pdtObj });
}


/**
 * Track page exit event with section visit data
 * @param {Object} landingData - The landing page data containing sections
 * @param {String} exitEventName - The name of the exit event to track
 */
export const trackPageExit = (landingData, exitEventName) => {
  try {
    // Safety checks for parameters
    if (!exitEventName) {
      console.log('Warning: trackPageExit called without exitEventName, setting default name -', PDT_PAGE_EXIT_EVENT);
      exitEventName = PDT_PAGE_EXIT_EVENT;
    }

    if (!landingData || typeof landingData !== 'object') {
      console.log('Warning: trackPageExit called with invalid landingData', landingData);
      landingData = {};
    }

    // Get specific section visit data from the section tracking module
    const viewedSections = getPageSectionVisitResponse({
      pageName: sectionTrackingPageNames.LANDING_PAGE,
    }) || {};

    // Format the viewed sections in the same structure as storeSectionsInPDTHolder
    const sectionContentDetails = [];

    // Get the original sections data from landing response
    const landingResponseSections = landingData?.sections || [];

  const topSectionData = landingResponseSections.find(section => section.type === 'TOP');
  if (topSectionData?.cards?.length) {
    const { id = '', header, name, sectionCode = '', order = 0 } = topSectionData;
    const sectionName = header || name || '';
    topSectionData.cards.forEach(({ type = '' }) => {
      sectionContentDetails.push({
        id,
        type,
        name: sectionName,
        section_code: sectionCode,
        position: {v:order},
      });
    });
  }

    // Process only the sections that were viewed (keys in viewedSections)
    Object.keys(viewedSections || {}).forEach(sectionName => {
      if (!sectionName) {
        return;
      }

      // Find the section in the original data
      const sectionData = landingResponseSections.find(section =>
        section && ((section.header === sectionName) || (section.name === sectionName))
      );

      if (sectionData) {
        const sectionId = sectionData.id || '';
        const sectionType = sectionData.type || sectionData.customTypeOf || '';
        const sectionCode = sectionData.sectionCode || '';
        const position = sectionData.order || 0;

        // Create content detail item with required fields
        sectionContentDetails.push({
          id: sectionId,
          type: sectionType,
          name: sectionName,
          section_code: sectionCode,
          position: {v:position},
        });
      }
    });

    // Log page exit event with PDT and pass section data as a parameter
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.pageExit,
      value: exitEventName,
      sectionData: sectionContentDetails,
    });
  } catch (error) {
    console.log('Error in trackPageExit:', error);
  }
};