import isEmpty from 'lodash/isEmpty';
import orderBy from 'lodash/orderBy';
import { getDateObject } from '../../Grouping/Components/ModifySearch/DateUtil';
import HolidayDeeplinkParser from '../../utils/HolidayDeeplinkParser';
import {
  getAPWindow,
  getExperimentValue,
  isLuxeFunnel,
  isMobileClient,
  isNonMMTAffiliate,
} from '../../utils/HolidayUtils';
import fecha from 'fecha';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import {
  createEmptyLoggingMap,
  createLoggingMap,
  generateCardKey,
  generateSectionKey,
} from '../Utils/HolidayLandingUtils';
import { appendParamToUrl } from '../../utils/HolidayNetworkUtils';
import {
  DEEPLINK_DETAIL_PAGE,
  DEEPLINK_DETAIL_PAGE_CUSTOMIZED,
  DEEPLINK_LAST_PAGE,
  DEEPLINK_LISTING_GROUPING_PARAM,
  DEEPLINK_MAP_PAGE,
  DEEPLINK_MMYT_DETAIL_PAGE,
  DEEPLINK_OPENOUTSIDE_PARAM,
  DEEPLINK_OPENWEB_PARAM,
  DEEPLINK_QUOTES_PAGE,
  DEEPLINK_REFER_AND_EARN_PARAM,
  DEEPLINK_SME_PAGE,
  PDT_RAW_EVENT,
} from '../../HolidayConstants';
import { NativeModules } from 'react-native';
import { showShortToast } from '@mmt/core/helpers/toast';
import { trackHolidayLandingClickEvent } from './HolidayLandingTrackingUtils';
import URL from 'url';
import queryString from 'query-string';
import { getStoryImages } from '../../PhoenixDetail/Utils/HolidayDetailUtils';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import { openPackageDetailV2 } from '../../utils/HolidayOpenPackageUtils';
import { removeLastPage } from '../../utils/HolidaysPokusUtils';
import { DATE_WITH_DAY_FORMAT } from '../../SearchWidget/SearchWidgetConstants';
import { logHolidaysLandingPDTEvents } from './HolidayLandingPdtTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { LANDING_TYPE } from 'mobile-holidays-react-native/src/Common/Components/Membership/utils/constants';
import { getMemberShipCardTypeWithOrder } from '../../Common/Components/Membership/utils/MembershipUtils';
import { KEY_HOLIDAY_LANDING_RESPONSE, KEY_HOLIDAY_LANDING_RESPONSE_LUX, KEY_HOLIDAY_WG_LANDING_RESPONSE } from '@mmt/legacy-commons/AppState/LocalStorage';
export const handleGroupDeeplink = ({
  queryParams,
  urlValue,
  redirectionPage,
  holidayLandingData,
  availableHubs,
}) => {
  const holidaysGroupingDeeplinkData = HolidayDeeplinkParser.parseListingGroupingPageDeeplink(
    urlValue,
  );
  if (isNonMMTAffiliate(holidayLandingData.aff)) {
    holidaysGroupingDeeplinkData.aff = holidayLandingData.aff;
  }
  if (!isEmpty(holidayLandingData.cmp) && isEmpty(holidaysGroupingDeeplinkData.cmp)) {
    holidaysGroupingDeeplinkData.cmp = holidayLandingData.cmp;
  }
  holidaysGroupingDeeplinkData.lastPage = DEEPLINK_LAST_PAGE;
  HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GROUPING, {
    ...holidaysGroupingDeeplinkData,
    redirectionPage: redirectionPage,
    availableHubs: availableHubs,
  });
};
const populateCommonOnmiData = (params) => {
  try {
    const { paxDetails, roomDetails, fromDateObj } = params || {};
    const date = getDateObject(fromDateObj?.selectedDate);
    const selectedDate = date?.selectedDate ? fecha.format(date?.selectedDate, 'YYYY-MM-DD') : null;
    const AP_WINDOW = selectedDate ? getAPWindow(selectedDate) : undefined;
    const userDepCity = HolidayDataHolder.getInstance().getDepartureCity();
    return {
      m_v4: AP_WINDOW,
      m_v3: userDepCity,
      m_v21: `adults:${paxDetails?.adult}_children:${paxDetails?.child}_infants:${paxDetails?.infantCount}`,
      m_v20: roomDetails?.length,
      m_v32: userDepCity,
      prop83: HolidayDataHolder.getInstance().getBanner(),
    };
  } catch (e) {
    console.log(e);
  }
};
// export const getSearchFilterPokus = () => {
//   return getExperimentValue(abConfigKeyMappings.landingSearchFilter, {
//     searchV2: true,
//     menuList: false,
//     filter: true,
//   });
// };
export const getGeoLocationPokus = () => {
  return getExperimentValue('enableGeoLoc', false);
};

export const trackClickEvent = ({
  eventName = '',
  eventNameOmni = '',
  prop1 = '',
  pdtExtraData = {},
  paxRoomData = {},
}) => {
  trackLocalClickEvent({ eventName, eventNameOmni, prop1, pdtExtraData, paxRoomData });
};
export const trackLocalClickEvent = async ({
  eventName = '',
  suffix = '',
  eventNameOmni = '',
  prop1 = '',
  prop66 = '',
  omniData = {},
  pdtExtraData = {},
  paxRoomData = {},
}) => {
  const data= await populateCommonOnmiData({...paxRoomData});
  omniData =  { ...data, ...omniData };
  const pdtData = {
    pageDataMap: createEmptyLoggingMap(false),
    eventType: PDT_RAW_EVENT,
    activity: eventName,
    extraData: pdtExtraData,
  };
  trackHolidayLandingClickEvent({
    omniEventName: eventName + eventNameOmni + suffix,
    pdtData,
    prop1,
    prop66,
    omniData,
  });
};
export const trackCardClickEvent = ({ eventName, eventNameOmni, destination }) => {
  const pdtData = {
    pageDataMap: createLoggingMap(destination, false),
    eventType: PDT_RAW_EVENT,
    activity: eventName,
  };
  trackHolidayLandingClickEvent({
    omniEventName: eventNameOmni,
    pdtData,
  });
};
export const trackLocalErrorClickEvent = ({ evar22 = '' }) => {
  const pdtData = {
    pageDataMap: createEmptyLoggingMap(false),
    eventType: PDT_RAW_EVENT,
  };
  trackHolidayLandingClickEvent({
    evar22,
    pdtData,
  });
};
const handleDetailDeeplink = ({ queryParams, urlValue, holidayLandingData, availableHubs }) => {
  const holidaysDetailData = HolidayDeeplinkParser.parseDetailPageDeeplink(urlValue);
  if (!isEmpty(holidayLandingData.cmp) && isEmpty(holidaysDetailData.cmp)) {
    holidaysDetailData.cmp = holidayLandingData.cmp;
  }
  HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL, {
    holidaysDetailData,
    availableHubs: availableHubs,
  });
};
const handleSmeDeeplink = ({ queryParams, holidayLandingData, availableHubs }) => {
  HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.SME_DETAIL, {
    profileId: queryParams.profileId,
    cmp: queryParams.cmp,
    aff: queryParams.aff ? queryParams.aff : holidayLandingData.aff,
    pt: queryParams.pt ? queryParams.pt : holidayLandingData.pt,
    availableHubs,
  });
};
export const handleDeeplink = ({ urlValue, mapObject = {}, deeplink = null }) => {
  const { userDepCity, availableHubs, holidayLandingData = {}, destinationCityData} = mapObject || {};
  const { aff = '', pt = '' } = holidayLandingData || {};
  if (urlValue) {
    const urlParams = URL.parse(urlValue);
    const queryParams = queryString.parse(urlParams.query);
    if (urlValue.includes(DEEPLINK_OPENOUTSIDE_PARAM)) {
      const { HolidayModule } = NativeModules;
      HolidayModule.handleWebDeeplink({ url: decodeURIComponent(deeplink || urlValue) });
    } else if (urlValue.includes(DEEPLINK_OPENWEB_PARAM)) {
      const { HolidayModule } = NativeModules;
      HolidayModule.handleWebDeeplink({ url: decodeURIComponent(deeplink || urlValue) });
    } else if (queryParams && urlValue.includes(DEEPLINK_LISTING_GROUPING_PARAM)) {
      handleGroupDeeplink({
        queryParams,
        urlValue,
        redirectionPage: queryParams.redirectionPage,
        holidayLandingData,
        availableHubs,
      });
    } else if (
      queryParams &&
      (urlValue.includes(DEEPLINK_DETAIL_PAGE) ||
        urlValue.includes(DEEPLINK_MMYT_DETAIL_PAGE) ||
        urlValue.includes(DEEPLINK_DETAIL_PAGE_CUSTOMIZED))
    ) {
      handleDetailDeeplink({ queryParams, urlValue, holidayLandingData, availableHubs });
    } else if(urlValue.includes(DEEPLINK_REFER_AND_EARN_PARAM)){
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.REFER_AND_EARN_PAGE);
    } else if (queryParams && urlValue.includes(DEEPLINK_SME_PAGE)) {
      handleSmeDeeplink({ queryParams, holidayLandingData, availableHubs });
    } else if (queryParams && urlValue.includes(DEEPLINK_MAP_PAGE)) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.MAP, {
        userDepCity: userDepCity,
        availableHubs: availableHubs,
        aff: aff,
        pt: pt,
        destinationCityData,
      });
    } else if (queryParams && urlValue.includes(DEEPLINK_QUOTES_PAGE)) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.QUOTES_LISTING_PAGE, {
        ...queryParams,
        ...holidayLandingData,
        from_quotes_deeplink: false,
      });
    } else {
      const { HolidayModule } = NativeModules;
      HolidayModule.openWebView({ url: deeplink || urlValue });
    }
  }
};
export const onCardClickDeeplinkHandling = ({ card, sectionKey, cardKey, mapObject, source = '' }) => {
  const deepLinkUrl = card.deeplink;
  if (!deepLinkUrl) return;
  const eventNameOmni = `${sectionKey}|${cardKey}`;
  trackCardClickEventWithPDTV3({ eventNameOmni, destination: card?.destination });
  const urlValue = appendParamToUrl(deepLinkUrl, [
    'lastPage=landing',
    ...(source ? [`source=${source}`] : []),
  ]);

  const shouldRemoveLastPage = removeLastPage();
  if(shouldRemoveLastPage) {
    const deeplinkValue = appendParamToUrl(deepLinkUrl, [
      ...(source ? [`source=${source}`] : []),
    ]);
    handleDeeplink({ urlValue, mapObject, deeplink: deeplinkValue });
  } else {
    handleDeeplink({ urlValue, mapObject });
  }
};
export const genericOnPressHandling = ({ card, data, cardKey = null, mapObject, source = '' }) => {
  const sectionKey = generateSectionKey(data);
  let cardName = cardKey;
  if (!cardName) {
    cardName = generateCardKey(card);
  }
  if (!card.dynamic) {
    onCardClickDeeplinkHandling({ card, sectionKey, cardKey: cardName, mapObject, source });
  } else {
    onCardClicked({ card, sectionKey, cardKey :cardName, mapObject });
  }
};
export const onCardClicked = ({
  card,
  sectionKey,
  cardKey,
  mapObject: { holidayLandingData = {}, availableHubs = [], landingDataRefreshed, source = '' } = {},
}) => {
  const eventNameOmni = `${sectionKey}|${cardKey}`;
  trackCardClickEventWithPDTV3({ eventNameOmni, destination: card.destination });

  const { pt, aff, cmp } = holidayLandingData || {};
  if (!isEmpty(card.redirectionPage)) {
    const { destination, filters } = card;
    const refreshLanding = isMobileClient() ? landingDataRefreshed : '';
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GROUPING, {
      destinationCity: destination,
      filters,
      refreshLanding,
      holCampaign: card.campaign,
      pt,
      aff,
      cmp,
      redirectionPage: card.redirectionPage,
      availableHubs: availableHubs,
      source,
    });
  } else {
    showShortToast('Something went wrong!!');
  }
};

const trackCardClickEventWithPDTV3 = ({ eventNameOmni, destination }) => {
  const eventName = 'generic_card';
  logHolidaysLandingPDTEvents({
    actionType: PDT_EVENT_TYPES.contentClicked,
    value: eventNameOmni,
  });
  trackCardClickEvent({
    eventName,
    eventNameOmni,
    destination,
  });
};

export const checkMenuTabsData = (tabs) => {
  let Arr = [];
  tabs = orderBy(tabs, (row) => row.priority, ['asc']);
  for (let i = 0; i < tabs.length; i++) {
    if (tabs[i].subMenu && tabs[i].subMenu.length > 0) {
      tabs[i].subMenu = orderBy(tabs[i].subMenu, (row) => row.priority, ['asc']);
      for (let j = 0; j < tabs[i].subMenu.length; j++) {
        if (tabs[i].subMenu[j].subMenu && tabs[i].subMenu[j].subMenu.length > 0) {
          tabs[i].subMenu[j].subMenu = orderBy(tabs[i].subMenu[j].subMenu, (row) => row.priority, [
            'asc',
          ]);
          Arr.push(tabs[i]);
          break;
        }
      }
    }
  }
  return Arr;
};
export const onDynamicPackageItemClicked = ({
  userPackageMeta,
  packageIndex,
  landingDataRefreshed,
  landingResponse,
  holidayLandingData,
  source,
}) => {
  const eventName = 'repeat_card_continueBooking';
  const refreshLanding = isMobileClient() ? landingDataRefreshed : '';
  if (!userPackageMeta) {
    return null;
  }
  let storyImageSize = null;
  if (landingResponse && landingResponse.attributes && landingResponse.attributes.values) {
    storyImageSize = landingResponse.attributes.values.StoryImageSize;
  }
  const { cmp = '', pt = ''} = holidayLandingData || {};
  openPackageDetailV2({
    holidayLandingGroupDto: {
      source,
    },
    userPackageMeta,
    packageEvent: eventName,
    refreshLanding,
    cmp,
    pt,
    lastPageName: 'landing',
    fromSeo: false,
    images: getStoryImages(userPackageMeta.imageDetail, storyImageSize),
  });
  trackLocalClickEvent({ eventName, eventNameOmni: `${eventName}_${packageIndex}` });
};

export const getFormattedTravelStartDate = (date) => {
  try {
    if (date) {
      return fecha.parse(date, DATE_WITH_DAY_FORMAT);
    }
  } catch {
    return null;
  }
};

export const getLandingMMTBlackDataFromResponse = (response) => {
  if (!response || !Array.isArray(response.sections)) {
    return {};
  }

  const mmtBlackSection = response.sections.find(
    (section) => section?.type === LANDING_TYPE && section?.type === 'TOP'
  );
  return mmtBlackSection || {};
};

export const trackMembershipRenderEvent = (mmtBlackSection) => {
  if (!isEmpty(mmtBlackSection)) {
    const eventName = 'Rendered_section_TOP';
    trackLocalClickEvent({
      eventName,
      prop1: getMemberShipCardTypeWithOrder(mmtBlackSection),
    });
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.contentSeen,
      value: `${eventName}|${getMemberShipCardTypeWithOrder(mmtBlackSection)}`,
      shouldTrackToAdobe:false
    });
  }
}


export const getLandingResponseKey = (isWg) => {
  if(isLuxeFunnel()) {
    return KEY_HOLIDAY_LANDING_RESPONSE_LUX;
  }
  return isWg ? KEY_HOLIDAY_WG_LANDING_RESPONSE : KEY_HOLIDAY_LANDING_RESPONSE;
}