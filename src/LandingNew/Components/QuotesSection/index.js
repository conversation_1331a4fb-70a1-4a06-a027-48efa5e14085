import React, { useEffect } from 'react';
import {FlatList} from 'react-native';
import PropTypes from 'prop-types';
import HolidaySectionHeader from '../../../Common/Components/Phoenix/HolidaySectionHeader';
import QuotesCard from './QuotesCard';
import {RcbCss} from '../phoenixCss';
import { trackLandingLoadEvent } from '../../../utils/HolidayTrackingUtils';
import { HOLIDAYS_SOURCE } from 'mobile-holidays-react-native/src/HolidayConstants';

const HolidayQuotesSection = ({data, onPress, editQuery, closeQuery, trackClickEvent, refreshContent}) => {
    const {header, cards, type, sectionCode} = data || {};
    useEffect(() => {
        trackLandingLoadEvent(false, 'presales_shown'); //@todo needs to be updated to new omniture changes
    },[]);
    const onPressLocal = (card, data) => {
      onPress({ card, data, source: `${HOLIDAYS_SOURCE}_psm` });
    };
    if (cards.length > 0) {
        return (
            <HolidaySectionHeader
                heading={header}
                styles={RcbCss}>
                <FlatList
                    data={cards}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    renderItem={({item, index}) => {
                        return (<QuotesCard card={item}
                                            onPress={onPressLocal}
                                            key={index}
                                            data={data}
                                            editQuery={editQuery}
                                            closeQuery={closeQuery}
                                            trackLocalClickEvent={trackClickEvent}
                                            cardNumber={index+1}
                                            refreshContent={refreshContent}
                                            />);
                    }}
                    keyExtractor={(item, index) => index}
                    contentContainerStyle={RcbCss.container}
                />
            </HolidaySectionHeader>
        );
    }
    return [];
};

HolidayQuotesSection.propTypes = {
    data: PropTypes.object.isRequired,
    onPress: PropTypes.func.isRequired,
};

export default HolidayQuotesSection;
