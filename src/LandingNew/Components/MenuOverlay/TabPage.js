import React from 'react';
import { View ,FlatList} from "react-native";
import HolidaySectionHeader from "../../../Common/Components/Phoenix/HolidaySectionHeader";
import {menuSectionType} from "../../LandingConstants";
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import CapsuleCard from './Cards/CapsuleCard';
import CircleCard from './Cards/CircleCard';
import ImageOfferCard from './Cards/ImageOfferCard';
import TextLinkCard from './Cards/TextLinkCard';
import CarousalSection from './CarousalSection';
import {
  CarousalSectionCss,
  SectionHeader,
  ImageOfferCard1,
  ImageOfferCard2, CapsuleCardCss, CircleCardCss, TextLinkCardCss, MenuTabPageCss,
} from '../phoenixCss';
import { holidayColors } from '../../../Styles/holidayColors';
import { logHolidaysLandingPDTEvents } from '../../Utils/HolidayLandingPdtTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

// Stores Component, Style
const ComponentMap = {
  [menuSectionType.CAPSULE_CARD]: [CapsuleCard, CapsuleCardCss],
  [menuSectionType.CIRCLE_CARD]: [CircleCard, CircleCardCss],
  [menuSectionType.OFFER_CARD_1]: [ImageOfferCard, ImageOfferCard1],
  [menuSectionType.OFFER_CARD_2]: [ImageOfferCard, ImageOfferCard2],
  [menuSectionType.LINK]: [TextLinkCard, TextLinkCardCss],
  [menuSectionType.TEXT]: [TextLinkCard, TextLinkCardCss],
};

class TabPage extends BasePage {
  constructor(props) {
    super(props);
  }

  onCardPress = (card, sectionId, type) => {
    if (card.deeplink) {
      const eventName = `Menu_${this.props.tabData.priority}|${sectionId}|${card.priority}_${card.name}_${type}`;
      logHolidaysLandingPDTEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: eventName,
      });
      this.props.menuCardOnPressHandling(card.deeplink, eventName);
    }
  }

  renderCard = (item, sectionId, type, index) => {
    const [MyComponent, style] = ComponentMap[type];

    const onPress = (card) => {
      this.onCardPress(card, sectionId, type);
    };

    if (MyComponent) {
      return (
        <MyComponent
          card={item}
          onPress={onPress}
          key={`${index}`}
          style={style}
        />
      );
    }
    return [];
  }

  getSectionContainer = (data) => {
    switch (data.type) {
      case menuSectionType.CIRCLE_CARD:
      case menuSectionType.OFFER_CARD_1:
      case menuSectionType.OFFER_CARD_2:
        return (
          <CarousalSection
            data={data}
            onPress={() => {}}
            renderItem={this.renderCard}
            style={CarousalSectionCss}
          />
        );
      case menuSectionType.CAPSULE_CARD:
      case menuSectionType.LINK:
      case menuSectionType.TEXT:
        return (
          <View style={{flexDirection: 'row', flexWrap: 'wrap', paddingHorizontal: 15}} >
            {data.subMenu.map((item, index) => this.renderCard(item, data.priority, data.type, index))}
          </View>
        );
      default: return [];
    }
  }

  renderSection = (data, index) => {
    if (data && data.subMenu && data.subMenu.length > 0) {
      return (
        <HolidaySectionHeader
          styles={SectionHeader}
          heading={data.name}
        >
          {this.getSectionContainer(data)}
        </HolidaySectionHeader>
      );
    }
  };

  render() {
    const {subMenu} = this.props.tabData;
    return (
      <View style={MenuTabPageCss.container}>
         <FlatList
         data={subMenu ? subMenu : []}
         renderItem={({item,index})=> this.renderSection(item,index)}
         vertical
         showsVerticalScrollIndicator
         contentContainerStyle={MenuTabPageCss.container}
       />
    </View>
    )
  }
}

export default TabPage;
