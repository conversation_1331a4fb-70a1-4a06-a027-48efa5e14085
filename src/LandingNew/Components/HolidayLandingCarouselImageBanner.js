import React from 'react';
import {View, TouchableOpacity, Platform, StyleSheet,Image} from 'react-native';
import Carousel from '@Frontend_Ui_Lib_App/Carousel';
import PropTypes from 'prop-types';
import genericCardDefaultImage from '@mmt/legacy-assets/src/no_package_default.webp';
import HolidayImageHolder from '../../Common/Components/HolidayImageHolder';
import { MAP_DEEPLINK_URL, sectionCodes } from '../LandingConstants';
import ExpiryTimer, { TIMER_DESIGN_TYPES } from '../../ExpiryTimer';
import {getScreenWidth, isMobileClient, isRawClient} from '../../utils/HolidayUtils';
import { getOfferTimerMobile } from '../../utils/HolidaysPokusUtils';

import HolidayAdCard from '../../Common/Components/HolidayAdCard';
import { HB_IMAGE_SIZE } from './phoenixCss';
import { marginStyles } from '../../Styles/Spacing';

const HolidayLandingCarouselImageBanner = React.memo(({ data, onPress, styles, resizeMode }) => {
  let { cards } = data || {};
  if (isRawClient()) {
    let index = cards.findIndex((el) => el.deeplink === MAP_DEEPLINK_URL);
    if (index > -1) { cards.splice(index, 1); }
  }
  const showOfferTimer =
    data?.sectionCode === sectionCodes.HERO_BANNER
      ? getOfferTimerMobile()
      : false;

  const width = getScreenWidth();
  const expiryHeight = 18;
  const onPressLocal = (card, data) => {
    onPress({ card, data });
  };
  const heightContainer = (width * (1 / HB_IMAGE_SIZE.aspectRatio)) + ( showOfferTimer ? expiryHeight : 0);
  const carousalStyle = isMobileClient() ? [styles.carousalContainer, { height: heightContainer }] : [];
  
  const renderBannerCard = (card: any, index) => {
  if (cards && cards.length > 1) {
    return (
      <View style={styles.container}>
        <Carousel
          delay={3000}
          style={carousalStyle}
          autoplay
          bullets
          bulletStyle={styles.bullet}
          bulletsContainerStyle={styles.bulletsContainer}
          chosenBulletStyle={[styles.bullet, styles.activeBullet]}
        >
          {cards.map((card, index) => (
            <View style={{ flex: 1 }} key={index}>
              {showOfferTimer && <View style={{height: expiryHeight, backgroundColor: 'white'}}/>}
              <View style={{ flex: 1 }}>
                <View style={[styles.offerStrip]}>
                  <ExpiryTimer
                    startTime={card.sectionCardTimeData?.startTime}
                    endTime={card.sectionCardTimeData?.endTime}
                    timerStartTime={card.sectionCardTimeData?.timerStartTime}
                    type={TIMER_DESIGN_TYPES.CHIP_DESIGN}
                  />
                </View>
                {card?.type === 'AD' ? (
                  <HolidayAdCard
                    card={card}
                    data={data}
                    adStyles={{ styles, resizeMode }}
                    onPress={onPressLocal}
                  />
                ) : (
                  <CarouselImage
                    card={card}
                    onPress={onPressLocal}
                    styles={styles}
                    resizeMode={resizeMode}
                    key={`${card.id}`}
                    data={data}
                  />
                )}
              </View>
            </View>
          ))}
        </Carousel>
      </View>
    );
  } else if (cards && cards.length === 1) {
    let card = cards[0];
    return (
      <View style={{ flex: 1 }}>
        {showOfferTimer && <View style={{ height: expiryHeight }} />}
        <View style={[styles.container, styles.singleImageContainer]}>
          <View style={[styles.offerStrip]}>
            <ExpiryTimer
              startTime={card.sectionCardTimeData?.startTime}
              endTime={card.sectionCardTimeData?.endTime}
              timerStartTime={card.sectionCardTimeData?.timerStartTime}
              type={TIMER_DESIGN_TYPES.CHIP_DESIGN}
            />
          </View>
          {card?.type === 'AD' ? (
            <HolidayAdCard card={card} data={data} adStyles={{ styles, resizeMode }} from={'HB'} onPress={onPressLocal} />
          ) : (
            <CarouselImage
              card={cards[0]}
              onPress={onPressLocal}
              styles={styles}
              resizeMode={resizeMode}
              data={data}
            />
          )}
        </View>
      </View>
    );
  }}
  return [];
});

const CarouselImage = ({ card, data, onPress, resizeMode, styles }) => (
  <TouchableOpacity
    onPress={() => onPress(card, data)}
    activeOpacity={0.8}
    style={styles.imgContainer}
  >
    {isRawClient() && <View>
          <Image
              style={styles.image}
              source={{uri: card.image.trim()}}
              defaultSource={genericCardDefaultImage}
              resizeMode={resizeMode}
          />
      </View>} 
    {isMobileClient() && <HolidayImageHolder
      style={StyleSheet.flatten(styles).image}
      imageUrl={card.image}
      defaultImage={genericCardDefaultImage}
      resizeMode={resizeMode}
    />}
  </TouchableOpacity>
);

CarouselImage.propTypes = {
  card: PropTypes.object.isRequired,
  onPress: PropTypes.func.isRequired,
  resizeMode: PropTypes.string.isRequired,
  styles: PropTypes.object.isRequired,
  data: PropTypes.object.isRequired,
};

HolidayLandingCarouselImageBanner.defaultProps = {
  resizeMode: 'contain',
};

HolidayLandingCarouselImageBanner.propTypes = {
  data: PropTypes.object.isRequired,
  onPress: PropTypes.func.isRequired,
  styles: PropTypes.object.isRequired,
  resizeMode: PropTypes.string,
};

export default HolidayLandingCarouselImageBanner;
