import HolidaysSharedModuleHolder, {
  HolidaysSharedComponents,
  HolidaysSharedConstants,
} from '@mmt/holidays-shared/src';

export function exportHolidaysSharedModule() {
  HolidaysSharedModuleHolder.set({
    getHolidaysSharedConstants(): HolidaysSharedConstants {
      const {
        WEB_CHECKIN_REQUEST_RAISED,
        ADULT,
        ADULTS,
        CHILD,
        CHILDREN,
        INFANT,
        INFANTS,
        SPACE,
      } = require('./HolidayConstants');
      return {
        Labels: {
          ADULT,
          ADULTS,
          CHILD,
          CHILDREN,
          INFANT,
          INFANTS,
          SPACE,
        },
        WebCheckingStatus: { WEB_CHECKIN_REQUEST_RAISED },
      };
    },
    getHolidaysSharedComponents(): HolidaysSharedComponents {
      const Loader = require('./SearchWidget/Components/Loader').default;
      const { HtmlHeading } = require('./PhoenixDetail/Components/DayPlan/HtmlHeading');
      const ACMEHolidayGroupingView = require('./Grouping/Components/ACMEHolidayGroupingView').default;
      return {
        ACMEHolidayGroupingView,
        HtmlHeading,
        Loader,
      };
    },
  });
}
