import {DEFAULT_TRAVELLER_COUNT} from '../PhoenixDetail/DetailConstants';
import {getDaysDiff, getDepartureCity, isRawClient, setThemeTagsMetric,updatedValuesforvariable_m_v83} from './HolidayUtils';
import {createDateFromString} from '../PhoenixDetail/Utils/HolidayDetailUtils';
import {getOmniPageName} from './HolidayTrackingUtils';
import { TRACKING_EVENTS } from '../HolidayTrackingConstants';
export const getAllOmniData = ({ params = {}, omniData = {} }) => {
  if (Object.keys(omniData).length > 0) {
    Object.keys(omniData).forEach(key => {
      if (omniData[key] && omniData[key] !== undefined) {
        params[key] = omniData[key];
      }
    });
  }
};

export const populateListingParams = async (params = {}, pageDataMap, prop66, { omniData = {} } = {}) => {
  getAllOmniData({ params, omniData });
  if (pageDataMap) {
    if (prop66) {
      params.m_c66 = prop66;
    }
    if (isRawClient()) {
      params.eVar93 = pageDataMap.collectionName;
    }
    if (pageDataMap.otherDetails) {
      if (pageDataMap.otherDetails.last_page_name) {
        params.m_c23 = pageDataMap.otherDetails.last_page_name;
      }
      params.m_v11 = pageDataMap.otherDetails.number_of_packages_shown;
      const dest = pageDataMap.otherDetails.dest_list ? pageDataMap.otherDetails.dest_list.join(', ') : '';
      const depCity = await getDepartureCity();
      params.m_v31 = dest;
      if (isRawClient()) {
        // As per discussion with @Naina Goyal | old value ${depCity}-${dest}
        params.m_v3 = `${depCity}`;
      }
    }
    if (pageDataMap.requestDetails && pageDataMap.requestDetails.cmp_channel) {
      params.m_v81 = pageDataMap.requestDetails.cmp_channel;
    }

    if (pageDataMap.fabCta) {
      addContactParams(params, pageDataMap.fabCta);
    }
  }
};

export const addRoomParams = (roomDetails, params) => {
  if (!roomDetails || roomDetails.length === 0) {
    roomDetails = [{
      noOfAdults: DEFAULT_TRAVELLER_COUNT,
      noOfChildrenWB: 0,
      noOfInfants: 0,
      listOfAgeOfChildrenWB: [],
      noOfChildrenWOB: 0,
    }];
  }

  let adults = 0;
  let children = 0;
  let infants = 0;
  roomDetails.forEach((room) => {
    adults += room.noOfAdults;
    children += room.noOfChildrenWB;
    infants += room.noOfInfants;
  });

  addRoomVariables(params, roomDetails.length, adults, children, infants);
};

export const addRoomVariables = (params, rooms, adults, children, infants) => {
  params.m_v20 = rooms;
  params.m_v21 = `adults:${adults}_children:${children}_infants:${infants}`;
};

export const addContactParams = (params, fabCta) => {
  params.m_c14 = `${fabCta.showCall ? 'C' : ''}${fabCta.showQuery ? 'Q' : ''}${fabCta.showChat ? 'Ch' : ''}${fabCta.branchLocator ? 'B' : ''}`;
};

export const populateDetailsParams = async (params, pageDataMap,suffix, { prop83 = '', omniData = {} } = {}) => {
  getAllOmniData({ params, omniData });
  if (pageDataMap) {
    if (suffix) {
      params.m_c66 = suffix;
    }
    // if(prop1) {
    //   params.m_c1 = prop1;
    // }
    // if(evar22) {
    //   params.m_v22 = evar22;
    // }
    if (prop83){
      params.m_v83 = await updatedValuesforvariable_m_v83({ [TRACKING_EVENTS.M_V83]: prop83 });
    }
    if (pageDataMap.packageDetails) {
      addRoomVariables(
        params, pageDataMap.packageDetails.roomCount, pageDataMap.packageDetails.pd_px_ad,
        pageDataMap.packageDetails.pd_px_ch, pageDataMap.packageDetails.pd_px_inf
      );
      params.m_v31 = pageDataMap.packageDetails.pkg_tag_dest;
      params.m_v6 = pageDataMap.packageDetails.pkg_hld_durn;

      let additional = '';
      if (pageDataMap.packageDetails.premium) {
        additional = additional + '|Premium';
      }
      if (pageDataMap.packageDetails.safe) {
        additional = additional + '|SafePackage';
      }
      if (pageDataMap.safe) {
        additional = additional + '|SafePackage';
      }

      params.m_v40 = `${pageDataMap.packageDetails.pkg_hld_id}|${pageDataMap.packageDetails.pkg_nm}${additional}`;
      params.m_v9 = pageDataMap.packageDetails.pkg_hld_type;
      params.m_c2 = pageDataMap.packageDetails.pkg_flight_included;
      if (isRawClient()) {
        params.prop50 = pageDataMap.packageDetails.pkg_hol_typ;
      }
    }

    if (pageDataMap.packageDetailsError
      && pageDataMap.packageDetailsError.packageId
      && pageDataMap.packageDetailsError.name) {
      params.m_v40 = `${pageDataMap.packageDetailsError.packageId}|${pageDataMap.packageDetailsError.name}`; '';
    }

    if (pageDataMap.otherDetails) {
      if (pageDataMap.otherDetails.last_page_name) {
        params.m_c23 = pageDataMap.otherDetails.last_page_name;
      }
      params.m_v3 = pageDataMap.otherDetails.departureCity;
      if (pageDataMap.otherDetails.travel_start_date) {
        const travelDate = pageDataMap.otherDetails.travel_start_date.split(' ')[0];
        params.m_v4 = 'AP_' + getDaysDiff(new Date(), createDateFromString(travelDate));
      }
    }

    if (pageDataMap.fabCta) {
      addContactParams(params, pageDataMap.fabCta);
    }

    if (pageDataMap.pricingDetails) {
      // Confirmed with Product(Madhur) for removing this value from m_v46 and send new Bucket values
      // params.m_v46 = pageDataMap.pricingDetails.prc_post_disc;
    }

    if (pageDataMap.discountDetails) {
      params.m_v45 = pageDataMap.discountDetails.cpn_code;
    }
  }
};

export const populateQueryParams = (params, pageDataMap, branch,props66, { prop1 = '', evar22 = '', omniData = {}} = {}) => {
  getAllOmniData({ params, omniData });
  if (pageDataMap) {
    if (props66) {
      params.m_c66 = props66;
    }
    if (prop1) {
      params.m_c1 = prop1;
    }
    if (evar22) {
      params.m_v22 = evar22;
    }
    if (pageDataMap.otherDetails) {
      if (pageDataMap.otherDetails.last_page_name) {
        params.m_c23 = getOmniPageName(pageDataMap.otherDetails.last_page_name, branch);
      }
      if (pageDataMap.otherDetails.tagDestination) {
        params.m_v31 = pageDataMap.otherDetails.tagDestination;
      }
      if (pageDataMap.otherDetails.packageId && pageDataMap.otherDetails.packageName) {
        additional = pageDataMap?.otherDetails?.isPremiumPackage ? '|Premium' : '';
        params.m_v40 =
          `${pageDataMap.otherDetails.packageId}|${pageDataMap.otherDetails.packageName}${additional}`;
      }
    }

    if (pageDataMap.querySubmitSuccess) {
      params.m_e110 = 1;
    }
  }
};

export const populateLandingParams = (params, pageDataMap,prop66) => {
  if (pageDataMap) {
    if (prop66) {
      params.m_c66 = prop66;
    }
    if (pageDataMap.otherDetails && pageDataMap.otherDetails.tagDestination) {
      params.m_v31 = pageDataMap.otherDetails.tagDestination;
    }
  }
};

export const populateThankyouParams = (params, pageDataMap, omniData) => {
  getAllOmniData({params, omniData});
  if (pageDataMap) {
    const { packageDetails = {}, pricingDetails = {} } = pageDataMap || {};
    if (pageDataMap.purchase) {
      params.m_purchase = 1;
    }

    if (pageDataMap.bookingDetails && pageDataMap.bookingDetails.booking_id) {
      params.purchaseID = pageDataMap.bookingDetails.booking_id;
      params.m_v16 = pageDataMap.bookingDetails.booking_id;
      params.m_v40 = pageDataMap.safe ? 'SafePackage' : null;
    }
    let additional = '';
    if (pageDataMap?.packageDetails?.isPremiumPackage) {
      additional = additional + '|Premium';
    }
    if (pageDataMap.packageDetails.safe) {
      additional = additional + '|SafePackage';
    }
    if (pageDataMap.safe) {
      additional = additional + '|SafePackage';
    }

    if (pageDataMap?.packageDetails?.pkg_hld_id && pageDataMap?.packageDetails?.pkg_nm) {
      params.m_v40 = `${pageDataMap.packageDetails.pkg_hld_id}|${pageDataMap.packageDetails.pkg_nm}${additional}`;
    }

    const packagePrice = pricingDetails?.prc_tot_bkg_amt || '';
    const packageHotelStarRating = packageDetails?.pkg_hol_star_rating || 'NA';
    const itineraryComponents = packageDetails?.pkg_itinry_components || 'NA';
    // need new evar for both events
    // params.m_v = `${params.m_v40}|${packagePrice}|${packageHotelStarRating}|${itineraryComponents}`;
    // params.m_v = setThemeTagsMetric({ packageDetails })
  }
};

export const populateProductsParam = (params, pageDataMap) => {
  if (pageDataMap && pageDataMap.otherDetails && pageDataMap.otherDetails.quote_id) {
    if (isRawClient()) {
      params.products = ';' + pageDataMap.otherDetails.quote_id;
      params.m_v58 = 'hld:' + pageDataMap.otherDetails.quote_id;
    } else {
      params['&&products'] = ';' + pageDataMap.otherDetails.quote_id;
      params.m_v58 = 'hld:' + pageDataMap.otherDetails.quote_id;
    }
  }  else {
      params.m_v58 = '';
  }
};


export const populateParameters = ({ params = {}, pageDataMap = {}, prop66 = '', prop1 = '', evar22 = '', omniData = {} }) => {
  if (Object.keys(pageDataMap).length > 0) {
    if (prop66) {
      params.m_c66 = prop66;
    }
    if (prop1) {
      params.m_c1 = prop1;
    }
    if (evar22) {
      params.m_v22 = evar22;
    }

    if (pageDataMap?.otherDetails?.tagDestination) {
      params.m_v31 = pageDataMap.otherDetails.tagDestination;
    }
  }

  if (Object.keys(omniData).length > 0) {
    Object.keys(omniData).forEach(key => {
      if (omniData[key] && omniData[key] !== undefined) {
        params[key] = omniData[key];
      }
    });
  }
};
