import {
  callPDTApiListing,
  callPDTApiQuery,
  callPDTApiSearchWidget,
  callPDTApiDetail,
  callPDTApiReview,
  callPDTApiThankyou,
  callPDTApiLanding,
} from './HolidayPDTTracking';
import {
  trackClickEvent,
  trackPageVisits,
  trackViewEvent,
} from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';
import {REVIEW_PDT_PAGE_NAME} from '../Review/HolidayReviewConstants';
import {
  populateDetailsParams, populateLandingParams,
  populateListingParams,
  populateProductsParam,
  populateQueryParams,
  populateThankyouParams,
} from './HolidayOmnitureUtils';

import {
  AFFILIATES,
  GI_AFFILIATE_NAME,
  GI_AFFILIATE_SUFFIX, HLD_PAGE_NAME, TP_AFFILIATE_NAME,
  TP_AFFILIATE_SUFFIX,
  WG_OMNI_SUFFIX,
} from '../HolidayConstants';
import {getDataFromStorage, KEY_HOL_META} from '@mmt/legacy-commons/AppState/LocalStorage';
import {isAndroidClient, isLuxeFunnel, isNonMMTAffiliate, isRawClient, removeSubstringAfterHyphen, setThemeTagsMetric , updatedValuesforvariableforv83} from './HolidayUtils';
import HolidayDataHolder from './HolidayDataHolder';
import { GROUPING_TRACKING_PAGE_NAME } from '../Grouping/HolidayGroupingConstants';
import { LISTING_TRACKING_PAGE_NAME } from '../Listing/ListingConstants';
import { PAGE_NAME_SEARCH_WIDGET } from '../SearchWidget/SearchWidgetConstants';
import { fetchKafkaGenericDataMap } from './HolidayPDTTracking';
import { getPokusExpVarientKey } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import { PokusLobs } from '@mmt/legacy-commons/Native/AbConfig/AbConfigKeyMappings';
import {LANDING_PAGE_NAME, sectionCodes} from '../LandingNew/LandingConstants';
import {callPDTApiSearch} from './HolidayPDTTrackingV2';
import {isEmpty} from 'lodash';
import { TRACKING_EVENTS } from '../HolidayTrackingConstants';
import { getAffiliateName, isAffiliateUser } from './HolidayNetworkUtils';
import { getdeviceType } from './HolidayDevicesTypes';

const store = isRawClient() ? require('web/webStore').default : require('@mmt/legacy-commons/AppState/Store').store;

export const HOLIDAYS_CHANNEL_LANDING = 'landing';
export const HOLIDAYS_CHANNEL_DOM = 'mob IN DOM holidays funnel';
export const HOLIDAYS_CHANNEL_OBT = 'mob IN OBT holidays funnel';

export const HOLIDAYS_LOB_LANDING = 'mob in holidays';
export const HOLIDAYS_LOB_DOM = 'mob in domestic holidays';
export const HOLIDAYS_LOB_OBT = 'mob in outbound holidays';
export const HOLIDAYS_BRANCH_NONE = 'NAN';
export const FUNNEL_TYPE = 'Holidays';
export const trackListingClickEvent = async (omniPageName, omniEventName,suffix, ...pdtData) => {
  const params = {};
  await populateListingParams(params, pdtData[0],suffix);
  const isWG = pdtData[0] && pdtData[0].requestDetails && pdtData[0].requestDetails.isWG;
  trackClickEventOmni(omniPageName, pdtData[5], omniEventName, params, isWG);
  callPDTApiListing(...pdtData);
};

export const trackDeeplinkRececived =  (params) => {
  trackClickEvent(
      "mob:landing:hld:deeplink",
      "DEEPLINK_RECEIVED",
      params,
  );
};


export const trackListingLoadEvent = async (logOmni, omniPageName, ...pdtData) => {
  if (logOmni) {
    const params = {};
    const isWG = pdtData[0] && pdtData[0].requestDetails && pdtData[0].requestDetails.isWG;
    await populateListingParams(params, pdtData[0]);
    params.m_c42 = pdtData[3];
    trackPageVisitsOmni(omniPageName, pdtData[5], params, isWG);
  }
  callPDTApiListing(...pdtData);
};

export const trackSmeListingLoadEvent = (omniPageName, omniEventName, smeProfileDetail ) => {
    const params = {};
    if (smeProfileDetail) {
      params.m_c42 = 'SME' + '_' + smeProfileDetail.id + '_' + smeProfileDetail.name;
    }
    trackPageVisitsOmni(omniPageName, HOLIDAYS_BRANCH_NONE, params, false);
};



export const trackMapPageLoadEvent = async (pageName, reqData) => {
  const {departureCity } = reqData;
  const params = {};
  params.m_ch = HOLIDAYS_CHANNEL_LANDING;
  params.m_v24 = HOLIDAYS_LOB_LANDING;

  if (isRawClient()) {
    params.m_v83 = await updatedValuesforvariable_m_v83({[TRACKING_EVENTS.M_V83]: HolidayDataHolder.getInstance().getBanner()});
  }

  params.m_v31 = 'India'; // To city
  params.m_v32 = departureCity; // from city
  trackPageVisits(getOmniPageName(pageName, 'DOM', false, await fetchAffiliateSuffix()), params);
};

export const trackHolidayMapPageClickEvent = async (omniPageName, omniEventName, data = {}) => {
  const params = {};
  params.m_v31 = data.dest ? data.dest : undefined; // To city
  params.m_v32 = data.departureCity ? data.departureCity : undefined; // from city
  params.m_c3 = (data.dest && data.departureCity) ? data.dest + '|' + data.departureCity : undefined; // To and From City
  params.m_c4 = data.packageDate ? data.packageDate : undefined;  // date of search
  trackClickEventOmni(omniPageName, 'DOM', omniEventName, params, false);
};

export const trackSmePackageCardClickedEvent = (omniPageName, packageDetails, index ) => {
  const params = {};
  params.m_c42 = 'smepackage_' + '_' + index + '_' + packageDetails.id;
  trackPageVisitsOmni(omniPageName, HOLIDAYS_BRANCH_NONE, params, false);
};

export const trackSmeSimilarHostCardClickedEvent = (omniPageName, card, index ) => {
  const params = {};
  params.m_c42 = 'smesimilar' + '_' + index + '_' + card.id;
  trackPageVisitsOmni(omniPageName, HOLIDAYS_BRANCH_NONE, params, false);
};

export const trackSmeBackPress = (omniPageName, omniEventName, smeProfileDetail) => {
  const params = {};
  if (smeProfileDetail) {
    params.m_c42 = 'SME' + '_' + smeProfileDetail.id + '_' + smeProfileDetail.name;
  }
  trackClickEventOmni(omniPageName, HOLIDAYS_BRANCH_NONE, omniEventName, params, false);
};

export const trackLandingLoadEvent = async (logOmni, omniPageName, ...pdtData) => {
  if (logOmni) {
    const params = {};
    params.m_ch = HOLIDAYS_CHANNEL_LANDING;
    params.m_v24 = HOLIDAYS_LOB_LANDING;
    if (isRawClient()) {
      params.m_v83 = await updatedValuesforvariable_m_v83({ [TRACKING_EVENTS.M_V83]: HolidayDataHolder.getInstance().getBanner()});
    }
    const isWG = pdtData[0] && pdtData[0].requestDetails && pdtData[0].requestDetails.isWG;
    if (isWG) {
      params.m_v31 = 'Weekend Getaways';
    }

    params.m_c42 = pdtData[3];
    trackPageVisits(getOmniPageName(omniPageName, '', isWG, await fetchAffiliateSuffix()), params);
  }
  callPDTApiLanding(...pdtData);
};
export const trackSearchWidgetV2Landing = async ({pdtData}) => {
  callPDTApiSearch(pdtData);
};

export const trackSearchWidgetClickEvent = (omniPageName, omniEventName, ...pdtData) => {
  const isWG = pdtData[0] && pdtData[0].requestDetails && pdtData[0].requestDetails.isWG;
  const branch = pdtData[4] ? pdtData[4] : HOLIDAYS_BRANCH_NONE;
  trackClickEventOmni(omniPageName, branch, omniEventName, {}, isWG);
  callPDTApiSearchWidget(...pdtData);
};
export const trackSearchWidgetClickEventNew = async ({omniPageName = '', omniEventName = '', pdtData = {}}) => {
  // for collections page use pagename as listing
  const fromPageName = omniPageName === GROUPING_TRACKING_PAGE_NAME ? LISTING_TRACKING_PAGE_NAME : omniPageName;
  const isWG = pdtData?.pageDataMap?.requestDetail?.isWG || false;
  const branch = pdtData?.branch ||  HOLIDAYS_BRANCH_NONE;
  const params = {};
  const affiliateSuffix = (await fetchAffiliateSuffix()) || 'MMT';
  params.m_v108 = getEver108({pageName: omniPageName, affiliateSuffix: affiliateSuffix, source: HolidayDataHolder.getInstance().getSource()});
  params.m_c1 = PAGE_NAME_SEARCH_WIDGET;

  trackClickEventOmni(fromPageName, branch, omniEventName, params, isWG);
  callPDTApiSearchWidget(...Object.values(pdtData));
};
export const trackSearchWidgetLoadEvent = (...pdtData) => {
  callPDTApiSearchWidget(...pdtData);
};

export const trackLandingClickEvent = async (omniPageName, omniEventName,suffix, ...pdtData) => {
  const params = {};
  populateLandingParams(params, pdtData[0],suffix);
  const isWG = pdtData[0] && pdtData[0].requestDetails && pdtData[0].requestDetails.isWG;
  trackClickEventOmni(omniPageName, HOLIDAYS_BRANCH_NONE, omniEventName, params, isWG);
  callPDTApiLanding(...pdtData);
};

export const trackQueryClickEvent = (omniPageName, omniEventName,suffix, ...pdtData) => {
  const params = {};
  populateQueryParams(params, pdtData[0], pdtData[4],suffix);
  const isWG = pdtData[0] && pdtData[0].requestDetails && pdtData[0].requestDetails.isWG;
  trackClickEventOmni(omniPageName, pdtData[4], omniEventName, params, isWG);
  callPDTApiQuery(...pdtData);
};

export const trackQueryLoadEvent = (logOmni, omniPageName, ...pdtData) => {
  if (logOmni) {
    trackOmniQueryLoadEvent(omniPageName, ...pdtData);
  }
  callPDTApiQuery(...pdtData);
};

export const trackOmniQueryLoadEvent = (omniPageName, ...pdtData) => {
  const params = {};
  populateQueryParams(params, pdtData[0], pdtData[4]);
 const isWG = pdtData[0] && pdtData[0].requestDetails && pdtData[0].requestDetails.isWG;
  trackPageVisitsOmni(omniPageName, pdtData[4], params, isWG);
};

export const trackDetailsClickEvent = (omniPageName, omniEventName,suffix, ...pdtData) => {
  const params = {};
  populateDetailsParams(params, pdtData[0],suffix);
  const isWG = pdtData[0] && pdtData[0].requestDetails && pdtData[0].requestDetails.isWG;
  trackClickEventOmni(omniPageName, pdtData[5], omniEventName, params, isWG);
  callPDTApiDetail(...pdtData);
};

export const trackPhoenixDetailsClickEvent = (omniPageName, omniEventName, pdtData) => {
  const params = {};
  populateDetailsParams(params, pdtData);
  const isWG = pdtData && pdtData.requestDetails && pdtData.requestDetails.isWG;
  trackClickEventOmni(omniPageName, pdtData.branch, omniEventName, params, isWG);
  callPDTApiDetail(pdtData);
};

export const trackPhoenixDetailsLoadEvent = (omniPageName, pdtData) => {
  const params = {};
  populateDetailsParams(params, pdtData);
  const isWG = pdtData && pdtData.requestDetails && pdtData.requestDetails.isWG;
  params.m_c42 = pdtData[3];
  trackPageVisitsOmni(omniPageName, pdtData.branch, params, isWG);
  callPDTApiDetail(pdtData);
};

export const trackDetailsViewEvent = async (omniPageName, omniEventName, ...pdtData) => {
  const params = {};
  populateDetailsParams(params, pdtData[0]);
  params.m_ch = getChannelName(pdtData[5]);
  params.m_v24 = getLobName(pdtData[5]);
  if (isRawClient()) {
    params.m_v83 = await updatedValuesforvariable_m_v83({ [TRACKING_EVENTS.M_V83]: HolidayDataHolder.getInstance().getBanner() });
  }

  const isWG = pdtData[0] && pdtData[0].requestDetails && pdtData[0].requestDetails.isWG;
  trackViewEvent(getOmniPageName(omniPageName, pdtData[5], isWG, await fetchAffiliateSuffix()), omniEventName, params);
  callPDTApiDetail(...pdtData);
};


export const trackReviewClickEvent = (omniPageName, omniEventName,suffix, ...pdtData) => {
  const params = {};
  populateProductsParam(params, pdtData[0]);
  populateDetailsParams(params, pdtData[0],suffix);
  const isWG = pdtData[0] && pdtData[0].requestDetails && pdtData[0].requestDetails.isWG;
  trackClickEventOmni(omniPageName, pdtData[5], omniEventName, params, isWG);
  callPDTApiReview(...pdtData);
};

export const trackReviewPageClickEvent = ({
  omniPageName = '',
  omniEventName = '',
  prop66 = '',
  pdtData = {},
  prop83 = '',
  omniData = {},

}) => {
  const params = {};
  const { pageDataMap = {}, branch = '' } = pdtData || {};
  // const evar = setThemeTagsMetric({ packageDetails: pdtData?.pageDataMap?.packageDetails }); No evar provided for this
  populateProductsParam(params, pageDataMap);
  populateDetailsParams(params, pageDataMap, prop66, {
    omniData: {
      ...omniData,
      [TRACKING_EVENTS.M_V83]: updatedValuesforvariableforv83(prop83),
    },
  });
  const isWG = pageDataMap?.requestDetails?.isWG || false;
  trackClickEventOmni(omniPageName, branch, omniEventName, params, isWG);
  callPDTApiReview(...Object.values(pdtData));
};

// to-do kept for referrence
// export const trackReviewPageLoadEvent = async ({
//   logOmni = false,
//   omniPageName = '',
//   pdtData = {},
//   showEvar108 = false,
// }) => {
//   if (logOmni) {
//     const params = {};
//     if (pdtData?.activity) {
//       params.m_c54 = pdtData?.activity;
//     }
//     let banner = HolidayDataHolder.getInstance().getBanner();
//     if(!isEmpty(banner)) {
//       params.m_v83 = banner
//     }
//     populateProductsParam(params, pdtData.pageDataMap);
//     populateDetailsParams(params, pdtData?.pageDataMap);
//     params.m_ch = getChannelName(pdtData?.branch);
//     if (showEvar108) {
//       const affiliateSuffix = (await fetchAffiliateSuffix()) || 'MMT';
//       params.m_v108 = getEver108({pageName: omniPageName, affiliateSuffix: affiliateSuffix, source: HolidayDataHolder.getInstance().getSource()});
//     }
//     params.m_c23 = HolidayDataHolder.getInstance().getPrevPageOmni(omniPageName);
//     const isWG = pdtData?.pageDataMap?.requestDetails?.isWG || false;
//     trackPageVisitsOmni(omniPageName, pdtData?.branch, params, isWG);
//   }
//   callPDTApiReview(...Object.values(pdtData));
// };

export const trackReviewPageLoadEvent = async ({
  logOmni = false,
  omniPageName = '',
  pdtData = {},
  omniData = {},
}) => {
  if (logOmni) {
    const params = {};
    if (pdtData?.activity) {
      params.m_c54 = pdtData?.activity;
    }
    let banner = HolidayDataHolder.getInstance().getBanner();
    if(!isEmpty(banner)) {
      params.m_v83 = banner
    }

    populateProductsParam(params, pdtData.pageDataMap);
    populateDetailsParams(params, pdtData?.pageDataMap, '', {
      omniData: {
        ...omniData,
        m_ch: getChannelName(pdtData?.branch),
        m_c23: HolidayDataHolder.getInstance().getPrevPageOmni(omniPageName),
      },
    });
    const isWG = pdtData?.pageDataMap?.requestDetails?.isWG || false;
    trackPageVisitsOmni(omniPageName, pdtData?.branch, params, isWG);
  }
  callPDTApiReview(...Object.values(pdtData));
};

export const trackReviewLoadEvent = (logOmni, omniPageName, ...pdtData) => {
  if (logOmni) {
    const params = {};
    populateProductsParam(params, pdtData[0]);
    populateDetailsParams(params, pdtData[0]);
    params.m_ch = getChannelName(pdtData[5]);
    const isWG = pdtData[0] && pdtData[0].requestDetails && pdtData[0].requestDetails.isWG;
    trackPageVisitsOmni(omniPageName, pdtData[5], params, isWG);
  }
  callPDTApiReview(...pdtData);
};

export const trackThankyouLoadEvent = ({logOmni, omniPageName, pdtData, omniData}) => {
  if (logOmni) {
    const params = {};
    const { pageDataMap = {}, branch } = pdtData || {};
    populateProductsParam(params, pageDataMap);
    populateThankyouParams(params, pageDataMap, omniData);
    params.m_ch = getChannelName(branch);
    const isWG = pdtData?.pageDataMap?.requestDetails?.isWG || false;
    trackPageVisitsOmni(omniPageName, pdtData?.branch, params, isWG);
  }
  callPDTApiThankyou(...Object.values(pdtData));
};

export const trackThankyouClickEvent = ({ omniPageName, omniEventName, pdtData, omniData }) => {
  const params = {};
  const { pageDataMap = {}, branch } = pdtData || {};
  const isWG = pdtData?.pageDataMap?.requestDetails?.isWG || false;
  populateThankyouParams(params, pageDataMap, omniData);
  trackClickEventOmni(omniPageName, branch, omniEventName, params, isWG);
  callPDTApiThankyou(...Object.values(pdtData));
};

export const trackClickEventOmni = async (pageName, branch, eventName, params, isWG) => {
  if (params) {
    params.m_ch = getChannelName(branch);
    params.m_v24 = getLobName(branch);
    const userDetailsObj = await fetchKafkaGenericDataMap();
    // params.m_v95 = userDetailsObj?.userDetails?.usr_wal_balnc;
    params.m_v94 = userDetailsObj?.userDetails?.hydra_segments;
    if (isRawClient()) {
      params.m_v83 = await updatedValuesforvariable_m_v83(params);
    }
    params.m_c23 = HolidayDataHolder.getInstance().getPrevPageOmni(pageName);
  }
  trackClickEvent(
    getOmniPageName(pageName, branch, isWG, await fetchAffiliateSuffix()),
    eventName,
    params,
  );
};

export const fetchAffiliateSuffix = async () => {
  const holMetaObj = await getDataFromStorage(KEY_HOL_META);
  return (holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate)) ?
    holMetaObj.affiliate : '';
};

export const fetchAffiliateName = async () => {
    const holMetaObj = await getDataFromStorage(KEY_HOL_META);
    return holMetaObj && holMetaObj.affiliate && isNonMMTAffiliate(holMetaObj.affiliate)
    ? `${holMetaObj.affiliate}_${getdeviceType() ? getdeviceType() : 'mSite'}`
    : '';
};

export const updatedValuesforvariable_m_v83 = async (params) => {
  const temp = await fetchAffiliateName()
  if(temp) {
    return `${temp}|${params.m_v83}`;
  }
return `${params.m_v83}`;
};

export const trackPageVisitsOmni = async (pageName, branch, params, isWG) => {
  if (params) {
    params.m_ch = getChannelName(branch);
    params.m_v24 = getLobName(branch);
    const userDetailsObj = await fetchKafkaGenericDataMap();
    // params.m_v95 = userDetailsObj?.userDetails?.usr_wal_balnc;
    params.m_v94 = userDetailsObj?.userDetails?.hydra_segments;
    if (isRawClient()) {
      params.m_v83 = await updatedValuesforvariable_m_v83(params);
    }
    params.m_c23 = HolidayDataHolder.getInstance().getPrevPageOmni(pageName);
  }
  trackPageVisits(getOmniPageName(pageName, branch, isWG, await fetchAffiliateSuffix()), params);
};

export const getOmniPageName = (page, branch, isWG = false, affiliateSuffix) => {
  const pageKey = page;

  const branchText = `in ${
    branch
      ? branch === HOLIDAYS_BRANCH_NONE
        ? ''
        : branch === 'OBT'
        ? 'outbound'
        : 'domestic'
      : ''
  }`;
  let omniPage = `mob:funnel:${branchText} holidays:${page}`;
  if (page === LANDING_PAGE_NAME) {
    omniPage = `mob:landing: in holidays:${page}`;
  }
  if (isWG) {
    omniPage = `${omniPage}${WG_OMNI_SUFFIX}`;
  }
  if (affiliateSuffix) {
    omniPage = `${omniPage}${affiliateSuffix}`;
  }
  HolidayDataHolder.getInstance().setOmniPageName(pageKey, omniPage);
  return omniPage;
};

export const getChannelName = (branch) => {
  switch (branch) {
    case 'DOM':
      return HOLIDAYS_CHANNEL_DOM;

    case HOLIDAYS_BRANCH_NONE:
      return HOLIDAYS_CHANNEL_LANDING;

    default:
      return HOLIDAYS_CHANNEL_OBT;
  }
};

export const getLobName = (branch) => {
  switch (branch) {
    case 'DOM':
      return HOLIDAYS_LOB_DOM;

    case HOLIDAYS_BRANCH_NONE:
      return HOLIDAYS_LOB_LANDING;

    default:
      return HOLIDAYS_LOB_OBT;
  }
};



/****** HOLIDAY TRACKING NEW FUNCTIONS ******/

/* Following are the values for PDT_DATA OBJECT
    pdtData =  {
      pageDataMap: {},
      interventionDetails: {},
      eventType: '',
      activity: '',
      requestId: '',
      branch: ''
    }
} */

const getLandingPageGcAttachedVal = (storeData) => {
  const mmtBlackDetails = storeData?.holidaysLanding?.landingData?.sections?.filter((section) => section?.sectionCode?.toLowerCase() === 'black')?.[0];
  if (!mmtBlackDetails) {
    return '';
  }
  const mmtAttached = mmtBlackDetails?.cards?.[0]?.mmtBlackDetail?.mmtBlackPdtData?.mmtBlackGCAttached;
  return mmtAttached ? 'GC_1' : 'GC_0';
};

const getListingPageGcAttachedVal = (storeData) => {
  const mmtBlackDetails = storeData?.holidaysPhoenixGroupingV2?.holidayLandingGroupReducer?.groupSections?.filter((section) => section?.sectionCode?.toLowerCase() === 'black')?.[0];
  if (!mmtBlackDetails) {
    return '';
  }
  const mmtAttached = mmtBlackDetails?.cards?.[0]?.mmtBlackDetail?.mmtBlackPdtData?.mmtBlackGCAttached;
  return mmtAttached ? 'GC_1' : 'GC_0';
};

const getDetailsPageGcAttachedVal = (storeData) => {
  if (!storeData?.holidaysDetail?.mmtBlackDetail) {
    return '';
  }
  return storeData?.holidaysDetail?.mmtBlackDetail?.mmtBlackPdtData?.mmtBlackGCAttached ? 'GC_1' : 'GC_0';
};

const getReviewPageGcAttachedVal = (storeData) => {
  const mmtBlackPdtDetails = storeData?.holidaysReview?.mmtBlackDetail?.mmtBlackPdtData;
  if (!mmtBlackPdtDetails) {
    return '';
  }
  return mmtBlackPdtDetails?.mmtBlackGCAttached ? 'GC_1' : 'GC_0';
};

const getMmtBlackAttachDetailsByPageName = (pageName) => {
  const storeData = store.getState();
  let mmtAttached = '';
  if (pageName?.toUpperCase() === HLD_PAGE_NAME.LANDING) {
    return getLandingPageGcAttachedVal(storeData);
  }
  if (pageName?.toLowerCase() === 'listing-v1' || pageName?.toLowerCase() === 'collections-v1') {
    return getListingPageGcAttachedVal(storeData);
  }
  if (pageName?.toLowerCase() === 'detail') {
    return getDetailsPageGcAttachedVal(storeData);
  }
  if (pageName?.toLowerCase() === 'review') {
    return getReviewPageGcAttachedVal(storeData);
  }
  return mmtAttached;
};

export const getEvar108ForReview = ({reviewType = '', pageName = REVIEW_PDT_PAGE_NAME, source = '', ticketSource = ''}) => {
  const value =  reviewType === sectionCodes.PRESALES
    ? getEvar108ForPSM({ pageName, source, ticketSource })
    : getEvar108ForFunnel({
        source,
        trackingPageName: pageName,
        ticketSource
      });
  return value;
};

const fetchAffiliateData = () => {
  return isRawClient() && isAffiliateUser() ? getAffiliateName() : 'MMT';
};
export const getEver108 = ({ pageName, affiliateSuffix, source = ''}) => {
  return `${FUNNEL_TYPE}|${pageName}|${affiliateSuffix}|${source}`;
};


export const getLuxeStringForEvar108 = () => {
  return `${isLuxeFunnel()? '|LUXE' : ''}`;
}
export const getEvar108ForPSM = ({ pageName, source = '', ticketSource = ''}) => {
  // Syntax: Funnel Type|Pagetype-Version-Presales|Affiliate e.g
  // Holidays|Collection-version-Presales|MMT
  return `${FUNNEL_TYPE}|${pageName}-v1-Presales|${fetchAffiliateData()}|${getMmtBlackAttachDetailsByPageName(pageName)}${source ? `|${source}` : ''}${getLuxeStringForEvar108()}${
    ticketSource ? `|${ticketSource}` : ''
  }`;
};

export const getEvar108ForFunnel = ({ source, trackingPageName, ticketSource = '' }) => {
  return `${FUNNEL_TYPE}|${trackingPageName}|${fetchAffiliateData()}|${getMmtBlackAttachDetailsByPageName(trackingPageName)}${source ? `|${source}` : ''}${getLuxeStringForEvar108()}${
    ticketSource ? `|${ticketSource}` : ''
  }`;
};

export const setBasicParameters = async ({
  parametersToSet,
  pageName,
  trackingPageName,
  pdtData,
  params = {},
  branch = '',
}) => {
  let affiliateSuffix = '';
  let source = '';
  let affiliateName = '';
  let userDetailsObj = await fetchKafkaGenericDataMap();
  const { activity = '', pageDataMap = '' } = pdtData || {};
  const { trackingDetails = '' } = pageDataMap || {};
  let pokusVariantKey = getPokusExpVarientKey(PokusLobs.HOLIDAY);
  if (parametersToSet.find((param) => (param = 'm_v83'))) {
    affiliateName = await fetchAffiliateName();
  }
  let banner = HolidayDataHolder.getInstance().getBanner();
  if (!isEmpty(banner)) {
    params.m_v83 = banner;
  }
  if(isRawClient())
  {
    params.m_v83 = await updatedValuesforvariable_m_v83(params);
  }

  if (parametersToSet.find((param) => param === 'm_v108')) {
    affiliateSuffix = (await fetchAffiliateSuffix()) || 'MMT';
    source = (HolidayDataHolder.getInstance().getSource()) || '';
  }
  parametersToSet.map((param) => {
    switch (param) {
      case 'm_ch':
        params.m_ch = getChannelName(branch || HOLIDAYS_BRANCH_NONE);
        break;
      case 'm_v24':
        params.m_v24 = getLobName(branch || HOLIDAYS_BRANCH_NONE);
        break;
      case 'm_v108':
        params.m_v108 = getEvar108ForFunnel({
          source: trackingDetails?.source || '',
          ticketSource:source,
          trackingPageName,
          affiliateSuffix,
        });
        break;
      case 'm_v31':
        params.m_v31 = 'Weekend Getaways';
        break;
      case 'm_c24':
        params.m_c24 = getLobName(branch || HOLIDAYS_BRANCH_NONE);
        break;
      case 'm_c23':
        params.m_c23 = HolidayDataHolder.getInstance()
          .getPrevPageOmni(removeSubstringAfterHyphen(trackingPageName))
          ?.replace('collections', 'listing');
        break;
      case 'm_c42':
        params.m_c54 = activity;
        break;
      case 'm_v95':
        params.m_v95 = userDetailsObj?.userDetails?.usr_wal_balnc;
        break;
      case 'm_v94':
        params.m_v94 = userDetailsObj?.userDetails?.hydra_segments;
        break;
      case 'm_v79':
        params.m_v79 = pokusVariantKey;
        break;
      default:
        // do nothing;
        break;
    }
  });
  return params;
};

export const trackClickEventOmniNew = async ({
  pageName,
  branch,
  eventName,
  params = {},
  isWG,
  pdtData = {},
  trackingPageName = '', // used for evar108 pagename
  parametersToSet,
}) => {
  if (params) {
    await setBasicParameters({
      trackingPageName,
      parametersToSet,
      params,
      pageName,
      branch,
      pdtData,
    });
  }
  trackClickEvent(
    getOmniPageName(pageName, branch, isWG, await fetchAffiliateSuffix()),
    eventName,
    params,
  );
};

export const holidayTrackingLoadEvent = async ({
  logOmni = false,
  pdtData = {},
  branch = '',
  params = {},
  pageVisitPageName = '',
  pageName = '',
  trackingPageName = '',
  parametersToSet = [],
  callPDTApiFunction,
  isCallPDTFunction = false,
}) => {
  if (logOmni) {
    await setBasicParameters({
      parametersToSet,
      params,
      pageName,
      trackingPageName,
      branch,
      pdtData,
    });
    trackPageVisits(pageVisitPageName, params);
  }
  if (isCallPDTFunction) {
    callPDTApiFunction({ pdtData });
  }
};
export const holidayTrackingClickEvent = async ({
  callPDTApiFunction = () => {},
  pdtData = {},
  clickEventParameters,
}) => {
  trackClickEventOmniNew({ ...clickEventParameters, pdtData });
  callPDTApiFunction({ pdtData });
};

export const holidayTrackingParameters = [
  'm_ch',
  'm_v24',
  'm_c24',
  'm_c23',
  'm_v108',
  'm_v94',
  'm_v79',
  ...(isRawClient() ? ['m_v83'] : []),
];
