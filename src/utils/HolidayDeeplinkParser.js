import url from 'url';
import { deepLinkParams } from '../PhoenixDetail/DetailConstants';
import {
  AFFILIATES,
  DEEPLINK_DETAIL_PAGE_CUSTOMIZED,
  FUNNELS,
  DEVICE_TYPE_CONSTANT,
  FLAVOUR,
  HOL_REQUEST_TYPE,
} from '../HolidayConstants';
import { convertDeepLinkDateToRequestDate, sanetizeRequestQuoteIdValue } from '../PhoenixDetail/Utils/HolidayDetailUtils';
import { formatDate, isAppPlatform, isNonMMTAffiliate } from './HolidayUtils';
import { isEmpty } from 'lodash';

const DELIMITER = "\\,";
const COMMA = ',';
const UNDER_SCORE = '_';

class HolidayDeeplinkParser {

  static openAlwaysNewFlow(query) {
    return true;
  }

  static isDeviceTypeAvailableinLink(query) {
    if (!query) {
      return false;
    }
    const aff=this.getAffiliateFromUrl(query);
    if(!isNonMMTAffiliate(aff)) return false;
    const q = url.parse(query, true);
    const queryParams = q.query;
    let deviceType = queryParams['device_type'] ? queryParams['device_type'] : (queryParams['flavour'] ? queryParams['flavour'] : '');
    return isAppPlatform(deviceType) ;
  }

  static getAffiliateFromUrl(query) {
    if (!query) {
      return AFFILIATES.MMT;
    }
    const q = url.parse(query, true);
    const queryParams = q.query;
    let aff = queryParams['aff'] || AFFILIATES.MMT;
    const { hostname } = new URL(query);
    if (hostname) {
      if(hostname.includes('6eholidays')) {
        aff = AFFILIATES.INDIGO; // if you want to handle other change ge here.
      } else if(hostname.includes('giholidays.makemytrip.com')) {
        aff = AFFILIATES.GI; // if you want to handle other change ge here.
      } else if(hostname.includes('hdfcsmartbuyholidays.makemytrip.com')) {
        aff = AFFILIATES.HDFC; // if you want to handle other change ge here.
      } else if(hostname.includes('airindiaexpressholidays.makemytrip.com')){
        aff = AFFILIATES.AIX;
       }
    }
    return aff;
  }

  static getAffiliateFromUrlForLoginPage(query) {
    if (!query) {
      return AFFILIATES.MMT;
    }
    const q = url.parse(query, true);
    const queryParams = q.query;
    let aff = queryParams['aff'] || AFFILIATES.MMT;
    const { hostname } = new URL(query);
    if (hostname) {
       if(hostname.includes('6eholidays')) {
        aff = AFFILIATES.INDIGO; // if you want to handle other change ge here.
      } else if(hostname.includes('giholidays.makemytrip.com')) {
         aff = AFFILIATES.GI; // if you want to handle other change ge here.
       } else if(hostname.includes('hdfcsmartbuyholidays.makemytrip.com')) {
         aff = AFFILIATES.HDFC; // if you want to handle other change ge here.
       } else if(hostname.includes('airindiaexpressholidays.makemytrip.com')){
        aff = AFFILIATES.AIX;
       }
    }
    return aff;
  }

  static getDeviceType(query){
    let device;
    if(query){
      device=query?.[DEVICE_TYPE_CONSTANT] ? query?.[DEVICE_TYPE_CONSTANT] : (query?.[FLAVOUR]);
    }
    return  device;
  }

  static createGroupingDeepLinkQuery (query, queryParams, fromDeeplink = false) {
    if (!query) {
      return {};
    }
    let deepLink = query;
    let queryResult = {
      dest: queryParams[ListingGroupingQueryParamsMap.DESTINATION] || queryParams[ListingGroupingQueryParamsMap.DESTINATION_CITY] || '',
      fromSeo: false
    };
    if(queryParams[ListingGroupingQueryParamsMap.DESTINATION_CITY_CODE]) queryResult.destCode = queryParams[ListingGroupingQueryParamsMap.DESTINATION_CITY_CODE];
    if(queryParams[ListingGroupingQueryParamsMap.DEPARTURE_CITY]) {
      queryResult.depCity = queryParams[ListingGroupingQueryParamsMap.DEPARTURE_CITY];
      queryResult.fromCity = queryParams[ListingGroupingQueryParamsMap.DEPARTURE_CITY];
    }
    if(queryParams[ListingGroupingQueryParamsMap.DEPARTURE_CITY_CODE]) queryResult.depCityCode = queryParams[ListingGroupingQueryParamsMap.DEPARTURE_CITY_CODE];
    if(queryParams[ListingGroupingQueryParamsMap.SHOW_FAB]) queryResult.showFab = queryParams[ListingGroupingQueryParamsMap.SHOW_FAB];
    if(queryParams[ListingGroupingQueryParamsMap.PACKAGE_IDS]) queryResult.packageIds = queryParams[ListingGroupingQueryParamsMap.PACKAGE_IDS];
    if(queryParams[ListingGroupingQueryParamsMap.DEFAULT_PACKAGE_ID]) queryResult.defDuration = queryParams[ListingGroupingQueryParamsMap.DEFAULT_PACKAGE_ID];
    if(queryParams[ListingGroupingQueryParamsMap.LAST_PAGE]) queryResult.LAST_PAGE = queryParams[ListingGroupingQueryParamsMap.LAST_PAGE];
    if(queryParams[ListingGroupingQueryParamsMap.PAGE_TYPE] || FUNNELS.HOLIDAY) queryResult.pt = queryParams[ListingGroupingQueryParamsMap.PAGE_TYPE] || FUNNELS.HOLIDAY;
    if(HolidayDeeplinkParser.getAffiliateFromUrl(query)) queryResult.aff = HolidayDeeplinkParser.getAffiliateFromUrl(query);
    if(queryParams[ListingGroupingQueryParamsMap.CMP]) queryResult.cmp = queryParams[ListingGroupingQueryParamsMap.CMP];
    if(queryParams[ListingGroupingQueryParamsMap.CAMPAIGN] || queryParams[ListingGroupingQueryParamsMap.HOL_CAMPAIGN]) queryResult.campaign = queryParams[ListingGroupingQueryParamsMap.CAMPAIGN] || queryParams[ListingGroupingQueryParamsMap.HOL_CAMPAIGN];
    if(queryParams[ListingGroupingQueryParamsMap.fromDeepLink]) queryResult.fromDeepLink = queryParams[ListingGroupingQueryParamsMap.fromDeepLink];

    queryResult = {
      ...queryResult,
      ...(queryParams[ListingGroupingQueryParamsMap.ROOMS] && {
        rooms: this.convertRoomForUrl(queryParams[ListingGroupingQueryParamsMap.ROOMS])
      }),
      ...((queryParams[ListingGroupingQueryParamsMap.PACKAGE_DATE] ||
        queryParams[ListingGroupingQueryParamsMap.DATE_SEARCHED]) && {
        dateSearched: formatDate(
          queryParams[ListingGroupingQueryParamsMap.PACKAGE_DATE] ||
            queryParams[ListingGroupingQueryParamsMap.DATE_SEARCHED],
          "YYYY-MM-DD"
        )
      })
    };
    if(queryParams?.filters?.urlParams) filters.forEach(
        (filter) => queryResult[filter.urlParams] = `${encodeURI(filter.values.join('~'))}`
    );
    Object.keys(queryResult).map((param, index)=>{
      const expression = index === 0 ? '?' : '&';
      if (Object.values(ListingGroupingParams).includes(param)) {
        deepLink = `${deepLink}${expression}${param}=${queryResult[param]}`;
      }
    })
    return deepLink;
  }

  static parseDetailPageDeeplink (query, fromDeeplink = false) {
    if (!query) {
      return {};
    }
    let decodeUrl = decodeURI(query);
    const q = url.parse(decodeUrl, true);
    const pathName = q.pathname;
    const queryParams = q.query;
    let result = {
      categoryId: queryParams[deepLinkParams.category] || queryParams[deepLinkParams.listingClassId],
      departureDetail: {
        departureCity: queryParams[deepLinkParams.fromCity],
        departureDate: convertDeepLinkDateToRequestDate(queryParams[deepLinkParams.depDate]),
        departureCityLocusCode: queryParams[deepLinkParams.fromCityCode],
      },
      pkgType: queryParams[deepLinkParams.pkgType],
      destinationDetail: {},
      storyEnabled: queryParams[deepLinkParams.story] === 'true',
      pt: queryParams[deepLinkParams.pt] || FUNNELS.HOLIDAY,
      aff: HolidayDeeplinkParser.getAffiliateFromUrl(query),
      deviceType: this.getDeviceType(queryParams) ,
      cmp: queryParams[deepLinkParams.cmp],
      initId: queryParams[deepLinkParams.initId] || undefined,
      fromSeo: queryParams[deepLinkParams.fromSeo] || undefined,
      fromDeepLink: fromDeeplink,
      enablePhoenixV3 :  (queryParams[deepLinkParams.enablePhoenixV3] === 'true'),
      quoteRequestId: sanetizeRequestQuoteIdValue(queryParams[deepLinkParams.quoteRequestId]) || undefined,
      banner: queryParams[deepLinkParams.banner] || undefined,
      source:queryParams[deepLinkParams.source] || undefined,
    };
    if (pathName.includes(DEEPLINK_DETAIL_PAGE_CUSTOMIZED)) {
      // User saved package deeplink
      if (queryParams[deepLinkParams.dynamicPackageId]) {
        result.fphSavePackageId = queryParams[deepLinkParams.dynamicPackageId];
      } else {
        result.savePackageId = queryParams[deepLinkParams.id];
      }
      result.fromDeepLink = true;
    } else {
      result.packageId = queryParams[deepLinkParams.id];
    }
    const intid = queryParams[deepLinkParams.initId];
    if(isEmpty(result.cmp) &&!isEmpty(intid)) {
      result.cmp = intid;
    }
    // if (!isEmpty(queryParams[deepLinkParams.rooms])){
    //   result.rooms = HolidayDeeplinkParser.getRoomFromParams(queryParams[deepLinkParams.rooms]);
    // }
    return result;
  }

  static parseListingGroupingPageDeeplink (query, fromDeeplink = false,redirectionpage, openDeeplink = false) {
    if (!query) {
      return {};
    }
    let decodeUrl = decodeURI(query)
    const q = url.parse(decodeUrl, true);
    const queryParams = q.query;

    const metaTagsString = queryParams[ListingGroupingParams.META_TAG];
    const metaTagsList = [];
    if (metaTagsString) {
      const metaValues = metaTagsString.split(DELIMITER);
      metaValues.forEach((themes) => {
        const themeList = themes.split(COMMA);
        themeList.forEach((theme) => {
          const valArr = theme.split(UNDER_SCORE);
          if (valArr != null && valArr.length >= 2) {
            metaTagsList.push(valArr[1]);
          }
        });
      });
    }

    let packageDate = queryParams[ListingGroupingParams.DATE_SEARCHED];
    // test for date format is dd/mm/yyyy
    const dateRegEx = new RegExp(/^(0?[1-9]|[12][0-9]|3[01])[\/\-](0?[1-9]|1[012])[\/\-]\d{4}$/);
    if (dateRegEx.test(packageDate)) {
      // new date takes format (mm/dd/yyyy) or (yyyy/mm/dd) so need to convert is suggested date
      packageDate = packageDate.split('/').reverse().join('-');
    }

    let result = {
      ...(!isEmpty(queryParams[ListingGroupingParams.ROOMS]) && {
        rooms: this.getRoomFromParams(queryParams[ListingGroupingParams.ROOMS])
      }),
      dest: queryParams[ListingGroupingParams.DESTINATION_CITY] || '',
      destinationCity: HolidayDeeplinkParser.getStringValueFromParam(queryParams[ListingGroupingParams.DESTINATION_CITY]),
      destinationLocusCode: queryParams[ListingGroupingParams.DESTINATION_CITY_CODE],
      departureCity: queryParams[ListingGroupingParams.DEPARTURE_CITY] || queryParams[ListingGroupingParams.FROM_CITY],
      departureLocusCode: queryParams[ListingGroupingParams.DEPARTURE_CITY_CODE],
      showFab: queryParams[ListingGroupingParams.SHOW_FAB] || '',
      packageIds: queryParams[ListingGroupingParams.PACKAGE_IDS]?.split(','),
      defDuration: queryParams[ListingGroupingParams.DEFAULT_DURATION],
      defPkgId: queryParams[ListingGroupingParams.DEFAULT_PACKAGE_ID],
      lastPage: queryParams[ListingGroupingParams.LAST_PAGE],
      pt: queryParams[ListingGroupingParams.PAGE_TYPE] || FUNNELS.HOLIDAY,
      aff: HolidayDeeplinkParser.getAffiliateFromUrl(query),
      deviceType: this.getDeviceType(queryParams) ,
      cmp: queryParams[ListingGroupingParams.CMP],
      packageDate: formatDate(packageDate, 'YYYY-MM-DD') || undefined,
      metaTags: metaTagsList.length ? metaTagsList : null,
      query: decodeUrl,
      // campaign: queryParams[ListingGroupingParams.CAMPAIGN],
      holCampaign: queryParams[ListingGroupingParams.CAMPAIGN],
      fromDeepLink: fromDeeplink,
      filters: null,
      fromSeo: false,
      enablePhoenixListing: (queryParams[ListingGroupingParams.enablePhoenixListing] === 'true'),
      redirectionPage:redirectionpage || queryParams[ListingGroupingParams.PARAM_REDIRECTION_PAGE],
      banner: queryParams[ListingGroupingParams.BANNER],
      source: queryParams[ListingGroupingParams.SOURCE] || undefined,
      activeConversationId: queryParams?.[ListingGroupingParams.ACTIVE_CONVERSATION_ID] || '',
      openTravelPlex: queryParams?.[ListingGroupingParams.OPEN_TRAVEL_PLEX] === 'true',
    };

    if (openDeeplink) {
      result.campaign = queryParams[ListingGroupingParams.CAMPAIGN];
    }

    if (!isEmpty(queryParams[ListingGroupingParams.ROOMS])){
      result.rooms = HolidayDeeplinkParser.getRoomFromParams(queryParams[ListingGroupingParams.ROOMS]);
    }

    const intid = queryParams[ListingGroupingParams.intid];
    if(isEmpty(result.cmp) && !isEmpty(intid)) {
      result.cmp = intid;
    }
    return result;
  }

  static parseDetailPageDeeplink (query, fromDeeplink = false) {
    if (!query) {
      return {};
    }
    let decodeUrl = decodeURIComponent(query);
    const q = url.parse(decodeUrl, true);
    const pathName = q.pathname;
    const queryParams = q.query;
    let result = {
      categoryId: queryParams[deepLinkParams.category] || queryParams[deepLinkParams.listingClassId],
      departureDetail: {
        departureCity: queryParams[deepLinkParams.fromCity],
        departureDate: convertDeepLinkDateToRequestDate(queryParams[deepLinkParams.depDate]),
        departureCityLocusCode: queryParams[deepLinkParams.fromCityCode],
      },
      pkgType: queryParams[deepLinkParams.pkgType],
      destinationDetail: {},
      storyEnabled: queryParams[deepLinkParams.story] === 'true',
      pt: queryParams[deepLinkParams.pt] || FUNNELS.HOLIDAY,
      aff: HolidayDeeplinkParser.getAffiliateFromUrl(query),  
      deviceType: this.getDeviceType(queryParams) ,    
      cmp: queryParams[deepLinkParams.cmp],
      initId: queryParams[deepLinkParams.initId] || undefined,
      fromSeo: queryParams[deepLinkParams.fromSeo] || undefined,
      fromDeepLink: fromDeeplink,
      source: queryParams[deepLinkParams.source] || '',
      requestType: queryParams[deepLinkParams.requestType] || '',
      banner: queryParams[deepLinkParams.banner] || undefined,
      quoteRequestId: sanetizeRequestQuoteIdValue(queryParams[deepLinkParams.quoteRequestId]) || undefined,
      docId: queryParams[deepLinkParams.docId],
      requestMeta: {
        ...(!isEmpty(queryParams[deepLinkParams.variantId]) && {
          varaintId: queryParams[deepLinkParams.variantId],
        }),
      },
      activeConversationId: queryParams[deepLinkParams.activeConversationId] || '',
      openTravelPlex: queryParams[deepLinkParams.openTravelPlex] === 'true',
    };
    if (pathName.includes(DEEPLINK_DETAIL_PAGE_CUSTOMIZED)) {
      // User saved package deeplink
      if (queryParams[deepLinkParams.dynamicPackageId]) {
        result.fphSavePackageId = queryParams[deepLinkParams.dynamicPackageId];
      } else {
        result.savePackageId = queryParams[deepLinkParams.id];
      }
      result.fromDeepLink = true;
    } else if (result.requestType === HOL_REQUEST_TYPE.DROP_OFF) {
      result.dropOffDynamicPackageId =  queryParams[deepLinkParams.dynamicPackageId] || '';
    } else {
      result.packageId = queryParams[deepLinkParams.id];
    }
    const intid = queryParams[deepLinkParams.initId];
    if(isEmpty(result.cmp) &&!isEmpty(intid)) {
      result.cmp = intid;
    }
    if (!isEmpty(queryParams[deepLinkParams.rooms])){
      result.rooms = HolidayDeeplinkParser.getRoomFromParams(queryParams[deepLinkParams.rooms]);
    }
    return result;
  }

  static getStringValueFromParam(str)
  {
     if(Array.isArray(str))
     {
      return str[0];
     }
     return str;
  }

  static convertRoomForUrl = (rooms) => {
    if (isEmpty(rooms)) {
      return '';
    }
    const roomStrs = [];
    rooms.map(room => {
      const {
        noOfAdults = '',
        noOfChildrenWB = '',
        noOfChildrenWOB = '',
        noOfInfants = '',
        listOfAgeOfChildrenWB = [],
        listOfAgeOfChildrenWOB = [],
        listOfInfAge = [],
      } = room;

      const str =
          `${noOfAdults ?? 0},${noOfChildrenWB ?? 0},${noOfChildrenWOB ?? 0},${noOfInfants ?? 0}` +
          `,${listOfAgeOfChildrenWB.join('-')},${listOfAgeOfChildrenWOB.join('-')},${listOfInfAge.join(
              '-'
          )}`;
      roomStrs.push(str);
    });
    return roomStrs.join('|');

  };

  static getRoomFromParams = (roomStr = '') => {
    const roomStrArr = roomStr.split('|');
    const rooms = [];
    roomStrArr.map(strRoom => {
      const [
        noOfAdults,
        noOfChildrenWB,
        noOfChildrenWOB,
        noOfInfants,
        listOfAgeOfChildrenWB,
        listOfAgeOfChildrenWOB,
        listOfInfAge,
      ] = strRoom.split(',');

      rooms.push({
        noOfAdults: parseInt(noOfAdults),
        noOfChildrenWB: parseInt(noOfChildrenWB),
        noOfChildrenWOB: parseInt(noOfChildrenWOB),
        noOfInfants: parseInt(noOfInfants),
        listOfAgeOfChildrenWB: listOfAgeOfChildrenWB
          ? listOfAgeOfChildrenWB.split('-').map(v => parseInt(v))
          : [],
        listOfAgeOfChildrenWOB: listOfAgeOfChildrenWOB
          ? listOfAgeOfChildrenWOB.split('-').map(v => parseInt(v))
          : [],
        listOfInfAge: listOfInfAge ? listOfInfAge.split('-').map(v => parseInt(v)) : [],
      });
    });
    return rooms;
  };

  static parseReviewPageDeeplink(query, fromDeeplink = false) {
    if (!query) {
      return {};
    }
    let decodeUrl = decodeURI(query)
    const q = url.parse(decodeUrl, true);
    const queryParams = q.query;
    let result = {
      quoteRequestId: sanetizeRequestQuoteIdValue(queryParams[ReviewParams.requestId]),
      reviewPackageId: queryParams[ReviewParams.reviewPackageId],
      dynamicPackageId: queryParams[ReviewParams.dynamicPackageId],
      fromAmendment: queryParams[ReviewParams.fromAmendment],
      trackStatus:isEmpty(queryParams[ReviewParams.whatsappStatus])?null:queryParams[ReviewParams.whatsappStatus]==="true" ? "W":null,
      pt: queryParams[ReviewParams.pt] || FUNNELS.HOLIDAY,
      aff: HolidayDeeplinkParser.getAffiliateFromUrl(query),
      deviceType: this.getDeviceType(queryParams) ,
      cmp: queryParams[ReviewParams.cmp],
      source: queryParams[ReviewParams.SOURCE],
      requestType: queryParams[ReviewParams.requestType],
      fromDeeplink: fromDeeplink,
      docId: queryParams[ReviewParams.docId],
      activeConversationId: queryParams[ReviewParams.activeConversationId] || '',
      openTravelPlex: queryParams[ReviewParams.openTravelPlex] === 'true',
      reviewType: queryParams[ReviewParams.reviewType],
      reviewSubtype: queryParams[ReviewParams.reviewSubtype],
    }
    if (result.reviewType === 'PRE_SALES') {
      result.oldQuoteRequestId = result.quoteRequestId;
    }
    const intid = queryParams[ReviewParams.intid];
    if(isEmpty(result.cmp) && !isEmpty(intid)) {
      result.cmp = intid;
    }
    return result;
  }

  static parseQueryFormDeeplink(query){
  if (!query) {
    return {};
  }
  let decodeUrl = decodeURIComponent(query);
  const q = url.parse(decodeUrl, true);
  const queryParams = q.query;
  const result = {
    destinationCity: queryParams?.dest,
    branch: queryParams?.branch,
    cmp: queryParams?.cmp,
    fromSeo: queryParams?.fromSeo ? queryParams?.fromSeo : queryParams?.seoQuery,
    query,
    pageName: queryParams?.pageName,
    DL: !queryParams?.nodl,
    formId : queryParams?.formId,
    pageSection: queryParams?.pageSection,
  };
  return result;
  }

  static createGroupingDeepLink({queryParams, query}) {
    const  q = url.parse(query, true)
    const existingQueryParams = q.query;
    if (Object.keys(queryParams).length < 0) {
      return null;
    }
    let deepLink = `${q.href}`;
    Object.keys(queryParams).map((param, index)=>{
      const expression = index === 0 ? '?' : '&';
      // to check if this is a valid param of not
      if (Object.values(ListingGroupingParams).includes(param)&& !existingQueryParams[param]) {
        deepLink = `${deepLink}${expression}${param}=${queryParams[param]}`;
      }

    })

    return deepLink;
  }
};

const ListingGroupingParams = {
  DESTINATION_CITY: 'dest',
  DESTINATION_CITY_CODE: 'destCode',
  DEPARTURE_CITY: "depCity",
  FROM_CITY: "fromCity",
  DEPARTURE_CITY_CODE: 'depCityCode',
  SHOW_FAB: 'showfab',
  PACKAGE_IDS: 'packageIds',
  DEFAULT_DURATION: 'defDuration',
  DEFAULT_PACKAGE_ID: 'defPackageId',
  CMP: 'cmp',
  PAGE_TYPE: 'pt',
  AFFILIATE: 'aff',
  LAST_PAGE: 'lastPage',
  DATE_SEARCHED: 'dateSearched',
  META_TAG: 'METATAG',
  PARAM_REDIRECTION_PAGE: 'redirectionPage',
  CAMPAIGN: 'campaign',
  enablePhoenixListing: 'enablePhoenixListing',
  intid: 'intid',
  ROOMS: 'rooms',
  BANNER:'banner',
  SOURCE: 'source',
  ACTIVE_CONVERSATION_ID: 'activeConversationId',
  OPEN_TRAVEL_PLEX: 'openTravelPlex',
}

const ListingGroupingQueryParamsMap = {
  DESTINATION: 'dest',
  DESTINATION_CITY: 'destinationCity',
  DESTINATION_CITY_CODE: 'destinationLocusCode',
  DEPARTURE_CITY: "departureCity",
  FROM_CITY: "fromCity",
  DEPARTURE_CITY_CODE: 'departureLocusCode',
  SHOW_FAB: 'showfab',
  PACKAGE_IDS: 'packageIds',
  DEFAULT_DURATION: 'defDuration',
  DEFAULT_PACKAGE_ID: 'defPkgId',
  CMP: 'cmp',
  PAGE_TYPE: 'pt',
  AFFILIATE: 'aff',
  LAST_PAGE: 'lastPage',
  DATE_SEARCHED: 'dateSearched',
  PACKAGE_DATE: 'packageDate',
  META_TAG: 'METATAG',
  PARAM_REDIRECTION_PAGE: 'redirectionPage',
  CAMPAIGN: 'campaign',
  HOL_CAMPAIGN: 'holCampaign',
  enablePhoenixListing: 'enablePhoenixListing',
  fromDeepLink: 'fromDeepLink',
  intid : 'intid',
  ROOMS: 'rooms',
}

export const ReviewParams = {
  requestId: 'requestId',
  reviewPackageId: 'reviewPackageId',
  dynamicPackageId: 'dynamicPackageId',
  fromAmendment: 'fromAmendment',
  whatsappStatus:"whatsapp",
  pt: 'pt',
  aff: 'aff',
  cmp: 'cmp',
  fromReviewDeeplink: 'from_review_deeplink',
  intid:'intid',
  reviewSubtype: 'reviewSubtype',
  reviewType: 'reviewType',
  source: 'source',
  SOURCE: 'source',
  requestType: 'requestType',
  docId: 'docId',
  activeConversationId: 'activeConversationId',
  openTravelPlex: 'openTravelPlex',
}

export default HolidayDeeplinkParser;
