import React from 'react';
import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import {connect} from 'react-redux';
import HolidaysSearchWidget from './Containers/HolidaySearchWidgetContainer';
import holidaysSearchWidget from './Reducers/HolidaySearchWidgetReducers';
import {withRouterState} from 'web/WebRouter';
import {injectAsyncReducer} from '@mmt/legacy-commons/AppState/asyncStore';

class HolidaysSearchWidgetWeb extends React.Component {
    render() {
        return (
            <HolidaysSearchWidget {...this.props}/>
        );
    }
}

const mapStateToProps = state => ({
    ...state.holidaysSearchWidget,
});

injectAsyncReducer('holidaysSearchWidget', holidaysSearchWidget);

export const HolidaysSearchWidgetContainer = connect(mapStateToProps, null)(HolidaysSearchWidgetWeb);

const HolidaysSearchWidgetRoutes = () => (
    <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
        <Switch>
            <Route exact path="/holidays/international/searchWidget"
                   component={withRouterState(HolidaysSearchWidgetContainer)}/>
        </Switch>
    </View>
);

export default withRouter(HolidaysSearchWidgetRoutes);
