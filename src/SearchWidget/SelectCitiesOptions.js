import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import CrossIcon from '@mmt/legacy-assets/src/cross_white.webp';
import {isEmpty, isArray} from 'lodash';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

export const SelectCitiesOptions = ({cities, selectedIndex, onCitySelect}) => {
    if (!isArray(cities) || cities.length <= 0){
        return [];
    }

    const renderListItemDay = (item, index) => {
        const selectedItem = index === selectedIndex;
        const containerStyle = selectedItem ? [styles.tabContainer, styles.tabActive] : styles.tabContainer;
        const textStyle = selectedItem ? [styles.tabText, styles.tabActiveText] : styles.tabText;
        const {cityName = '', distance} = item || {};
        const {unit, value} = distance || {};

        const UNIT_TEXT = (unit === 'KM') ? (value > 1 ? 'Kms' : 'Km') : unit;
        const DISTANCE_TEXT = (isEmpty(unit) && isEmpty(value)) ? '' : ' - ' + value + ' ' + UNIT_TEXT + ' away';

        return (
            <TouchableOpacity activeOpacity={0.8} style={containerStyle} onPress={() => onCitySelect(index)}>
                <Text style={textStyle}>{cityName} {DISTANCE_TEXT}</Text>
                {selectedItem && <Image source={CrossIcon} style={styles.cross}/>}
            </TouchableOpacity>
        );
    };

    const retMap = [];
    cities.map((item, index) => {
        retMap.push(renderListItemDay(item, index));
    });
    return (<View style={{marginTop: 20}}>{retMap}</View>);
};

const styles = StyleSheet.create({
    tabs: {
        marginBottom: 10,
        marginLeft: 5,
    },
    tabContainer: {
        backgroundColor: holidayColors.white,
        borderColor: colors.grey27,
        borderWidth: 1,
        borderRadius: 30,
        marginBottom: 12,
        height: 36,
        paddingHorizontal: 7,
        flexDirection: 'row',
        alignItems: 'center',
        alignSelf: 'baseline',
    },
    tabText: {
        paddingBottom: 2,
        fontFamily: 'lato',
        fontSize: 11,
        color: holidayColors.black,
        marginLeft: 12,
        marginRight:10,
    },
    tabActive: {
        shadowOpacity: 0,
        borderColor: holidayColors.primaryBlue,
        borderWidth: 1,
        backgroundColor: holidayColors.lightBlueBg,
    },
    tabActiveText: {
        color: colors.black29,
        fontSize: 11,
    },
    cross: {
        tintColor: colors.black29,
        height: 7,
        width: 7,
        marginRight: 10,
    },
});
