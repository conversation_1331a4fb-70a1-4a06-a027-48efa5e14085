import React from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TextInput,
  View,
} from 'react-native';
import PropTypes from 'prop-types';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss.js';
import {CLEAR} from '../SearchWidgetConstants';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

class FilterDestinationSelectorHeader extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      textInput: null,
    };
  }

  onClear = () => {
    this.setState({
      textInput: this._textInput.clear(),
    });

    this.props.onClear();
  };

  render() {
    const {
      showClear, onClear, showLoading, useClearIcon, ...textInputProps
    } = this.props;
    return (
      <View style={styles.container}>
        <View style={styles.textInput}>

          <TextInput
            autoFocus
            autoCorrect={false}
            {...textInputProps}
            ref={(e) => {
              this._textInput = e;
            }}
          />
        </View>
        {showLoading &&
        
        <View style={[styles.spinnerWrap]}>
          <Spinner
          size={20}
          strokeWidth={2}
          progressPercent={85}
          speed={1.5}
          color={holidayColors.black}
          />
        </View>
       
        }
        
        <View style={[AtomicCss.pushRight, styles.clearContainer]}>
          <AnchorBtn label={CLEAR} handleClick={this.onClear} />
        </View>
      </View>);
  }
}

FilterDestinationSelectorHeader.propTypes = {
  ...TextInput.PropTypes,
  showClear: PropTypes.bool,
  useClearIcon: PropTypes.bool,
  showLoading: PropTypes.bool,
  onClear: PropTypes.func.isRequired,
};

FilterDestinationSelectorHeader.defaultProps = {
  showClear: true,
  useClearIcon: true,
  showLoading: false,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
    justifyContent: 'center',
  },
  textInput: {
    flex: 1,
  },
  clearIcon: {
    position: 'relative',
    right: 10,
    height: 20,
    width: 20,
  },
  clearText: {
    color: holidayColors.black,
    fontFamily: fonts.bold,
    fontSize: 10,
    paddingRight: 10,
  },
  clearContainer: {
    marginTop: -10,
    paddingTop: 12,
  },
  spinnerWrap:{
    alignItems:"center",
  }
});

export default FilterDestinationSelectorHeader;
