import React from 'react';
import {View} from 'react-native';
import PropTypes from 'prop-types';
import Title from '@mmt/legacy-commons/Common/Components/Title';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import {CLEAR, FILTER_URL_NAME_CONSTANTS, NIGHT, NIGHTS} from '../SearchWidgetConstants';
import FilterSelectionBar from './FilterSelectionBar';

const GenericFilter = ({filterObj, handleSelectionChange, handleClear}) => {

  let optionsText = [],
    optionsTextMaster = [];
  let textAppend = '';
  for (const option in filterObj.optionsList) {
    if (filterObj.urlParam == FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME) {
      if (filterObj.optionsList[option].uniqueId == '1') {
        textAppend = NIGHT;
      } else {
        textAppend = NIGHTS;
      }
      optionsText.push(getOptionTextAppend(filterObj.optionsList, option, textAppend));
    } else {
      optionsText.push(getOption(filterObj.optionsList, option));
    }
  }

  for (const option in filterObj.masterOptionsList) {
    if (filterObj.urlParam == FILTER_URL_NAME_CONSTANTS.DURATION_FILTER_URL_NAME) {
      if (filterObj.masterOptionsList[option].uniqueId == '1') {
        textAppend = NIGHT;
      } else {
        textAppend = NIGHTS;
      }
      optionsTextMaster.push(getOptionTextAppend(filterObj.masterOptionsList, option, textAppend));
    } else {
      optionsTextMaster.push(getOption(filterObj.masterOptionsList, option));
    }
  }

  const optionsObj = {
    options: optionsText,
    optionsMaster: optionsTextMaster,
  };

  const selectionBarObj = {
    key: filterObj.id,
    filterObj,
    optionsObj,
    handleToggle: handleSelectionChange,
  };


  return (
    <View>
      <View style={[AtomicCss.flexRow,AtomicCss.marginBottom15]}>
        <View>
          <Title heading={filterObj.name} />
        </View>
        <View style={AtomicCss.pushRight}>
          <AnchorBtn key={filterObj.id} label={CLEAR}
                     handleClick={() => handleClear(filterObj.id, filterObj.urlParam)}> </AnchorBtn>
        </View>
      </View>
      <FilterSelectionBar
        {...selectionBarObj}
       />
    </View>
  );
};

const getOption = (list, option) => {
  const optionFilter = {
    filterText: list[option].filterText,
    uniqueId: list[option].uniqueId,
    isActive: list[option].isActive,
  };

  return optionFilter;
};

const getOptionTextAppend = (list, option, textAppend) => {
  const optionFilter = {
    filterText: list[option].filterText + textAppend,
    uniqueId: list[option].uniqueId,
    isActive: list[option].isActive,
  };

  return optionFilter;
};

GenericFilter.propTypes = {
  filterObj: PropTypes.object.isRequired,
  handleSelectionChange: PropTypes.func.isRequired,
  handleClear: PropTypes.func.isRequired,
};


export default GenericFilter;
