import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import CloseBtn from '@mmt/legacy-commons/Common/Components/Buttons/CloseBtn';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import PropTypes from 'prop-types';

const FilterHeader = ({titleText, resetText, handleReset, handleClose}) => {
  return (
    <View style={styles.header}>
      <Text style={styles.title}>{titleText}</Text>
      <AnchorBtn
        label={resetText}
        handleClick={handleReset}
       />
      <CloseBtn handleClose={handleClose} />
    </View>
  );
};

FilterHeader.propTypes = {
  titleText: PropTypes.string.isRequired,
  resetText: PropTypes.string.isRequired,
  handleClose: PropTypes.func.isRequired,
  handleReset: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 15,
    paddingVertical: 20,
    height: 60,
    shadowColor: '#330000',
    shadowOpacity: 0.2,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    position: 'relative',
    zIndex: 2,
    elevation: 2,

  },
  title: {
    flex: 3,
    fontFamily: 'Lato-Bold',
    letterSpacing: 0.3,
    color: '#000000',
    fontSize: 16,
  },
});

export default FilterHeader;
