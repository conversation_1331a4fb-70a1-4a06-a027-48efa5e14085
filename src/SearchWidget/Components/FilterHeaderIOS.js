import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import CloseBtn from '@mmt/legacy-commons/Common/Components/Buttons/CloseBtn';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import PropTypes from 'prop-types';

const FilterHeader = ({titleText, resetText, handleReset, handleClose}) => {

  return (
    <View style={styles.header}>

      <CloseBtn handleClose={handleClose} />

      <Text style={styles.title}>{titleText}</Text>
      <View style={styles.btnWrapper}>
        <AnchorBtn
          label={resetText}
          handleClick={handleReset}
         />
      </View>

    </View>
  );
};

FilterHeader.propTypes = {
  titleText: PropTypes.string.isRequired,
  resetText: PropTypes.string.isRequired,
  handleClose: PropTypes.func.isRequired,
  handleReset: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',

    backgroundColor: '#fff',
    paddingRight: 15,
    paddingLeft: 5,
    paddingVertical: 20,
    height: 60,

  },
  title: {
    flex: 3,
    color: '#4a4a4a',
    fontFamily: 'Lato-Regular',
    letterSpacing: 0.3,
    fontSize: 16,
    textAlign: 'center',
  },
  btnWrapper: {
    marginLeft: -7,
  },
});

export default FilterHeader;
