
import React from 'react';
import {View, Text, Switch} from 'react-native';
import PropTypes from 'prop-types';
import Title from '@mmt/legacy-commons/Common/Components/Title';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import {CLEAR, AVAILABLE_INCLUSIONS, AVAILABLE_INCLUSIONS_WG} from '../SearchWidgetConstants';
import FilterSelectionBar from './FilterSelectionBar';
import {getFlightInclusionOption} from '../utils/SearchWidgetUtil';
import Separator from '@mmt/legacy-commons/Common/Components/Separator';
import { AFFILIATES } from '../../HolidayConstants';
const AvailableInclusionFilter = ({
  filterObjHotel, filterObjInclusions, handleSelectionChange, handleAvailableInclusionClear, isWG, aff
}) => {

  const optionsObjHotel = {
    options: filterObjHotel.optionsList,
    optionsMaster: filterObjHotel.masterOptionsList,
  };

  const optionsObjInclusion = {
    options: filterObjInclusions.masterOptionsList,
    optionsMaster: filterObjInclusions.masterOptionsList,
  };

  const selectionBarObjHotel = {
    filterObj: filterObjHotel,
    hotelStar: true,
    optionsObj: optionsObjHotel,
    handleToggle: handleSelectionChange,
  };

  const flightOption = (aff === AFFILIATES.INDIGO ) ? null:(filterObjInclusions && filterObjInclusions.optionsList ? getFlightInclusionOption(optionsObjInclusion.options) : null);
  const showFlightOption = !!flightOption;

  return (
    <View>
      <View style={[AtomicCss.flexRow, AtomicCss.marginBottom15]}>
        <View style={AtomicCss.pushLeft}>
          <Title heading={isWG ? AVAILABLE_INCLUSIONS_WG : AVAILABLE_INCLUSIONS} />
        </View>
        <View style={AtomicCss.pushRight}>
          <AnchorBtn
            label={CLEAR}
            handleClick={() =>
              handleAvailableInclusionClear(filterObjHotel.id, filterObjHotel.urlParam,
                filterObjInclusions.id, filterObjInclusions.urlParam
              )}
          />
        </View>
      </View>
      <FilterSelectionBar
        {...selectionBarObjHotel}
      />

      {showFlightOption &&
      <View>
        <Separator />
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <Text style={{
            fontFamily: 'Lato-Bold',
            fontSize: 12,
            flex: 1,
            color: '#4a4a4a',
          }}
          >INCLUDE FLIGHTS
          </Text>
          <Switch
            tintColor="#cccccc"
            thumbColor={[(flightOption.isActive ? '#008cff' : '#f5f5f5')]}
            onTintColor="#d7edff"
            onValueChange={() => handleSelectionChange(filterObjInclusions.id, flightOption.uniqueId, filterObjInclusions.urlParam)}
            value={flightOption.isActive}
          />
        </View>
      </View>
      }
    </View>
  );
};

AvailableInclusionFilter.propTypes = {
  filterObjHotel: PropTypes.object.isRequired,
  filterObjInclusions: PropTypes.object.isRequired,
  handleSelectionChange: PropTypes.func.isRequired,
  handleAvailableInclusionClear: PropTypes.func.isRequired,
  isWG: PropTypes.bool.isRequired,

};

export default AvailableInclusionFilter;
