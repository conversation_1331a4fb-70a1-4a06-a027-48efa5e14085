import React from 'react';
import PropTypes from 'prop-types';
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import Title from '@mmt/legacy-commons/Common/Components/Title';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import calenderIcon from '@mmt/legacy-assets/src/calenderHolidays.webp';
import {TRAVELLING_IN, TRAVELLING_ON, SPECIFY_DATE_LABEL, CLEAR} from '../SearchWidgetConstants';
import FilterSelectionBar from './FilterSelectionBar';
import Separator from '@mmt/legacy-commons/Common/Components/Separator';

class DateFilter extends React.Component {

  handleSelectionChange = (id) => {
    this.props.handleSelectionChange(id);
  };

  render() {
    const activeBtnOpacity = 0.7;
    const btnStyle = [styles.text];
    const {TravellingMonthsOptions, TravellingMonthsOptionsMaster, handleTravellingMonthClear} = this.props;

    const optionsObj = {
      options: TravellingMonthsOptions,
      optionsMaster: TravellingMonthsOptionsMaster,
    };

    const selectionBarObj = {
      optionsObj,
      handleToggle: this.handleSelectionChange,
      scrollable: true,
    };
    const {dateSelected, day, date, monthYear} = this.props;

    return (
      <View style={styles.container}>
        <View style={[AtomicCss.flexRow, AtomicCss.marginBottom12]}>
          <View style={AtomicCss.pushLeft}>
            {!dateSelected && <Title heading={TRAVELLING_IN} />}
            {dateSelected && <Title heading={TRAVELLING_ON} />}
          </View>
          <View style={AtomicCss.pushRight}>
            <AnchorBtn label={CLEAR} handleClick={handleTravellingMonthClear}/>
          </View>
        </View>
        {!dateSelected &&
        <FilterSelectionBar
          {...selectionBarObj}
        />
        }
        {!dateSelected &&
        <View style={{marginTop: 12}}>
          <Separator type="noMargin" />
        </View>
        }
        {!dateSelected &&
        <TouchableOpacity style={styles.btnContainer} onPress={this.props.onClickSpecifyDate} activeOpacity={activeBtnOpacity}>
          <View style={styles.linkCenter}>
            <Text style={btnStyle}>{SPECIFY_DATE_LABEL}</Text>
          </View>
        </TouchableOpacity>
        }
        {dateSelected &&
        <View style={styles.link}>
          <TouchableOpacity style={styles.btnContainer} onPress={this.props.onClickSpecifyDate} activeOpacity={activeBtnOpacity}>
            <View style={styles.calSelection}>
              <Image style={styles.IconCal} source={calenderIcon} />
              <Text style={styles.selectedDate}>  {date} {monthYear} </Text>
              <Text style={styles.selectedDay}> {day}</Text>
            </View>
          </TouchableOpacity>
        </View>
        }
      </View>
    );
  }
}


DateFilter.propTypes = {
  date: PropTypes.number,
  monthYear: PropTypes.string,
  day: PropTypes.string,
  onClickSpecifyDate: PropTypes.func.isRequired,
  handleTravellingMonthClear: PropTypes.func.isRequired,
  handleSelectionChange: PropTypes.func.isRequired,
  TravellingMonthsOptions: PropTypes.array.isRequired,
  TravellingMonthsOptionsMaster: PropTypes.array.isRequired,
  dateSelected: PropTypes.bool.isRequired,
};

const styles = StyleSheet.create({
  link: {
    flex: 1,
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  linkCenter: {
    flex: 1,
    alignItems: 'center',
    marginVertical: 12,
    paddingVertical: 5,
    paddingHorizontal: 5,
  },
  btnContainer: {
    flex: 1,
  },
  text: {
    fontFamily: 'Lato',
    fontSize: 12,
    fontWeight: 'bold',
    letterSpacing: 0.23,
    color: '#008cff',
  },
  disable: {
    fontFamily: 'Lato-Bold',
    color: '#b7b7b7',
  },
  calSelection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  selectedDate: {
    fontSize: 18,
    color: '#008cff',
    fontFamily: 'Lato-Bold',

  },
  selectedDay: {
    fontSize: 18,
    color: '#000',
    fontFamily: 'Lato-Regular',
  },
});

export default DateFilter;
