import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FlatList,
  SectionList, Text,
} from 'react-native';
import { cloneDeep, isEmpty, isEqual } from 'lodash';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import styles from './style';
import Header from './header';
import ListItemSeparator from './ListItemSeparator';
import Footer from './Footer';
import {
  FUNNEL_PAGE_NAMES,
  REQUEST_LOB,
  REQUEST_WEBSITE,
  SUB_PAGE_NAMES,
} from '../../../HolidayConstants';
import {
  getPlatformIdentifier,
} from '../../../utils/HolidayUtils';
import HolidayGroupingNoFilter from '../../../Grouping/Filters/HolidayGroupingNoFilter';
import FilterLoader from '../FilterLoader';
import {
  DEFAULT_PAGE_NAME,
  G<PERSON><PERSON>_PAGE_NAME,
  HARDWARE_BACK_PRESS,
  RESET_TEXT, ERROR_MSG, FILTER_URL_NAME_CONSTANTS, LANDING_PAGE_NAME,
} from '../../SearchWidgetConstants';
import {
  KEY_USER_DEP_CITY,
  setDataInStorage,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import { createMasterFilterList, FILTER_URL_PARAM_NAME, FILTER_UI_TYPES, canShowRangeFilter, isTrueFalseFilter } from './Utils';

import AppliedFiltersList from './AppliedFiltersList';
import SortingOptions from './SortingOptions';
import withFilterOptions from './withFilterOptions';
import FilterTitle from './FilterTitle';
import {getNameAndSorterTextById} from "../../utils/SearchWidgetUtil";
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';
import { element } from 'prop-types';
import HolidayDataHolder from '../../../../src/utils/HolidayDataHolder';
import withBackHandler from '../../../../src/hooks/withBackHandler'
 class PhoenixSearchPage extends BasePage {
  constructor(props) {
    super(props, 'search_widget');
    const { pageName, quickFilter, sorterCriterias, visibleOn, isBottomSheet = false } = props;
    this.pageNameForFilters = pageName ? GROUP_PAGE_NAME : DEFAULT_PAGE_NAME;
    this.visibilityMode = !isEmpty(visibleOn)
      ? visibleOn
      : quickFilter
      ? 'QUICK_FILTER'
      : this.pageNameForFilters;
    const criterias = cloneDeep(props.criterias);
    const dateObj = cloneDeep(props.dateObj);
    const packageIds = cloneDeep(props.packageIds);
    this.showDepDestPopUp = cloneDeep(props.showGoingTo);
    this.isWG = !!props.isWG;
    this.isLanding = !!props.isLanding;
    this.holidayLandingGroupDto = cloneDeep(props.holidayLandingGroupDto);
    this.isBottomSheet = isBottomSheet;
    this.request = {
      lob: REQUEST_LOB,
      destinationCity: cloneDeep(props.destinationCity),
      channel: getPlatformIdentifier(),
      criterias,
      website: REQUEST_WEBSITE,
      filterSorterParam: true,
      userDepCity: this.props.userDepCity,
      ...(!isEmpty(props.requestParams) && { ...(props?.requestParams || {}) }),
    };

    this.groupedData = {};

    // taken care of variables need to pass in metadata (request) and pass without any change in group call (groupdata).
    if (this.props.campaign) {
      this.request.campaign = cloneDeep(props.campaign);
      this.groupedData.campaign = cloneDeep(props.campaign);
    }

    if (this.holidayLandingGroupDto?.packageIds) {
      this.groupedData.packageIds = this.holidayLandingGroupDto.packageIds;
      this.request.packageIds = this.holidayLandingGroupDto.packageIds;
    } else if (packageIds) {
      this.groupedData.packageIds = packageIds;
      this.request.packageIds = packageIds;
    }

    if (this.holidayLandingGroupDto?.rooms) {
      this.groupedData.rooms = this.holidayLandingGroupDto.rooms;
    }

    if (this.holidayLandingGroupDto?.destinationCityData) {
      this.groupedData.destinationCityData = this.holidayLandingGroupDto.destinationCityData;
    }

    if (dateObj) {
      const { fromDate, toDate, packageDate } = dateObj;
      this.request.fromDate = fromDate;
      this.request.toDate = toDate;
      this.request.packageDate = packageDate;
    }
    if (packageIds) {
      this.request.packageIds = packageIds.split(',');
    }
    this.lastFilter = {};
    this.cmpChannel = props.cmpChannel;
    const metaResponse = this.getMetaResponseForFilters();
    const { headerDetail } = metaResponse || {};
    const { masterFilterList, filtersIdMap, sectionData, selectedSection, masterFilterIdMap } = createMasterFilterList(
      metaResponse,
      this.pageNameForFilters,
      quickFilter,
      visibleOn,
      this.isLanding,
      this.isBottomSheet,
    );
    this.state = {
      showCalendar: false,
      masterFilterList,
      filtersIdMap,
      masterFilterIdMap,
      sectionData,
      selectedSection,
      sorterCriterias: sorterCriterias?.length > 0 ? sorterCriterias : [],
      packagesCount: headerDetail?.packagesCount || 0,
      totalPackageCount: headerDetail?.totalPackageCount || 0,
    };
  }
  getDestinationCity = () => {
    return this.request.destinationCity;
  };
  getMetaResponseForFilters = () => {
    const { masterMetaResponse, searchWidgetData } = this.props;
    return isEmpty(this.request.criterias) || this.isLanding
      ? masterMetaResponse
      : searchWidgetData
      ? searchWidgetData
      : masterMetaResponse;
  };

  componentDidMount() {
    HolidayDataHolder.getInstance().setSubPageName(SUB_PAGE_NAMES.FILTER)
    BackHandler.addEventListener(HARDWARE_BACK_PRESS, this.handleBackPress);
    if (!this.isLanding) {
      if (isEmpty(this.props.searchWidgetData)) {
        this.props.fetchSearchWidgetData(this.getSearchWidgetDataObj());
      } else {
        this.props.loadPhoenixSearchPageData();
        if (this.props.searchWidgetRefreshRequired) {
          this.props.setSearchWidgetRefreshRequired(false);
          this.props.fetchSearchWidgetData(this.getSearchWidgetDataObj());
        }
      }
    }
    //for scroll to specific location
    // const wait = new Promise(resolve => setTimeout(resolve, 200));
    // wait.then(() => {
    //   if (this.refFL) {
    //     this.refFL.scrollToLocation({
    //       animated: true,
    //       itemIndex: this.state.selectedSection.index + 1,
    //       sectionIndex: this.state.selectedSection.section,
    //       viewPosition: 0
    //     });
    //   }
    // });
  }

  componentWillUnmount() {
    HolidayDataHolder.getInstance().clearSubPageName()
  }

  componentDidUpdate(prevProps) {
    if (!isEqual(prevProps.searchWidgetData, this.props.searchWidgetData)) {
      const { searchWidgetData, quickFilter } = this.props;
      const { headerDetail } = searchWidgetData || {};
      this.setState({
        ...createMasterFilterList(
          searchWidgetData,
          this.pageNameForFilters,
          quickFilter,
          this.props.visibleOn,
          this.isLanding,
          this.isBottomSheet,
        ),
        packagesCount: headerDetail?.packagesCount || 0,
        totalPackageCount: headerDetail?.totalPackageCount || 0,
      });
    }
  }

  render() {
    return (
      <View style={this.isBottomSheet ? {} : AtomicCss.flex1}>
        {this.props.isError && this.renderError()}
        {(this.props.isSuccess || this.isLanding) && this.renderContent()}
      </View>
    );
  }

  renderError = () => {
    showShortToast(ERROR_MSG);
    this.handleBackPress();
    return null;
  };

  renderFilterRowItem = ({ item, index }) => {
    const showTitleText = !(this.props.isBottomSheet && this.props.quickFilter); // do not show title for quick filters in botto sheet
    switch (item.type) {
      case FILTER_UI_TYPES.RANGE: {
        const RangeFilter = canShowRangeFilter(item)
          ? require('./RangeFilter')
          : require('./RangeFilterWithoutSlider');
        const GenericFilter = withFilterOptions(require('./GenericFilter').default);
        return (
          <View>
            <RangeFilter.default
              data={item}
              criterias={cloneDeep(this.request.criterias)}
              onChange={this.onSelectionChange}
              showTitleText={showTitleText}
            />
            <GenericFilter
              filter={item}
              criterias={this.request.criterias}
              onChange={this.onSelectionChange}
              isRange={true}
              isSingleSelect={true}
              isLanding={this.isLanding}
              showTitleText={showTitleText}
            />
          </View>
        );
      }
      case FILTER_UI_TYPES.TABS_SINGLE_SELECT:
      case FILTER_UI_TYPES.TABS_MULTISELECT:
        const TabsFilter = withFilterOptions(require('./TabsFilter').default);
        return (
          <TabsFilter
            filter={item}
            criterias={this.request.criterias}
            onChange={this.onSelectionChange}
            isSingleSelect={item.type === FILTER_UI_TYPES.TABS_SINGLE_SELECT}
            isLanding={this.isLanding}
            showTitleText={showTitleText}
          />
        );

      case FILTER_UI_TYPES.ICON_SINGLESELECT:
      case FILTER_UI_TYPES.ICON_MULTISELECT:
        const IconFilters = withFilterOptions(require('./IconFilters').default);
        return (
          <IconFilters
            filter={item}
            criterias={this.request.criterias}
            onChange={this.onSelectionChange}
            isSingleSelect={item.type === FILTER_UI_TYPES.ICON_SINGLESELECT}
            showTitleText={showTitleText}
          />
        );
      case FILTER_UI_TYPES.GENERIC_SINGLE_SELECT:
      case FILTER_UI_TYPES.GENERIC_MULTISELECT:
        const GenericFilter = withFilterOptions(require('./GenericFilter').default);
        return (
          <GenericFilter
            filter={item}
            criterias={this.request.criterias}
            onChange={this.onSelectionChange}
            isSingleSelect={item.type === FILTER_UI_TYPES.GENERIC_SINGLE_SELECT}
            isLanding={this.isLanding}
            showTitleText={showTitleText}
          />
        );
      case FILTER_UI_TYPES.DOUBLE_TAB_SINGLESELECT:
        const DoubleTabFilter = withFilterOptions(require('./DoubleTabFilter').default);
        return (
          <DoubleTabFilter
            filter={item}
            criterias={this.request.criterias}
            onChange={this.onSelectionChange}
            isSingleSelect
            isLanding={this.isLanding}
            showTitleText={showTitleText}
          />
        );
      case FILTER_UI_TYPES.CHECKBOX:
        const CheckboxFilter = withFilterOptions(require('./CheckboxFilter').default);
        return (
          <CheckboxFilter
            filter={item}
            criterias={this.request.criterias}
            onChange={this.onSelectionChange}
            isSingleSelect
            isLanding={this.isLanding}
            showTitleText={showTitleText}
          />
        );
      case FILTER_UI_TYPES.TOGGLE:
        const ToggleFilter = withFilterOptions(require('./ToggleFilter').default);
        return (
          <ToggleFilter
            filter={item}
            criterias={this.request.criterias}
            onChange={this.onSelectionChange}
            isSingleSelect
            isLanding={this.isLanding}
            showTitleText={showTitleText}
          />
        );
      case FILTER_URL_PARAM_NAME.sorter:
        return (
          <SortingOptions
            options={item.options}
            sorterCriterias={this.state.sorterCriterias}
            onChange={this.onSortingOptionChanged}
            showTitleText={showTitleText}
          />
        );
    }
  };
  trackEventWithPDTV3 = ({ eventName, actionType, value }) => {
    if (this.props.trackClickEvent) {
      this.props.trackClickEvent({ eventName });
    }
    if (this.props.trackPDTV3Event) {
      this.props.trackPDTV3Event({ actionType, value });
    }
  };
  trackEvent = (eventName) => {
    if (this.props.trackClickEvent) {
      this.props.trackClickEvent({ eventName });
    }
  };
  trackFilterAddEvent = ({ filterId, filterValue, isRecommendedValue = false } = {}) => {
    // track event
    const { filtersIdMap } = this.state;
    const { quickFilter } = this.props;
    const filter = filtersIdMap[filterId];
    if (!filter) {
      return;
    }
    const filterSuffix = `${filter.urlParam}${isRecommendedValue ? '_rec' : ''}`;
    let eventName = `filter_apply_${this.visibilityMode}_${filterSuffix}`;
    let eventValue = `${quickFilter ? 'quick_filter' : 'filter'}_select|${filterSuffix}`;
    if (!isTrueFalseFilter(filter.type)) {
      eventName = `${eventName}_${filterValue}`;
      eventValue = `${eventValue}_${filterValue}`;
    }
    const actionType = isRecommendedValue
      ? PDT_EVENT_TYPES.valueSelected
      : PDT_EVENT_TYPES.filterApplied;
    this.trackEventWithPDTV3({ eventName, actionType, value: eventValue });
  };
  trackFilterRemoveEvent = ({
    filterId,
    filterValue,
    isRecommendedValue = false,
    isFilterRemoveFromTop = false,
  }) => {
    // track event
    const { filtersIdMap } = this.state;
    const { quickFilter } = this.props;
    const filter = filtersIdMap[filterId];
    if (!filter) {
      return;
    }
    const filterSuffix = `${filter.urlParam}${isRecommendedValue ? '_rec' : ''}`;
    let eventName = `filter_remove_${this.visibilityMode}_${filterSuffix}`;
    let eventValue = `${quickFilter ? 'quick_filter' : 'filter'}_${
      isFilterRemoveFromTop ? 'top_' : ''
    }remove|${filterSuffix}`;

    if (!isTrueFalseFilter(filter.type)) {
      eventName = `${eventName}_${filterValue}`;
      eventValue = `${eventValue}_${filterValue}`;
    }
    this.trackEventWithPDTV3({
      eventName,
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventValue,
    });
  };
  onSelectionChange = ({
    filterId,
    filterUrlParam,
    filterValue,
    isActive,
    isSingleSelect,
    isRecommendedValue = false,
  } = {}) => {
    if (!isActive) {
      this.removeFromRequest({ filterId, filterValue, isRecommendedValue });
    } else {
      this.addToRequest({
        id: filterId,
        value: filterValue,
        replace: isSingleSelect,
        isRecommendedValue,
        filterUrlParam,
      });
    }
  };

  onSortingOptionChanged = (id, sorterName, value, isActive) => {
    // always one type of sorting allowed
    const sorterCriterias = [];
    if (isActive) {
      sorterCriterias.push({
        id,
        values: [value.toString()],
      });
    }
    this.props.setSearchWidgetRefreshRequired(true);

    const eventName = `sorter_${isActive ? 'apply' : 'remove'}_${
      this.visibilityMode
    }_${sorterName}_${value}`;
    const actionType = isActive ? PDT_EVENT_TYPES.filterApplied : PDT_EVENT_TYPES.buttonClicked;
    const eventValue = `${this.props.quickFilter ? 'quick_filter' : 'filter'}_${
      isActive ? 'select' : 'remove'
    }|sorter_${sorterName}_${value}`;
    this.trackEventWithPDTV3({ eventName, actionType, value: eventValue });
    this.setState({ sorterCriterias });
  };
  onRemoveFilter = (filterId, filterValue) => {
    const isFilterRemoveFromTop = true;
    this.removeFromRequest({ filterId, filterValue, isFilterRemoveFromTop });
  };

  renderContent = () => {
    const { packagesCount, totalPackageCount } = this.state;
    const {
      quickFilter,
      bottomsheetHeader,
      isBottomSheet = false,
      isFilterQuickView = false,
    } = this.props || {};
    const { header, subHeader } = bottomsheetHeader || {};
    const titleText = isBottomSheet ? header : quickFilter ? quickFilter.name : 'Filters';
    return (
      <View
        key="MASTER_SEARCH_WIDGET_VIEW"
        style={[
          isBottomSheet ? styles.containerForGuidedSearchBottomSheet : styles.container,
          isFilterQuickView && styles.containerForBottomSheet,
        ]}
      >
        {/* {this.props.isLoading && (
          <FilterLoader loadingFirstTime={this.loadingFirstTime} isBottomsheet={isBottomSheet} />
        )} */}
        {packagesCount > 0 && this.props.isSuccess && (
          <View style={isBottomSheet && isFilterQuickView ? [] : [AtomicCss.flex1]}>
            <Header
              titleText={titleText}
              subTitle={isBottomSheet ? subHeader : null}
              isBottomSheet={isBottomSheet}
              quickFilter={quickFilter}
              resetText={'Clear All'}
              handleReset={this.onReset}
              handleClose={this.onClose}
              showResetInBottomSheet={isBottomSheet && isFilterQuickView}
              containerStyle={
                isBottomSheet
                  ? [styles.headerContainerGuidedSearch]
                  : [styles.headerContainer, styles.headerContainerStatusBarDimensions]
              }
            />
            <SectionList
              ref={(ref) => {
                if (ref) {
                  this.refFL = ref;
                }
              }}
              style={[
                styles.filterFlatList,
                isBottomSheet ? (isFilterQuickView ? paddingStyles.pb20 : marginStyles.mb30) : {},
              ]}
              sections={this.state.sectionData}
              keyExtractor={(item) => `${item.id}_${item.name}`}
              renderItem={this.renderFilterRowItem}
              ListHeaderComponent={
                !quickFilter && (
                  <AppliedFiltersList
                    criterias={this.request.criterias}
                    filtersIdMap={this.state.filtersIdMap}
                    masterFilterIdMap={this.state.masterFilterIdMap}
                    onRemove={this.onRemoveFilter}
                  />
                )
              }
              renderSectionHeader={({ section: { title } }) => (
                <View style={{ backgroundColor: 'white' }}>
                  {!!title && (
                    <View style={styles.headingContainer}>
                      <FilterTitle title={title} />
                    </View>
                  )}
                  {!!title && <ListItemSeparator />}
                </View>
              )}
              ItemSeparatorComponent={() => <ListItemSeparator />}
              showsVerticalScrollIndicator={false}
              onScrollToIndexFailed={(info) => {
                // const wait = new Promise(resolve => setTimeout(resolve, 200));
                // wait.then(() => {
                //   if (this.refFL) {
                //     this.refFL.scrollToLocation({
                //       animated: true,
                //       itemIndex: this.state.selectedSection.index + 1,
                //       sectionIndex: this.state.selectedSection.section,
                //       viewPosition: 0
                //     });
                //   }
                // });
              }}
            />
            <View style={{ position: 'relative' }}>
              
                <FilterLoader
                  loadingFirstTime={this.loadingFirstTime}
                  isBottomsheet={isBottomSheet}
                  show={this.props.isLoading}
                />
         
            </View>
            <Footer
              packageCount={packagesCount}
              totalPackageCount={totalPackageCount}
              handleDone={() => this.onDoneClicked(titleText)}
              isCountDisplay={!this.isLanding}
              isLoading={this.props.isLoading}
            />
          </View>
        )}
        {packagesCount === 0 && this.props.isSuccess && !this.props.isLoading && (
          <HolidayGroupingNoFilter
            removeLastFilter={this.removeLastFilter}
            remark={'REMOVE THE LAST FILTER'}
            filterErrorHeading={'No Packages found'}
            trackEvent={this.trackEvent}
          />
        )}
      </View>
    );
  };

  handleBackPress = () => {
    this.props.onSWClose();
    return true;
  };

  onReset = () => {
    const { quickFilter } = this.props;
    const EVENT_FILTER_REMOVE_ALL = 'filter_remove_all';
    const EVENT_FILTER_REMOVE_ALL_QUICK = 'filter_remove_all_quick';
    const SORT_VALUE_SORT = 'Sort';
    const isQuickFilter = quickFilter && quickFilter?.name !== SORT_VALUE_SORT;
    this.trackEventWithPDTV3({
      eventName: quickFilter ? EVENT_FILTER_REMOVE_ALL_QUICK : EVENT_FILTER_REMOVE_ALL,
      actionType: PDT_EVENT_TYPES.filterAllCleard,
      value: quickFilter ? `quick_filter_remove_all|${quickFilter?.urlParam}` : 'filter_remove_all',
    });
    this.props.setSearchWidgetRefreshRequired(true);
    if (isQuickFilter) {
      // clear on quick filter
      this.removeFromRequest({
        filterId: quickFilter.id,
        filterValue: undefined,
        trackEvent: false,
      });
    } else {
      this.handleNonQuickFilterReset();
    }
  };

  handleNonQuickFilterReset() {
    this.setState(
      {
        sorterCriterias: [],
      },
      () => {
        this.request = this.getResetRequest();
        if (!this.isLanding) {
          this.props.fetchSearchWidgetData(this.getSearchWidgetDataMasterObj());
        } else {
          this.setState({
            updateTime: Date.now(),
          });
        }
      },
    );
  }

  onClose = () => {
    if (this.props.userDepCity) {
      setDataInStorage(KEY_USER_DEP_CITY, this.props.userDepCity);
    }
    const value = `${
      this.props?.quickFilter
        ? 'quick_filter_close|' + `${this.props?.quickFilter?.urlParam}`
        : 'filter_close'
    }`;
    this.trackEventWithPDTV3({
      eventName: 'filter_close',
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value,
    });
    this.props.onSWClose();
  };

  showToast = () => {
    if (this.request.criterias) {
      let noOfFilters = 0;
      for (let criteria = 0; criteria < this.request.criterias.length; criteria += 1) {
        if (
          this.request.criterias[criteria].id !==
          this.filterIdMap.get(FILTER_URL_NAME_CONSTANTS.PLACES_FILTER_URL_NAME)
        ) {
          noOfFilters += 1;
        }
      }
      if (noOfFilters > 0) {
        showShortToast(RESET_TEXT);
      }
    }
  };

  getResetRequest = () => {
    return {
      ...this.request,
      lob: REQUEST_LOB,
      destinationCity: cloneDeep(this.props.destinationCity),
      channel: getPlatformIdentifier(),
      website: REQUEST_WEBSITE,
      filterSorterParam: true,
      criterias: [],
    };
  };

  onDoneClicked = (type) => {
    this.onDone(type);
    this.props.trackViewedSectionClickEvent?.();
  };

  getAppliedValueForSorter = () => {
    const { sorterCriterias = [] } = this.state || {};
    const [sortData] = sorterCriterias;
    const { id } = sortData || {};
    const metaResponse = this.getMetaResponseForFilters();
    const { listingSorters } = metaResponse || {};
    const { name, sorterText } = getNameAndSorterTextById(listingSorters, id) || {};

    let appliedValueForOmniture;
    if (name && sorterText) {
      appliedValueForOmniture = `Sort_Applied_${name}_${sorterText}_${id}`;
    } else {
      appliedValueForOmniture = '';
    }

    return appliedValueForOmniture.replace(/ /g, '_');
  };

  onDone = async (type) => {
    this.props.setSearchWidgetRefreshRequired(false);
    const { criterias } = this.request || {};
    const { quickFilter } = this.props || {};
    const appliedValueForOmniture =
      type === 'Sort' ? this.getAppliedValueForSorter() : this.getFilterApplyOmnitureValue();
    const appliedFilters = criterias?.map((item) => this.getFilterNameFromMap(item.id));
    const pdtExtraData = { filter_selected: appliedFilters?.toString() };
    const eventName = quickFilter
      ? 'quick_filter_apply|'
      : this.props?.isBottomSheet
      ? 'filter_apply_guided|'
      : 'filter_apply|';
    this.props.trackClickEvent({
      eventName,
      prop1: appliedValueForOmniture,
      pdtExtraData,
    });
    const eventValue = `${eventName}${
      quickFilter
        ? type === 'Sort'
          ? this.getAppliedValueForSorter()
          : this.getFilterApplyOmnitureValue()
        : this.getFilterApplyForPDTV3()
    }`;
    this.props?.trackPDTV3Event?.({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventValue,
    });

    const aff = this.props.aff;
    this.props.onSWDone({
      holidayGroupingNewDto: this.getGroupingPageData(aff),
      backFromSW: true,
      newMetaResponse: this.props.searchWidgetData,
    });
  };

  getGroupingPageData(aff) {
    return {
      fromDate: this.request.fromDate,
      toDate: this.request.toDate,
      packageDate: this.request.packageDate,
      destinationCity: this.getDestinationCity(),
      aff,
      filters: this.request.criterias,
      ...this.groupedData,
      sorterCriterias: this.state.sorterCriterias,
    };
  }

  getSearchWidgetDataObj() {
    return {
      popularlyPairedWithOptions: this.popularlyPairedWithOptions,
      updatePopularlyPairedWithOptions: this.updatePopularlyPairedWithOptions,
      travellingMonthsOptions: this.travellingMonthsOptions,
      availableHubs: this.availableHubs,
      masterMap: this.masterMap,
      pageName: this.pageNameForFilters,
      pageNamePDT: this.props.pageName,
      destinationCity: this.getDestinationCity(),
      destinations: this.destinations,
      criterias: this.request.criterias,
      request: this.request,
      holidaySearchWidgetDataOld: this.props.searchWidgetData,
      filterIdMap: this.filterIdMap,
      cmpChannel: this.cmpChannel,
      budgetFilterStats: this.budgetFilterStats,
      isWG: this.isWG,
      isLanding: this.props.isLanding,
    };
  }

  getSearchWidgetDataMasterObj(applyFiltersDirectly = false) {
    const dateObj = {
      fromDate: this.request.fromDate,
      toDate: this.request.toDate,
      packageDate: this.request.packageDate,
    };
    return {
      availableHubs: this.props.availableHubs,
      request: this.request,
      pageName: this.pageNameForFilters,
      pageNamePDT: this.props.pageName,
      destinations: this.destinations,
      criterias: this.request.criterias,
      destinationCity: this.getDestinationCity(),
      dateObj,
      showGoingTo: this.showDepDestPopUp,
      cmpChannel: this.cmpChannel,
      applyFiltersDirectly,
      isWG: this.isWG,
    };
  }

  removeFromRequest({
    filterId,
    filterValue,
    trackEvent = true,
    isRecommendedValue = false,
    isFilterRemoveFromTop = false,
  } = {}) {
    if (this.request.criterias) {
      for (
        let criteriaIndex = 0;
        criteriaIndex < this.request.criterias.length;
        criteriaIndex += 1
      ) {
        const filter = this.request.criterias[criteriaIndex];
        if (filter.id === filterId) {
          if (filterValue && filter.values.length > 0) {
            // remove the filter value if its there
            const valueIndex = filter.values.indexOf(filterValue);
            if (valueIndex >= 0) {
              filter.values.splice(valueIndex, 1);
            }
            // remove entire filter if there is no values
            if (filter.values.length === 0) {
              this.request.criterias.splice(criteriaIndex, 1);
            }
          } else {
            this.request.criterias.splice(criteriaIndex, 1);
          }
          break;
        }
      }
    }
    if (!this.isLanding) {
      this.props.setSearchWidgetRefreshRequired(true);
      this.props.fetchSearchWidgetData(this.getSearchWidgetDataObj());
    } else {
      this.setState({ updateTime: Date.now() });
    }
    if (trackEvent) {
      this.trackFilterRemoveEvent({
        filterId,
        filterValue,
        isRecommendedValue,
        isFilterRemoveFromTop,
      });
    }
  }

  addToRequest({ id, value, replace = false, isRecommendedValue = false, filterUrlParam } = {}) {
    if (!this.request.criterias) {
      const criterias = [];
      this.lastFilter = this.getLastFilter(id, value);
      criterias.push({
        id,
        values: [value],
        urlParam: filterUrlParam,
      });
      this.request.criterias = criterias;
    } else {
      let found = false;
      for (
        let criteriaIndex = 0;
        criteriaIndex < this.request.criterias.length;
        criteriaIndex += 1
      ) {
        if (this.request.criterias[criteriaIndex].id === id) {
          found = true;
          if (replace) {
            this.lastFilter = this.getLastFilter(id, value);
            this.request.criterias[criteriaIndex].values = [value];
          } else if (this.request.criterias[criteriaIndex].values.indexOf(value) === -1) {
            this.lastFilter = this.getLastFilter(id, value);
            this.request.criterias[criteriaIndex].values.push(value);
          } else {
            this.request.criterias[criteriaIndex].values.splice(
              this.request.criterias[criteriaIndex].values.indexOf(value),
              1,
            );
            if (this.request.criterias[criteriaIndex].values.length === 0) {
              this.request.criterias.splice(criteriaIndex, 1);
            }
          }
        }
      }
      if (!found) {
        this.lastFilter = this.getLastFilter(id, value);
        this.request.criterias.push({
          id,
          values: [value],
          urlParam: filterUrlParam,
        });
      }
    }
    if (!this.isLanding) {
      this.props.setSearchWidgetRefreshRequired(true);
      this.props.fetchSearchWidgetData(this.getSearchWidgetDataObj());
    } else {
      this.setState({ updateTime: Date.now() });
    }
    this.trackFilterAddEvent({ filterId: id, filterValue: value, isRecommendedValue });
  }

  getFilterNameFromMap = (id) => {
    const { filtersIdMap } = this.state || {};
    if (id === undefined) {
      return null;
    }
    for (let key in filtersIdMap) {
      if (Number(key) === id) {
        return filtersIdMap[key].name;
      }
    }
    return null;
  };

  updateMasterMap = (id) => {
    for (const [key] of this.masterMap) {
      const mapObj = this.masterMap.get(key);
      if (mapObj.id === id) {
        mapObj.updateFlag = true;
      }
    }
  };

  getLastFilter = (id, value) => ({
    filterId: id,
    uniquefilterVal: value,
  });

  removeLastFilter = () => {
    if (!isEmpty(this.lastFilter)) {
      this.removeFromRequest({
        filterId: this.lastFilter.filterId,
        filterValue: this.lastFilter.uniquefilterVal,
      });
    }
  };
  getFilterApplyOmnitureValue = () => {
    const { filtersIdMap } = this.state;
    let value = '';
    this.request?.criterias?.forEach((criteria, index) => {
      const filter = filtersIdMap[criteria.id];
      if (filter) {
        value +=
          `${filter.urlParam}_${criteria.values.toString()}` +
          (index < this.request.criterias.length - 1 ? '|' : '');
      }
    });
    return value;
  };
  getFilterApplyForPDTV3 = () => {
    let { masterFilterList } = this.state;
    let value = '';
    masterFilterList.forEach((item, index) => {
      if (item.id === -1) {
       value += isEmpty(this.getAppliedValueForSorter()) ? 'Sort_Applied_NA' : this.getAppliedValueForSorter();
      } else {
        const criteria = this.request?.criterias.find(
          (criterion) => criterion.id === parseInt(item.id, 10),
        );
        if (criteria) {
          value += `${item.urlParam}_${criteria.values.toString()}`;
        } else {
          value += `${item.urlParam}_NA`;
        }
      }
      value += index < masterFilterList.length - 1 ? '|' : '';
    });

    return value;
  };
}
export default withBackHandler(PhoenixSearchPage);



