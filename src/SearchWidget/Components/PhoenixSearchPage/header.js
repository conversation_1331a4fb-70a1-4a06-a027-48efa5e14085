import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import PropTypes from 'prop-types';
import { holidayColors } from '../../../Styles/holidayColors';
import { PageHeaderBackButton, PageHeaderTitle } from '../../../Common/Components/PageHeader';
import CrossIcon from '@mmt/legacy-assets/src/holidays/iconCross.webp';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';

const FilterHeader = ({
  titleText,
  resetText,
  handleReset,
  handleClose,
  subTitle,
  isBottomSheet,
  containerStyles,
  showResetInBottomSheet = false,
}) => {
  return isBottomSheet ? (
    <View style={[styles.headerBottomSheet, showResetInBottomSheet ? paddingStyles.ph10 : {}]}>
      <PageHeaderBackButton iconSource={CrossIcon} onBackPressed={handleClose} />
      <View style={styles.titleWrapper}>
        <PageHeaderTitle title={titleText} />
        {!!subTitle && <Text style={styles.subTitle}>{subTitle}</Text>}
      </View>
      {showResetInBottomSheet && (
        <View style={styles.btnWrapper}>
          <AnchorBtn label={resetText} handleClick={handleReset} />
        </View>
      )}
    </View>
  ) : (
    <View style={[styles.header]}>
      <PageHeaderBackButton iconSource={CrossIcon} onBackPressed={handleClose} />
      <PageHeaderTitle title={titleText} />
      <View style={styles.btnWrapper}>
        <AnchorBtn label={resetText} handleClick={handleReset} />
      </View>
    </View>
  );
};

FilterHeader.propTypes = {
  titleText: PropTypes.string.isRequired,
  resetText: PropTypes.string.isRequired,
  handleClose: PropTypes.func.isRequired,
  handleReset: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: holidayColors.white,
    ...paddingStyles.pa16,
    shadowColor: '#330000',
    position: 'relative',
    ...marginStyles.mb2,
  },
  headerBottomSheet: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: holidayColors.white,
    paddingHorizontal: 0,...paddingStyles.pa16,
    borderBottomColor: holidayColors.grayBorder,
    borderBottomWidth: 1,
    position: 'relative',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  titleWrapper: {
    display: 'flex',
    flexDirection: 'column',
    paddingRight: 50,
  },
  btnWrapper: {
    marginLeft: 'auto',
  },
  subTitle: { fontSize: 14, fontFamily: 'Lato', color: holidayColors.gray },
});

export default FilterHeader;
