import { isEmpty, toInteger } from 'lodash';
import {rupeeFormatter, rupeeFormatterWithLocale} from '@mmt/legacy-commons/Helpers/currencyUtils';
import {marginStyles} from "../../../Styles/Spacing";
import {holidayColors} from "../../../Styles/holidayColors";
export const SORTER_POSITION_IN_FILTERS = 6;
export const FILTER_IGNORED = {
    destinations: 'destinations',
    // places: "places"
};

export const QUICK_FILTER_TYPES = {
  ALL_FILTER: 'ALL_FILTER',
};

export const FILTER_URL_PARAM_NAME = {
    budget: 'budget',
    duration: 'duration',
    hotelChoice: 'hotelChoice',
    majorTransport: 'majorTransport',
    themes: 'themes',
    suitableFor: 'suitableFor',
    places: 'places',
    packageType: 'packageType',
    holidayType: 'holidayType',
    inTripTransport: 'inTripTransport',
    thingsTodo: 'thingsTodo',
    visa: 'visa',
    onlyWithFlight: 'onlyWithFlight',
    destinations: 'destinations',
    generalTags: 'generalTags',
    packageInclusion: 'packageInclusion',
    covidSafe: 'covidSafe',
    premiumPackage: 'premiumPackage',
    sorter: 'sorter',
};
export const FILTER_UI_TYPES = {
    RANGE: 'range',
    GENERIC_MULTISELECT: 'multiSelect',
    GENERIC_SINGLE_SELECT: 'singleSelect',
    TABS_MULTISELECT: 'Tabs_Multiselect',
    TABS_SINGLE_SELECT: 'Tabs_Singleselect',
    DOUBLE_TAB_MULTISELECT: 'Double_Tab_Multiselect',
    DOUBLE_TAB_SINGLESELECT: 'Double_Tab_Singleselect',
    ICON_MULTISELECT: 'Icon_Multiselect',
    ICON_SINGLESELECT: 'Icon_Singleselect',
    CHECKBOX: 'Checkbox',
    TOGGLE: 'Toggle',
};
export const isTrueFalseFilter = uiType => uiType === FILTER_UI_TYPES.CHECKBOX || uiType === FILTER_UI_TYPES.TOGGLE;
/**
 * Creates master filters to be displayed
 * @param {*} metaResponse packages/meta api response
 * @param {*} page page name for which we need to create master filters to be displayed
 * @returns {*{masterFilterList=[], filtersIdMap={id, filter}}
 */
export const createMasterFilterList = (metaResponse, page, quickFilter, visibleOn, isLanding = false, isBottomSheet = false) => {
    if (isEmpty(metaResponse)) {
        return {};
    }
    const sorter = (/*!quickFilter && */!isLanding && !isBottomSheet ) && getSorterItem(metaResponse.listingSorters);
    const list = (isLanding || isBottomSheet || (quickFilter?.name !== 'Sort' && quickFilter !== undefined)) ? [] : [sorter];
    const { listingFilters, filterGroupMap = {}} = metaResponse;
    const filtersIdMap = {};
    const masterFilterIdMap = {};
    let masterFilterList = [];
    let sectionData = [];
    if (quickFilter) {
        // check if quick filter applied then show only quick filter
        if (!isEmpty(visibleOn)){
            for (let index = 0; index < listingFilters.length; index++) {
                const item = listingFilters[index];
                if (!item.hidden && !isEmpty(item.type) && item.visibleOn?.includes(visibleOn) && !FILTER_IGNORED[item.urlParam] && quickFilter.urlParam === item.urlParam) {
                    list.push(item);
                    filtersIdMap[item.id] = item;
                    break;
                }
                masterFilterIdMap[item.id] = item;
            }
        } else if (quickFilter.name === 'Sort') {
            list.push(getSorterItem(metaResponse?.listingSorters));
        }
        else {
            for (let index = 0; index < listingFilters.length; index++) {
                const item = listingFilters[index];
                if (!item.hidden && !isEmpty(item.type) && item.visibleOn?.includes(page) && !FILTER_IGNORED[item.urlParam] && quickFilter.urlParam === item.urlParam) {
                    list.push(item);
                    filtersIdMap[item.id] = item;
                    break;
                }
                masterFilterIdMap[item.id] = item;
            }
        }
        masterFilterList = list;
    }
    else if (!isEmpty(visibleOn)){
        listingFilters?.forEach((item, index) => {
            if (!item.hidden && !isEmpty(item.type) && item.visibleOn?.includes(visibleOn) && !FILTER_IGNORED[item.urlParam]) {
                list.push(item);
                filtersIdMap[item.id] = item;
            }
            masterFilterIdMap[item.id] = item;
        });
        masterFilterList = list.sort((f1, f2) => f1.priorityOrder - f2.priorityOrder);
    }
    else {
        listingFilters?.forEach((item, index) => {
            if (!item.hidden && !isEmpty(item.type) && item.visibleOn?.includes(page) && !FILTER_IGNORED[item.urlParam]) {
                list.push(item);
                filtersIdMap[item.id] = item;
            }
            masterFilterIdMap[item.id] = item;
        });
        masterFilterList = list.sort((f1, f2) => f1.priorityOrder - f2.priorityOrder);
    }
    let sectionKeys = {};
    let selectedSection = {'section' : 0, 'index' : 0};
    masterFilterList?.forEach((itemMaster, index) => {
        if (isEmpty(sectionKeys[itemMaster.groupId])) {
            sectionData.push({
                'title': filterGroupMap[itemMaster.groupId] || '',
                'data': masterFilterList.filter((item) => item.groupId === itemMaster.groupId),
            });
            sectionKeys[itemMaster.groupId] = 'true';
        }
    });
    // sectionData?.forEach((sectionfilters, section:index) => {
    //     let filters = sectionfilters?.data;
    //     filters?.forEach((filter, index) => {
    //         if(filter?.urlParam === quickFilter?.urlParam) {
    //             selectedSection = {"section" : section, "index" : index}
    //         }
    //     })
    // })
    return { masterFilterList, filtersIdMap, sectionData , selectedSection, masterFilterIdMap};
};
export const getSorterItem = (listingSorters) => {
    if (!listingSorters) {
        return null;
    }
    const options = [];
    listingSorters.forEach(sorter => {
        sorter.listingSorterValues.forEach(val => {
            options.push({
                id: sorter.id,
                name: sorter.name,
                priorityOrder: sorter.priorityOrder,
                urlParam: sorter.urlParam,
                value: val,
                isActive: false,
            });
        });
    });
    options.sort((f1, f2) => f1.priorityOrder - f2.priorityOrder);
    return {
        id: -1,
        type: FILTER_URL_PARAM_NAME.sorter,
        urlParam: FILTER_URL_PARAM_NAME.sorter,
        name: FILTER_URL_PARAM_NAME.sorter,
        priorityOrder: SORTER_POSITION_IN_FILTERS,
        options,
    };
};
export const getQuickFilterSorterItem = ({listingSorters = {}, sorterCriterias = []}) => {
    if (!listingSorters) {
        return null;
    }
    const options = [];
    listingSorters.forEach(sorter => {
        sorter.listingSorterValues.forEach(val => {
            options.push({
                id: sorter.id,
                name: sorter.name,
                priorityOrder: sorter.priorityOrder,
                urlParam: sorter.urlParam,
                value: val,
                isActive: false,
            });
        });
    });
    options.sort((f1, f2) => f1.priorityOrder - f2.priorityOrder);
    return {
        id: -1,
        type: FILTER_URL_PARAM_NAME.sorter,
        urlParam: FILTER_URL_PARAM_NAME.sorter,
        name: 'Sort',
        value: sorterCriterias.length > 0 ?  'Sort' : '',
        priorityOrder: -1,
        options,
    };
};
export const createQuickFilterList = (
    masterListingFilters,
    filters,
    filterType,
    { listingSorters, sorterCriterias = [], showNewFilterUI = false } = {},
  ) => {
    const list = [];
    masterListingFilters?.forEach((item) => {
      if (
        !item.hidden &&
        item.visibleOn?.includes(filterType ? filterType : 'QUICK_FILTER') &&
        !isEmpty(item.type)
      ) {
        const selectedFilter = filters?.find((ele) => item.id === ele.id);
        let value = formatQuickFilterText(item, selectedFilter);
        const itemTypeCondition =
          item.type === FILTER_UI_TYPES.TOGGLE ||
          item.type === FILTER_UI_TYPES.CHECKBOX ||
          item.type === FILTER_UI_TYPES.DOUBLE_TAB_SINGLESELECT;
        if (itemTypeCondition && !isEmpty(value)) {
          const selectedValue = item?.listingFilterValues?.find((ele) => value === ele.uniqueId);
          value = selectedValue.filterText;
        }
        list.push({
          id: item.id,
          urlParam: item.urlParam,
          name: item.name,
          type: item.type,
          value,
          prefix: item.urlParam === FILTER_URL_PARAM_NAME.suitableFor ? 'for' : undefined,
          priorityOrder: item.priorityOrder,
        });
      }
    });
    list.sort((f1, f2) => f1.priorityOrder - f2.priorityOrder);

    if (showNewFilterUI) {
      list.unshift({ type: QUICK_FILTER_TYPES.ALL_FILTER }); // add all filters tab at start of array
    }

    /* Add Sort Quick Filter */
    if (!isEmpty(listingSorters)) {
      const sorterItem = getQuickFilterSorterItem({ listingSorters, sorterCriterias });
      list.unshift(sorterItem);
    }
    return list;
  };


export const addRupeesSymbol = value => rupeeFormatterWithLocale(value);
export const addDaysString = value => value === 1 ? `${value} Night` : `${value} Nights`;
export const addStarSymbol = value => `${value}★`;
export const addStarString = value => `${value} Star`;

export const findFilterInCriterias = (criterias = [], filterId = -1) => {
    return criterias?.find((item) => item.id === filterId);
};

export const findValueInCriterias = (criterias = [], filterId = -1, value = '') => {
    const filter = findFilterInCriterias(criterias, filterId);
    return filter?.values?.includes(value);
};
/**
 * Marks all filter options inactive/unselect
 * @param {*} filterOptions to be unselect
 * @returns void
 */
export const markAllOptionsInActive = (filterOptions = []) => {
    filterOptions.forEach(op => {
        op.isActive = false;
    });
};
/**
 * Checks if any filter option is active or not
 * @param {*} filterOptions to be look up
 * @returns Active filter option otehrwise null
 */
export const isAnyFilterOptionActive = (filterOptions = []) => {
    filterOptions.forEach(op => {
        if (op.isActive) {
            return op;
        }
    });
    return null;
};

const getSingleRangeValue = (r1, r2) => {
    if (isEmpty(r1) && isEmpty(r2)) {
        return undefined;
    }
    if (!isEmpty(r1) && !isEmpty(r2) && r1 !== r2) {
        return undefined;
    }
    if (r1 === r2) {
        return r1;
    }
    if (r1) {
        return r1;
    }
    if (r2) {
        return r2;
    }
    return undefined;
};

const getRange = (filterValues) => {
    if (isEmpty(filterValues)) {
        return { min: 1, max: 10000 };
    }
    const budgetOptions = filterValues.split('_');
    return { min: parseInt(budgetOptions[0]), max: parseInt(budgetOptions[1]) };
};

// const getFormattedText = (value, prefix, suffix) => {
//     if (!isEmpty(prefix) && !isEmpty(suffix)) {
//         return `${prefix}${value} ${suffix}`
//     } else if (!isEmpty(prefix)) {
//         return `${prefix}${value}`
//     } else if (!isEmpty(suffix)) {
//         return `${value} ${suffix}`
//     }
//     return value
// }

const getRangeFromListingFilter = (urlParam, listingFilterValues) => {
        if (isEmpty(listingFilterValues)) {
            return { min: 1, max: 10000 };
        }
        if (urlParam === FILTER_URL_PARAM_NAME.budget) {
            const budgetOptions = listingFilterValues[0].filterText.split('_');
            return { min: parseInt(budgetOptions[0]), max: parseInt(budgetOptions[1]) };
        }
        return { min: parseInt(listingFilterValues[0].filterText), max: parseInt(listingFilterValues[listingFilterValues.length - 1].filterText) };
    };

const getMinMaxForRange = (urlParam, listingFilterValues, selectedFilterValue) => {
        if (isEmpty(listingFilterValues)) {
            return ['1', '10000'];
        }
        let values = null;
        if (selectedFilterValue) {
            const rangeValues = selectedFilterValue.split('_');
            values = { min: parseInt(rangeValues[0]), max: parseInt(rangeValues[1]) };
        }
        const range = getRangeFromListingFilter(urlParam, listingFilterValues);
        const min = values ? ((values.min < range.min) ? range.min : values.min)  : range.min;
        const max = values ? ((values.max > range.max) ? range.max : values.max) : range.max;

        return [`${min}`, `${max}`];
    };


export const formatQuickFilterText = (item, selectedFilter) => {
    if (isEmpty(selectedFilter)) {
      return undefined;
    }
    const prefix = item?.listingFilterValues?.[0]?.prefix || '';
    const suffix = item?.listingFilterValues?.[0]?.suffix || '';
    const value = selectedFilter.values[0];
    if (item.type === FILTER_UI_TYPES.RANGE) {
        // const range = value.split('_');
        const range = getMinMaxForRange(item.urlParam, item?.listingFilterValues, value);
        const rangeSingleValue = getSingleRangeValue(range[0], range[1]);
        if (rangeSingleValue) {
            return `${getFormatText({ filterText: rupeeFormatter(range[0]), prefix, suffix })}`;
        }
        return `${getFormatText({
            filterText: rupeeFormatter(range[0]),
            prefix,
            suffix,
        })} - ${getFormatText({ filterText: rupeeFormatter(range[1]), prefix, suffix })}`;
    } else if (
        (item.type === FILTER_UI_TYPES.TOGGLE ||
          item.type === FILTER_UI_TYPES.CHECKBOX ||
          item.type === FILTER_UI_TYPES.DOUBLE_TAB_SINGLESELECT) &&
        !isEmpty(value)
      ) {
        return value;
      }
     const selectedValue = item?.listingFilterValues?.find(ele => value === ele.uniqueId);
     const updateValue = selectedValue.filterText;
    return getFormatText({ filterText: updateValue, prefix, suffix });
  };

export const getDisplayStringForFilterCriteria = (filterId, value, filtersIdMap, masterFilterIdMap) => {
    let filter = filtersIdMap[filterId];
    if (isEmpty(filter)) {
        filter = masterFilterIdMap[filterId];
    }
    if (isEmpty(filter)) {
        return value;
    }
    const prefix = filter.listingFilterValues?.[0]?.prefix || '';
    const suffix = filter.listingFilterValues?.[0]?.suffix || '';
    switch (filter.type) {
        case FILTER_UI_TYPES.RANGE:
            // const range = value.split('_')
            const range = getMinMaxForRange(filter.urlParam, filter?.listingFilterValues, value);
            const rangeSingleValue = getSingleRangeValue(range[0], range[1]);
          if (rangeSingleValue) {
            return `${getFormatText({ filterText: rupeeFormatter(range[0]), prefix, suffix })}`;
          }
            return `${getFormatText({
                filterText: rupeeFormatter(range[0]),
                prefix,
                suffix,
              })} - ${getFormatText({ filterText: rupeeFormatter(range[1]), prefix, suffix })}`;

        case FILTER_UI_TYPES.TOGGLE:
        case FILTER_UI_TYPES.CHECKBOX:
        case FILTER_UI_TYPES.DOUBLE_TAB_SINGLESELECT:
            return filter.listingFilterValues.find(item => item.uniqueId == value)?.filterText || value;
        default:
            const filterText = filter.listingFilterValues.find(item => item.uniqueId == value)?.filterText || value;
            return getFormatText({ filterText: filterText, prefix, suffix});
    }
};

export const findValueInSorterCriterias = (sorterCriterias = [], sorter = {}) => {
    if (!sorterCriterias || sorterCriterias.length === 0) {
        return false;
    }
    const { listingSorterValues } = sorter;
    if (listingSorterValues?.length > 0) {
        const uniqueId = `${listingSorterValues[0].uniqueId}`;
        sorterCriterias.forEach(criteria => {
            if (criteria.id === sorter.id) {
                const found = criteria.values.indexOf(uniqueId) >= 0;
                return found;

            }
        });
    }
    return false;
};
export const getCountMap = filterStats => {
    const countMap = {};
    if (filterStats?.type === 'DISCRETE') {
        filterStats.discreteStats.forEach(item => {
            if (!isEmpty(item.filterValue)) {
                countMap[item.filterValue] = item.packageCount;
            } else if (!isEmpty(item.lowerBound))  {
                let key = `${item.lowerBound}_${item.upperBound}`;
                countMap[key] = item.packageCount;
            }
        });
    }
    return countMap;
};
export const getOptionTrueFalseValues = options => {
    const values = { trueValue: undefined, falseValue: undefined };
    options.forEach(op => {
        if (op.data.uniqueId == 1) {
            values.trueValue = op;
        } else if (op.data.uniqueId == 0) {
            values.falseValue = op;
        }
    });
    return values;
};
/**
 * Decides whether we can show range filter or not
 * @param {*} filter
 */
export const canShowRangeFilter = filter => {
    /**
     * We can show a range filter if and only if there are 2 distinct range otherwise there is a crash in Multislider lib
     * That is if MIN and MAX values are same lib is crashing internally while it is applying CSS to slider components
     */
    const { listingFilterValues } = filter;
    if (isEmpty(listingFilterValues)) {
        return false;
    }
    else if (listingFilterValues.length === 1) {
        const { filterText } = listingFilterValues[0];
        if (filterText.includes('_')) {
            const values = filterText.split('_');
            return values[0] === values[1] ? false : true;
        }
        return false;
    }
    return true;
};

export const  getFormatText = (option) => {
    const prefix = option.prefix;
    const suffix = option.suffix;
    const value = option.filterText;
    if (!isEmpty(prefix) && !isEmpty(suffix)) {
        return `${prefix}${value} ${suffix}`;
    } else if (!isEmpty(prefix)) {
        return `${prefix}${value}`;
    } else if (!isEmpty(suffix)) {
        return `${value} ${suffix}`;
    }
    return value;
};

const baseStyle = {
    ...marginStyles.mt2,
    tintColor: holidayColors.gray,
};

export const FILTER_SORT_ICON_DATA = {
    'Sort': {
        icon: require('mobile-holidays-react-native/src/PhoenixGroupingV2/Components/assets/SortGrey.webp'),
        style: {
            ...baseStyle,
            width: 17,
            height: 14,
        },
    },
    'default': {
        icon: require('@mmt/legacy-assets/src/arrow_dropdown.webp'),
        style: {
            ...baseStyle,
            width: 10,
            height: 6,
        },
    },
};

