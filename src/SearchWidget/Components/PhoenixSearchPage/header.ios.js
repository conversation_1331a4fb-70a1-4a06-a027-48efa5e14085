import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import PropTypes from 'prop-types';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../../Styles/holidayColors';
import { PageHeaderBackButton, PageHeaderTitle } from '../../../Common/Components/PageHeader';
import CrossIcon from '@mmt/legacy-assets/src/holidays/iconCross.webp';
import { paddingStyles } from '../../../Styles/Spacing';
const FilterHeader = ({
  titleText,
  resetText,
  handleReset,
  handleClose,
  containerStyle,
  subTitle,
  isBottomSheet,
  showResetInBottomSheet = false,
}) => {
  const renderContent = () =>
    isBottomSheet ? (
      <View style={[styles.headerBottomSheet]}>
        <PageHeaderBackButton iconSource={CrossIcon} onBackPressed={handleClose} />
        <View style={styles.titleWrapper}>
          <PageHeaderTitle title={titleText} />
          {!!subTitle && <Text style={styles.subTitle}>{subTitle}</Text>}
        </View>
        {showResetInBottomSheet && (
          <View style={styles.btnWrapper}>
            <AnchorBtn label={resetText} handleClick={handleReset} />
          </View>
        )}
      </View>
    ) : (
      <View style={styles.header}>
        <PageHeaderBackButton iconSource={CrossIcon} onBackPressed={handleClose} />
        <PageHeaderTitle title={titleText} />
        <View style={styles.btnWrapper}>
          <AnchorBtn label={resetText} handleClick={handleReset} />
        </View>
      </View>
    );
  return containerStyle ? <View style={containerStyle}>{renderContent()}</View> : renderContent();
};

FilterHeader.propTypes = {
  titleText: PropTypes.string.isRequired,
  resetText: PropTypes.string.isRequired,
  handleClose: PropTypes.func.isRequired,
  handleReset: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: holidayColors.white,
    ...paddingStyles.pa16,
  },
  headerBottomSheet: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: holidayColors.white,
    ...paddingStyles.pa16,
    borderBottomColor: colors.grey6,
    borderBottomWidth: 1,
    position: 'relative',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  btnWrapper: {
    marginLeft: 'auto',
  },
  titleWrapper: {
    display: 'flex',
    flexDirection: 'column',
    paddingRight: 50,
  },
  subTitle: { fontSize: 14, fontFamily: 'Lato', color: colors.defaultTextColor },
});

export default FilterHeader;
