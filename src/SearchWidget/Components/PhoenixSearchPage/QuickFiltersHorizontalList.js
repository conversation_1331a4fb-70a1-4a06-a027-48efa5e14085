import React, { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  TouchableHighlight,
  UIManager,
  LayoutAnimation,
  Platform,
} from 'react-native';
import { isEmpty } from 'lodash';
import {
  createMasterFilterList,
  createQuickFilterList,
  FILTER_URL_PARAM_NAME,
  FILTER_SORT_ICON_DATA,
  QUICK_FILTER_TYPES,
} from './Utils';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { borderRadiusValues, holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';
import ShadowLine from '../../../Common/Components/ShadowLine';
import { widthPixel } from '../../../Styles/holidayNormaliseSize';
import {isAndroidClient} from "../../../utils/HolidayUtils";
import filterIconGray from '@mmt/legacy-assets/src/filter.webp';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';

const filterIcon = require('@mmt/legacy-assets/src/holidays/ic_filter_without_bg.webp');

if (isAndroidClient()) {
    UIManager.setLayoutAnimationEnabledExperimental(true);
}

const renderNewFilterTabView = ({ filtersSize, handleAllFilterClicked }) => {
  const isActive = filtersSize > 0;
  return (
    <TouchableOpacity style={styles.filterItemContainer} onPress={handleAllFilterClicked}>
      <View style={filterNewStyles.tabCard}>
       <Text style={[styles.filterItemText]}>Filters</Text>
        <HolidayImageHolder
          defaultImage={filterIconGray}
          resizeMode={'contain'}
          style={filterNewStyles.iconStyleFilters}
        />
      </View>
      {isActive ? (
        <View style={filterNewStyles.counterBg}>
          <Text style={filterNewStyles.counterText}>{filtersSize}</Text>
        </View>
      ) : null}
    </TouchableOpacity>
  );
};
const QuickFiltersHorizontalList = ({
    groupingData,
    onQuickFilterClicked,
    onAllFilterClicked,
    isExpanded = true,
    selectedQuickFilterIndex,
    fromLandingPage,
    landingFilters,
    containerStyles,
    showShadow = false,
    metaData = {},
    showNewFilterUI = false,
  }) => {
    const flatListRef = useRef();
    useEffect(() => {
      const { holidayLandingGroupDto } = groupingData || {};
      const { masterListingFilters, filters = [], listingSorters = {}, sorterCriterias = [] } =
        (!fromLandingPage ? holidayLandingGroupDto : landingFilters) || {};
      const { filterType } = landingFilters || {};
      const filtersList =
        filters?.length > 0 &&
        createQuickFilterList(masterListingFilters, filters, filterType, {
          listingSorters,
          sorterCriterias,
          showNewFilterUI,
        });
      setTimeout(() => {
        if (flatListRef.current && !isEmpty(filtersList[selectedQuickFilterIndex]?.value)) {
          flatListRef.current.scrollToIndex({ animated: true, index: selectedQuickFilterIndex });
        }
      }, 1000);
    }, []);
    const [expanded, setExpanded] = useState(isExpanded);
    const hideFilterCTA = () => {
        if (expanded) {
            LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
            setExpanded(false);
        }
    };
    const { filtersSize, holidayLandingGroupDto } = !fromLandingPage
      ? groupingData
      : landingFilters;
    const { masterListingFilters, filters, listingSorters = {}, sorterCriterias = []} =
      (!fromLandingPage ? holidayLandingGroupDto : landingFilters) || {};
    const { filterType } = landingFilters || {};
    const filtersList = useMemo(
      () => createQuickFilterList(masterListingFilters, filters, filterType, { listingSorters, sorterCriterias, showNewFilterUI }),
      [masterListingFilters, filters],
    );
    const handleAllFilterClicked = useCallback(() => {
        hideFilterCTA(); // hide filter cta text
        if (onAllFilterClicked) {
            setTimeout(() => {
                onAllFilterClicked();
            }, 50);
        }
    }, []);
    const handleScroll = useCallback((event) => {
        if (!expanded) {
            return;
        }
        const scrollIndex = event.nativeEvent.contentOffset.x;
        if (scrollIndex > 10) {
            hideFilterCTA();
        }
    }, []);

    // const renderSorter = (item, index) => {
    //     return (<View style={{paddingStart: 10}}>
    //         {renderItem({item, index})}
    //     </View>);
    // };

    const renderAllFilterView = () => {
      return showNewFilterUI ? (
        renderNewFilterTabView({ filtersSize, handleAllFilterClicked, expanded })
      ) : (
        <TouchableHighlight
          underlayColor="#DDDDDD"
          activeOpacity={0.5}
          style={[
            styles.allFiltersContainer,
            !fromLandingPage ? {} : styles.allFiltersContainerExtra,
            !expanded && {
              paddingStart: 8,
              paddingEnd: 8,
            },
          ]}
          onPress={handleAllFilterClicked}
        >
          <>
            {expanded && <Text style={styles.filterCTAText}>All Filters</Text>}
            <Image style={styles.filterIcon} source={filterIcon} />
            {filtersSize > 0 && (
              <View style={[styles.countContainer, expanded && { right: 18 }]}>
                <Text style={styles.countText}>{filtersSize}</Text>
              </View>
            )}
          </>
        </TouchableHighlight>
      );
    };

    const renderItem = ({ item, index }) => {
        if (item?.type === QUICK_FILTER_TYPES.ALL_FILTER) {
          return renderAllFilterView();
        }
        const isSuitableForFilter = item.urlParam === FILTER_URL_PARAM_NAME.suitableFor;
        const isSelected = !isEmpty(item.value);
        const text = isSelected ? item.value : isSuitableForFilter ? 'All' : item.name;
        const filterSortIcon = FILTER_SORT_ICON_DATA[item.name] || FILTER_SORT_ICON_DATA.default;
        return (
          <TouchableOpacity
            activeOpacity={0.5}
            style={[
              styles.filterItemContainer,
              isSelected && styles.filterItemContainerSelected,
              index === filtersList.length - 1 && !fromLandingPage && { marginEnd: 30 },
              index === filtersList.length - 1 && fromLandingPage && { marginEnd: 12 },
            ]}
            onPress={() => onQuickFilterClicked(item, index)}
          >
            {item.prefix && <Text style={styles.filterItemPrefixText}>{item.prefix}</Text>}
            <Text style={[styles.filterItemText, isSelected && styles.filterItemTextSelected]}>
              {text}
            </Text>
            {showNewFilterUI ? (
              <HolidayImageHolder defaultImage={filterSortIcon.icon} style={filterSortIcon.style} />
            ) : (
              <Image style={filterSortIcon.style} source={filterSortIcon.icon} />
            )}
          </TouchableOpacity>
        );
      };
  return (
    <>
      <View
        style={[styles.container, containerStyles, !fromLandingPage ? styles.containerExtra : {}]}
      >
        {/* {!fromLandingPage &&
          showNewFilterUI &&
          renderSorter(
            {
              id: 999,
              name: 'Sort',
              priorityOrder: 1,
              urlParam: 'sort',
              value: holidayLandingGroupDto?.sorterCriterias?.length > 0 ? 'Sort' : '', // do this make it highlighted
            },
            0,
          )} */}

          {!showNewFilterUI && renderAllFilterView()}
          <FlatList
            style={paddingStyles.pl8}
            ref={flatListRef}
            horizontal
            onScroll={handleScroll}
            onScrollToIndexFailed={() => {}}
            data={filtersList}
            keyExtractor={(item) => `${item.id}_${item.name}`}
            renderItem={renderItem}
            showsHorizontalScrollIndicator={false}
          />
        </View>
        {showShadow && <ShadowLine />}
      </>
    );
};
export default QuickFiltersHorizontalList;
const styles = StyleSheet.create({
    container: {
        ...paddingStyles.ph0,
        ...paddingStyles.pt4,
        display:'flex',
        flexDirection:'row',
    },
    shadow: {
        height: 2,
    },
    containerExtra:{
    },
    filterItemContainerForLastItem: {
        marginEnd: 30,
    },
    filterItemContainer: {
        height: 32,
        ...paddingStyles.ph8,
        flexDirection: 'row',
        borderWidth: 1,
        ...holidayBorderRadius.borderRadius8,
        borderColor: holidayColors.grayBorder,
        alignItems: 'center',
        marginEnd: 6,
        backgroundColor:holidayColors.white,
    },
    filterItemContainerSelected: {
        borderColor: holidayColors.grayBorder,
        backgroundColor: holidayColors.lightBlueBg,
    },
    filterItemPrefixText: {
        color: holidayColors.gray,
        ...fontStyles.labelBaseRegular,
        lineHeight: 19,
        marginEnd: 4,
    },
    filterItemText: {
        color: holidayColors.gray,
        ...fontStyles.labelSmallRegular,
        lineHeight: 19,
        marginEnd: 6,
    },
    filterItemTextSelected: {
        fontWeight: '700',
    },
    filterItemIcon: {
        ...marginStyles.mt2,
        width: 10,
        height: 6,
        tintColor: holidayColors.gray,
    },
    allFiltersContainerExtra:{
        height: 32,
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
    },
    allFiltersContainer: {
        height: 32,
        position: 'relative',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        paddingStart: 10,
        paddingEnd: 10,
        backgroundColor: holidayColors.white,
        borderRadius:borderRadiusValues.br8
    },
    filterCTAText: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.primaryBlue,
        marginEnd: 6,
    },
    filterIcon: {
        width: widthPixel(20),
        height: 20,
        tintColor: holidayColors.primaryBlue,
    },
    countContainer: {
        position: 'absolute',
        top: 0,
        right: 10,
        width: widthPixel(14),
        height: 16,
        ...holidayBorderRadius.borderRadius8,
        backgroundColor: holidayColors.primaryBlue,
        alignItems: 'center',
        justifyContent: 'center',
    },
    countText: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.white,
    },
});

const filterNewStyles = StyleSheet.create({
  counterBg: {
    width: 15,
    height: 15,
    borderRadius: 50,
    position: 'absolute',
    backgroundColor: holidayColors.primaryBlue,
    alignItems: 'center',
    justifyContent: 'center',
    right: 3,
    top: 3,
  },
  tabCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  iconStyleFilters: {
    width: 15,
    height: 15,
    tintColor: holidayColors.gray,
  },
  iconStyleFiltersActive: {
    width: 15,
    height: 15,
    ...marginStyles.ml8,
    tintColor: holidayColors.primaryBlue,
  },
  counterText: {
    color: holidayColors.white,
    fontSize: 10,
    lineHeight: 12,
    fontWeight: '700',
  },
});
