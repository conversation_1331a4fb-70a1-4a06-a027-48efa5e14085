import React from 'react';
import {StyleSheet, View, Text, Platform} from 'react-native';
import PropTypes from 'prop-types';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss.js';
import Title from '@mmt/legacy-commons/Common/Components/Title';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import {CLEAR} from '../SearchWidgetConstants';
import MultiSlider from './Slider/MultiSlider';
import {getScreenWidth} from '../../utils/HolidayUtils';


class DurationSlider extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      min: props.min,
      max: props.max,
      step: props.step,
      multiSliderValue: [props.startValue, props.endValue],
    };
  }

  componentWillReceiveProps(props) {
    this.setState({
      min: props.min,
      max: props.max,
      step: props.step,
      multiSliderValue: [props.startValue, props.endValue],
    });
  }

  multiSliderValuesChange = (values) => {
    this.setState({
      multiSliderValue: values,
    });
  };

  onValuesChanged = (values) => {
    this.props.onChange(values[0], values[1]);
  };

  handleClear = () => {
    this.props.onClear();
  };

  render() {
    return (
      <View style={styles.container}>

        <View style={[AtomicCss.flexRow, AtomicCss.marginBottom15]}>
          <View>
            <Title heading="Duration"/>
          </View>
          <View style={AtomicCss.pushRight}>
            <AnchorBtn
              key="duration"
              label={CLEAR}
              handleClick={() => this.handleClear()}
            />
          </View>
        </View>

        <Text
          style={styles.text}>{`${this.getNightsText(this.state.multiSliderValue[0])}  - ${this.getNightsText(this.state.multiSliderValue[1])}`}</Text>

        <View style={[AtomicCss.flex, AtomicCss.justifyCenter, AtomicCss.alignCenter]}>
          <MultiSlider
            values={[
              this.state.multiSliderValue[0],
              this.state.multiSliderValue[1],
            ]}
            sliderLength={getScreenWidth() - 50}
            trackStyle={{
              height: 6,
              backgroundColor: '#e6e6e6',
            }}
            selectedStyle={{
              backgroundColor: '#008cff',
            }}
            onValuesChange={this.multiSliderValuesChange}
            min={this.state.min}
            max={this.state.max}
            step={this.state.step}
            allowOverlap
            snapped
            markerStyle={styles.markerStyle}
            pressedMarkerStyle={styles.markerStyle}
            onValuesChangeFinish={this.onValuesChanged}
          />
        </View>

      </View>
    );
  }

  getNightsText = (nights) => {
    return nights === 1 ? '1 Night' : `${nights} Nights`;
  };
}

DurationSlider.propTypes = {
  startValue: PropTypes.number.isRequired,
  endValue: PropTypes.number.isRequired,
  step: PropTypes.number.isRequired,
  min: PropTypes.number.isRequired,
  max: PropTypes.number.isRequired,
  onChange: PropTypes.func.isRequired,
  onClear: PropTypes.func.isRequired,
};

export default DurationSlider;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 4,
  },
  text: {
    fontFamily: 'Lato-Bold',
    fontSize: 14,
    color: '#4a4a4a',

  },
  markerStyle: {
    height: 30,
    width: 30,
    borderRadius: 30,
    backgroundColor: '#fff',
    elevation: 3,
    borderWidth: 1,
    marginTop: 5,
    borderColor: '#ccc', ...Platform.select({
      ios: {
        marginTop: 0,
      },
    }),
  },
});
