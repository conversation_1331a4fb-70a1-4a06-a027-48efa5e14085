import React, {Component} from 'react';
import {
  View,
  StyleSheet,
  Text,
  Image,
  Platform,
  TouchableOpacity,
  NativeModules,
  ActivityIndicator,
  Linking,
} from 'react-native';
import PropTypes from 'prop-types';
import {isEmpty, debounce} from 'lodash';
import {
  fetchCitiesData,
  fetchCitiesDataFromPreloadedList,
  getBranch,
  getCitiesName,
  getCityData,
} from '../utils/cityRepository';
import {statusBarHeightForIphone} from '@mmt/legacy-commons/Styles/globalStyles';
import Card from '@mmt/legacy-commons/Common/Components/Card/index';
import AutoCompleteList from './AutoCompleteList';
import iconBack from '@mmt/legacy-assets/src/img/iconBack.webp';
import iconAdvanced from '@mmt/legacy-assets/src/sort_filter.webp';
import {DEPARTURE_PLACEHOLDER, DESTINATION_PLACEHOLDER, TO} from '../SearchWidgetConstants';
import {USER_DEFAULT_CITY} from '../../HolidayConstants';
import FilterDestinationSelectorHeader from './FilterDestinationSelectorHeader';
import {
  filterListIgnoreCase,
  getLocationPermissionStatus,
  isIosClient,
  isNotNullAndEmptyCollection
} from '../../utils/HolidayUtils';
import SearchHistorySelector from './SearchHistorySelector';
import {getNearestCity} from '../utils/cityRepository';
import {getLocationWithPerm} from '../../utils/HolidayUtils';
import withPermissionDialog from '@mmt/legacy-commons/Common/Components/PermissionDialog/withPermissionDialog';
import { checkForLocationPermission } from '@mmt/legacy-commons/Helpers/locationHelper';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

const locationIcon = require('@mmt/legacy-assets/src/location.webp');

class FilterDestinationSelector extends Component {
  constructor(props) {
    super(props);
    this.citiesData = null;
    this.state = {
      query: '',
      isLoading: false,
      updateLocation: false,
      hideInput: !this.props.forceShowInput && props.destinations && props.destinations.length > 0,
      cities: props.destinations ? props.destinations : [],
    };
    this._onTextChangeDebounced = debounce(this._onTextChange, 300);
  }

  componentWillReceiveProps(props) {
    this.setState({
      isLoading: false,
      hideInput: !this.props.forceShowInput && props.destinations && props.destinations.length > 0,
      cities: props.destinations ? props.destinations : [],
    });
  }

  _onTextChange = async (text) => {
    if (isNotNullAndEmptyCollection(this.props.destinations)) {
      this.setState({
        cities: filterListIgnoreCase(this.props.destinations, text),
        isLoading: false,
      });
    } else {
      this.setState({
        isLoading: true,
        query: text,
      });
      try {
        let cities = [];
        if (this.props.autoCompleteObj.label === TO) {
          const { destinationMapInfo } = this.props;
          if (destinationMapInfo) {
            this.citiesData = fetchCitiesDataFromPreloadedList(destinationMapInfo, text);
          } else {
            this.citiesData = await fetchCitiesData(text, 'SEARCH');
          }
          cities = getCitiesName(this.citiesData);
        } else {
          const { availableHubs } = this.props.autoCompleteObj;
          availableHubs.forEach((hub) => {
            if (hub.name.toLowerCase().includes(text.toLowerCase())) {
              cities.push(hub.name);
            }
          });
        }
        this.setState({
          cities,
          isLoading: false,
        });
      } catch (e) {
        this.citiesData = null;
        this.setState({
          isLoading: false,
          cities: [],
        });
      }
    }
  };

  _onTextCleared = () => {
    this.citiesData = null;
    if (isNotNullAndEmptyCollection(this.props.destinations)) {
      this.setState({
        cities: filterListIgnoreCase(this.props.destinations, ''),
        isLoading: false,
      });
    } else {
      this.setState({
        cities: [],
      });
    }
  };

  _onBack = () => {
    this.props.onBack();
  };

  _onCitySelected = (city) => {
    if (
      this.citiesData &&
      isNotNullAndEmptyCollection(this.citiesData.data) &&
      this.props.autoCompleteObj.label === TO
    ) {
      this.props.citySelect(city);
    } else if (
      this.citiesData &&
      isNotNullAndEmptyCollection(this.citiesData) &&
      this.props.autoCompleteObj.label === TO
    ) {
      this.props.citySelect(city, getBranch(this.citiesData, city));
    } else if (this.props.availableHubs && isNotNullAndEmptyCollection(this.props.availableHubs)) {
      const data = getCityData(this.props.availableHubs, city);
      const { branch, locusId } = data || {};
      this.props.citySelect(city, branch, locusId, {
        citySearchType: 'Airport',
        citySelectionType: 'Manual',
      });
    } else {
      this.props.citySelect(city);
    }
    this.citiesData = null;
    this.setState({
      updateLocation: false,
    });
  };

  handleNeverAskLocationPermission = () => {
    this.props.showPermissionDialog({
      message:
        'Location permission is permanently disabled, Please go to app settings and allow location manually.',
      buttonPositive: { title: 'Settings', callback: () => Linking.openSettings() },
      buttonNegative: { title: 'Cancel', callback: () => {} },
    });
  };
  captureClickEvents = (eventName) => {
    const { trackClickEvent = () => {} } = this.props || {};
    this.props?.trackPDTV3Event?.({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    trackClickEvent(eventName);
  };
  handleLocationPermission = async () => {
    this.captureClickEvents('use_current_location');
    const havingPermission = await checkForLocationPermission();
    if (havingPermission || isIosClient()) {
      this.getLocationFromLatLong();
    } else {
      this.props.showPermissionDialog({
        title: 'Current Location Required',
        message: 'Please allow location permission to show the correct results.',
        buttonPositive: {
          title: 'Go Ahead',
          callback: () => {
            this.getLocationFromLatLong();
            this.captureClickEvents('Use_Current_Location_GoAhead');
          },
        },
        buttonNegative: {
          title: 'Not Now',
          callback: () => {
            this.captureClickEvents('Use_Current_Location_NotNow');
          },
        },
      });
    }
  };
  getLocationFromLatLong = async () => {
    const havingPermission = await checkForLocationPermission();
    if (Platform.OS !== 'ios' || havingPermission) {
      this.setState({
        updateLocation: true,
      });
    }
    let { lat, long } = {};
    try {
      const getLocation = await getLocationWithPerm();
      if (!getLocation) {
        this.setState({
          cities: filterListIgnoreCase(this.props.destinations, ''),
          updateLocation: false,
        });
        return;
      }
      this.setState({
        updateLocation: true,
      });
      lat = getLocation.lat;
      long = getLocation.lng;
    } catch (error) {
      if (error?.toString().toLowerCase().includes('never_ask_again')) {
        // if permission is never ask then show open settings dialog
        this.handleNeverAskLocationPermission();
      }
      this.setState({
        cities: filterListIgnoreCase(this.props.destinations, ''),
        updateLocation: false,
      });
      return;
    }
    const cityName = await getNearestCity(lat, long);
    if (cityName) {
      this._onCitySelected(cityName);
    } else {
      this._onCitySelected(USER_DEFAULT_CITY);
    }
  };

  render() {
    return (
      <View style={styles.container}>
        {this.state.updateLocation && this.renderUpdateLocation()}
        {!this.state.updateLocation && this.renderContent()}
      </View>
    );
  }

  renderUpdateLocation = () => {
    return (
      <View style={styles.progressContainer}>
        
        <Spinner
        size={36}
        strokeWidth={4}
        progressPercent={85}
        speed={1.5}
        color={holidayColors.primaryBlue}
    />
   
        <Text />
      </View>
    );
  };

  renderContent = () => {
    const cities = this.state.cities;
    const showAutoCompleteList = cities.length > 0;
    return (
      <React.Fragment>
        <Card
          style={{
            backgroundColor: holidayColors.white,
            marginHorizontal: 0,
            marginVertical: 0,
            height: 54,
          }}
          elevation={1}
        >
          <View style={styles.headerWrapper}>
            <TouchableOpacity style={styles.backWrapper} onPress={this._onBack}>
              <Image style={styles.iconBack} source={iconBack} />
            </TouchableOpacity>
            {this.state.hideInput && <Text style={styles.text}>Choose from our cities</Text>}

            {!this.state.hideInput && (
              <FilterDestinationSelectorHeader
                style={{
                  paddingVertical: 12,
                  paddingRight: 12,
                  fontSize: 16,
                  flex: 1,
                  justifyContent: 'center',
                  backgroundColor: holidayColors.white,
                }}
                onChangeText={this._onTextChangeDebounced}
                onClear={this._onTextCleared}
                showClear
                placeholder={
                  this.props.autoCompleteObj.label === TO
                    ? DESTINATION_PLACEHOLDER
                    : DEPARTURE_PLACEHOLDER
                }
                showLoading={this.state.isLoading}
              />
            )}
          </View>
        </Card>
        {this.props.showAdavanceSearch && (
          <TouchableOpacity
            style={styles.advanceSearch}
            onPress={this.props.onAdvanceSearchClicked}
          >
            <Text style={styles.advanceSearchText}>Advanced search options</Text>
            <Image style={styles.iconAdvance} source={iconAdvanced} />
          </TouchableOpacity>
        )}
        {this.props.showGetCurrLocation && (
          <TouchableOpacity style={styles.getLocation} onPress={this.handleLocationPermission}>
            <Image style={styles.iconLocation} source={locationIcon} />
            <Text style={styles.text}>Use Current Location</Text>
          </TouchableOpacity>
        )}
        {showAutoCompleteList && (
          <AutoCompleteList
            isLoading={isEmpty(this.state.query) || this.state.isLoading}
            results={cities}
            onItemClicked={this._onCitySelected}
          />
        )}

        {isNotNullAndEmptyCollection(this.props.searchHistoryResults) &&
          !showAutoCompleteList &&
          this.props.searchHistoryResults.map((label, index) => (
            <SearchHistorySelector key={index} label={label} citySelect={this.props.citySelect} />
          ))}
        <View
          style={{
            alignSelf: 'center',
            position: 'absolute',
            bottom: 24,
          }}
        />
      </React.Fragment>
    );
  };
}

FilterDestinationSelector.propTypes = {
  autoCompleteObj: PropTypes.object.isRequired,
  destinations: PropTypes.array.isRequired,
  citySelect: PropTypes.func.isRequired,
  forceShowInput: PropTypes.bool,
  onBack: PropTypes.func.isRequired,
  showAdavanceSearch: PropTypes.bool,
  showGetCurrLocation: PropTypes.bool,
  onAdvanceSearchClicked: PropTypes.func,
  searchHistoryResults: PropTypes.array,
};

FilterDestinationSelector.defaultProps = {
  forceShowInput: false,
  showAdavanceSearch: false,
  searchHistoryResults: [],
  showGetCurrLocation: false,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    backgroundColor: '#fff',
    flex: 1,
  },
  text: {
    fontFamily: 'Lato-Regular',
    color: 'black',
    fontSize: 16,
  },
  headerWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    backgroundColor: '#fff',
    ...Platform.select({
      ios: {
        marginTop: -statusBarHeightForIphone,
        paddingTop: statusBarHeightForIphone,
      },
    }),
  },
  button: {
    fontFamily: 'Lato-Bold',
    fontSize: 12,
    fontWeight: 'bold',
    lineHeight: 14,
    letterSpacing: 1,
    textAlign: 'center',
    color: '#ffffff',

  },
  backWrapper: {
    padding: 16,
  },
  iconLocation: {
    height: 16,
    width: 16,
    marginRight: 12,
  },
  iconBack: {
    height: 16,
    width: 16,
  },
  iconAdvance: {
    height: 24,
    width: 24,
  },
  advanceSearch: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    width: '100%',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  advanceSearchText: {
    flex: 1,
    color: '#9b9b9b',
  },
  getLocation: {
    marginLeft: 19,
    marginRight: 19,
    fontFamily: 'Lato-Regular',
    paddingTop: 19,
    paddingBottom: 19,
    borderBottomWidth: 1,
    borderBottomColor:'rgba(0, 0, 0, 0.1)',
    flexDirection:'row',
  },
  progressContainer: {
    width: '100%',
    height: '100%',
    backgroundColor: '#ffffffd9',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default withPermissionDialog(FilterDestinationSelector);
