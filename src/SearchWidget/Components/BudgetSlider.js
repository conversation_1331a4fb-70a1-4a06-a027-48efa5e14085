import React from 'react';
import {StyleSheet, View, Text, Platform} from 'react-native';
import PropTypes from 'prop-types';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss.js';
import Title from '@mmt/legacy-commons/Common/Components/Title';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import {CLEAR} from '../SearchWidgetConstants';
import MultiSlider from './Slider/MultiSlider';
import {getScreenWidth} from '../../utils/HolidayUtils';
import {rupeeFormatterWithLocale} from '@mmt/legacy-commons/Helpers/currencyUtils';

class BudgetSlider extends React.Component {

  constructor(props) {
    super(props);
    this.state = {
      min: props.min,
      max: props.max,
      step: props.step,
      multiSliderValue: [props.startValue, props.endValue],
    };
  }

  componentWillReceiveProps(props) {
    this.setState({
      min: props.min,
      max: props.max,
      step: props.step,
      multiSliderValue: [props.startValue, props.endValue],
    });
  }

  multiSliderValuesChange = (values) => {
    this.setState({
      multiSliderValue: values,
    });
  };

  onValuesChanged = (values) => {
    this.props.onChange(values[0], values[1]);
  };

  handleClear = () => {
    this.props.onClear();
  };

  createStatValues = (filterStats, start, end) => {
    const valueStats = [];
    let max = 0;
    if (filterStats) {
      filterStats.forEach((element) => {
        valueStats.push({
          value: element.packageCount,
          selected: (start >= element.lowerBound && start < element.upperBound)
            || (end <= element.upperBound && end >= element.lowerBound)
          || (start <= element.lowerBound && end >= element.upperBound),
        });
        max = Math.max(element.packageCount, max);
      });
    }
    return {
      valueStats,
      max,
    };
  };

  render() {
    const stats = this.createStatValues(this.props.filterStats, this.state.multiSliderValue[0], this.state.multiSliderValue[1]);
    return (
      <View style={styles.container}>

        <View style={[AtomicCss.flexRow, AtomicCss.marginBottom15]}>
          <View>
            <Title heading="BUDGET" subHeading=" per person" />
          </View>
          <View style={AtomicCss.pushRight}>
            <AnchorBtn
              key="budget"
              label={CLEAR}
              handleClick={() => this.handleClear()}
            />
          </View>
        </View>

        <View style={styles.priceSection}>
          <Text style={styles.text}>{rupeeFormatterWithLocale(this.state.multiSliderValue[0])} - </Text>
          <Text style={styles.text}>{rupeeFormatterWithLocale(this.state.multiSliderValue[1])} </Text>
        </View>
        {stats.max > 0 &&
        <View style={styles.barGraph}>
          {stats.valueStats.map((element) => <View
            style={{
              width: (getScreenWidth() - 50) / stats.valueStats.length,
              height: Math.ceil((element.value / stats.max) * 95),
              backgroundColor: element.selected ? '#d7edff' : '#f5f5f5',
            }}
          />)}
        </View>
        }
        <View style={[AtomicCss.flex, AtomicCss.justifyCenter, AtomicCss.alignCenter]}>
          <MultiSlider
            values={[
              this.state.multiSliderValue[0],
              this.state.multiSliderValue[1],
            ]}
            sliderLength={getScreenWidth() - 50}
            trackStyle={{
              height: 6,
              backgroundColor: '#e6e6e6',
            }}
            selectedStyle={{
              backgroundColor: '#008cff',
            }}
            onValuesChange={this.multiSliderValuesChange}
            min={this.state.min}
            max={this.state.max}
            step={this.state.step}
            allowOverlap={false}
            snapped
            markerStyle={styles.markerStyle}
            pressedMarkerStyle={styles.markerStyle}
            onValuesChangeFinish={this.onValuesChanged}
          />
        </View>

      </View>
    );
  }
}

BudgetSlider.propTypes = {
  startValue: PropTypes.number.isRequired,
  endValue: PropTypes.number.isRequired,
  step: PropTypes.number.isRequired,
  min: PropTypes.number.isRequired,
  max: PropTypes.number.isRequired,
  onChange: PropTypes.func.isRequired,
  onClear: PropTypes.func.isRequired,
  filterStats: PropTypes.array.isRequired,
};

export default BudgetSlider;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    marginTop: 4,
    marginBottom: 10,

  },
  text: {
    fontFamily: 'Lato-Bold',
    fontSize: 14,
    color: '#4a4a4a',

  },
  priceSection:
    {
      flexDirection: 'row',
      justifyContent: 'flex-start',
    },
  markerStyle: {
    height: 30,
    width: 30,
    borderRadius: 30,
    backgroundColor: '#fff',
    elevation: 2,
    borderWidth: 1,
    marginLeft: 2,
    marginRight: 2,
    shadowOpacity: 0.2,
    marginTop: 8,
    marginBottom: 3,
    borderColor: '#ccc', ...Platform.select({
      ios: {
        marginTop: 0,
      },
    }),
  },
  barGraph: {
    height: 95,
    width: '100%',
    marginTop: 20,
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingLeft: 10,
  },
});
