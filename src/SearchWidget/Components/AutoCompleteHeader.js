import React from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TextInput,
  View,
} from 'react-native';
import PropTypes from 'prop-types';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss.js';
import {CLEAR} from '../SearchWidgetConstants';

class AutoCompleteInput extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      textInput: null,
    };
  }

  clear = () => {
    if (this._textInput) {
      this._textInput.clear();
    }
  };
  blur = () => {
    if (this._textInput) {
      this._textInput.blur();
    }
  };

  onClear = () => {
    this.setState({
      textInput: this._textInput.clear(),
    });

    this.props.onClear();
  };

  render() {
    const {
      showClear, onClear, showLoading, useClearIcon, ...textInputProps
    } = this.props;
    return (
      <View style={styles.container}>
        <View style={styles.textInput}>

          <TextInput
            autoFocus={true}
            autoCorrect={false}
            {...textInputProps}
            ref={(e) => {
              this._textInput = e;
            }}
          />
        </View>
        {showLoading &&
        <ActivityIndicator style={styles.clearIcon} color={colors.black}/>
        }
        <View style={[AtomicCss.pushRight, styles.clearContainer]}>
          <AnchorBtn label={CLEAR} handleClick={this.onClear}/>
        </View>
      </View>);
  }
}

AutoCompleteInput.propTypes = {
  ...TextInput.PropTypes,
  underlineColorAndroid: PropTypes.string,
  showClear: PropTypes.bool,
  useClearIcon: PropTypes.bool,
  showLoading: PropTypes.bool,
  onClear: PropTypes.func.isRequired,
};

AutoCompleteInput.defaultProps = {
  showClear: true,
  useClearIcon: true,
  showLoading: false,
  underlineColorAndroid: colors.transparent,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
    justifyContent: 'center',
    height: 50,

  },
  textInput: {
    flex: 1,
  },
  clearIcon: {
    position: 'relative',
    right: 10,
    height: 20,
    width: 20,
  },
  clearText: {
    color: colors.black,
    fontFamily: fonts.bold,
    fontSize: 10,
    paddingRight: 10,
  },
  clearContainer: {
    marginTop: -10,
    paddingTop: 12,
  },
});

export default AutoCompleteInput;
