import React from 'react';
import {Image, View, StyleSheet, Text} from 'react-native';
import noPlacesError from '@mmt/legacy-assets/src/error_no_places.webp';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

export const NoResult = () => (
    <View style={styles.noPlaces}>
        <Image resizeMode="contain" source={noPlacesError} style={styles.noPlacesImage} />
        <Text style={styles.noPlacesTitle}>Oops! We didn’t get you.</Text>
        <Text style={styles.noPlacesSubtitle}>Try entering a different destination or check the spelling of location entered</Text>
    </View>
);


const styles = StyleSheet.create({
    noPlaces: {
        margin: 16,
        flexDirection: 'column',
        flex: 1,
        alignItems: 'center',
    },
    noPlacesImage: {
        height: 200,
        marginTop: 16,
    },
    noPlacesTitle: {
        ...fontStyles.labelLargeBold,
        color: holidayColors.black,
        textAlign: 'center',
        marginTop: 16,
        lineHeight: 32,
    },
    noPlacesSubtitle: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.lightGray,
        textAlign: 'center',
        marginTop: 8,
        lineHeight: 21,
    },
});
