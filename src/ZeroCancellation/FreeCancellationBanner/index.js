import React from 'react';
import { TouchableOpacity, StyleSheet, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { overlays } from '../../PhoenixDetail/DetailConstants';
import { isZCAvailable } from '../../utils/HolidayUtils';
import { isFlexiDateAvailable } from '../CancellationDateChange/InnerContent';
import {
  getFreeCancellationBannerSubText,
  getFreeCancellationBannerText,
} from '../../PhoenixDetail/Utils/HolidayDetailUtils';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { marginStyles } from '../../Styles/Spacing';
import { actionStyle } from '../../PhoenixDetail/Components/DayPlan/dayPlanStyles';

const FreeCancellationBanner = (props) => {
  const { cancellationPolicyData } = props;

  if (
    !cancellationPolicyData ||
    !cancellationPolicyData.penaltyDetail ||
    !showBanner(cancellationPolicyData.penaltyDetail.zcOptions)
  ) {
    return null;
  }

  const activeBtnOpacity = 0.7;
  const { zcOptions } = cancellationPolicyData.penaltyDetail;
  const bannerText = getFreeCancellationBannerText(zcOptions);
  const bannerSubText = getFreeCancellationBannerSubText(zcOptions);

  return (
    <LinearGradient
      start={{ x: 1.0, y: 0.0 }}
      end={{ x: 0.0, y: 1.0 }}
      colors={['#ccecdf', '#ffe1e1']}
      style={styles.cancellationBanner}
    >
      <Text style={styles.bannerText}>{bannerText}</Text>
      <View style={[AtomicCss.flexRow, AtomicCss.spaceBetween, styles.alignEnd]}>
        <Text style={styles.bannerSubText}>{bannerSubText}</Text>
        <TextButton
          buttonText="View"
          handleClick={() => props.togglePopup(overlays.EXTRA_INFO_OVERLAY, '', props.sectionToShow)}
          btnTextStyle={actionStyle}
        />
      </View>
    </LinearGradient>
  );
};

const showBanner = (zcOptions) => {
  if (!zcOptions){
    return false;
  }
  return isZCAvailable(zcOptions) || isFlexiDateAvailable(zcOptions);
};

const styles = StyleSheet.create({
  cancellationBanner: { paddingVertical: 10, paddingLeft: 12, paddingRight: 10, borderRadius: 4 },
  alignEnd: { alignItems: 'flex-end' },
  bannerText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    ...marginStyles.mb6,
  },
  bannerSubText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    flex: 1,
  },
});
export default FreeCancellationBanner;
