import React from 'react';
import { View } from 'react-native';
import ActiveTab from './ActiveTab';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

const TabsItem = [
  'CANCELLATION', 'DATE CHANGE',
];

class TabsButton extends React.Component {

  constructor (props) {
    super(props);
    this.state = {
      activeTab: this.props.activeTabIndex,
    };
  }

  onHandleTabChange = (index) => {
    this.setState({
      activeTab: index,
    });
    this.props.onHandleTabChange(index);
  }

  render () {
    return (
      <View style={[AtomicCss.flexRow, AtomicCss.marginBottom16]}>
        {
          TabsItem.map((Item, index) => {
              return (
                <ActiveTab
                  item={Item}
                  index={index}
                  key={index}
                  onHandleTabChange={this.onHandleTabChange}
                  activeTab={this.state.activeTab}
                />
              );
            },
          )
        }
      </View>
    );
  }
}

export default TabsButton;
