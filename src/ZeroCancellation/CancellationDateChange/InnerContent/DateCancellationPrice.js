import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { CANCELLATION, DATE_CHANGE } from './index';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import {DETAIL_TRACKING_PAGE_NAME} from '../../../PhoenixDetail/DetailConstants';
import { rupeeFormatterUtils } from '../../../utils/HolidayUtils';

const DateCancellationPrice = ({ bgColorGrey, activePlan, penalty, nonRefundable, activeIndex, pageName }) => {
  return (
    <View style={(bgColorGrey ? [styles.cancellationSection, { backgroundColor: bgColorGrey }] : [styles.cancellationSection]) &&
    (activePlan ? [AtomicCss.whiteBg, styles.cancellationSection] : [styles.cancellationSection, { backgroundColor: bgColorGrey }])}>
      <Text style={[AtomicCss.font14, AtomicCss.blackFont, AtomicCss.marginBottom3, AtomicCss.blackText]}>{nonRefundable ? 'Non Refundable' : rupeeFormatterUtils(penalty)  }</Text>
      <Text style={[styles.font8, AtomicCss.regularFont, AtomicCss.marginBottom6]}> {getPriceExtraInfo(pageName, nonRefundable)}</Text>
      <Text style={[AtomicCss.font10, AtomicCss.regularFont, AtomicCss.defaultText]}>{getSubText(activeIndex, nonRefundable )}</Text>
    </View>
  );
};

const getSubText = (activeIndex, nonRefundable) => {
  if (activeIndex === CANCELLATION) {
    return nonRefundable ? 'Cancellation is not allowed' : 'Cancellation fee';
  } else if (activeIndex === DATE_CHANGE) {
    return nonRefundable ? 'Date Change is not allowed' : 'Date Change Fee. Fare Difference will be extra.';
  } else {
    return '';
  }
};

const getPriceExtraInfo = (pageName, nonRefundable) => {
  if (!nonRefundable && pageName && pageName === DETAIL_TRACKING_PAGE_NAME) {
    return 'Per Person';
  }
  return '';
};

const styles = StyleSheet.create({
  cancellationSection: { height: 87, paddingLeft: 15 , paddingRight: 3,  justifyContent: 'center' },
  bgGrey: { backgroundColor: '#f3f3f3' },
  font8: { fontSize: 8 },
  width107: { width: 107 },

});

export default DateCancellationPrice;
