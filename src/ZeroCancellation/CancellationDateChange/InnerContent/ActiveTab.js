import React from 'react';
import { Text, TouchableOpacity } from 'react-native';
import styles from './TabsCss';

const Tabs = (props) => {
  const index = props.index;
  const tabStyle = [styles.tabStyle];
  const tabStyleTxt = [styles.tabStyleTxt];

  if (index === props.activeTab) {
    tabStyle.push(styles.tabActive);
    tabStyleTxt.push(styles.tabTxtActive);
  }
  return (
    <TouchableOpacity style={tabStyle} onPress={() => props.onHandleTabChange(index)}>
      <Text style={tabStyleTxt}>{props.item}</Text>
    </TouchableOpacity>
  );
};
export default Tabs;
