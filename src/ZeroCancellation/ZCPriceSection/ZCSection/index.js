import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import ZCIcon from '../images/ZCIcon.webp';
import FDIcon from '../images/FDIcon.webp';
import {CANCELLATION_OVERLAY, DATE_CHANGE_OVERLAY} from '../../../Review/HolidayReviewConstants';
import { connect } from 'react-redux';
import { calculateTravellersCount } from '../../../PhoenixDetail/Utils/HolidayDetailUtils';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import { fillDateAndTime } from '../../../Common/HolidaysCommonUtils';
import {isFlexiDateAvailable, isZCAvailable, rupeeFormatterUtils} from '../../../utils/HolidayUtils';

class ZCSection extends React.Component {
  constructor (props) {
    super(props);
    const { reviewData } = this.props || {};
    const { reviewDetail } = reviewData || {};
    const { zcDetail } = reviewDetail || {};
    const { selected, selectedType } = zcDetail || {};
    this.state = {
      radiobtn: selected ? selectedType : 0,
    };
  }

  componentDidUpdate (prevProps, prevState, snapshot) {
    if (prevProps.zcResponse !== this.props.zcResponse) {
      this.radio();
    }
  }

  radio () {
    const { zcResponse } = this.props;
    if (!zcResponse) {
      return;
    }

    const { zcDetail } = zcResponse;

    if (!zcDetail) {
      return;
    }
    const { selected, selectedType } = zcDetail || {};
    this.setState({
      radiobtn: selected ? selectedType : 0,
    });
  }

  getZCData = (zcOptions, t) => {
    for (let i = 0; i < zcOptions.length; i++) {
      const { type } = zcOptions[i];
      if (type === t) { //FlexiDate' // ZC
        return zcOptions[i];
      }
    }
    return null;
  }

  /**************   ZERO CANCELLATION SELECTION ************/

  ZeroCancellationSelector = (zcOptions) => {
    const data = this.getZCData(zcOptions, 'ZC');

    if (data && data.available) {
      const { amount, lastFCDate } = data;
      return (
        <TouchableOpacity activeOpacity={0.7} style={[styles.radioBtn]}
                          onPress={() => {
                            if (this.state.radiobtn !== 'ZC') {
                              this.props.toggleZC('ZC', 'APPLY');
                            }
                          }}>
          <View style={styles.Radio}>
            <View style={[this.state.radiobtn === 'ZC' ? styles.RadioInside : '']}/>
          </View>
          <View style={styles.radioText}>
            <View style={[AtomicCss.flexRow, AtomicCss.spaceBetween, AtomicCss.marginBottom5]}>
              <View style={{flex: 3}}>
                <Image style={styles.ZCIcon} source={ZCIcon}/>
                  <Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.regularFont]}>Free Cancellation valid{'\n'}till {this.getFormattedDate(lastFCDate)}.</Text>
                  <TouchableOpacity onPress={() => this.props.togglePopup(CANCELLATION_OVERLAY)}>
                    <Text style={[AtomicCss.azure, AtomicCss.boldFont]}>Know More</Text>
                  </TouchableOpacity>
              </View>
              <View style={{flex: 1, alignItems: 'flex-end', marginEnd: 26}}>
                <Text style={[styles.optionName, AtomicCss.marginBottom8]}>
                  <Text style={[AtomicCss.blackFont, AtomicCss.blackText, AtomicCss.font14]}>{rupeeFormatterUtils(amount)}</Text>
                </Text>
                {this.state.radiobtn === 'ZC' &&
                <TouchableOpacity onPress={() => {this.props.toggleZC('ZC', 'REMOVE');}}>
                  <Text style={[AtomicCss.font10, AtomicCss.azure, AtomicCss.boldFont]}>
                    {'REMOVE'}
                  </Text>
                </TouchableOpacity>}
              </View>
            </View>
          </View>
        </TouchableOpacity>);
    } else {
      return [];
    }
  }

  /**************   FLEXI DATE SELECTION ************/
  FlexiDateSelector = (zcOptions) => {
    const data = this.getZCData(zcOptions, 'FlexiDate');
    if (data && data.available) {
      const { amount, lastFCDate, selected } = data;
      return (

        <TouchableOpacity activeOpacity={0.7} style={[styles.radioBtn]} onPress={() => {
          if (this.state.radiobtn !== 'FlexiDate') {
            this.props.toggleZC('FlexiDate', 'APPLY');
          }
        }}>

          <View style={styles.Radio}>
            <View style={[this.state.radiobtn === 'FlexiDate' ? styles.RadioInside : '']}/>
          </View>
          <View style={styles.radioText}>
            <View style={[AtomicCss.flexRow, AtomicCss.spaceBetween]}>
              <View style={{flex: 3}}>
                <Image style={styles.FDIcon} source={FDIcon}/>
                  <Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.regularFont]}>One free date change allowed{'\n'}before {this.getFormattedDate(lastFCDate)}.</Text>
                  <TouchableOpacity onPress={() => this.props.togglePopup(DATE_CHANGE_OVERLAY)}>
                    <Text style={[AtomicCss.azure, AtomicCss.boldFont]}>Know More</Text>
                  </TouchableOpacity>
              </View>
              <View style={{flex: 1, alignItems: 'flex-end', marginEnd: 26}}>
                <Text style={[styles.optionName, AtomicCss.marginBottom8]}><Text style={[AtomicCss.blackFont, AtomicCss.blackText, AtomicCss.font14]}>{rupeeFormatterUtils(amount)}</Text></Text>
                {this.state.radiobtn === 'FlexiDate' && <TouchableOpacity onPress={() => {this.props.toggleZC('FlexiDate', 'REMOVE');}}><Text
                  style={[AtomicCss.font10, AtomicCss.azure, AtomicCss.boldFont]}>{'REMOVE'}</Text></TouchableOpacity>}
              </View>
            </View>
          </View>
        </TouchableOpacity>);
    } else {
      return [];
    }
  }

  render () {
    const { zcOptions } = this.props.penaltyDetail;
    const { roomDetails } = this.props;

    return (
      <View style={styles.zcSectionWrapper}>
        {this.ZeroCancellationSelector(zcOptions)}
        {isFlexiDateAvailable(zcOptions) && isZCAvailable(zcOptions) &&
        <View style={styles.radioSeparator}/>}
        {this.FlexiDateSelector(zcOptions)}
      </View>
    );
  }

  getFormattedDate = (date) => {
    if (date) {
      return fillDateAndTime(date, 'DD MMM YYYY');
    }
    return '';
  }
}

const styles = StyleSheet.create({
  FDIcon: { width: 96, height: 24, marginBottom: 6 },
  zcSectionWrapper: { paddingLeft: 26, paddingTop: 11, paddingBottom: 7 },
  ZCIcon: { width: 126, height: 23, marginBottom: 6 },

  Radio: {
    width: 20,
    height: 20,
    backgroundColor: '#fff',
    borderRadius: 20,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#008cff',
    marginRight: 8,

  },

  RadioInside: {
    width: 12,
    height: 12,
    backgroundColor: '#008cff',
    borderRadius: 20,
    overflow: 'hidden',
    marginLeft: 3,
    marginTop: 3,
  },
  radioText: {
    flex: 1,
  },
  radioBtn: {
    marginTop: 4,
    flexDirection: 'row',
    backgroundColor: '#fff',

  },
  radioSeparator: { borderBottomWidth: 1, borderBottomColor: '#9b9b9b', paddingBottom: 11,  marginBottom: 14, marginRight: 26  },
  optionName: {
    fontFamily: 'Lato-Regular',
    color: '#9b9b9b',
    fontSize: 10,
  },
  bold: {
    fontFamily: 'Lato-Bold',
  },
});

const mapStateToProps = state => ({
  ...state.holidaysReview,
});

export default connect(mapStateToProps, null)(ZCSection);
