import React from 'react';
import { View } from 'react-native';
import Dots from './Dots';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

export default class CarousalDots extends React.Component {
    render() {
          const { options, active, handleChange} = this.props;
    return (
        <View style={AtomicCss.flexRow}>
            {
            options.map((option, index) =>
            <Dots active={active === index} key={option} label={option.nights} onToggle={() => handleChange(index)} />
            )
            }
        </View>

    );
}
}
