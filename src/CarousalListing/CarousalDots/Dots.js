import React, { Component } from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';


class Dots extends Component {
    render() {
        const {active, onToggle } = this.props;
        const activeBtnOpacity = 0.7;
        const containerStyle = [styles.container];
        if (active) {
            containerStyle.push(styles.activeContainer);
        }
        return (
            <TouchableOpacity style={containerStyle} onPress={onToggle} activeOpacity={activeBtnOpacity} />
        );
    }
}

const styles = StyleSheet.create({
    container: {
        borderRadius: 4,
        marginRight: 5    ,
        backgroundColor: '#fff',
        width:5,
        height:5,
        justifyContent:'center',
        alignItems:'center',
    },
    activeContainer: {
        backgroundColor: '#008cff',
    },
});

export default Dots;
