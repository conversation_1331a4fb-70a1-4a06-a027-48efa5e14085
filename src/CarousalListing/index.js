import React from 'react';
import {View, ScrollView, Image, StyleSheet, Text} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import CarousalDots from './CarousalDots';
import {getHotelRatingColor, DEVICE_WINDOW} from '../utils/HolidayUtils';

export default class ListingTopSlider extends React.Component {
  constructor(props) {
    super(props);
    // console.log(`VIVEK props.hotel ${JSON.stringify(props.hotel)}`);
    this.state = {
      active: 0,
      containerWidth: DEVICE_WINDOW.width,
    };
    this.scrollAmount = 0;
  }
   onNightChange = (index) => {
     this.setState({active: index}, () => {
       this.sliderChange();
     });
   }


    sliderChange=() => {
      this.scrollAmount = this.state.containerWidth * this.state.active;
      this.refs.slider.scrollTo({x: this.scrollAmount, animated: true});
    }
    slideScroll=(event) => {
      const scrollIndex = event.nativeEvent.contentOffset.x / this.state.containerWidth;
      this.setState({active: scrollIndex});
    }
    render() {
      const {hotelInformation = {}, mmtRatingInfo = {}, imageInfo = {}} = this.props.hotel;
      const {mmtAssured = false} = hotelInformation;
      const {reviewCount = '', userRating = ''} = mmtRatingInfo;
      const {imageDataList = []} = imageInfo;
      const userRatingStyle = this.getStyle(parseFloat(userRating));
      return (
        <ScrollView onMomentumScrollEnd={this.slideScroll} ref="slider" pagingEnabled horizontal showsHorizontalScrollIndicator={false}>
          {
            imageDataList && imageDataList.length > 0 && imageDataList.map(item =>
                (<View style={{backgroundColor: '#fff'}}>
                {
                  imageDataList && imageDataList.length > 1 &&
                  <View style={styles.sliderDots}>
                    <CarousalDots
                      active={this.state.active}
                      options={imageDataList}
                      handleChange={this.onNightChange}
                    />
                  </View>
                }
                <View style={{marginHorizontal: 16, paddingTop: 16, flex: 1}}>
                  <Image style={[styles.experienceimg, {width: this.state.containerWidth - 32}]} source={item.mainImage ? {uri: item.mainImage.path} : require('@mmt/legacy-assets/src/no_package_default.webp')} />
                </View>
                {
                  reviewCount !== '' && userRating !== '' &&
                  <View style={userRatingStyle.ratingReview}>
                    <Text style={[AtomicCss.font10, AtomicCss.regularFont, AtomicCss.whiteText]}>
                      <Text style={[AtomicCss.boldFont, AtomicCss.font12]}>
                        {userRating}/5
                      </Text>
                      ({reviewCount} Reviews)
                    </Text>
                  </View>
                }
                <View style={styles.mmtAssuredWrapper}>
                  {
                    mmtAssured &&
                    mmtAssured === true &&
                    <Image style={styles.mmtAssured} source={require('./images/mmtAssured.webp')} />
                  }
                </View>
              </View>))
          }
        </ScrollView>
      );
    }

    getStyle = (rating) => {
      const style = StyleSheet.create({
        ratingReview: {
          backgroundColor: getHotelRatingColor(rating), alignSelf: 'flex-start', position: 'absolute', bottom: 0, left: 16, borderBottomLeftRadius: 4, borderTopRightRadius: 4, paddingHorizontal: 7, height: 21, justifyContent: 'center',
        },
      });
      return style;
    }
}

const styles = StyleSheet.create({

  experienceimg: {
    height: 153, borderRadius: 4,
  },
  mmtAssured: {width: 99, height: 18},
  alignSelfStart: {alignSelf: 'flex-start'},
  sliderDots: {
    position: 'absolute', bottom: 10, left: '42%', zIndex: 2,
  },
  mmtAssuredWrapper: {position: 'absolute', top: 28, left: 16},
});
