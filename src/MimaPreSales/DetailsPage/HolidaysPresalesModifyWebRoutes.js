import React from 'react';
import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import {connect} from 'react-redux';
import {injectAsyncReducer} from '@mmt/legacy-commons/AppState/asyncStore';
import {withRouterState} from 'web/WebRouter';
import HolidaysWebRoute from '../../HolidayWebRoute';
import HolidayDeeplinkParser from '../../utils/HolidayDeeplinkParser';
import MimaPreSalesEditDetailContainer from '../Containers/MimaPreSalesEditDetailContainer';
import mimaPreSalesEditDetailReducer from '../reducer/MimaPreSalesEditDetailReducer';
import {isReturningBackRaw} from '../../utils/HolidayUtils';
import holidayHotelListingReducer from '../../ListingHotelNew/Reducers/HolidayHotelListingReducer';
import holidaysDetailOverlays from '../../PhoenixDetail/Components/DetailOverlays/Redux/DetailOverlaysReducer';
import holidaysReview from '../../Review/Reducers/HolidayReviewReducers';
import holidaysFlightOverlay from '../../PhoenixDetail/Reducers/HolidayFlightOverlayReducer';
import holidaysActivityDetail from '../../PhoenixDetail/Reducers/PhoenixActivityDetailReducer';
import holidaysActivityOverlay from '../../PhoenixDetail/Reducers/PhoenixActivityOverlayReducer';
import travelTidbitsReducer from '../../PhoenixDetail/Reducers/HolidayTidbitsReducer';
class HolidaysPresalesModifyWeb extends HolidaysWebRoute {

    constructor(props) {
        super(props, 'holidaysPresalesModify');
        let holidaysDetailData = props.holidaysDetailData ? props.holidaysDetailData : {destinationDetail : {}};
        const parsedParams = HolidayDeeplinkParser.parseDetailPageDeeplink(window.location.href);
        holidaysDetailData = {
            ...parsedParams,
            ...holidaysDetailData,
            ticketId: props.ticketId,
        };
        if (!holidaysDetailData.fromDeepLink) {
            holidaysDetailData.images = isReturningBackRaw(props) ? [] : holidaysDetailData.images;
        }
        this.state = {
            holidaysDetailData: holidaysDetailData,
        };
    }

    render() {
        return (
            <MimaPreSalesEditDetailContainer {...this.state} />
        );
    }
}
const mapStateToProps = state => ({
    ...state.mimaPreSalesEditDetailReducer,
});
injectAsyncReducer('mimaPreSalesEditDetailReducer', mimaPreSalesEditDetailReducer);
injectAsyncReducer('holidaysDetailOverlays',holidaysDetailOverlays);
injectAsyncReducer('holidaysReview', holidaysReview);
injectAsyncReducer('holidaysFlightOverlay', holidaysFlightOverlay);
injectAsyncReducer('holidayHotelListingReducer',holidayHotelListingReducer);
injectAsyncReducer('holidaysActivityDetail',holidaysActivityDetail);
injectAsyncReducer('holidaysActivityOverlay',holidaysActivityOverlay);
injectAsyncReducer('travelTidbitsReducer', travelTidbitsReducer);
export const HolidaysPresalesDetailContainer = connect(mapStateToProps, null)(HolidaysPresalesModifyWeb);

const HolidaysPresalesDetailRoutes = () => (
    <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
        <Switch>
            <Route exact path="/holidays/psm/quotes/edit" component={withRouterState(HolidaysPresalesDetailContainer)}/>
        </Switch>
    </View>
);

export default withRouter(HolidaysPresalesDetailRoutes);
