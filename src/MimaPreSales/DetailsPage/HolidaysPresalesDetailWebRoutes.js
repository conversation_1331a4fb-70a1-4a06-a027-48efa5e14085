import React from 'react';
import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import {connect} from 'react-redux';
import {injectAsyncReducer} from '@mmt/legacy-commons/AppState/asyncStore';
import {withRouterState} from 'web/WebRouter';
import HolidaysWebRoute from '../../HolidayWebRoute';
import HolidayDeeplinkParser from '../../utils/HolidayDeeplinkParser';
import MimaPresalesContainer from '../Containers/MimaPreSalesContainer';
import holidaysDetail from '../../PhoenixDetail/Reducers/HolidayDetailReducers';
import {isReturningBackRaw} from '../../utils/HolidayUtils';
//import HolidaysFlightOverlay from '../../PhoenixDetail/HolidaysFlightOverlay';
import holidayHotelListingReducer from '../../ListingHotelNew/Reducers/HolidayHotelListingReducer';
import holidaysDetailOverlays from '../../PhoenixDetail/Components/DetailOverlays/Redux/DetailOverlaysReducer';
import holidaysReview from '../../Review/Reducers/HolidayReviewReducers';
import holidaysFlightOverlay from '../../PhoenixDetail/Reducers/HolidayFlightOverlayReducer';
import feedbackReducer from '../../Common/Components/Feedback/redux/reducer';
import travelTidbitsReducer from '../../PhoenixDetail/Reducers/HolidayTidbitsReducer';
import {FEEDBACK} from '../../HolidayConstants';

class HolidaysPresalesDetailWeb extends HolidaysWebRoute {

    constructor(props) {
        super(props, 'holidaysPresalesDetail');
        let holidaysDetailData = props.holidaysDetailData ? props.holidaysDetailData : {destinationDetail : {}};
        const parsedParams = HolidayDeeplinkParser.parseDetailPageDeeplink(window.location.href);
        holidaysDetailData = {
            ...parsedParams,
            ...holidaysDetailData,
        };
        if (!holidaysDetailData.fromDeepLink) {
            holidaysDetailData.images = isReturningBackRaw(props) ? [] : holidaysDetailData.images;
        }
        this.state = {
            holidaysDetailData: holidaysDetailData,
        };
    }

    render() {
        return (
            <MimaPresalesContainer {...this.state} />
        );
    }
}

const mapStateToProps = state => ({
    ...state.holidaysDetail,
});

injectAsyncReducer('holidaysDetail', holidaysDetail);
injectAsyncReducer('holidayHotelListingReducer',holidayHotelListingReducer);
injectAsyncReducer('holidaysDetailOverlays',holidaysDetailOverlays);
injectAsyncReducer('holidaysReview', holidaysReview);
injectAsyncReducer('holidaysFlightOverlay', holidaysFlightOverlay);
injectAsyncReducer('travelTidbitsReducer', travelTidbitsReducer);
injectAsyncReducer(FEEDBACK,feedbackReducer);

export const HolidaysPresalesDetailContainer = connect(mapStateToProps, null)(HolidaysPresalesDetailWeb);

const HolidaysPresalesDetailRoutes = () => (
    <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
        <Switch>
            <Route exact path="/holidays/psm/quotes/detail" component={withRouterState(HolidaysPresalesDetailContainer)}/>
        </Switch>
    </View>
);

export default withRouter(HolidaysPresalesDetailRoutes);
