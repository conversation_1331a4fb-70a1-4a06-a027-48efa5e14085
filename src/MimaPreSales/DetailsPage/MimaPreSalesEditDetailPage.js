import React, { Component } from 'react';
import {
    <PERSON><PERSON>,
    Animated,
    DeviceEventEmitter,
    FlatList,
    LayoutAnimation,
    NativeModules,
    Platform,
    StyleSheet,
    View,
} from 'react-native';
import {
    componentImageTypes,
    deepLinkParams,
    DEFAULT_TRAVELLER_COUNT,
    DETAIL_LOCAL_NOTIFICATION_PAGE_NAME,
    DETAIL_QUERY_PAGE_NAME,
    DETAIL_TRACKING_PAGE_NAME,
    detailReviewFailure,
    extraInfoRefs,
    itineraryUnitTypes,
    overlays,
    packageActionComponent,
    packageActions,
    PDTConstants,
} from '../../PhoenixDetail/DetailConstants';
import { cloneDeep, isEmpty } from 'lodash';
import {
    createChatID,
    createCuesSteps,
    createRandomString,
    doCall,
    doQuery,
    exitLocalNotification,
    getPaxConfig,
    getReturnUrl,
    hasOnBoardingCuesLastVisit,
    isEmptyString,
    isIosClient,
    isMobileClient,
    isNotNullAndEmptyCollection,
    isOnBoardingCuesDelayOver,
    isRawClient,
    openSeoQueryDeepLink,
    removeCuesStepsShown,
    saveHolMeta,
    sharePackage,
    startReactChat,
} from '../../utils/HolidayUtils';

import {
    DOM_BRANCH,
    FUNNEL_ENTRY_TYPES,
    HLD_CUES_POKUS_KEYS,
    PDT_PAGE_ENTRY_EVENT,
    PDT_PAGE_EXIT_EVENT,
    WEEKEND_GETAWAY_PAGE_TYPE,
} from '../../HolidayConstants';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import {
    addPersuasionToDetailData,
    calculateTravellersCount,
    createLoggingMap,
    createRoomDetailsFromApi,
    createRoomDetailsFromRoomDataForPhoenix,
    createTravellerObjForLoader,
    fetchErrorMessage,
    getActionData,
    getEventName,
    isTourManagerExist,
    openChangeHotelFromPhoenixPage,
    toggleDayPlan,
    updateEMIPriceForPersuasion,
} from '../../PhoenixDetail/Utils/HolidayDetailUtils';
import DetailPageHeader from '../../PhoenixDetail/Components/PageHeader';
import { getEvar108ForPSM, trackDetailsClickEvent } from '../../utils/HolidayTrackingUtils';
import LightHeader from '../Components/LightHeader';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import HolidayDetailLoader from '../../PhoenixDetail/Components/HolidayDetailLoader';
import DateSection from '../../PhoenixDetail/Components/DayPlan/dateSection';
import {
    createChildAgeArrayFromApi,
    createChildAgeArrayFromRoomDataForPhoenix,
    createDestinationMap,
    createFlightRequestParams,
    createSightSeeingDayPlanDataMap,
    createStaticItineraryData,
    createSubtitleData,
    getComponentAccessRestrictions,
    extractDataForPDTFromDetailResponse,
    getActivityExtraData,
    getDates,
    getFilteredPackageFeatures,
    getHotelObject,
    getOptimizedPackageDetail,
    getPackageFeatureByKey,
} from '../../PhoenixDetail/Utils/PhoenixDetailUtils';
import DayPlan from '../../PhoenixDetail/Components/DayPlan';
import { TABS } from '../../PhoenixDetail/Components/DayPlan/Sorter';
import BlackFooter from '../../PhoenixDetail/Components/BlackFooter';
import OfferOverlay from '../../DetailMimaComponents/OffersOverlayComponent/OfferOverlay';
import PricePersuasionOverlay from '../../PhoenixDetail/Components/PricePersuasionOverlay';
import { showShortToast } from '@mmt/legacy-commons/Common/Components/Toast';
import CustomizationPopup from '../../PhoenixDetail/CustomizationPopup';
import {AbConfigKeyMappings,  initAbConfig } from '@mmt/legacy-commons/Native/AbConfig/AbConfigModule';
import FloatingWidget from '../../PhoenixDetail/Components/FloatingWidget';
import { setTrackingData } from '../../PhoenixDetail/Utils/PhoenixDetailTracking';
import PackageUpdatedToast from '../../PhoenixDetail/Components/PackageUpdatedToast';
import ReviewFailureHandlingPopup from '../../PhoenixDetail/Components/ReviewFailureHandlingPopup';
import PerformanceMonitorModule from '@mmt/legacy-commons/Native/PerformanceMonitorModule';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import BottomSheet from '../../PhoenixDetail/Components/BottomSheet/BottomSheet';
import SelectOptionPopUp from '../Components/Popup/SelectOptionPopUp';
import { fetchAgentStatus, validateAddons } from '../../utils/HolidayNetworkUtils';
import { sectionCodes } from '../../LandingNew/LandingConstants';
import SaveAsNewPlanPopup from './SaveAsNewPlanPopup';
import ExpiredPlanPopup from '../Components/Popup/ExpiredPlanPopup';
import successIcon from '@mmt/legacy-assets/src/ic_home_holidays.webp';
import {trackDetailsLoadEvent, trackOmniClickEvent} from '../utils/MimaPreSalesTrackingUtils';
import { populateDetailsParams } from '../utils/MimaPreSalesOmnitureUtils';
import FullPageError from '../../Common/Components/FullPageError';
import FeatureList from '../../PhoenixDetail/Components/FDFeatureEdit/FeatureList';
import {
    clearCuesStepPositions,
    getValidCuesSteps,
    updatedCuesSteps,
} from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCueStepsUtils';
import HolidayCancellationOverlayV2 from '../../PhoenixDetail/Components/HolidayCancellationOverlayV2';
import {
    getCuesConfig,
    getHolShowStoryMob,
    getMaxUndoAllowed,
    getPokusForNewDetailContent,
} from '../../utils/HolidaysPokusUtils';
import FDFeatureListV2 from '../../PhoenixDetail/Components/PackageInfoSection/FDFeatureListV2';
import FDFeatureEditOverlayV2 from '../../PhoenixDetail/Components/FDFeatureEditOverlayV2';
import VisaContainer from '../../Common/Components/visa';
import { PACKAGE_FEATURES } from '../../PhoenixDetail/Utils/PheonixDetailPageConstants';
import { Overlay } from '../../PhoenixDetail/Components/DetailOverlays/OverlayConstants';
import { getPersuasionV2, persuasions } from '../../PhoenixDetail/Utils/PersuationUtils';
import { TRACKING_EVENTS } from '../../HolidayTrackingConstants';
import DetailOverlays from "../../PhoenixDetail/Components/DetailOverlays";
import { logPhoenixDetailPDTEvents } from '../../utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import withBackHandler from '../../hooks/withBackHandler';
import HolidayPDTMetaIDHolder from '../../utils/HolidayPDTMetaIDHolder';

import { holidayColors } from '../../Styles/holidayColors';
import AddonsUpdatePopup from '../Components/AddonsUpdatePopup';
import { ADDON_TRACKING_VALUES } from '../../Common/Components/VisaProtectionPlan/VPPConstant';
import ValidationSummary from '../../Common/Components/ValidationSummary';

const VIEW_STATE_LOADING = 'loading';
const VIEW_STATE_NO_NETWORK = 'no_network';
const VIEW_STATE_SUCCESS = 'success';
const VIEW_STATE_ERROR = 'error';
const CONTACT_EXPERT = 'contact_expert';
const OVERLAY_HEADING = 'Chat/Call to modify this travel plan';
const OKAY = 'OKAY';
const LOGIN_EVENT_DETAIL = 'login_event_detail';

let loginEventDetailListener;
class MimaPreSalesEditDetailPage extends Component {
    flatListRef = React.createRef();
    scrollNode = null;
    activityFailedScrollRef = React.createRef();
    canScrollCalender = false;
    constructor(props) {
        super(props, 'detail');
        PerformanceMonitorModule.start('MimaPreSalesEditDetailPage');
        this.replaced = !!this.props.replaced;

        // if (this.props[deepLinkParams.deepLink]) {
        //     this.holidayDetailData = HolidayDeeplinkParser.parseDetailPageDeeplink(
        //         this.props[deepLinkParams.query],
        //         true,
        //     );
        // }
        // else {
            this.holidayDetailData = cloneDeep(this.props.holidaysDetailData);
        // }
        this.holidayDetailData.isWG = this.holidayDetailData.pt === WEEKEND_GETAWAY_PAGE_TYPE;
        this.dynamicCuesSteps = [];
        this.dynamicCoachMarkTimeOut = null;
        this.showNewContentUI = false;
        this.cuesConfig = {};
        this.state = {
            showSearch: false,
            showPageHeader: false,
            fixed: false,
            fab: false,
            popupData: {},
            cityId: '',
            popup: overlays.NONE,
            sectionToShow: '',
            openHalf: false,
            videoPaused: false,
            showActivityDetailForDay: false,
            showActivityDetailForInclusion: false,
            showStoryPage: false,
            isItineraryVisible: true,
            currentActivePlanOnItinerary: TABS.DAY,
            openFDFeatureEditVisibility: false,
            undoStack: [],
            isUndoVisible: false,
            fabTextShrinked: false,
            showPackageUpdatedToast: false,
            reviewError: {},
            viewState:'',
            isOnline:false,
            agentData:{},
            expertModalVisibility:false,
            expertActiveOverlay:'',
            proceedToBookPopup:false,
            quoteRequestId:null,
            showSavePopUp: false,
            saveError:{},
            showCoachMarks: false,
            showDynamicCoachMarks: false,
            showAddonsUpdatePopup: false,
            addonsValidationDetail:{},
            fromSavePlanFLow: false,
        };
        this.packageDetailDTO = {
            DFD: false,
            cmp: this.holidayDetailData.cmp ? this.holidayDetailData.cmp : 'detail_share',
            searchCriteria: this.holidayDetailData.searchCriteria,
            dynamicPackageId: this.holidayDetailData.dynamicPackageId,
            pageType: this.holidayDetailData.pt ? this.holidayDetailData.pt : '',
        };
        if (isEmpty(this.holidayDetailData.cmp) && !isEmpty(this.props.campaign)) {
            this.holidayDetailData.cmp = this.props.campaign;
        }
        HolidayDataHolder.getInstance().setCmp(this.holidayDetailData.cmp);
        HolidayDataHolder.getInstance().setCampaign(this.props.campaign);
        this.cusCountLimit = 3;
        if (isNotNullAndEmptyCollection(this.holidayDetailData.rooms)) {
            this.roomDetails = createRoomDetailsFromApi(this.holidayDetailData.rooms);
        } else {
            this.roomDetails = [
                {
                    noOfAdults: DEFAULT_TRAVELLER_COUNT,
                    noOfChildrenWB: 0,
                    noOfInfants: 0,
                    listOfAgeOfChildrenWB: [],
                    listOfAgeOfChildrenWOB: [],
                    noOfChildrenWOB: 0,
                },
            ];
        }
        const detailData = {
            requestId: createRandomString(),
            holidayDetailData: this.holidayDetailData,
            cmp: this.holidayDetailData.cmp,
            isWG: this.holidayDetailData.isWG,
        };
        detailData.pageDataMap = createLoggingMap(detailData, []);
        HolidayDataHolder.getInstance().setCurrentPage('holidaysDetail');
        trackDetailsLoadEvent({
            logOmni: false,
            omniPageName: DETAIL_TRACKING_PAGE_NAME,
            pdtData: {
              pageDataMap: detailData ? detailData.pageDataMap : {},
              interventionDetails: {},
              activity: PDTConstants.PDT_RAW_EVENT,
              event: PDT_PAGE_ENTRY_EVENT,
              requestId: createRandomString(),
              branch: '',
            },
          });
        saveHolMeta(this.holidayDetailData.isWG, this.holidayDetailData.aff, this.holidayDetailData.pt, this.holidayDetailData.cmp);
        this.calenderRef = React.createRef();
    }

    updatePackage = (packageId) => {
        this.toggleFDFeatureBottomSheet(false);
        this.refreshDetails(false, false, packageId, false);
    };
    updateInPDTRequestIdV2 = () => {
        HolidayPDTMetaIDHolder.getInstance().setPdtId();
     }
    updateMeal = (mealCode) => {
        this.toggleFDFeatureBottomSheet(false);
        const actionData = {
            action: packageActions.CHANGE,
            dynamicPackageId: this.packageDetailDTO.dynamicPackageId,
            mealCode: mealCode,
        };
        this.updateInPDTRequestIdV2();
        this.props.changeMeal(actionData, this.onPackageComponentToggled, this.onApiError);
    };

    updateVisa = (visaIncluded) => {
        this.toggleFDFeatureBottomSheet(false);
        this.setState({ reviewError: {}, showReviewPopUp: false ,saveError:{},showSavePopUp:false });
        this.checkAndUpdateReviewError(packageActionComponent.VISA);
        this.checkAndUpdatePrePaymentError({}, packageActionComponent.VISA);
        const actionData = {
            action: packageActions.TOGGLE,
            dynamicPackageId: this.packageDetailDTO.dynamicPackageId,
            removed: !visaIncluded,
        };
        this.updateInPDTRequestIdV2();
        this.props.togglePackageComponent(
            actionData,
            this.onPackageComponentToggled,
            this.onApiError,
            packageActionComponent.VISA,
        );
    };

    onAddonSelected = (response, addonType, action,addonDetails) => {
        if (response) {
            const priceChange =`-${addonDetails?.addonPrice}`
            this.pushToStack(priceChange, addonType, action);
            this.togglePopup('');
            this.props.refreshEditDetailPageWithResponse(this.packageDetailDTO, response, this.holidayDetailData, this.roomDetails);
            this.setPackageUpdatedToastState(true);
        }
    };

    toggleDayPlan = (plan) => {
        this.trackLocalClickEvent('inclusion_', plan);
        this.setState({ currentActivePlanOnItinerary: plan });

    };
    static getDerivedStateFromProps(props, current_state) {
        if (!current_state.quoteRequestId && current_state.quoteRequestId !== props?.detailData?.quoteRequestId) {
          return {
            ...current_state,
            quoteRequestId: props?.detailData?.quoteRequestId,
          };
        }
        return null;
      }
    async componentDidMount() {
        loginEventDetailListener =
          DeviceEventEmitter &&
          DeviceEventEmitter.addListener(LOGIN_EVENT_DETAIL, this.onLoginEventReceived);
          HolidayDataHolder.getInstance().setFunnelEntry(FUNNEL_ENTRY_TYPES.PSM)
        this.props.clearSaveData();
        this.props.setReviewComponentFailureData(null);
        await this.initAb();
        await HolidayDataHolder.getInstance().setSubFunnel();
        this.refreshDetails(
            false,
            !isEmptyString(this.holidayDetailData.dynamicPackageId),
            null,
            false,
        );
        this.lastPageName = HOLIDAY_ROUTE_KEYS.DETAIL;
        clearCuesStepPositions();
    }
    componentDidUpdate(){
        const activities = this?.newPackageDetail?.activityDetail?.cityActivities?.flatMap(cityActivity => cityActivity?.activities || []);
        const firstUnavailableActivity = activities?.find(activity => activity.unavailable === true);
        if (firstUnavailableActivity && this.activityFailedScrollRef.current) {
          const dayOfFirstUnavailableActivity = firstUnavailableActivity.day;
          this?.scrollToIndexFunc(dayOfFirstUnavailableActivity + 7, -this.activityIndexPerDay?.[dayOfFirstUnavailableActivity] * 150);
       }
    }

    onBackClick = ()=> {
        return this.onBackPressed();
        this.props.clearOverlays();
    }

    refreshDetails = (isChangingDate, isActionApiCalled = false, packageId, undo) => {
        if(!isActionApiCalled){
            this.updateInPDTRequestIdV2();
            }
        this.fetchDetailDataFunc(isChangingDate, isActionApiCalled, packageId, undo);
        this.setState({ fixed: false });
    };

    // Below function removes flight data present in this.props.componentFailureData;
    checkAndUpdatePrePaymentError = async (actionRequest, componentType) => {
        if (isEmpty(this.props.componentFailureData)) {
            return;
        }
        if (
            componentType === componentImageTypes.FLIGHT ||
            componentType === packageActionComponent.FLIGHT
        ) {
            const newReviewError = { ...this.props.componentFailureData };
            if (
                newReviewError &&
                newReviewError.componentErrors &&
                newReviewError.componentErrors.FLIGHT
            ) {
                delete newReviewError.componentErrors.FLIGHT;
                if (
                    newReviewError.componentErrors.HOTEL &&
                    newReviewError.componentErrors.HOTEL.length > 0
                ) {
                    this.props.setReviewComponentFailureData(newReviewError);
                } else {
                    this.props.setReviewComponentFailureData(null);
                }
            }
        } else if (componentType === componentImageTypes.HOTEL) {
            const newReviewError = { ...this.props.componentFailureData };
            const { componentErrors } = newReviewError;
            if (componentErrors && componentErrors.HOTEL && componentErrors.HOTEL.length) {
                componentErrors.HOTEL = componentErrors.HOTEL.filter((item) => {
                    return !(
                        item.sellableId !== actionRequest.sellableId &&
                        item.sellableId === actionRequest.prevSellableId
                    );
                });
                if (componentErrors.HOTEL.length === 0) {
                    delete componentErrors.HOTEL;
                }
                if (isEmpty(componentErrors.HOTEL) && isEmpty(componentErrors.FLIGHT)) {
                    this.props.setReviewComponentFailureData(null);
                } else {
                    this.props.setReviewComponentFailureData(newReviewError);
                }
            }
        }
    };

    checkAndUpdateReviewError = async (componentType) => {
        if (isEmpty(this.state.reviewError)) {
            return;
        }
        const {error} = this.state.reviewError;
        const {errorType} = error || {};
        if (errorType === componentType) {
            this.setState({reviewError: {}});
        }
    }

    /**
     * Common function to update components in the package.
     **/
    onComponentChange = (actionRequest, componentType) => {
        const clonedActionRequest = Object.assign({}, actionRequest);
        clonedActionRequest.requestSource = sectionCodes.PRESALES;
        this.setState({ reviewError: {}, showReviewPopUp: false ,saveError:{},showSavePopUp:false });
        switch (componentType) {
            case componentImageTypes.FLIGHT:
                this.onFlightSelected(clonedActionRequest);
                break;
            case componentImageTypes.HOTEL:
                this.onHotelSelected(clonedActionRequest);
                break;
            case componentImageTypes.TRANSFERS:
                this.onTransferSelected(clonedActionRequest);
                break;
            case componentImageTypes.ACTIVITY:
                this.onActivitySelected(clonedActionRequest);
                break;
            case componentImageTypes.COMMUTE:
                this.onCommuteSelected(clonedActionRequest);
                break;
            default:
                break;
        }
        this.checkAndUpdateReviewError(componentType);
        this.checkAndUpdatePrePaymentError(clonedActionRequest, componentType);
    };

    /**
     * Common function to remove/toggle a component from the package.
     * Same function as used in old detail page
     * */
    onPackageComponentToggle = (add, packageComponent, transferObj) => {
        this.setState({ showReviewPopUp: false });
        const action = { requestSource: sectionCodes.PRESALES };
        action.action = packageActions.TOGGLE;
        if (packageComponent === packageActionComponent.CAR) {
            action.sellableId = transferObj.carItinerary.sellableId;
            action.startDay = transferObj.startDay;
        } else if (packageComponent === packageActionComponent.ACTIVITY) {
            action.removeAll = true;
            action.action = packageActions.REMOVE;
        } else if (packageComponent === packageActionComponent.FLIGHT) {
            this.checkAndUpdateReviewError(packageComponent);
            this.checkAndUpdatePrePaymentError({}, packageActionComponent.FLIGHT);
        }
        action.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
        this.updateInPDTRequestIdV2();
        this.props.togglePackageComponent(
            action,
            this.onPackageComponentToggled,
            this.onApiError,
            packageComponent,
        );
        const actionData = getActionData(add, packageComponent);
        this.actionLoadingText = actionData.actionLoadingText;
    };

    onFlightSelected = (actionRequest) => {
        actionRequest.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
        this.updateInPDTRequestIdV2();
        this.props.changeFlight(actionRequest, this.onPackageComponentToggled, this.onApiError);
        this.actionLoadingText = 'Updating selected flight';
    };

    onHotelSelected = (actionRequest) => {
        actionRequest.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
        this.updateInPDTRequestIdV2();
        this.props.changeHotel(actionRequest, this.onPackageComponentToggled, this.onApiError);
        this.actionLoadingText = 'Updating Hotel';
    };

    onTransferSelected = (actionRequest) => {
        actionRequest.dynamicPackageId = this.packageDetailDTO.dynamicPackageId;
        this.updateInPDTRequestIdV2();
        if (actionRequest.action === packageActions.TOGGLE) {
            this.props.togglePackageComponent(
                actionRequest,
                this.onPackageComponentToggled,
                this.onApiError,
                actionRequest.packageComponent,
            );
            this.actionLoadingText = 'Removing Transfers..';
        }

        if (actionRequest.action === packageActions.CHANGE) {
            this.props.changeTransfer(
                actionRequest,
                actionRequest.packageComponent,
                this.onPackageComponentToggled,
                this.onApiError,
            );
            this.actionLoadingText = 'Updating Transfers..';
        }
    };

    onActivitySelected = (actionRequest) => {
        this.updateInPDTRequestIdV2();
        this.props.modifyActivity(actionRequest, this.onPackageComponentToggled, this.onApiError);
        this.actionLoadingText = 'Updating Activities...';
    };

    onCommuteSelected = (actionRequest) => {
        this.updateInPDTRequestIdV2();
        this.props.modifyCommute(actionRequest, this.onPackageComponentToggled, this.onApiError);
        this.actionLoadingText = 'Updating Commute...';
    }
    onPackageComponentToggled = (response, lob, action) => {
        if (response) {
            if (lob === 'Activity') {
                const { actionResults } = response || {};
                let res = '';
                if (actionResults && actionResults.length > 0) {
                    actionResults.forEach((item) => {
                        res += item.errorMsg + '\n';
                    });
                    showShortToast(res);
                }
            }
            const priceChange =
                response.packageDetail.pricingDetail.categoryPrices[0].discountedPrice -
                this.packageDetailDTO.discountedPrice;
            this.pushToStack(priceChange, lob, action);
            this.togglePopup('');
            this.props.refreshEditDetailPageWithResponse(this.packageDetailDTO, response, this.holidayDetailData, this.roomDetails);
            this.setPackageUpdatedToastState(true);
        }
    };

    pushToStack = (priceChange, lob, action) => {
        const tempStack = [...this.state.undoStack];
        tempStack.push({ priceChange: priceChange, lob: lob, action: action });
        if (tempStack.length > this.cusCountLimit) {
            tempStack.shift();
        }
        this.setState({
            undoStack: tempStack,
        });
    };

    popFromStack = () => {
        const tempStack = [...this.state.undoStack];
        tempStack.pop();
        this.setState({
            undoStack: tempStack,
            isUndoVisible: false,
        });
        this.setState({reviewError: {},saveError:{}});
        this.props.setReviewComponentFailureData({});
        this.updateInPDTRequestIdV2();
        this.refreshDetails(false, false, this.packageDetailDTO.dynamicPackageId, true);
        this.setPackageUpdatedToastState(true);
        logPhoenixDetailPDTEvents({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            value: ADDON_TRACKING_VALUES.UNDO,
        })
    };

    onApiError = (msg, alert, reload) => {
        if (msg) {
            if (alert) {
                Alert.alert('', msg);
            } else {
                showShortToast(msg);
            }
            this.props.isLoading = false;
        }
        if (reload) {
            this.reloadOnError();
        } else {
            this.refreshDetails(false, false, false, false);
        }
    };

    reloadOnError = () => {
        this.packageDetailDTO.dynamicPackageId = '';
        this.refreshDetails(false, false, null, false);
    };

    fetchDetailDataFunc = async (isChangingDate, isActionApiCalled = false, packageId, undo, activityFailurePeek = false) => {
        if (packageId !== null) {
            this.holidayDetailData.packageId = packageId; //to handle update package type in case of FD packages
        }
        const response = await this.props.fetchDetailData(
            this.holidayDetailData,
            this.packageDetailDTO,
            this.packageDetailDTO.dynamicPackageId,
            this.roomDetails,
            isChangingDate,
            isActionApiCalled,
            this.actionLoadingText,
            undo,
            activityFailurePeek,
        );
        /********* Handle onboarding cues **********/
        const hasPageVisitTime = await hasOnBoardingCuesLastVisit(HLD_CUES_POKUS_KEYS.PSM_MODIFY);
        if (hasPageVisitTime) {
        const delayOver = await isOnBoardingCuesDelayOver(HLD_CUES_POKUS_KEYS.PSM_MODIFY);
        if (delayOver) {
            //  clean page keys, since we need to show it again
            await removeCuesStepsShown(HLD_CUES_POKUS_KEYS.PSM_MODIFY);
            await this.showCoachMarksOverlay(response);
        }
        } else {
            await this.showCoachMarksOverlay(response);
        }

        PerformanceMonitorModule.stop();
    };

    onScroll = (event) => {
        if (!isIosClient()) {
          LayoutAnimation.configureNext(LayoutAnimation.create(100, 'easeInEaseOut', 'opacity'));
        }
        const scrollIndex = event.nativeEvent.contentOffset.y;
        // Handle FAB button animation here
        if (scrollIndex > 50 && !this.state.fabTextShrinked) {
            this.setState({
                fabTextShrinked: true,
            });
        }
    };

    prepareDayPlanSections = (packageDetail,sightSeeingMap) => {
        const {currentActivePlanOnItinerary} = this.state;
        if (currentActivePlanOnItinerary === TABS.VISA && this.state.isItineraryVisible) {
            return [
                {
                    id: 'visa',
                    component: [
                        <View style={styles.visaContainer}>
                            <VisaContainer
                              packageContent={this.props.packageContent}
                              visaContentLoading={isEmpty(this.props.packageContent)}
                              dynamicPackageId={this.packageDetailDTO.dynamicPackageId}
                              updateVisa={this.updateVisa}
                              visaPackageFeature={getPackageFeatureByKey({
                                  key: PACKAGE_FEATURES.VISA,
                                  packageFeatures: this.props.detailData.packageDetail.packageFeatures,
                              })}
                            />
                            <View style={styles.seperator} />
                        </View>,
                    ],
                },
            ];
        }
        const { itineraryDetail, metadataDetail, imageDetail, destinationDetail } = packageDetail || {};
        const { images = [] } = imageDetail || {};
        const { bundled = false } = metadataDetail || {};
        const { dynamicItinerary, staticItinerary } = itineraryDetail || {};
        const { dayItineraries = [] } = dynamicItinerary || {};
        const { showOverlay,hideOverlays,clearOverlays } = this.props || {};
        const { duration, destinations } = destinationDetail || {};
        const destinationMap = createDestinationMap(duration, destinations);
        const staticData = createStaticItineraryData(staticItinerary, images);
        const flightReqParams = createFlightRequestParams(packageDetail);
        const sections = [];
        if (dayItineraries && dayItineraries.length > 0) {
            dayItineraries.forEach((data, index) => {
            // Find the index of activity unit type for each day
            let activityIndex = data.itineraryUnits.findIndex((unit) => {
                const { itineraryUnitType } = unit;
                return itineraryUnitType === itineraryUnitTypes.ACTIVITY;
              });
              // Store activity index per day in an object
              if (!this.activityIndexPerDay) {
                this.activityIndexPerDay = {};
              }
              this.activityIndexPerDay[data.day] = activityIndex;
                let indexItem = data.itineraryUnits.findIndex((unit, index) => {
                    const { itineraryUnitType } = unit;
                    return (
                        itineraryUnitType === itineraryUnitTypes.TRANSFERS ||
                        itineraryUnitType === itineraryUnitTypes.CAR
                    );
                });

                if (Object.keys(sightSeeingMap).length > 0 && indexItem === -1) {
                    if (sightSeeingMap[data.day]) {
                        data.itineraryUnits.push(sightSeeingMap[data.day]);
                    }
                } else if (Object.keys(sightSeeingMap).length > 0 && indexItem >= 0) {
                    if (sightSeeingMap[data.day]) {
                        data.itineraryUnits.splice(indexItem + 1, 0, sightSeeingMap[data.day]);
                    }
                }
                let failedHotels = [];
                let ifFlightGroupFailed = false;
                const reviewError = !isEmpty(this.state.reviewError) ? this.state.reviewError : this.state.saveError;
                if (!isEmpty(reviewError)) {
                    if (reviewError.error) {
                        if (
                            reviewError.error.errorType ===
                            detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL
                        ) {
                            if (reviewError.error.errorData) {
                                failedHotels = reviewError.error.errorData.failedHotels || [];
                            }
                        } else if (
                            reviewError.error.errorType ===
                            detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT
                        ) {
                            if (reviewError.error.code) {
                                ifFlightGroupFailed = true;
                            }
                        }
                    }
                } else if (!isEmpty(this.props.componentFailureData)) {
                    const { componentErrors = {} } = this.props.componentFailureData || {};
                    const { FLIGHT = [], HOTEL = [] } = componentErrors;
                    if (FLIGHT && FLIGHT.length > 0) {ifFlightGroupFailed = true;}
                    if (HOTEL && HOTEL.length > 0) {failedHotels = HOTEL;}
                }

                sections.push({
                    id: `DayplanSection_${index}`,
                    component: [
                        <DayPlan
                            failedHotels={failedHotels}
                            ifFlightGroupFailed={ifFlightGroupFailed}
                            data={data}
                            roomDetails={this.roomDetails}
                            staticData={staticData}
                            destinationMap={destinationMap}
                            bundled={bundled}
                            packageDetail={packageDetail}
                            index={index + 1}
                            totalDays={dayItineraries.length}
                            isVisible={this.state.isItineraryVisible}
                            currentActivePlanOnItinerary={this.state.currentActivePlanOnItinerary}
                            togglePopup={this.togglePopup}
                            onComponentChange={this.onComponentChange}
                            onPackageComponentToggle={this.onPackageComponentToggle}
                            packageDetailDTO={this.packageDetailDTO}
                            flightReqParams={flightReqParams}
                            branch={this.props.detailData.branch}
                            trackLocalClickEvent={this.trackLocalClickEvent}
                            trackLocalPageLoadEvent={this.trackLocalPageLoadEvent}
                            lastPageName={this.lastPageName}
                            packageContent={this.props.packageContent}
                            isLoading={this.props.isLoading}
                            detailData={this.props.detailData}
                            hotelDetailLoading={this.props.hotelDetailLoading}
                            fromPresales={true}
                            showOverlay={showOverlay}
                            hideOverlays={hideOverlays}
                            clearOverlays={clearOverlays}
                        />,
                    ],
                });
            });
        }
        return sections;
    };

    scrollToIndexFunc = (index,viewOffset = 125) => {
        if (this.flatListRef) {
            this.canScrollCalender = true;
            setTimeout(() => {
                this?.flatListRef?.scrollToIndex?.({ animated: true, index, viewOffset });
            }, 200);
        }
    };
    onScrollDragBegin = () => {
        this.canScrollCalender = false;
    };

    onViewableItemsChanged = ({ viewableItems, changed }) => {
        if (!this.canScrollCalender) {
            if (this.state.currentActivePlanOnItinerary === TABS.DAY) {
                const index = this.getDaySectionIndex(viewableItems);
                if (this.calenderRef && this.calenderRef.current && index != null) {
                    this.calenderRef.current._updateSelectedDate(index);
                }
            }
        }
    };

    renderItem = ({ item }) => item.component;

    handlePDT = (eventName) => {
        logPhoenixDetailPDTEvents({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            value: eventName,
        })
        this.trackLocalClickEvent(eventName, '');
    };

    editTravelDetails = () => {
        this.setState({ showSearch: false });
        this.setState({ showPageHeader: false });
        this.trackLocalClickEvent(PDTConstants.EDIT_INTENT, '');
        this.togglePopup(overlays.EDIT_OVERLAY);
    };

    trackLocalClickEvent = (eventName, suffix = '', pageName = 'modify') => {
        const { pageDataMap = {} } = this.props.detailData || {};
        const { trackingDetails } = pageDataMap || {};
        const { source, ticketSource} = trackingDetails || {};
        const paramsObject = {
            omniPageName: pageName,
            omniEventName: eventName + suffix,
            suffix: '',
            omniData: {
                [TRACKING_EVENTS.M_V108]: getEvar108ForPSM({
                    pageName,
                    source,
                    ticketSource,
                }),
            },
            pageDataMap: { ...pageDataMap, ticketId: this.props.ticketId },
            eventType: PDTConstants.PDT_RAW_EVENT,
            branch: this.props.detailData?.branch,
            activity: eventName + suffix,
          };
        trackOmniClickEvent(populateDetailsParams,paramsObject);
    };

    trackLocalChatClickEvent = (eventName, suffix, pageName = DETAIL_TRACKING_PAGE_NAME) => {
        trackDetailsClickEvent(
            pageName,
            eventName,
            suffix,
            this.props.detailData.pageDataMap,
            this.props.fabCta ? this.props.fabCta.interventionLoggingDetails : {},
            PDTConstants.PDT_RAW_EVENT,
            suffix,
            createRandomString(),
            this.props.detailData.branch,
        );
    };

    trackLocalPageLoadEvent = (event, logOmni = false, pageName = '') => {
        trackDetailsLoadEvent({
            logOmni,
            omniPageName:  pageName,
            pdtData: {
              pageDataMap: this.props.detailData ? this.props.detailData.pageDataMap : {},
              interventionDetails: this.props.fabCta ? this.props.fabCta.interventionLoggingDetails : {},
              activity: PDTConstants.PDT_RAW_EVENT,
              event: event,
              requestId: createRandomString(),
              branch: this.props.detailData ? this.props.detailData.branch : '',
            },
          });
    };

    togglePopup = (popupName, cityId, sectionToShow, openHalf = false) => {
        const eventName = getEventName(popupName, sectionToShow, this.placesName);
        this.setState({
            popup: popupName,
            popupData: {},
            cityId,
            sectionToShow,
            openHalf,
            videoPaused: true,
            showActivityDetailForInclusion: false,
            activityDetailData: {},
        });
        if (this.props.detailData && this.props.detailData.pageDataMap) {
            trackDetailsClickEvent(
                DETAIL_TRACKING_PAGE_NAME,
                eventName,
                this.props.detailData.pageDataMap,
                this.props.fabCta ? this.props.fabCta.interventionLoggingDetails : {},
                PDTConstants.PDT_RAW_EVENT,
                eventName,
                createRandomString(),
                this.props.detailData.branch,
            );
        }
    };


    addOnAvailable = () => {
        // Check if any packageFeature has type 'ADDON'
        const packageFeatures = this.props.detailData?.packageDetail?.packageFeatures || [];
        // If no ADDON type present, skip validation and go directly to review
        return packageFeatures.some(feature => feature.type === 'ADDON');
    };

    showPopup = async () => {
        this.trackLocalClickEvent('Continue');

        const hasUnsavedChanges = this.state.undoStack?.length > 0;

        // Show save confirmation popup if there are unsaved changes
        if (hasUnsavedChanges) {
            this.setState({ proceedToBookPopup: true });
            return;
        }

        // Proceed with addon validation and review
        await this.validateAndProceed();
    };

    validateAndProceed = async () => {
        // If addons are not available, then no need for validation
        if (!this.addOnAvailable()){
            this.packageReview();
            return;
        }

        const dynamicId = this.props.detailData?.packageDetail?.dynamicId;

        // Validate addons before proceeding
        const cmp = this.holidayDetailData?.cmp || 'email';
        const cmpCreatedTime = this.holidayDetailData?.cmpCreatedTime || Date.now();
        const validationData = await validateAddons(dynamicId, cmp, cmpCreatedTime);

        if (!validationData?.success) {
            // If validation fails or no data, show error.
            showShortToast('Something went wrong. Please try again.');
            return;
        }

        // Check if all addons are valid (CONTINUE action)
        const { addonsValidationDetail = [] } = validationData;
        const invalidAddons = addonsValidationDetail.filter(addon => addon.addonAction !== 'CONTINUE');

        if (isEmpty(invalidAddons)) {
            // if coming from save, as new plan flow save plan
            if (this.handleSavePlanFlow()) {
                return;
            }
            // All addons are valid, proceed to review
            this.packageReview();
        } else {
            // Show addons update popup for invalid addons

            this.setState({
                showAddonsUpdatePopup: true,
                addonsValidationDetail: validationData,
                proceedToBookPopup: false, // Close the save popup when showing addons popup
            });
            logPhoenixDetailPDTEvents({
                actionType: PDT_EVENT_TYPES.contentSeen,
                value: ADDON_TRACKING_VALUES.POPUP_VIEW,
                errorDetails: !isEmpty(invalidAddons) ?  invalidAddons.map(({ addonId, promptMessage }) => ({
                    code: addonId,
                    message: promptMessage?.messageSubHeadings?.[0] || '',
                })):[]
            });
    };
    }


    packageReview=async (reviewRequestSource="DETAILS_PAGE_REVIEW")=>{
        this.setState({ videoPaused: true, proceedToBookPopup:false });
        this.setState({ reviewError: {}, showReviewPopUp: false,saveError:{},showSavePopUp:false });
        if (!isEmpty(this.props.componentFailureData)) {
            this.props.setReviewComponentFailureData({});
        }
        this.props.detailData?.branch === DOM_BRANCH && this.trackLocalClickEvent('modify_proceed','');
        const dynamicPackageId = this.props.detailData.packageDetail.dynamicId;
        if (dynamicPackageId) {
            const holidayReviewData = {};
            holidayReviewData.showChat =
                this.props.fabCta && this.props.fabCta.showChat ? this.props.fabCta.showChat : false;
            holidayReviewData.dynamicPackageId = dynamicPackageId;
            holidayReviewData.searchCriteria = this.holidayDetailData.searchCriteria;
            holidayReviewData.cmp = this.holidayDetailData.cmp;
            holidayReviewData.roomDetails = this.roomDetails;
            holidayReviewData.packageName = this.props.detailData.packageDetail.name;
            holidayReviewData.packageId = this.props.detailData.packageDetail.id;
            holidayReviewData.packageType = this.props.detailData.packageDetail.metadataDetail.packageType;
            holidayReviewData.categoryId = this.holidayDetailData.categoryId;
            holidayReviewData.duration = this.props.detailData.packageDetail.destinationDetail.duration;
            holidayReviewData.branch = this.props.detailData.packageDetail.metadataDetail.branch;
            holidayReviewData.tagDestination = this.props.detailData.packageDetail.tagDestination.name;
            holidayReviewData.isWG = this.props.detailData.isWG;
            holidayReviewData.pt = this.holidayDetailData.pt;
            holidayReviewData.aff = this.holidayDetailData.aff;
            holidayReviewData.reviewType = sectionCodes.PRESALES;
            holidayReviewData.reviewSubtype = sectionCodes.PRESALES_EDIT;
            holidayReviewData.oldQuoteRequestId = this?.state?.quoteRequestId;
            holidayReviewData.source = this.holidayDetailData.source || '';
            this.props.fetchReviewData(
                holidayReviewData,
                holidayReviewData.roomDetails,
                false,
                {},
                null,
                true,
                (reviewData) => {
                    holidayReviewData.quoteRequestId = this?.state?.quoteRequestId;
                    this.goToReview(holidayReviewData, reviewData);
                },
                this.handleReviewFailure,
                true,
                reviewRequestSource
            );
        }
    };

    handleAddonsUpdateAcknowledge = () => {
        // if coming from save, as new plan flow save plan
        if (this.handleSavePlanFlow()) {
            return;
        }
        logPhoenixDetailPDTEvents({
            actionType:PDT_EVENT_TYPES.buttonClicked,
            value:ADDON_TRACKING_VALUES.POPUP_CONTINUE,
            errorDetails:this.state.addonsValidationDetail.addonsValidationDetail.map(({ addonId, promptMessage }) => ({
                code: addonId,
                message: promptMessage?.messageSubHeadings?.[0] || '',
            }))

        })
        // else proceed to review
        this.packageReview();
        this.setState({ showAddonsUpdatePopup: false });
    };

    pageName = () => {
        if (this.props.branch === DOM_BRANCH) {
            return 'mob IN DOM holidays funnel';
        }
        return 'mob IN OBT holidays funnel';
    };

    onBackPressed = () => {
        if (this.state.popup !== '') {
            this.togglePopup('');
        } else {
            if (this.holidayDetailData.refreshLanding) {
                this.holidayDetailData.refreshLanding();
            }
            HolidayNavigation.pop();
        }
        exitLocalNotification(DETAIL_LOCAL_NOTIFICATION_PAGE_NAME);
        this.trackLocalClickEvent('Modify_back', '');
        return true;
    };

    getDaySectionIndex = (viewableItems) => {
        if (viewableItems && viewableItems.length > 0) {
            const { item } = viewableItems[0];
            const { id } = item || {};
            if (id && id.includes('DayplanSection_')) {
                const items = id.split('_');
                if (items.length > 1) {
                    return parseInt(items[1]);
                }
            }
        }
        return null;
    };

    updateDepDate = (newDate) => {
        this.holidayDetailData.departureDetail.departureDate = newDate;
        this.togglePopup('');
        this.refreshDetails(true, false, null, false);
    };

    render() {
        return (
            <View style={{ flex: 1 }}>
                <View style={styles.pageWrap} />
                {(this.props.isLoading || this.props.saveLoading) && this.renderProgressView()}
                {this.props.isError && this.renderError()}
                {!this.props.isLoading && !this.props.saveLoading && this.props.isSuccess && this.props.detailData?.packageDetail && this.renderContent()}
            </View>
        );
    }

    openCustomizationPopup = () => {
        if (this.state.undoStack && this.state.undoStack.length > 0) {
            this.trackLocalClickEvent('expand_customizations', '');
            this.toggleUndoBottomSheet(true);
        }
    };

    checkAndUpdateCurrentActivePlan = () => {
        const { currentActivePlanOnItinerary } = this.state;
        const { detailData } = this.props;
        const { packageDetail } = detailData || {};
        const { packageInclusionsDetail, basePackageInclusionsDetail } = packageDetail || {};
        toggleDayPlan(currentActivePlanOnItinerary, packageInclusionsDetail, this.toggleDayPlan, basePackageInclusionsDetail);
    };

    handleSavePlanFlow = () => {
        if (this.state.fromSavePlanFLow) {
            this.setState({
                fromSavePlanFLow: false,
                showAddonsUpdatePopup: false,
            });
            this.saveFinalQuote();
            return true;
        }
        return false;
    };

    save = () => {
        this.setState({
            proceedToBookPopup: false,
            fromSavePlanFLow: true,
        }, () => {
            // If addons are available, we need to validate addons.
            if (this.addOnAvailable()) {
                this.validateAndProceed();
            } else {
                this.saveFinalQuote();
            }
        });
    };

    saveFinalQuote = () => {
        this.props.saveNewItenary(this.holidayDetailData,this.packageDetailDTO.dynamicPackageId,
          this.props.detailData,this.handleSaveFailure);
        this.props.detailData?.branch === DOM_BRANCH && this.trackLocalClickEvent('Modify_SavePlan');
    };

    renderContent = () => {
        const { detailData, persuasionData, cancellationPolicyData, packageContent } = this.props;
        this.newPackageDetail = getOptimizedPackageDetail( detailData.packageDetail, packageContent );
        const {
            name,
            itineraryDetail,
            departureDetail,
            sightSeeingDetails,
        } = this.newPackageDetail || {};
        const { dynamicItinerary } = itineraryDetail || {};
        const { dayItineraries = [] } = dynamicItinerary || {};
        const { loggedIn, branch } = detailData || {};
        const isShortListed = this.isShortListedPackage();
        const travellerCount = calculateTravellersCount(this.roomDetails);
        const { departureDate, packageDate } = departureDetail || {};
        const calenderDates = getDates(new Date(packageDate), dayItineraries, sightSeeingDetails);
        const { tagDestinationName } = this.fetchTagDestAndBranch();
        let disableBookNowButton = !(isEmpty(this.state.reviewError) && isEmpty(this.state.saveError));
        if (!isEmpty(this.props.componentFailureData)) {
            disableBookNowButton = true;
        }

        if (persuasionData) {
            addPersuasionToDetailData(detailData, persuasionData, cancellationPolicyData);
            updateEMIPriceForPersuasion(persuasionData, this.newPackageDetail);
        }
        this.checkAndUpdateCurrentActivePlan();
        setTrackingData(detailData.pageDataMap, branch, { source: this.holidayDetailData.source });
        const reviewError = !isEmpty(this.state.reviewError) ? this.state.reviewError : this.state.saveError;
        const sightSeeingMap = createSightSeeingDayPlanDataMap(this.props.packageContent,departureDetail);
        const sightseeingCount = Object.keys(sightSeeingMap)?.length;
        const flatListData = [
            { id: '0', component: [this.BasicDetails()] },
            {id: '1', component: <View/>},
            { id: '2', component: [this.getFDFeatureList(detailData)] },
            {id: '3', component: <View/>},
            {id: '4', component: <View/>},
            {id: '5', component: <View/>},
            {
                id: '6',
                component: (
                    <DateSection
                        failedItineraryName={this.getNameOfFailedItinerary(
                            reviewError,
                            this.newPackageDetail,
                        )}
                        reviewError={reviewError}
                        componentFailureData={this.props.componentFailureData}
                        ref={this.calenderRef}
                        isVisible={this.state.isItineraryVisible}
                        onPressHandler={this.scrollToIndexFunc}
                        calenderDates={calenderDates}
                        index={6}
                        toggleDayPlan={this.toggleDayPlan}
                        currentActivePlanOnItinerary={this.state.currentActivePlanOnItinerary}
                        detailData={detailData}
                        componentCount={this.props.componentCount}
                        sightseeingCount={sightseeingCount}
                    />
                ),
            },
            {
                id: '7',
                component: (
                  <ValidationSummary
                    validationSummary={detailData?.packageDetail?.metadataDetail?.validationSummary}
                    containerStyles={styles.messageStripContainer}
                  />
                ),
              },
            ...this.prepareDayPlanSections(this.newPackageDetail,sightSeeingMap),
        ];

        this.flatListDataSize = flatListData.length;
        return (
            <View style={{ flex: 1 }}>
                <Animated.View
                    style={{
                        zIndex: 99999999,
                        elevation: 4,
                    }}
                >
                    <DetailPageHeader
                        heading={name}
                        showSearch={this.state.showSearch}
                        showPageHeader={this.state.showPageHeader}
                        onBackPress={this.onBackPressed}
                        onSharePress={this.onSharePress}
                        onFavPress={this.updateShortListedPackage}
                        isShortListed={isShortListed}
                        editTravelDetails={this.editTravelDetails}
                    />
                </Animated.View>

                {this.state.popup !== '' &&
                this.state.popup === overlays.EDIT_OVERLAY &&
                this.state.popup !== overlays.IMAGE_OVERLAY &&
                createGenericTextualBottomSheet(this.state.popup, this.togglePopup)}

                <FlatList
                    data={flatListData}
                    renderItem={this.renderItem}
                    keyExtractor={(item) => item.id}
                    ref={(ref) => (this.flatListRef = ref)}
                    onScroll={this.onScroll}
                    onScrollToIndexFailed={() => {}}
                    stickyHeaderIndices={[6]}
                    onScrollBeginDrag={this.onScrollDragBegin}
                    onViewableItemsChanged={this.onViewableItemsChanged}
                    viewabilityConfig={{
                        viewAreaCoveragePercentThreshold: 100,
                        waitForInteraction: true,
                    }}
                    style={{ width: '100%' }}
                />

                {this.state.openFDFeatureEditVisibility &&
                    detailData?.packageDetail?.packageFeatures?.length > 0 ? (
                    <FDFeatureEditOverlayV2
                        packageFeatures={getFilteredPackageFeatures(
                        detailData.packageDetail.packageFeatures,
                        )}
                        toggleFDFeatureBottomSheet={this.toggleFDFeatureBottomSheet}
                        isOverlay={true}
                        selectedMealCode={detailData.packageDetail.mealDetail?.meal.mealCode}
                        updateMeal={this.updateMeal}
                        updateVisa={this.updateVisa}
                        dynamicPackageId={this.packageDetailDTO.dynamicPackageId}
                        activeIndex={this.state.activeFeature}
                    />
                    ) : null
                }

                {this.state.isUndoVisible && this.state.undoStack && this.state.undoStack.length > 0 && (
                    <CustomizationPopup
                        popFromStack={this.popFromStack}
                        undoStack={this.state.undoStack}
                        toggleUndoBottomSheet={this.toggleUndoBottomSheet}
                    />
                )}

                <FloatingWidget
                    handleClick={() => this.openCustomizationPopup()}
                    count={this.state.undoStack.length}
                    addDynamicCuesStep={this.addDynamicCuesStep}
                />

                <PackageUpdatedToast
                    showPackageUpdatedToast={this.state.showPackageUpdatedToast}
                    setToastMessageState={this.setPackageUpdatedToastState}
                />

                {!this.state.showDynamicCoachMarks && <BlackFooter
                    togglePopup={this.togglePopup}
                    packageReview={this.showPopup}
                    categoryPrice={this.newPackageDetail.pricingDetail.categoryPrices[0]}
                    dealDetail={this.newPackageDetail.dealDetail}
                    travellerCount={travellerCount}
                    loggedIn={loggedIn}
                    aff={this.holidayDetailData.aff}
                    disableBookNowButton={disableBookNowButton}
                    zIndexVal={this.state.showCoachMarks ? 0 : 2}
                    offerSection = {this.props.offerSection}
                    continueBtn
                />}

                {this.state.popup === overlays.OFFER_OVERLAY && (
                  <OfferOverlay
                    togglePopup={this.togglePopup}
                    dealDetail={this.newPackageDetail.dealDetail}
                    loggedIn={loggedIn}
                    onLoginClicked={this.onLoginClicked}
                    travellerCount={travellerCount}
                    categoryPrice={this.newPackageDetail.pricingDetail.categoryPrices[0]}
                    aff={this.holidayDetailData.aff}
                    applyOfferSection={(offerCode,action) => this.props.applyOfferSection(action,offerCode,this.props.detailData?.packageDetail?.dynamicId)}
                    offerSection = {this.props.offerSection}
                    showOfferHorizontalLoader = {this.props.showOfferHorizontalLoader}
                    emiDetails = {this.newPackageDetail?.emiDetail}
                    getEmiOptions = {() => this.props.getEmiOptions(this.props.detailData?.packageDetail?.dynamicId)}
                    emiOptions = {this.props.emiOptions}
                    trackLocalClickEvent={this.trackLocalClickEvent}
                    persuasion={getPersuasionV2(persuasionData,persuasions.OFFER_SECTION)}/>
                )}

                {this.state.popup === extraInfoRefs.CANCELLATION_POLICY &&
                        this.props?.cancellationPolicyData?.success && (
                            <HolidayCancellationOverlayV2
                            togglePopup={this.togglePopup}
                            trackClickEvent={(event, suffix) => this.trackLocalClickEvent(event, suffix)}
                            />
                )}
                {this.state.expertModalVisibility && this.state.viewState === VIEW_STATE_SUCCESS ? (
                    <BottomSheet
                        title={OVERLAY_HEADING}
                        isCloseBtnVisible
                        onBackPressed={() => {
                        this.setState({
                            expertModalVisibility: false,
                            expertActiveOverlay: '',
                        });
                        }}
                    >
                        <SelectOptionPopUp
                        actionType={this.state.expertActiveOverlay}
                        agentData={this.state.agentData}
                        isOnline={this.state.isOnline}
                        makeChangesMyself={false}
                        trackLocalClickEvent={this.trackLocalClickEvent}
                        destination={tagDestinationName}
                        quoteId={this?.state?.quoteRequestId}
                        ticketId={this.props.ticketId}
                        />
                    </BottomSheet>
                ) : null}
                {this.state.popup === overlays.PRICE_OVERLAY && (
                    <PricePersuasionOverlay
                        togglePopup={this.togglePopup}
                        persuasionData={persuasionData}
                        departureDate={departureDate}
                        updateDepDate={this.updateDepDate}
                    />
                )}
                <DetailOverlays/>
                {/*{this.state.popup === overlays.VISA_OVERLAY && (*/}
                {/*    <VisaOverlay*/}
                {/*        togglePopup={this.togglePopup}*/}
                {/*        packageContent={this.props.packageContent}*/}
                {/*        tourManagerExist={isTourManagerExist(this.newPackageDetail.tourManagerDetail)}*/}
                {/*    />*/}
                {/*)}*/}
                {!this.props.isLoading &&
                    this.showReviewErrorPopup(this.state.reviewError, this.newPackageDetail)}
                {this.state.showCoachMarks && this.renderCoachMarks()}
                {this.state.showDynamicCoachMarks && this.renderDynamicCoachMarks()}
            </View>
        );
    };

    onLoginClicked = () => {
        const { HolidayModule } = NativeModules;
        if (isMobileClient()) {
            HolidayModule.onLoginUserDetail();
        } else {
            HolidayModule.onLoginUserDetail(getReturnUrl(this.packageDetailDTO.dynamicPackageId));
        }
        this.trackLocalClickEvent(PDTConstants.LOGIN, '');
    };

    onLoginEventReceived = (response) => {
        if (response && response.loggedIn) {
            this.refreshDetails(false, false, null, false);
        }
    };
    onSharePress = () => {
        this.trackLocalClickEvent('share', '');
        sharePackage(this.props.detailData.packageDetail);
    };

    isShortListedPackage = () => {
        const { detailData } = this.props;
        const { packageDetail } = detailData || {};
        let isShortListed = false;
        if (packageDetail && this.props.shortListedPackages) {
            isShortListed = this.props.shortListedPackages.has(packageDetail?.id);
        }
        return isShortListed;
    };

    updateShortListedPackage = (isShortList) => {
        this.trackLocalClickEvent('shortlist', '');
        const { detailData } = this.props;
        const { packageDetail } = detailData || {};
        const { id, name } = packageDetail || {};
        if (!this.props.shortListedPackages) {
            this.props.shortListedPackages = new Set();
        }
        if (isShortList) {
            this.props.shortListedPackages.add(id);
        } else {
            this.props.shortListedPackages.delete(id);
        }
        this.props.updateShortListedPackage(id, name, isShortList);
    };

    renderProgressView = () => {
        let openingSavedPackage = false;
        if (this.holidayDetailData.savePackageId && !this.packageDetailDTO.dynamicPackageId) {
            openingSavedPackage = true;
        }
        return (
            <HolidayDetailLoader
                departureCity={this.holidayDetailData.departureDetail.departureCity}
                departureDate={this.holidayDetailData.departureDetail.departureDate}
                duration={this.holidayDetailData.destinationDetail.duration}
                travellerObj={createTravellerObjForLoader(this.roomDetails)}
                openingSavedPackage={openingSavedPackage}
                showDateText={this.props.changingDate}
                changeAction={this.props.changeAction}
                loadingText={this.props.loadingText}
            />
        );
    };

    renderError = () => {
        const errorMessage = fetchErrorMessage(this.props.error);
        let errorActions = [
            {
                displayText: 'Go Back',
                props: {onPress: () => HolidayNavigation.goBack()},
            },
        ];
        errorActions = [{
                displayText: 'REFRESH',
                props: {onPress: () => this.refreshDetails(false, false, null, false)},
            },
            ...errorActions,
        ];
        return <FullPageError subTitle={errorMessage} actions={errorActions} />;
    };

    redirectToListing = () => {
        // performActionOnQuotesListing is used to refresh quotes listing page
        // This handling is specific and needs to be done by caller.
        // http://jira.mmt.com/browse/HLD-11424
        const {performActionOnQuotesListing} = this.props;
        if (performActionOnQuotesListing) {
            performActionOnQuotesListing({refresh: true});
        }

        const {tagDestinationName: tagDestination} = this.fetchTagDestAndBranch();
        const {cmp} = this.holidayDetailData || {};
        const ticketId = this.props?.ticketId || this.holidayDetailData?.ticketId;

        if(isRawClient()) {
            HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.QUOTES_LISTING_PAGE, {
                tagDestination,
                cmp,
                ticketId,
                'from_quotes_deeplink': false,
            });
        } else {
        HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.QUOTES_LISTING_PAGE, {
            tagDestination,
            cmp,
            ticketId,
            'from_quotes_deeplink': false,
        });
    }
        this.trackLocalClickEvent('Modify_saved_success');
    }

    handleAddonsPopupClose = () => {
        this.setState({ showAddonsUpdatePopup: false });
    };

    renderAddonsUpdatePopup = () => {
        const { addonsValidationDetail } = this.state;

        return (
            <AddonsUpdatePopup
                addonsValidationDetail={addonsValidationDetail}
                onClose={this.handleAddonsPopupClose}
                onAcknowledge={this.handleAddonsUpdateAcknowledge}
            />
        );
    };

    BasicDetails = () => {
        return (
            <View>
                <LightHeader onBackPress={this.onBackPressed} fromEditPage/>


                    {this.state.proceedToBookPopup && <BottomSheet isCloseBtnVisible
                        onBackPressed={() => {
                            this.setState({
                            proceedToBookPopup:false,
                            });
                            this.props.detailData?.branch === DOM_BRANCH && this.trackLocalClickEvent('Modify_close','');
                             }}
                             headerStyle={styles.headerStyling}
                             crossButtonStyle={styles.crossButtonStyling}
                             containerStyle={styles.containerStyle}
                      >
                        <SaveAsNewPlanPopup proceed={this.validateAndProceed} saveAsNew={this.save}/>
                    </BottomSheet>
                    }
                    {  isEmpty(this.state.saveError) && !this.props.saveLoading && !isEmpty(this.props.saveData)
                       &&
                       <ExpiredPlanPopup message={this.props.saveData.header}
                       subMessage={this.props.saveData.subHeader}
                       btnText={OKAY}
                       icon={successIcon}
                       onBtnClick={this.redirectToListing}
                       />
                    }
                    {this.state.showAddonsUpdatePopup && this.state.addonsValidationDetail &&  this.renderAddonsUpdatePopup()}
            </View>
        );
    };


    toggleFDFeatureBottomSheet = (visibility) => {
        this.trackLocalClickEvent('edit_fd', '');
        this.setState({
            openFDFeatureEditVisibility: visibility,
        });
    };

    toggleUndoBottomSheet = (visibility) => {
        this.setState({
            isUndoVisible: visibility,
        });
    };

    async initAb() {
        await initAbConfig();
        this.cusCountLimit = getMaxUndoAllowed();
        this.showNewContentUI = getPokusForNewDetailContent(true);
        this.cuesConfig = getCuesConfig();
        const showStoryMob = getHolShowStoryMob();
        if (this.props[deepLinkParams.deepLink]) {
            await setTimeout(() => {
                if (showStoryMob) {
                    this.setState({
                        showStoryAB: true,
                    });
                }
            }, 500);
        } else {
            if (showStoryMob) {
                this.setState({
                    showStoryAB: true,
                });
            }
        }
    }
    addDynamicCuesStep = async ({ cueStepKey }) => {
        const localSteps = require('./MimaPreSalesEditPageCoachMark.json');
        const cuesSteps = await createCuesSteps({
            pokusSteps: this.cuesConfig[HLD_CUES_POKUS_KEYS.PSM_MODIFY],
            localSteps,
            isDynamic: true,
            pageName: HLD_CUES_POKUS_KEYS.PSM_MODIFY,
          });
        this.finalDynamicCuesStep =
          cuesSteps.filter((cueStep) => cueStep.key === cueStepKey)?.[0] || null;

        if (this.finalDynamicCuesStep) {
          this.dynamicCuesSteps.push(this.finalDynamicCuesStep);
        }
        // Check if there are more one cue steps to be shown if yes then add delay to show coach mark
        if (this.dynamicCuesSteps.length > 0) {
          clearTimeout(this.dynamicCoachMarkTimeOut);
          this.dynamicCoachMarkTimeOut = setTimeout(this.showDynamicCoachMarksOverlay, 200);
        } else {
          this.dynamicCoachMarkTimeOut = setTimeout(this.showDynamicCoachMarksOverlay, 200);
        }
      };

    showDynamicCoachMarksOverlay = () => {
        this.setState({
        showDynamicCoachMarks: this.dynamicCuesSteps && this.dynamicCuesSteps.length > 0,
        });
    };

    showCoachMarksOverlay = async () => {
        const localSteps = require('./MimaPreSalesEditPageCoachMark.json');
        this.finalCuesSteps = await createCuesSteps({
          pokusSteps: this.cuesConfig[HLD_CUES_POKUS_KEYS.PSM_MODIFY],
          localSteps,
          pageName: HLD_CUES_POKUS_KEYS.PSM_MODIFY,
        });
        this.finalCuesSteps = getValidCuesSteps(this.finalCuesSteps);
        this.finalCuesSteps = updatedCuesSteps({
            updatedCuesSteps: {
              changeOrRemove: { shape: { top: 125, left: '-2%' }},
            },
            steps: this.finalCuesSteps,
          });
        this.setState({ showCoachMarks: this.finalCuesSteps && this.finalCuesSteps.length > 0 });
    };

    hideCoachMarksOverlay = (isDynamic) => {
        this.setState(
            {
            ...(isDynamic ? { showDynamicCoachMarks: false } : { showCoachMarks: false }),
            showDynamicCoachMarks: false,
            currentActivePlanOnItinerary: TABS.DAY,
            },
            () => {
            setTimeout(() => {
                if (this.flatListRef) {
                this?.flatListRef?.scrollToIndex?.({ animated: true, index: 0 });
                }
            }, 500);
            },
        );
        if (isDynamic) {
            this.dynamicCuesSteps = [];
            clearTimeout(this.dynamicCoachMarkTimeOut);
        }

    };

    handleScrollForCoachMarks = (step) => {
        if (step.key === 'continue') {
            this.flatListRef.scrollToIndex({ animated: true, index: 1 });
        } else if (step.key === 'changeOrRemove') {
         this.flatListRef.scrollToIndex({ animated: true, index: 0, viewOffset: -(step.extraInfo?.fy - 170 - statusBarHeightForIphone)});
        } else if (step.key === 'customizationWidget') {
        this.flatListRef.scrollToIndex({ animated: true, index: 0 });
        }
    };
    renderDynamicCoachMarks = () => {
        if (this.dynamicCuesSteps && this.dynamicCuesSteps.length > 0) {
          const CoachMarks = require('@mmt/legacy-commons/Common/Components/CoachMarks').default;
          const validCuesSteps = getValidCuesSteps(this.dynamicCuesSteps);
          if (validCuesSteps.length === 0) {
            return null;
          }
          return (
            <CoachMarks
              steps={validCuesSteps}
              onDone={() => this.hideCoachMarksOverlay(true)}
              onSkip={() => this.hideCoachMarksOverlay(true)}
              onStart={(step) =>
                setTimeout(() => {
                  this.handleScrollForCoachMarks(step);
                }, 250)
              }
              onStepChange={(step) => this.handleScrollForCoachMarks(step)}
              pageName={HLD_CUES_POKUS_KEYS.PSM_MODIFY}
              trackEvent={this.handlePDT}
            />
          );
        }
        return null;
      };

    renderCoachMarks = () => {
        if (this.finalCuesSteps && this.finalCuesSteps.length) {
          const CoachMarks = require('@mmt/legacy-commons/Common/Components/CoachMarks').default;
          return (
            <CoachMarks
              steps={this.finalCuesSteps}
              onDone={this.hideCoachMarksOverlay}
              onSkip={this.hideCoachMarksOverlay}
              onStart={(step) =>
                setTimeout(() => {
                  this.handleScrollForCoachMarks(step);
                }, 250)
              }
              onStepChange={(step) => this.handleScrollForCoachMarks(step)}
              pageName={HLD_CUES_POKUS_KEYS.PSM_MODIFY}
              trackEvent={this.handlePDT}
            />
          );
        }
        return null;
    };

    getFDFeatureList = (detailData) => {
        if (detailData.packageDetail.packageFeatures)
          {return (
            <View style={{ marginBottom: 5 }}>
              {this.showNewContentUI ? (
                <FDFeatureListV2
                  packageDetail={detailData.packageDetail}
                  updateVisa={this.updateVisa}
                  packageFeatures={detailData.packageDetail.packageFeatures}
                  isOverlay={false}
                  toggleFDFeatureBottomSheet={this.toggleFDFeatureBottomSheet}
                  containerStyles = {{ marginBottom: 10 }}
                  onAddonSelected={this.onAddonSelected}
                  cmp={this.holidayDetailData.cmp}
                  selectAddonAPI={this.props.selectAddonAPI}
                  trackEvent={logPhoenixDetailPDTEvents}
                  showOverlay={this.props.showOverlay}
                  hideOverlays={this.props.hideOverlays}
                />
              ) : (
                <FeatureList
                  packageFeatures={detailData.packageDetail.packageFeatures}
                  isOverlay={false}
                  toggleFDFeatureBottomSheet={this.toggleFDFeatureBottomSheet}
                />
              )}
            </View>
          );}
        return [];
    };

    setPackageUpdatedToastState = (status) => {
        this.setState({
            showPackageUpdatedToast: status,
        });
    };

    trackPageExit = () => {
        trackDetailsLoadEvent({
            logOmni: false,
            omniPageName:  '',
            pdtData: {
              pageDataMap: this.props.detailData ? this.props.detailData.pageDataMap : {},
              interventionDetails: this.props.fabCta ? this.props.fabCta.interventionLoggingDetails : {},
              activity: PDTConstants.PDT_RAW_EVENT,
              event: PDT_PAGE_EXIT_EVENT,
              requestId: createRandomString(),
              branch: this.props.detailData ? this.props.detailData.branch : '',
            },
        });
           const couponListData= this.props?.offerSection?.couponList || [];
           // Implement the new PDT
           const { pricingData } = extractDataForPDTFromDetailResponse(this.props.detailData, couponListData, this.holidayDetailData);

           // Log page exit event with pricing data
           logPhoenixDetailPDTEvents({
               actionType: PDT_EVENT_TYPES.pageExit,
               value: PDT_PAGE_EXIT_EVENT,
               pricingData, // Include pricing data for page exit event
           });
    };

    startCall = (fromIcon) => {
        if (!fromIcon) {
            this.setState({ fab: !this.state.fab });
        }
        const { branchName } = this.fetchTagDestAndBranch();
        doCall(branchName);
        this.handleDefaultFabClick();
        const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
        this.trackLocalClickEvent(eventName, PDTConstants.CALL_SUFFIX);
        this.trackPageExit();
        this.setState({ videoPaused: true });
    };

    startQuery = (fromIcon) => {
        if (!fromIcon) {
            this.setState({ fab: !this.state.fab });
        }
        const {
            tagDestinationName,
            branchName,
            packageId,
            packageName,
            pkgType,
            dynamicPackageId,
        } = this.fetchTagDestAndBranch();
        if (this.holidayDetailData.fromSeo) {
            openSeoQueryDeepLink(tagDestinationName, branchName);
        } else {
            const queryDto = {};
            queryDto.destinationCity = tagDestinationName;
            queryDto.branch = branchName;
            queryDto.packageId = packageId;
            queryDto.packageName = packageName;
            queryDto.pageName = DETAIL_QUERY_PAGE_NAME;
            queryDto.funnelStep = DETAIL_QUERY_PAGE_NAME;
            queryDto.pkgType = pkgType;
            queryDto.dynamicPackageId = dynamicPackageId;
            queryDto.isWG = this.holidayDetailData.isWG;
            queryDto.aff = this.holidayDetailData.aff;
            queryDto.cmp = this.holidayDetailData.cmp ? this.holidayDetailData.cmp : '';
            if (queryDto.cmp === '') {
                queryDto.cmp = this.holidayDetailData.initId ? this.holidayDetailData.initId : '';
            }
            if (this.props.fabCta.formId) {
                queryDto.formId = this.props.fabCta.formId;
            }
            doQuery(queryDto);
        }
        const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
        let querySuffix = PDTConstants.QUERY_SUFFIX;
        if (this.props.fabCta.formId) {
            querySuffix = `${querySuffix}_${this.props.fabCta.formId}`;
        }
        this.trackLocalClickEvent(eventName, querySuffix);
        this.trackPageExit();
        this.setState({ videoPaused: true });
    };

    startChat = (fromIcon) => {
        if (!fromIcon) {
            this.setState({ fab: !this.state.fab });
        }
        const { tagDestinationName, branchName } = this.fetchTagDestAndBranch();
        let cmpValue = this.props.detailData.cmp ? this.props.detailData.cmp : '';
        if (cmpValue === '') {
            cmpValue = this.props.detailData.initId ? this.props.detailData.initId : '';
        }

        const chatIdentifier = createChatID();
        const chatDto = {
            destinationCity: tagDestinationName,
            branch: branchName,
            travelDate: this.props.detailData.pageDataMap.otherDetails.travel_start_date,
            packageId: `${this.props.detailData.packageDetail.id}`,
            dynamicPackageId: this.props.detailData.packageDetail.dynamicId,
            paxConfig: getPaxConfig(this.props.detailData.pageDataMap),
            cmp: cmpValue,
            chatId: chatIdentifier,
            pageName: DETAIL_QUERY_PAGE_NAME,
        };
        startReactChat(chatDto);
        const eventName = fromIcon ? PDTConstants.CONTACT_ICON : PDTConstants.FAB_STRIP;
        this.trackLocalClickEvent(eventName, PDTConstants.CHAT_SUFFIX);

        this.trackPageExit();
        this.setState({ videoPaused: true });
    };


    handleDefaultFabClick = () => {
        this.setState({ fab: !this.state.fab });
        const totalCtasToBeShown =
            (this.props.fabCta.showCall ? 1 : 0) +
            (this.props.fabCta.showQuery ? 1 : 0) +
            (this.props.fabCta.showChat ? 1 : 0);
        if (!this.state.fab && totalCtasToBeShown > 1) {
            this.trackLocalClickEvent(
                'fab',
                `_${this.props.fabCta.showCall ? 'C' : ''}${this.props.fabCta.showQuery ? 'Q' : ''}
        ${this.props.fabCta.showChat ? 'Ch' : ''}${this.props.fabCta.branchLocator ? 'B' : ''}`,
            );
        }
    };

    fetchTagDestAndBranch() {
        let tagDestinationName = '';
        let branchName = DOM_BRANCH;
        let packageId = '';
        let packageName = '';
        let pkgType = '';
        let dynamicPackageId = '';
        if (this.props.detailData && this.props.detailData.packageDetail) {
            tagDestinationName = this.props.detailData.packageDetail.tagDestination.name;
            branchName = this.props.detailData.packageDetail.metadataDetail.branch;
            packageId = this.props.detailData.packageDetail.id;
            packageName = this.props.detailData.packageDetail.name;
            dynamicPackageId = this.props.detailData.packageDetail.dynamicId;
            if (this.props.detailData.packageDetail.metadataDetail) {
                pkgType = this.props.detailData.packageDetail.metadataDetail.packageType;
            }
        } else if (this.holidayDetailData) {
            if (
                this.holidayDetailData.destinationDetail &&
                this.holidayDetailData.destinationDetail.tagDestination
            ) {
                tagDestinationName = this.holidayDetailData.destinationDetail.tagDestination;
            }
            if (this.holidayDetailData.branch) {
                branchName = this.holidayDetailData.branch;
            }
            if (this.holidayDetailData.packageId) {
                packageId = this.holidayDetailData.packageId;
            }
            if (this.holidayDetailData.name) {
                packageName = this.holidayDetailData.name;
            }
        }
        return {
            tagDestinationName,
            branchName,
            packageId,
            packageName,
            pkgType,
            dynamicPackageId,
        };
    }

    goToReview = (holidayReviewData, reviewData) => {
        const { packageConfigDetail } = this.newPackageDetail;
        const { componentAccessRestriction = {} } = packageConfigDetail || {};
        this.trackLocalClickEvent(PDTConstants.BOOK, '');
        this.trackPageExit();
        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.REVIEW, {
            holidayReviewData,
            reviewData,
            fromDetails: true,
            openForwardFlowFromReviewPage: isRawClient() ? undefined : this.openForwardFlowFromReviewPage,
            componentAccessRestriction : isEmpty(componentAccessRestriction) ? getComponentAccessRestrictions() : componentAccessRestriction,
             handleContactExpertClick:isRawClient() ? undefined :this.handleContactExpertClick,
          });
    };
    handleExpertClickFromFailure=()=>{
        this.setState({
          showReviewPopUp:false,
          showSavePopUp:false,
        });
        this.handleContactExpertClick();
      }

      handleContactExpertClick = async () => {
       if (this.state.viewState === VIEW_STATE_LOADING) {
         return;
       }
       const hasNetwork = await isNetworkAvailable();
       if (!hasNetwork) {
         this.setState({
           viewState:VIEW_STATE_NO_NETWORK,
         });
         return;
       }
       this.setState({
         viewState:VIEW_STATE_LOADING,
       });
       const response = await fetchAgentStatus(this.props.agentUserName, 'PRESALES_DETAIL', this.props.ticketId);
       if (!response) {
         this.setState({
           viewState:VIEW_STATE_ERROR,
         });
         return null;
       }
       const { statusCode, success, agentDetail } = response || {};
       const { availability } = agentDetail || '';
       if (statusCode !== 1 || !success) {
         this.setState({
           viewState:VIEW_STATE_ERROR,
         });
         return null;
       }
       this.setState({
         isOnline:availability === 'ONLINE',
         viewState:VIEW_STATE_SUCCESS,
         agentData:agentDetail,
         expertModalVisibility:true,
         expertActiveOverlay:CONTACT_EXPERT,
       });

    };
    showReviewErrorPopup(reviewError, packageDetail) {
        const isReviewError = reviewError && reviewError.error && this.state.showReviewPopUp;
        const saveItenaryError = this.state.saveError && this.state.saveError?.error && this.state.showSavePopUp;
        const errorObject = saveItenaryError ? this?.state?.saveError : reviewError;
        if (isReviewError || saveItenaryError) {
            return (
                <ReviewFailureHandlingPopup
                    type={errorObject.error.errorType}
                    reviewError={errorObject}
                    packageReview={this.packageReview}
                    name={this.getNameOfFailedItinerary(errorObject, packageDetail)}
                    componentAccessRestriction={packageDetail?.packageConfigDetail?.componentAccessRestriction}
                    openCorrespondingListingPage={() =>
                        this.openCorrespondingListingPage(errorObject, packageDetail)
                    }
                    onReviewFailurePopupClosed={() => this.onReviewFailurePopupClosed(errorObject)}
                    openListingPage={this.openListingPage}
                    trackLocalClickEvent={this.trackLocalClickEvent}
                    contactExpert={true}
                    handleContactExpertClick={this.handleExpertClickFromFailure}
                />
            );
        }
        return [];
    }

    onReviewFailurePopupClosed = (reviewError) => {
        this.setState({ showReviewPopUp: false, showSavePopUp: false });

        const isFlightOrHotelError =
          reviewError.error.errorType === detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT ||
          reviewError.error.errorType === detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL;

        if (isFlightOrHotelError) {
            this.scrollToIndexFunc(6);
        }
    };

    openListingPage = () => {
        const { notFromDeeplink } = this.props;
        const { holidayDetailData, packageDetailDTO } = this;
        const { departureDetail = {}, departureCity, destinationDetail = {} } = holidayDetailData || {};
        const { pt, aff } = holidayDetailData || {};

        const listingData = {
            dest: destinationDetail.tagDestination,
            destinationCity: destinationDetail.tagDestination,
            departureCity: departureCity,
            packageDate: departureDetail.departureDate,
            cmp: packageDetailDTO.cmp,
            pt,
            aff,
            fromDeepLink: true,
        };

        if (notFromDeeplink) {
            this.onBackPressed();
        } else {
            // try {
            //     HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.LISTING, { holidaysListingData: listingData });
            // } catch (e) {
            //     HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.LISTING, { holidaysListingData: listingData });
            // }
            try {
                HolidayNavigation.pop();
                HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GROUPING, listingData);
              } catch (e) {
                HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.GROUPING, listingData);
              }
        }
    };

    // Callback function to open forward flow overlays
    openForwardFlowFromReviewPage = () => {
        if (!isEmpty(this.props.componentFailureData)) {
            const { componentErrors } = this.props.componentFailureData || {};
            const { FLIGHT = [], HOTEL = [] } = componentErrors || {};
            if (FLIGHT && FLIGHT.length > 0) {
                setTimeout(() => this.forwardFlowOpenFlightOverlay(), 1000);
            } else if (HOTEL && HOTEL.length > 0) {
                setTimeout(() => this.forwardFlowOpenHotelOverlay(HOTEL[0].sellableId), 1000);
            }
        }
    };

    forwardFlowOpenHotelOverlay = (hotelSellableId) => {
        const hotel = getHotelObject(this.newPackageDetail.hotelDetail, hotelSellableId);
        openChangeHotelFromPhoenixPage(
            hotel,
            this.packageDetailDTO,
            this.roomDetails,
            this.onComponentChange,
            this.lastPageName,
            '',
            hotelSellableId,
            this.props.showOverlay,
            this.props.hideOverlays,
            this.props.detailData,
        );
    };

    forwardFlowOpenFlightOverlay = () => {
        const { packageConfigDetail = {} } = this.newPackageDetail;
        const { componentAccessRestriction = {} } = packageConfigDetail || {};
        const subtitleData = createSubtitleData(
            this.props.detailData.packageDetail.departureDetail,
            this.roomDetails,
        );
        const flightReqParams = createFlightRequestParams(this.newPackageDetail);
        const { flightSelections, overnightDelays } = flightReqParams;
        const requestParams = {};
        requestParams.listingFlightSequence = 1;
        if (flightSelections && flightSelections.length > 0) {
            requestParams.flightSelections = flightSelections;
        }
        if (overnightDelays) {
            requestParams.overnightDelays = overnightDelays;
        }
        const flightOverlayProps={
            flightRequestObject: requestParams,
            dynamicId: this.packageDetailDTO.dynamicPackageId,
            pricingDetail: this.props.detailData.packageDetail.pricingDetail,
            onComponentChange: this.onComponentChange,
            onPackageComponentToggle: this.onPackageComponentToggle,
            subtitleData: subtitleData,
            accessRestriction: componentAccessRestriction,
            lastPage: this.lastPageName,
            packageDetailDTO: this.packageDetailDTO,
            roomDetails: this.roomDetails,
            trackLocalClickEvent: this.trackLocalClickEvent,
            trackLocalPageLoadEvent: this.trackLocalPageLoadEvent,
            isFlightFailed: true,
            fromPresales:true,
            showOverlay:this.props.showOverlay,
            hideOverlays:this.props.hideOverlays,
            clearOverlays:this.props.clearOverlays
        };
        isMobileClient() ?
            HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.FLIGHT_LISTING, flightOverlayProps)
            : this.props.showOverlay(Overlay.FLIGHT_OVERLAY, flightOverlayProps);
    };

    openCorrespondingListingPage = (reviewError, optimizedPackageDetail) => {
        this.setState({showReviewPopUp:false,showSavePopUp:false});
        const { packageConfigDetail } = this.newPackageDetail || {};
        const { componentAccessRestriction = {} } = packageConfigDetail || {};
        const {changeFlightRestricted = false , changeHotelRestricted = false } = componentAccessRestriction;
        switch (reviewError.error.errorType) {
            case detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT: {
                if (!changeFlightRestricted) {
                    this.forwardFlowOpenFlightOverlay();
                } else {
                    isMobileClient() ? HolidayNavigation.pop() : hideOverlays(Overlay.FLIGHT_OVERLAY);
                }
                break;
            }
            case detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL: {
                if (!changeHotelRestricted) {
                    const { failedHotels } = reviewError.error.errorData || [];
                    this.forwardFlowOpenHotelOverlay(failedHotels[0].sellableId);
                } else {
                    isMobileClient() ? HolidayNavigation.pop(): hideOverlays(Overlay.PHOENIX_HOTELS_LISTING);
                }
                break;
            }
            case detailReviewFailure.REVIEW_FAILED_TYPE_VISA: {
                this.updateVisa(false);
                break;
            }
            case detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY:
            case detailReviewFailure.REVIEW_FAILED_TYPE_ADDON_INCLUSION:
            case detailReviewFailure.REVIEW_FAILED_TYPE_TRANSFER: {
                const { pricingDetail, departureDetail } = this.props.detailData.packageDetail;
                const day = reviewError?.error?.errorData?.dialogResponse?.content?.soldOutItems?.[0]?.day;
                const roomDetails = this.props.detailData?.roomDetails || this.roomDetails;
                const activityReqParams = getActivityExtraData(this.props.detailData.packageDetail, day);
                const subtitleData = createSubtitleData(departureDetail, roomDetails, {});
                this.activityFailedScrollRef.current = true;

                HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.TRAVEL_TIDBITS, {
                  departureDetail,
                  activityReqParams,
                  day: day,
                  dynamicId: this.packageDetailDTO.dynamicPackageId,
                  pricingDetail: pricingDetail,
                  onComponentChange: this.onComponentChange,
                  subtitleData: subtitleData,
                  lastPage: HOLIDAY_ROUTE_KEYS.DETAIL,
                  branch: this.props.detailData.branch,
                  packageDetailDTO: this.packageDetailDTO,
                  roomDetails: roomDetails,
                  trackLocalClickEvent: this.trackLocalClickEvent,
                  trackLocalPageLoadEvent: this.trackLocalPageLoadEvent,
                  activityProductType: reviewError?.error?.errorData?.dialogResponse?.content?.soldOutItems?.[0]?.productType,
                  currentActivePlanOnItinerary: this.state.currentActivePlanOnItinerary,
                });
                break;
              }
            default: {
                isMobileClient() ? HolidayNavigation.pop() : clearOverlays();
            }
        }
    };

     getNameOfFailedItinerary = (reviewError, packageDetail) => {
         const { errorType, errorData } = reviewError?.error || {};
        if (errorType) {
            switch (errorType) {
                case detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL: {
                    const failedHotels = errorData?.failedHotels || [];
                    for (const element of failedHotels) {
                        const hotelName = packageDetail?.hotelDetail?.[element.sellableId]?.name;
                        if (!isEmpty(hotelName)) {
                            return hotelName;
                        }
                    }
                    return '';
                }
                case detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT: {
                    return 'Flights';
                }
                case detailReviewFailure.REVIEW_FAILED_TYPE_VISA:
                {
                    return 'VISA';
                }

                default:
                    return '';
            }
        } else if (!isEmpty(this.props.componentFailureData)) {
            const componentErrors = this.props.componentFailureData?.componentErrors || {};
            const { FLIGHT = [], HOTEL = [] } = componentErrors;

            if (HOTEL.length > 0) {
                return HOTEL[0].hotelName;
            }
            if (FLIGHT.length > 0) {
                return 'Flights';
            }
        }
        return '';
    };

    handleReviewFailure = (reviewError) => {
        const { errorType = '' } = reviewError?.error || {};
        if (errorType === detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY || errorType === detailReviewFailure.REVIEW_FAILED_TYPE_ADDON_INCLUSION || errorType === detailReviewFailure.REVIEW_FAILED_TYPE_TRANSFER) {
            logPhoenixDetailPDTEvents({actionType:PDT_EVENT_TYPES.contentSeen,value:PDTConstants.ACTIVITY_SOLD_OUT,shouldTrackToAdobe:false});
          this.fetchDetailDataFunc(
            false,
            true,
            null,
            false,
            true,
          );
        }
        this.setState({
            reviewError,
            showReviewPopUp: true,
        });
    };
    handleSaveFailure = (saveError) => {
        const { errorType = '' } = saveError?.error || {};
        this.setState({
            showSavePopUp: true,
            saveError:saveError,
        });
        if (errorType === detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY || errorType === detailReviewFailure.REVIEW_FAILED_TYPE_ADDON_INCLUSION || errorType === detailReviewFailure.REVIEW_FAILED_TYPE_TRANSFER) {
            logPhoenixDetailPDTEvents({actionType:PDT_EVENT_TYPES.contentSeen,value:PDTConstants.ACTIVITY_SOLD_OUT,shouldTrackToAdobe:false});
          this.fetchDetailDataFunc(
            false,
            true,
            null,
            false,
            true,
          );
        }
    };
}

const styles = StyleSheet.create({
    pageWrap: {
        ...Platform.select({
            ios: {
                marginTop: -statusBarHeightForIphone,
                paddingTop: statusBarHeightForIphone,
            },
        }),
        backgroundColor: 'white',
    },
    visaContainer: {
        paddingHorizontal: 15,
    },
    exploreCardWrap: {
        paddingHorizontal: 15,
        paddingVertical: 10,
    },
    mr4: {
        marginRight: 4,
    },
    mt9: { marginTop: 9 },
    pb25: { paddingBottom: 25 },
    row: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    bb1: { borderBottomColor: '#e7e7e7', borderBottomWidth: 1 },
    mapIcon: {
        width: 28,
        height: 28,
    },
    leftRightSection: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 15,
    },
    pkgName: {
        marginTop: 20,
        paddingHorizontal: 15,
        fontSize: 20,
        fontWeight: '900',
        color: '#000',
        fontFamily: 'lato',
    },
    actionContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingTop: 42,
        position: 'absolute',
        top: 0,
        zIndex: 1,
        width: '100%',
    },
    rightContainer: {
        flexDirection: 'row',
    },
    iconMarginRight: {
        marginRight: 23,
    },
    icon: {
        width: 32,
        height: 32,
    },
    carousalContainer: { width: '100%', height: 213 },
    imgContainer: { flex: 1 },
    carousalImage: {
        height: '100%',
        width: '100%',
    },
    imageCarousal: {},
    activeBullet: {
        transform: [{ scale: 1.5 }],
    },
    bullet: {
        transform: [{ scale: 0.8 }],
        backgroundColor: '#fff',
        marginHorizontal: 4,
    },
    modifySearchContainer: {
        backgroundColor: '#eaf5ff',
        borderRadius: 14,
        borderWidth: 1,
        borderColor: '#e7e7e7',
        paddingHorizontal: 13,
        paddingVertical: 6,
        marginHorizontal: 16,
        top: -14,
    },
    onlineDot: {
        position: 'absolute',
        right: 0,
        bottom: -2,
        width: 13,
        height: 13,
        backgroundColor: '#6CB61B',
        borderRadius: 10,
    },
    blueBtnRounded: {
        marginVertical: 8,
    },
    messageStripContainer: {
        marginTop: 0,
        paddingTop: 5,
      },
    blueBtnRoundedBg: {
        height: 32,
        width: 110,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 20,
        //...getPlatformElevation(2),
    },
    spFooter: {
        height: 69,
        backgroundColor: '#F2F2F2',
        alignItems: 'center',
        justifyContent: 'center',
    },
    baloonIconWrapper: {
        height: 64,
        width: 64,
        backgroundColor: '#EAF5FF',
        borderRadius: 35,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 12,
        marginTop: 15,
    },
    baloonIcon: {
        height: 36,
        resizeMode: 'contain',
    },
    spCloseBtn: {
        position: 'absolute',
        right: 0,
        top: 8,
        width: 25,
        height: 25,
        alignItems: 'center',
        justifyContent: 'center',
    },
    spModalContainer: {
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
    },
    spModal: {
        backgroundColor: '#ffffff',
        position: 'relative',
        zIndex: 1,
        alignSelf: 'center',
        width: 302,
        borderRadius: 4,
        //...getPlatformElevation(2),
    },
    spBody: {
        padding: 15,
        alignItems: 'center',
    },
    spBg: {
        backgroundColor: 'rgba(0,0,0,0.5)',
        position: 'absolute',
        left: 0,
        top: 0,
        height: '100%',
        width: '100%',
    },
    blueBtn: {
        marginVertical: 8,
    },
    blueBtnGhost: {
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 6,
        borderWidth: 1,
        borderColor: '#008CFF',
    },
    blueBtnBg: {
        height: 40,
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 6,
    },
    bsContainer: {
        padding: 0,
    },
    offineMsgToast: {
        padding: 15,
        backgroundColor: '#FFEDD1',
    },
    ghostBtn: {
        borderRadius: 4,
        height: 44,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 10,
        marginTop: 22,
        //...getPlatformElevation(2),
    },
    ghostEditIcon: {
        height: 20,
        width: 20,
        tintColor: '#008CFF',
        marginRight: 15,
    },
    separator: {
        height: 1,
        width: '100%',
        backgroundColor: '#D8D8D8',
        position: 'absolute',
        left: 0,
        top: 7,
    },
    callCta: {
        backgroundColor: '#F2F2F2',
        height: 40,
        width: 40,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 25,
    },
    callCtaIcon: {
        height: 16,
        resizeMode: 'contain',
    },
    expertThumbWrapper: {
        height: 58,
        width: 58,
        borderRadius: 30,
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 15,
    },
    expertThumbInnerWrapper: {
        height: 50,
        width: 50,
        borderRadius: 30,
        overflow: 'hidden',
        backgroundColor: '#e7f6e5',
    },
    expertThumb: {
        height: 54,
        resizeMode: 'cover',
    },
    coHeader: {
        borderBottomColor: '#E7E7E7',
        borderBottomWidth: 1,
        flexDirection: 'row',
        alignItems: 'center',
        height: 50,
        marginBottom: 20,
        paddingHorizontal: 15,
    },
    saveButton:{
        height:50,
        width:'100%',
        justifyContent:'center',
        alignItems:'center',
    },
    button:{
        padding:10,
        borderRadius:3,
        backgroundColor:'gray',
    },
    persuasionContainer: {
        borderBottomColor: '#EEEEEE',
        borderBottomWidth: 5,
        marginBottom: 0,
    },
    headerStyling:{marginBottom:5,paddingHorizontal:10},
    crossButtonStyling:{height:27,width:27},
    containerStyle:{paddingTop:0},
    addonsUpdateContainer: {
        paddingHorizontal: 16,
        paddingTop: 8,
        paddingBottom: 16,
    },
    addonItem: {
        marginBottom: 16,
    },
    addonTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#000',
        marginBottom: 4,
    },
    addonDescription: {
        fontSize: 14,
        color: '#666',
        lineHeight: 20,
    },
    addonDivider: {
        height: 1,
        backgroundColor: '#E7E7E7',
        marginTop: 16,
    },
    priceUpdateSection: {
        flexDirection: 'row',
        marginTop: 20,
        marginBottom: 24,
        paddingHorizontal: 16,
    },
    priceLabel: {
        fontSize: 16,
        color: '#000',
    },
    priceValue: {
        fontSize: 16,
        fontWeight: '600',
        color: '#000',
    },
    okayButton: {
        backgroundColor: '#008CFF',
        paddingVertical: 14,
        borderRadius: 6,
        alignItems: 'center',
        marginHorizontal: 16,
    },
    okayButtonText: {
        color: '#FFFFFF',
        fontSize: 16,
        fontWeight: '600',
    },
    addonsHeaderContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 8,
    },
    addonsHeaderTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#000',
    },
    addonsCloseButton: {
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: '#F2F2F2',
        alignItems: 'center',
        justifyContent: 'center',
    },
    addonsCloseIcon: {
        fontSize: 20,
        color: '#666',
        fontWeight: '400',
    },
    crossIcon: {
        width: 12,
        height: 12,
        tintColor: holidayColors.white,
      },
});
export default withBackHandler(MimaPreSalesEditDetailPage);
