import React from 'react';
import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import url from 'url';
import HolidaysWebRoute from '../../HolidayWebRoute';
import QuotesListing from '../QuotesListing';
import {withRouterState} from 'web/WebRouter';
import {has} from 'lodash';
import { FEEDBACK } from '../../HolidayConstants';
import feedbackReducer from '../../Common/Components/Feedback/redux/reducer';
import { injectAsyncReducer } from '@mmt/legacy-commons/AppState/asyncStore';

class HolidayPsmListingWebRoutes extends HolidaysWebRoute {
    constructor(props) {
        super(props, 'QuotesListing');
        const urlObj = url.parse(window.location.href, window.location.search);
        const {query} = urlObj || {};
        const {tagDestination, ticketId} = this.getParamObject(props, query);
        this.state = {ticketId, tagDestination};
    }

    getParamObject = (props, query) => {
        if (has(query, 'tagDestination') && has(query, 'ticketId')) {
            const  {tagDestination, ticketId} = query || {};
            return {tagDestination, ticketId};
        } else {
            const {tagDestination, ticketId} = props || {};
            return {tagDestination, ticketId};
        }
    }

    render() {
        return (<QuotesListing {...this.state}/>);
    }
}

injectAsyncReducer(FEEDBACK,feedbackReducer);
const HolidaysPsmRoutes = () => (
    <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
        <Switch>
            <Route exact path="/holidays/psm/quotes/listing" component={withRouterState(HolidayPsmListingWebRoutes)}/>
        </Switch>
    </View>
);

export default withRouter(HolidaysPsmRoutes);
