import React from 'react';
import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import HolidaysWebRoute from '../../HolidayWebRoute';
import {withRouterState} from 'web/WebRouter';
import CompareQuotes from '../CompareQuotes';

class HolidaysPsmCompareWebRoutes extends HolidaysWebRoute {
    constructor(props) {
        super(props, 'QuotesCompare');
        this.state = {...props};
    }
    render() {
        return (<CompareQuotes {...this.state}/>);
    }
}

const HolidaysPsmCompareRoutes = () => (
    <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
        <Switch>
            <Route exact path="/holidays/psm/quotes/compare" component={withRouterState(HolidaysPsmCompareWebRoutes)}/>
        </Switch>
    </View>
);

export default withRouter(HolidaysPsmCompareRoutes);
