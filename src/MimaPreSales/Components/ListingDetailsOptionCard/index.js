import React, { useEffect, useState } from 'react';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {View, Text, Image, TouchableOpacity} from 'react-native';
import moment from 'moment';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import styles from '../../QuotesListing/quotesListingCss';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import {formatAgentNameToDisplay, getInclusionLabel} from '../../utils/MimaPreSalesUtils';
import { ddd_DD_MMM_YY, NEW, READ } from '../../../HolidayConstants';
import { formatDate } from '../../../utils/HolidayUtils';
import { isEmpty } from 'lodash';
import { StyleSheet } from 'react-native';
import { PresalesImages } from '../../utils/PresalesImages';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { borderRadiusValues, holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { PRESALE_CARD_STATUS } from '../../constants/preSalesMimaConstants';
const MAX_COUNT = 4;

const ListingDetailsOptionCard = ({ quote, itineraryDetail, onCardClick}) => {
    const [readMore, showReadMore] = useState(false);
    const [initialExtraInfo, setInitialExtraInfo] = useState([]);
    const FIRST_VERSION = 1;
    const { version, price, agentName, createdAt, quotationStatus, highlights, packageInclusionsDetail, updates, isBookedQuote = false, quoteId, parentQuoteId , finalPrice} = quote || {};
    const isFirstVersion = version === FIRST_VERSION;
    const extraInfo = isFirstVersion ? highlights : updates;
    const EXTRA_INFO_TEXT = isFirstVersion ? 'HIGHLIGHTS' : 'UPDATES';

    useEffect(() => {
        if (extraInfo && extraInfo.length > MAX_COUNT){
            setInitialExtraInfo(extraInfo.slice(0,MAX_COUNT));
            showReadMore(true);
        } else {setInitialExtraInfo(extraInfo);}
    }, [extraInfo]);

    const handleReadMoreClick = () => {
        const more = extraInfo?.slice(MAX_COUNT);
        setInitialExtraInfo([...initialExtraInfo, ...more]);
        showReadMore(false);
    };
    const { departureDetail, destinationDetail } = itineraryDetail || {};
    const { duration = 0, destinations = [] } = destinationDetail || {};
    const { departureDate = '' } = departureDetail || {};
    const endDate = destinations?.[destinations.length - 1]?.end || null;
    const durationInDays = parseInt(duration) + 1;
    const durationLabel = `${duration}N/${durationInDays}D`;
    const agentNameToDisplay = formatAgentNameToDisplay(agentName);
    const quoteIdText = quoteId ? `QID ${quoteId} `: '';
    const parentQuoteIdText = parentQuoteId ? `• PQID ${parentQuoteId}`: '';
    return (
        <TouchableOpacity onPress={()=> onCardClick(itineraryDetail, quote)}>
        <View style={style.cardContainer}>
            <View style={style.card}>
                <View style={style.cardHeader}>
                    <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, {flex:3}]}>
                        <View style={[AtomicCss.makeRelative, marginStyles.mr16]}>
                            <Image source={{uri:PresalesImages.CoolIcon}} style={style.coolIcon} />
                            <Image source={{uri:PresalesImages.WhiteTickIcon}} style={style.WhiteTickIcon} />
                            {quotationStatus === READ &&
                                <Image source={{uri:PresalesImages.WhiteTickIcon}} style={[style.WhiteTickIcon, style.WhiteTickIcon2]} />}
                        </View>
                        <View style={style.quoteInfoContainer}>
                            <Text style={style.optionText}>Option {version}</Text>
                            {!isEmpty(agentName) && <Text numberOfLines={2} style={style.agentInfo}>Customised by {agentNameToDisplay} {moment(new Date(Number(createdAt))).fromNow()}</Text>}
                            {!!quoteId && <Text numberOfLines={2} style={style.agentInfo}>{quoteIdText + parentQuoteIdText}</Text>}
                        </View>
                    </View>
                    <View style={[{flex:1}]}>
                        <Text style={style.price}>₹{rupeeFormatter(Number(finalPrice))}</Text>
                        <Text style={style.priceText}>per person</Text>
                        {!isBookedQuote && quotationStatus === NEW &&
                        <View style={style.badge}>
                            <View style={style.badgeInner}>
                                <Text style={style.quoteText}>{PRESALE_CARD_STATUS.NEW_QUOTE}</Text>
                            </View>
                            <Image source={{uri:PresalesImages.BadgeIcon}} style={style.badgeIcon} />
                        </View>
                        }
                        {isBookedQuote &&
                        <View style={style.bookedQuoteStyle}>
                            <View style={style.bookedQuoteStyleInner}>
                                <Text style={style.bookedText}>{PRESALE_CARD_STATUS.BOOKED}</Text>
                            </View>
                            <Image source={{uri:PresalesImages.BadgeIcon}} style={style.bookedQuoteBadgeIcon} />
                        </View>
                        }
                    </View>
                </View>
                <View style={style.cardBody}>
                    <View style={style.cardBodyRow}>
                        <Text
                            style={style.leftTitle}>{durationLabel}</Text>
                        <Text style={style.titleText}>{formatDate(departureDate, ddd_DD_MMM_YY)} - {formatDate(new Date(endDate), ddd_DD_MMM_YY)}</Text>
                    </View>
                    <View style={style.cardBodyRow}>
                        <Text
                            style={style.leftTitle}>INCLUDES</Text>
                        <Text style={style.titleText}>{getInclusionLabel(packageInclusionsDetail)}</Text>
                    </View>
                    {
                        !isEmpty(initialExtraInfo) && <View style={[style.cardBodyRow, style.cardBodyRowLast]}>
                            <Text
                                style={style.leftTitle}>{EXTRA_INFO_TEXT}</Text>
                            <View style={{ flex: 7 }}>
                                {
                                    initialExtraInfo?.map((highlight, index) => (
                                        <View key={index} style={[AtomicCss.flexRow, extraInfo.length - 1 === index ? {} : AtomicCss.marginBottom3]}>
                                            <View style={styles.bullet} />
                                            <Text style={[style.highlightText,initialExtraInfo.length - 1 === index ? {width:'70%'} : null]} numberOfLines={1}>{highlight}</Text>
                                            {initialExtraInfo.length - 1 === index && readMore && (
                                                <TouchableOpacity onPress={handleReadMoreClick} >
                                                    <Text style={[AtomicCss.font12 ,AtomicCss.blueText, AtomicCss.alignRight,{marginLeft:5,marginTop:0}]}>
                                                        {' '}
                                                        +{extraInfo?.length - MAX_COUNT} More
                                                    </Text>
                                                </TouchableOpacity>
                                             )}
                                        </View>
                                    ))
                                }

                            </View>
                        </View>
                    }
                </View>
            </View>

        </View>
        </TouchableOpacity>
    );
};


const style = StyleSheet.create({
    cardContainer: {
        paddingHorizontal: 4,
        position: 'relative',
        marginHorizontal: -3,
        marginBottom: 12,
    },
    card: {
        backgroundColor: holidayColors.white,
        ...holidayBorderRadius.borderRadius4,
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
      },
      cardHeader: {
        backgroundColor: holidayColors.lightBlueBg,
        ...paddingStyles.ph12,
        ...paddingStyles.pv16,
        borderTopLeftRadius: borderRadiusValues.br4,
        borderTopRightRadius: borderRadiusValues.br4,
        flexDirection: 'row',
        alignItems: 'center',
        flex:4,
        justifyContent: 'space-between',
      },
      bullet: {
        width: 3,
        height: 3,
        borderRadius: 2,
        backgroundColor: holidayColors.gray,
        marginRight: 5,
        marginTop:8,
      },
    optionText: {
        marginBottom: 3,
        ...fontStyles.labelBaseBlack,
        color: holidayColors.black,
    },
    quoteInfoContainer: {
        width: '100%',
    },
    agentInfo: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.gray,
        width:'90%',
    },
    quoteText: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.white,
    },
    bookedText: {
        ...fontStyles.labelSmallBold,
        fontFamily: fonts.italic,
        color: holidayColors.white,
    },
    badge: {
        position: 'absolute',
        top:45,
        right: -15,
        zIndex: 1,
        height: 30,
        backgroundColor: 'transparent',
      },
    bookedQuoteStyle:{
        position: 'absolute',
        top: 50,
        right: -18,
        zIndex: 1,
        height: 30,
        backgroundColor: 'transparent',
    },
    bookedQuoteStyleInner:{
        backgroundColor: holidayColors.green,
        height: 22,
        borderTopLeftRadius: 4,
        borderBottomLeftRadius: 4,
        borderTopRightRadius: 4,
        ...paddingStyles.ph6,
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 3,
    },
    bookedQuoteBadgeIcon: {
        position: 'absolute',
        tintColor: holidayColors.mosqueGreen,
        right: 0,
        top: 0,
        height: 24,
        width: 3,
        zIndex: 1,
    },
      badgeInner: {
        backgroundColor: '#FF6666',
        height: 22,
        borderRadius: 4,
        paddingHorizontal: 5,
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 2,
      },
      badgeIcon: {
        position: 'absolute',
        right: 0,
        top: 0,
        height: 20,
        width: 3,
        zIndex: 1,
      },
    titleText: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.gray,
        flex: 7,
    },
    highlightText: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.gray,
    },
    price: {
        ...fontStyles.labelMediumBlack,
        color: holidayColors.black,
        textAlign: 'right',
    },
    priceText: {
        textAlign: 'right',
        ...fontStyles.labelSmallRegular,
        color: holidayColors.lightGray,
    },
    leftTitle: {
        width: 100,
        flex: 3,
        ...fontStyles.labelSmallRegular,
        color: holidayColors.lightGray,
      },
      coolIcon: {
        width: 18,
        height: 23,
      },
      WhiteTickIcon: {
        height: 6.11,
        width: 8,
        position: 'absolute',
        top: 12,
        left: 4,
      },
      WhiteTickIcon2: {
        top: 12,
        left: 7,
      },
      cardBody: {
        paddingHorizontal: 8,
      },
      cardBodyRow: {
        paddingVertical: 15,
        paddingHorizontal: 3,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E5E5',
        flexDirection: 'row',
      },
      cardBodyRowLast: {
        borderBottomWidth: 0,
      },
});


export default ListingDetailsOptionCard;
