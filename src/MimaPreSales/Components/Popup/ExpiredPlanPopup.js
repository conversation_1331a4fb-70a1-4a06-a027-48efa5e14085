import React, {useState} from 'react';
import {Modal, StyleSheet, Text, View, Image} from 'react-native';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const ExpiredPlanPopup = (props) => {
    const {message,subMessage,btnText,icon ,onBtnClick} = props || {};
    const [modalVisible, setModalVisible] = useState(true);

    const onClose = () => {
        setModalVisible(false);
    };
    const execFxn = () => {
        onClose();
        onBtnClick();
    };

    return (
        <View style={styles.centeredView}>
            <Modal animationType="slide" transparent={true} visible={modalVisible} onRequestClose={execFxn}>
                <View style={[styles.centeredView]}>
                    <View style={[styles.modalView]}>
                        <View style={styles.imageWrapper}>
                            <Image source={icon} style={styles.lobIcon}/>
                        </View>
                        <Text style={styles.message}>{message}</Text>
                        <Text style={styles.subText}>{subMessage}</Text>
                        <PrimaryButton
                            buttonText={btnText}
                            handleClick={execFxn}
                            btnContainerStyles={styles.submitBtn}
                        />
                    </View>
                </View>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    message:{
        ...fontStyles.labelMediumBlack,
        color: holidayColors.gray,
    },
    subText:{
        ...fontStyles.labelSmallBlack,
        color: holidayColors.gray,
        ...marginStyles.mt16,

        textAlign: 'center',
    },
    centeredView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.75)',
    },
    modalView: {
        backgroundColor: 'white',
        borderRadius: 4,
        padding: 25,
        paddingTop: 38,
        width: 320,
        position: 'relative',
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageWrapper: {
        backgroundColor: holidayColors.lightBlueBg,
        width: 54,
        height: 54,
        borderRadius: 40,
        marginRight: 10,
        marginBottom: 10,
        alignItems: 'center',
        justifyContent: 'center',
    },
    submitBtn: {
        ...paddingStyles.pv15,
        ...paddingStyles.ph30,
        marginTop: 20,
    },
    lobIcon: {
        resizeMode: 'contain',
        width: 33,
    },
});

export default ExpiredPlanPopup;
