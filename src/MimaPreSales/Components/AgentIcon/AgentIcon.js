import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import {gradientColorOnline, VIEW_STATE_LOADING} from '../../utils/PreSalesMimaConstants';
import { ActivityIndicator, TouchableOpacity, View, StyleSheet } from 'react-native';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import { PresalesImages } from '../../utils/PresalesImages';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const AgentIcon = ({onPress, profileImageUrl, agentStatus, isLoading, customPositionStyle}) => {
    const onPressDisabled = !onPress || typeof onPress !== 'function';
    const {defaultIcon} = PresalesImages;

    return (
        <LinearGradient
            start={{x: 1.0, y: 0.0}}
            end={{x: 0.0, y: 1.0}}
            colors={gradientColorOnline}
            style={[styles.expertThumbWrapper, customPositionStyle]}>
            <View style={styles.whiteCircle}>
                <LinearGradient
                    start={{x: 1.0, y: 0.0}}
                    end={{x: 0.0, y: 1.0}}
                    colors={gradientColorOnline}
                    style={styles.innerCircle}>
                    <TouchableOpacity
                        disabled={onPressDisabled}
                        activeOpacity={0.7}
                        onPress={onPress}
                        style={styles.expertThumbWrapper}>
                        {isLoading === VIEW_STATE_LOADING ? (
                            <View style={styles.loader}>
                                <Spinner
                                    size={30}
                                    strokeWidth={3}
                                    progressPercent={85}
                                    speed={1.5}
                                    color={holidayColors.primaryBlue}
                                />
                            </View>
                        ) : (
                            <HolidayImageHolder
                                imageUrl={profileImageUrl || defaultIcon}
                                style={styles.profileImage}
                            />
                        )}
                    </TouchableOpacity>
                </LinearGradient>
            </View>
        </LinearGradient>
    );
};

const styles = StyleSheet.create({
    expertThumbWrapper: {
        height: 58,
        width: 58,
        borderRadius: 30,
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 15,
    },
    profileImage: {
        height: 43,
        width: 42,
        marginLeft: 14,
        marginTop: 4,
        resizeMode: 'cover',
    },
    innerCircle: {
        height: 46,
        width: 46,
        borderRadius: 30,
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center',
    },
    whiteCircle: {
        height: 50,
        width: 50,
        overflow: 'hidden',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 30,
        backgroundColor: '#ffffff',
    },
    loader: {
        justifyContent: 'center',
        height: '100%',
        width: '100%',
        marginLeft: 14,
        flexDirection: 'row',
        alignItems: 'center',
    },
});
export default AgentIcon;
