import React, { useState, useEffect } from 'react';
import { StyleSheet, Image, TouchableOpacity, View, Text, ActivityIndicator } from 'react-native';
import moment from 'moment';
import DropdownIcon from '../../../PhoenixDetail/Components/images/ic_downArrow.png';
import CloseIcon from '../../../PhoenixDetail/Components/images/ic_cross.webp';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import { showShortToast } from '@mmt/core/helpers/toast';
import { fetchTravelPlans } from '../../../utils/HolidayNetworkUtils';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import  {isEmpty} from 'lodash';
import { PresalesImages } from '../../utils/PresalesImages';
import {colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {formatAgentNameToDisplay, getUpdateTextForSelectOptionPopUp} from '../../utils/MimaPreSalesUtils';
import rightArrow from '@mmt/legacy-assets/src/Images/right_arrow.webp';
import BottomSheetOverlay from 'mobile-holidays-react-native/src/Common/Components/BottomSheetOverlay';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const VIEW_STATE_LOADING = 'loading';
const VIEW_STATE_NO_NETWORK = 'no_network';
const VIEW_STATE_SUCCESS = 'success';
const VIEW_STATE_ERROR = 'error';

const TravelPlanOption = (props) => {
  const { detailData, onRequestClose } = props;
  const [viewState, setViewState] = useState(VIEW_STATE_LOADING);
  const [itineraryData, setItinerary] = useState({});

  useEffect(() => {
    fetchItinerary();
  }, []);

  const fetchItinerary = async () => {
    const { ticketId, quoteRequestId, packageDetail } = detailData || {};
    const { tagDestination } = packageDetail || {};
    const { name } = tagDestination || {};
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      setViewState(VIEW_STATE_NO_NETWORK);
      onRequestClose();
      return;
    }

    //Start Loader
    setViewState(VIEW_STATE_LOADING);
    // fetch data from DATA
    const response = await fetchTravelPlans(ticketId, name, quoteRequestId);

    if (!response) {
      // show error page
      setViewState(VIEW_STATE_ERROR);
      onRequestClose();
      return;
    }
    // Validate Response object
    const { statusCode, success } = response || {};

    if (statusCode !== 1 || !success) {
      // show error page
      setViewState(VIEW_STATE_ERROR);
      onRequestClose();
      return;
    }

    // Set success data
    setViewState(VIEW_STATE_SUCCESS);
    setItinerary(response);
  };

  const renderProgressView = () => (
    <View style={styles.progressContainer}>
      <Spinner
          size={30}
          strokeWidth={3}
          progressPercent={85}
          speed={1.5}
          color={holidayColors.primaryBlue}
      />
      
      <Text style={styles.darkText}> Loading</Text>
    </View>
  );

  const renderContent = () => {
    const { itineraries } = itineraryData || {};
    return (
      itineraries &&
      itineraries.map((itinerary, index) => (
        <OptionGroup {...props} itinerary={itinerary} itineraryIndex={index} />
      ))
    );
  };
  return (
    <View>
      {viewState === VIEW_STATE_LOADING  && renderProgressView()}
      {viewState === VIEW_STATE_NO_NETWORK && showShortToast('Something went wrong.')}
      {viewState === VIEW_STATE_ERROR && showShortToast('Something went wrong.')}
      {viewState === VIEW_STATE_SUCCESS && renderContent()}
    </View>
  );
};

const OptionGroup = (props) => {
  const [isExpended, handleAccordion] = useState(true);
  const { itinerary, itineraryIndex } = props || {};
  const { itineraryDetail, quotes } = itinerary || {};
  return (
    <View>
      <AccordionHeader
        itineraryDetail={itineraryDetail}
        isActive={isExpended}
        onPressHandler={() => handleAccordion(!isExpended)}
      />
      {isExpended && quotes && quotes.map((quote, index) => <Option {...props} quote={quote} quoteIndex={index} itineraryIndex={itineraryIndex}/>)}
      {/* //   <Option {...props} isActive={props.activeIndex === 1} />*/}
    </View>
  );
};

const Option = ({ quote, onRequestClose,onModifyPackagePlanSelected, itineraryIndex, quoteIndex }) => {
  const { version, price, agentName, createdAt, selected, quoteRequestId, updates ,finalPrice } = quote || {};
  const activeTextStyle = selected ? AtomicCss.whiteText : {};
  const updateText = getUpdateTextForSelectOptionPopUp(updates);
  const shortAgentName = formatAgentNameToDisplay(agentName);
  return (
    <TouchableOpacity
      onPress={() => !selected && onModifyPackagePlanSelected(quoteRequestId, itineraryIndex, quoteIndex)}
      activeOpacity={0.8}
      style={[styles.optionRow, selected ? styles.optionRowActive : {}]}
    >
      <View style={[{flex:2}, AtomicCss.paddingRight10]}>
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
          <Text
            style={[
              AtomicCss.font14,
              AtomicCss.blackFont,
              AtomicCss.blackText,
              AtomicCss.marginRight5,
              activeTextStyle,
              {flex:0.3},
            ]}
          >
            Option {version}
          </Text>
          <Text style={[AtomicCss.font10, AtomicCss.regularFont, AtomicCss.blackText, activeTextStyle,{flex:0.7}]}>
            Curated by {shortAgentName} {moment(new Date(Number(createdAt))).fromNow()}
          </Text>
        </View>

        {!isEmpty(updateText) && <Text numberOfLines={1} style={[styles.updateText, activeTextStyle]}>{updateText}</Text>}

      </View>
      <View style={[AtomicCss.alignEnd,{flex:1}]}>
        {!isEmpty(finalPrice) && <Text
            style={[AtomicCss.font16, AtomicCss.blackFont, AtomicCss.blackText, activeTextStyle, {textAlign: 'right'}]}>
          ₹{rupeeFormatter(Number(finalPrice))}
          <Text style={[AtomicCss.regularFont, AtomicCss.lightTextColor, activeTextStyle]}>
            {' '}
            /p
          </Text>
        </Text>}
        <View style={[AtomicCss.flexRow, AtomicCss.alignEnd,{justifyContent:'flex-end'}]}>
          {selected && <Image source={{uri:PresalesImages.CircleTick}} style={styles.circleTick} />}
          <Text
            style={[
              AtomicCss.marginTop8,
              AtomicCss.font12,
              AtomicCss.blackFont,
              AtomicCss.azure,
              activeTextStyle,
              {textAlign:'right'},
            ]}
          >
            {selected ? 'SELECTED' : 'SELECT'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const AccordionHeader = ({ itineraryDetail, isActive, onPressHandler }) => {
  const { destinationDetail } = itineraryDetail || {};
  const { destinations } = destinationDetail || {};
  return (
    <TouchableOpacity
      onPress={() => onPressHandler()}
      activeOpacity={0.8}
      style={styles.accordionHeader}
    >
      <View style={[AtomicCss.flexRow, AtomicCss.flexWrap, {flex:20}]}>
        {destinations &&
          destinations.map((destination, index) => {
            const { duration, name } = destination || {};

            return (
              <>
                <Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.redText]}>
                  {duration}N{' '}
                </Text>
                <Text style={[AtomicCss.font14, AtomicCss.blackFont, AtomicCss.blackText]}>
                  {name}
                </Text>
                {destinations.length - 1 === index ? [] : <Image source={rightArrow} style={styles.rightArrow}/>}
              </>
            );
          })}
      </View>
      <Image source={DropdownIcon} style={[styles.dropdownIcon, isActive ? {} : styles.upArrow]} />
    </TouchableOpacity>
  );
};

export default TravelPlanOption;

const styles = StyleSheet.create({
  dropdownIcon: {
    height: 18,
    width: 18,
    marginLeft: 6,
  },
  progressContainer: {
    marginBottom:20,
    backgroundColor: '#ffffffd9',
    alignItems: 'center',
    justifyContent: 'center',
  },
  darkText: {
    color: '#000000',
  },
  backBtn: {
    position: 'absolute',
    left: 12,
    top: 12,
    height: 30,
    width: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  backIcon: {
    width: 15,
    height: 15,
  },
  overlayWrapper: {
    padding: 0,
  },
  overlayHeader: {
    height: 50,
  },
  closeBtn: {
    height: 30,
    width: 30,
    position: 'absolute',
    left: 15,
    top: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeIcon: {
    height: 15,
    width: 15,
    tintColor: '#9b9b9b',
  },
  accordionHeader: {
    height: 37,
    backgroundColor: '#EAF5FF',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
  },
  rightArrow: {
    width: 7,
    height: 7,
    marginTop: 5,
    marginLeft:8,
    marginRight: 8,
  },
  optionRow: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#efefef',
    flexDirection: 'row',
  },
  optionRowActive: {
    backgroundColor: '#008CFF',
  },
  upArrow: {
    transform: [{ rotate: '-180deg' }],
  },
  circleTick: {
    height: 16,
    width: 16,
    marginRight: 5,
    marginTop: 7,
  },
  updateText:{
    marginTop: 10,
    color: colors.lightTextColor,
    fontFamily: fonts.regular,
    fontSize: 12,
  },
});
