import React, { useEffect, useState } from 'react';
import BottomSheet from '../../../PhoenixDetail/Components/BottomSheet/BottomSheet';
import { StyleSheet, Image, TouchableOpacity, View, Text, Dimensions, ScrollView } from 'react-native';
import DropdownIcon from '../../../PhoenixDetail/Components/images/ic_downArrow.png';
import BackIcon from '@mmt/legacy-assets/src/ic_arrowLeftTail.webp';
import getPlatformElevation, { getWebCompatElevation } from '@mmt/legacy-commons/Styles/getPlatformElevation';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import TravelPlanOption from '../TravelPlanOption';
import { isEmpty } from 'lodash';
import moment from 'moment';
import { PageHeaderBackButton, PageHeaderTitle } from 'mobile-holidays-react-native/src/Common/Components/PageHeader';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import BottomSheetOverlay from 'mobile-holidays-react-native/src/Common/Components/BottomSheetOverlay';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
const MODIFY_ITENARY = 'Modify Itinerary';
const VIEW_OPTIONS = 'View Options';
const VIEW_OPTIONS_BACK = 'View Options Back';

interface LightHeaderProps {
  onBackPress: () => void;
  onModifyPackagePlanSelected: (quoteRequestId: string) => void;
  detailData: any;
  fromEditPage?: boolean;
  trackLocalClickEvent?: (event: string) => void;
}

const LightHeader = ({
  onBackPress,
  onModifyPackagePlanSelected,
  detailData,
  fromEditPage = false,
  trackLocalClickEvent,
}: LightHeaderProps) => {
  const [isOptionsVisible, handleOptionsOverlay] = useState(false);
  const { additionalQuoteDetail } = detailData || {};
  const { agentName, version, createdAt, parentQuoteId, quoteId } = additionalQuoteDetail || {};
  const quoteIdText = quoteId ? `QID ${quoteId} `: '';
  const parentQuoteIdText = parentQuoteId ? `• PQID ${parentQuoteId}`: '';

  const captureClickEvents = (eventName = '') => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    trackLocalClickEvent && trackLocalClickEvent(eventName);
  }
  const handleOptionClick = (visible: boolean = false) => {
    const eventName = visible ?  VIEW_OPTIONS : VIEW_OPTIONS_BACK ;
    captureClickEvents(eventName);
    handleOptionsOverlay(visible);
  };

  return (
    <View style={[styles.lightHeaderContainer, styles.shadow]}>
      <View style={styles.backBtn}>
        <PageHeaderBackButton onBackPressed={onBackPress} />
      </View>
      <View>
        {fromEditPage && (
          <View style={styles.modify}>
            <PageHeaderTitle title={MODIFY_ITENARY} titleStyles={styles.modifyText} />
          </View>
        )}
        {!fromEditPage && (
          <TouchableOpacity
            style={[AtomicCss.flexRow, AtomicCss.marginBottom2, AtomicCss.alignCenter]}
            onPress={() => handleOptionClick(true)}
          >
            <PageHeaderTitle title={`Option ${version || ''}`} />
            <Image source={DropdownIcon} style={styles.dropdownIcon} />
          </TouchableOpacity>
        )}
        {!isEmpty(agentName) && (
          <Text style={styles.curatedBy}>
            Customized by {agentName} {moment(new Date(Number(createdAt))).fromNow()}{' '}
          </Text>
        )}
        {!!quoteId && (
          <Text numberOfLines={2} style={styles.curatedBy}>
            {quoteIdText + parentQuoteIdText}
          </Text>
        )}
      </View>
      {isOptionsVisible && (
        <BottomSheetOverlay
          toggleModal={() => handleOptionsOverlay(!isOptionsVisible)}
          childStyle={styles.overlayWrapper}
          headingContainerStyles={styles.overlayHeaderContainer}
          closeIconWrapper={styles.closeIconWrapper}
          title={'Select Option'}
          visible={isOptionsVisible}
        >
          <ScrollView>
            <TravelPlanOption
              detailData={detailData}
              onModifyPackagePlanSelected={onModifyPackagePlanSelected}
              onRequestClose={() => handleOptionClick(false)}
            />
          </ScrollView>
        </BottomSheetOverlay>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  lightHeaderContainer: {
    ...AtomicCss.makeRelative,
    flexDirection: 'row',
    ...paddingStyles.pv10,
  },
  shadow: {
    shadowColor: holidayColors.black,
    backgroundColor: holidayColors.white,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 3,
  },
  dropdownIcon: {
    height: 18,
    width: 18,
    marginLeft: 6,
  },
  backBtn: {
    height: 30,
    width: 30,
    alignItems: 'center',
    justifyContent: 'center',
    ...marginStyles.ml16,
  },
  overlayWrapper: {
    ...paddingStyles.pa0, 
    maxHeight: Dimensions.get('window').height-200,
    ...marginStyles.mb40,
  },
  overlayHeaderContainer: {
    ...paddingStyles.ph16,
    ...marginStyles.mb16,
    ...paddingStyles.pt16
  },
  closeIconWrapper: {
    right: 20,
    top: 20
  },
  curatedBy: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  closeBtn: {
    height: 30,
    width: 30,
    position: 'absolute',
    left: 15,
    top: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeIcon: {
    height: 15,
    width: 15,
    tintColor: '#9b9b9b',
  },
  accordionHeader: {
    height: 37,
    backgroundColor: holidayColors.lightBlueBg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
  },
  rightArrow: {
    borderRightWidth: 5,
    borderTopWidth: 5,
    borderBottomWidth: 5,
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
    borderRightColor: 'transparent',
    borderLeftWidth: 7,
    borderLeftColor: '#9b9b9b',
    marginLeft: 10,
    marginRight: 5,
    marginTop: 2,
  },
  optionRow: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#efefef',
  },
  optionRowActive: {
    backgroundColor: '#008CFF',
  },
  upArrow: {
    transform: [{ rotate: '-180deg' }],
  },
  circleTick: {
    height: 16,
    width: 16,
    marginRight: 5,
    marginTop: 7,
  },
  modify: {
    width: '100%',
    marginLeft: 100,
    ...marginStyles.mt6,
  },
  modifyText: {
    justifyContent: 'flex-start',
  },
});

export default LightHeader;
