import { isEmpty } from 'lodash';
import {
  initializeCampaignDetails,
  initializeEventTrackingContext,
  initializeSearchContext,
  logHolidaysEventToPDT,
  populateExperimentalDetails,
  populatePageContext,
} from '../../../utils/HolidayPDTTrackingV3';
import { getPreSalesListingPDTObj, updatePreSalesListingPDTObj } from './QuotesListingPDTDataHolder';
import { DOM_BRANCH } from 'mobile-holidays-react-native/src/HolidayConstants';
import { LOB_CATEGORIES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';


export const logPreSalesListingPDTClickEvents = ({
  actionType,
  value,
  errorDetails = [],
  subPageName = '',
  shouldTrackToAdobe=true
}) => {
  const pdtObj = {
    ...getPreSalesListingPDTObj(),
    ...(isEmpty(errorDetails) || isEmpty(errorDetails?.code) ? {} : { error_details_list: [errorDetails] }),
  };
  logHolidaysEventToPDT({
    pdtObj,
    actionType,
    value,
    subPageName,
    shouldTrackToAdobe
  });
};

export const initPreSalesListingPDTObj = ({branch, pageName }) => {
  const pdtObj = initializePreSalesListingPDTObj({ branch, pageName  });
  updatePreSalesListingPDTObj({ pdtObj });
}

export const initializePreSalesListingPDTObj = ({ branch = '' , pageName = '' }) => {
  const pdtObj = getPreSalesListingPDTObj();
  return {
    ...JSON.parse(JSON.stringify(pdtObj)),
    experiment_details: populateExperimentalDetails(),
    page_context: setPreSalesPageContextData({ branch, pageName }),
    event_tracking_context: initializeEventTrackingContext(),
    search_context: initializeSearchContext(),
    campaign_details: initializeCampaignDetails(),
  };
};


export const setPreSalesPageContextData = ({ branch, pageName }) => {
  const lobCategory = branch === DOM_BRANCH ? LOB_CATEGORIES.DOM_HOLIDAYS : LOB_CATEGORIES.INT_HOLIDAYS;
  return populatePageContext({funnelStep: pageName, pageName: `${pageName}-v1-Presales`, lobCategory })
}

export const populatePreSalesLisitingSearchContext = ({ }) =>{

}