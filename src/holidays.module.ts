import holidayRouteConfig from './Navigation/holidayRouteConfig';
// Remove the import of the main store
// import store from '@mmt/legacy-commons/AppState/Store';
import { exportHolidaysSharedModule } from './exportHolidaysSharedModule';
// Import the holidays-specific store
import holidaysStore from './holidaysStore';
import { RouteConfig } from '@mmt/navigation';

// Use a flag to indicate we're using a dedicated store
const useDedicatedStore = true;

const holidayModule = {
  id: '@mmt-rn/holidays',
  routeConfig: holidayRouteConfig,
  // Modify onMount to not add reducers to the main store
  onMount: () => {
    // No need to add reducers to the main store anymore
    // The holidaysStore already has all the required reducers
    console.log('Holidays module mounted with dedicated store');
  },
  onUnmount: () => {
  },
  onBootstrap: () => {
    exportHolidaysSharedModule();
  },
  // Add a reference to the dedicated store
  store: holidaysStore,
  // Add a flag to indicate we're using a dedicated store
  useDedicatedStore,
};

export default holidayModule;
