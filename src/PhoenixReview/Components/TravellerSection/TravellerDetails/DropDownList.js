import React from 'react';
import { Image, Text, TouchableOpacity, View, StyleSheet } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconArrowDown from '../../../images/ic_arrowDownBlue.webp';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { capitalizeText } from '../../../../../src/utils/textTransformUtil';

const DropDownList = ({ savedTravellersSet, currentTravellerText, handleTogglePopup }) => {
  return savedTravellersSet && savedTravellersSet.size > 0 ?
      <View style={styles.selectFormListWrapper}>
        <TouchableOpacity onPress={() => handleTogglePopup()}>
          <View style={styles.formDropdownList}>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
              {/*<View><Image source={iconTickBlue} style={styles.iconTickBlue} /></View> */}
              <Text
                numberOfLines={1}
                style={[styles.currentTraveller]}
              >{capitalizeText(currentTravellerText)}</Text>
            </View>
            <View>
              <Image source={iconArrowDown} style={styles.iconArrowDown} />
            </View>
          </View>
        </TouchableOpacity>
        <View style={[AtomicCss.marginLeft8]}>
          <Text style={[styles.subText]}> OR Enter Manually</Text>
        </View>
      </View> : [];
};

const styles = StyleSheet.create({
  selectFormListWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  formDropdownList: {
    borderWidth: 1,
    backgroundColor: holidayColors.white,
    borderColor: holidayColors.primaryBlue,
    ...holidayBorderRadius.borderRadius8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: 150,
    padding: 10,
  },
  iconArrowDown: {
    width: 12,
    height: 8,
    resizeMode: 'contain',
  },
  subText:{
    ...fontStyles.labelSmallRegular,
    color:holidayColors.lightGray,
  },
  currentTraveller:{
    ...fontStyles.labelBaseBlack,
    color:holidayColors.primaryBlue,
  },
});

export default DropDownList;
