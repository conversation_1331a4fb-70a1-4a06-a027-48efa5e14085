import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  StatusBar,
  Dimensions,
  Platform,
  BackHandler,
} from 'react-native';
import SnackBar from '@Frontend_Ui_Lib_App/SnackBar';
import { statusBarBootomHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
// import iconClose from '@mmt/legacy-assets/src/hubble/ic_close_grey.webp';
import SavedTravellerList from '../SavedTravellerList';
import { addTravellerFormLeaf, initializeTravellerFormData } from '../../../Utils/HolidayReviewUtils';
import RoomsComponent from './RoomsComponent';
import AddTravellerFormSection from '../AddTravellerForm/AddTravellerFormSection';
import { validateForm } from '../AddTravellerForm/AddTravellerFormSection';
import { getCompleteFieldList } from '../../../Utils/HolidayReviewUtils';
import { FORM_FILL_STATUS } from '../../../Utils/HolidayReviewConstants';
import DropDownList from './DropDownList';
import { validateSinglePaxRequest } from '../../../../utils/HolidayNetworkUtils';
import FieldConfirmation from '../../FieldConfirmation';
import ReviewPopup from '../../Popup/ReviewPopup';
import iconBlueTick from '@mmt/legacy-assets/src/tick.webp';
import errorRed from '@mmt/legacy-assets/src/error_red.webp';
import { PDTConstants } from '../../../Utils/HolidayReviewConstants';
import { cloneDeep } from 'lodash';
import CrossButtonToastMessage from './CrossButtonToastMessage';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import PopupContent from '../../Popup/PopupContent';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { borderRadiusValues, holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import PageHeader from 'mobile-holidays-react-native/src/Common/Components/PageHeader';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';
import BottomSheetOverlay from 'mobile-holidays-react-native/src/Common/Components/BottomSheetOverlay';
import {  getScreenWidth  } from '../../../../utils/HolidayUtils';
import { HARDWARE_BACK_PRESS } from 'mobile-holidays-react-native/src/SearchWidget/SearchWidgetConstants';
import { logHolidayReviewPDTClickEvents } from '../../../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import useBackHandler from '../../../../hooks/useBackHandler';
import HubbleSharedModuleHolder from '@mmt/hubble-shared';
import HolidayDataHolder from '../../../../../src/utils/HolidayDataHolder';
import { SUB_PAGE_NAMES } from '../../../../../src/HolidayConstants';
const CROSS_BUTTON_COMPONENT_STATE = {
  DEFAULT: 0,
  SHOW: 1,
  HIDE: 2,
};

const DROP_DOWN_TEXT = 'Select From List';
const FIELD_CONFIRMATION = 'fieldConfirmation';
const SAVED_TRAVELLER = 'savedTraveller';
const screenHeight = Dimensions.get('screen').height;
const windowHeight = Dimensions.get('window').height;
const navbarHeight = screenHeight - windowHeight;

// Note: The icon for CLOSE_GREY_ICON is @mmt/legacy-assets/src/hubble/ic_close_grey.webp
// That bundled icon has been removed by Hubble/W2G team
let CLOSE_GREY_ICON = null;
try {
  // @ts-expect-error
  CLOSE_GREY_ICON =
    HubbleSharedModuleHolder.get()?.getHubbleSharedComponents()?.getCloseIconGreyIcon?.() ?? null;
} catch (error) {
  // @ts-expect-error
  CLOSE_GREY_ICON = null;
  console.warn('Error while getting RECENT_SEARCH_ICON', error);
}

const getFooterBottom = () => {
  if (navbarHeight === 0) { return 25; }
  else if (navbarHeight > 30) { return 28; }
  else { return 42; }
};

const TravellerDetails = (props) => {
  const {
    savedTravellerList,
    travellerList,
    formIndex,
    dynamicFormList,
    dynamicFormData,
    onFormFilled,
    trackReviewLocalClickEvent,
    closeTravellerDetails,
    updateTravellerList,
  } = props;
  const options = dynamicFormData?.travellerFormOptionsDetails?.options;
  const [errors, setErrors] = useState({});
  const [showAccordian, setAccordian] = useState([0]);
  const [savedTravellersSet, setSavedTravellersSet] = useState(() => new Set(savedTravellerList));
  const [showPopup, setShowPopup] = useState(false);
  const [currentFormIndex, setCurrentFormIndex] = useState(formIndex);
  const [currentTravellerList, setTravellerList] = useState(cloneDeep(travellerList));
  const [validateObj, setValidateObj] = useState({});
  const [comp, setComp] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [submitAlert, setSubmitAlert] = useState(false);
  const [errorToast, setErrorToast] = useState(false);
  let scroll = useRef();
  let layoutMap = useRef();
  let isConfirmClick = useRef(false);
  layoutMap.current = {};
  const [onCrossPressState, setOnCrossPressState] = useState(CROSS_BUTTON_COMPONENT_STATE.DEFAULT);

  useEffect(() => {
    setErrors({});
  }, [currentFormIndex]);

  useEffect(() => {
    updateTravellerList(savedTravellersSet);
  }, [savedTravellersSet]);


  useEffect(() => {
    HolidayDataHolder.getInstance().setSubPageName(SUB_PAGE_NAMES.TRAVELLER_DETAIL)
    return (() => {
      HolidayDataHolder.getInstance().clearSubPageName()
    })
  }, [])
  useBackHandler(handleBackPress);

  const handleBackPress = () => {
    openUnchangedModal();
    return true;
  }
  const handleAccordian = (index) => {
    if (showAccordian.includes(index)) {
      let newState = showAccordian.filter(e => e !== index);
      setAccordian(newState);
    }
    else {
      let newState = [...showAccordian, index];
      setAccordian(newState);
    }
  };

  const onToggleField = (isOpen, gotoField) => {
    const fieldList = Object.keys(fieldSectionWise);
    scroll?.current?.scrollTo({ y: 70 * (fieldList.findIndex(field => field === gotoField) + 1), animated: true });
  };

  const handleTogglePopup = () => {
    setShowPopup(!showPopup);
  };
  const onSavedTravellerClick = (savedTravellerItem) => {
    if (savedTravellersSet.has(savedTravellerItem)) {
      isConfirmClick.current=true
      checkAndAddFormDetailsToSet();
      setSavedTravellersSet(prev => {
        const newSet = new Set(prev);
        newSet.delete(savedTravellerItem);
        return newSet;
      });
    }
    fillSavedCurrentFormIndex(savedTravellerItem);
    handleTogglePopup();
  };
  const checkAndAddFormDetailsToSet = () => {
    if (currentTravellerList[currentFormIndex].status === FORM_FILL_STATUS.COMPLETE) {
      setSavedTravellersSet(prev => new Set(prev).add({
        status: FORM_FILL_STATUS.FILLED,
        fields: currentTravellerList[currentFormIndex].fieldValues,
      }));
    }
  };

  const fillSavedCurrentFormIndex = (savedTravellerItem) => {
    const { fields, status } = savedTravellerItem;
    const data = [...currentTravellerList];
    data[currentFormIndex].status = status;
    if (fields) { data[currentFormIndex].fieldValues = fields; }
    setTravellerList(data);
    setErrors({});
  };

  const fillCurrentFormIndex = (savedTravellerItem) => {
    const { fields,status } = savedTravellerItem;
    const data = [...currentTravellerList];
    // Update field values if provided
    if (fields) { 
      data[currentFormIndex].fieldValues = { ...data[currentFormIndex].fieldValues, ...fields }; 
    }
    if(status == FORM_FILL_STATUS.INCOMPLETE){

    // Check if all field values are filled
    const fieldValues = data[currentFormIndex].fieldValues;
    
    // Check if all fieldValues are non-empty arrays
    if (fieldValues && Object.keys(fieldValues).length > 0) {
      let allFieldsFilled = true;
      allFieldsFilled = Object.values(fieldValues).every(
        value => Array.isArray(value) && value.length > 0 && value !=""
      );
    data[currentFormIndex].status = allFieldsFilled ? "" : FORM_FILL_STATUS.INCOMPLETE;
    }
  }
    else{
    data[currentFormIndex].status = status
  }
    setTravellerList(data);
  };

  const captureClickEvents = ({ name = '', suffix = '' }) => {
    logHolidayReviewPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: name + suffix,
      subPageName:SUB_PAGE_NAMES.TRAVELLER_DETAIL,
    });
    trackReviewLocalClickEvent(name, suffix);
  };

  const clearCurrentFormIndex = (index) => {
    let emptyFieldValues = {};
    let fillFieldValues = {};
    for (const [key, value] of Object.entries(currentTravellerList[index].fieldValues)) {
      emptyFieldValues[key] = [];
      fillFieldValues[key] = value;
    }
    if (currentTravellerList[index].status === FORM_FILL_STATUS.COMPLETE) {
      setSavedTravellersSet(prev => new Set(prev).add({
        status: FORM_FILL_STATUS.FILLED,
        fields: fillFieldValues,
      }));
    }
    currentTravellerList[index].status = FORM_FILL_STATUS.UNFILLED;

    currentTravellerList[index].fieldValues = emptyFieldValues;
    setTravellerList([...currentTravellerList]);
  };

  const updateNextIndex = () => {
    let index = currentFormIndex + 1;
    let size = currentTravellerList.length;
    for (let i = 0; i < size; i++) {
      index %= size;
      if (currentTravellerList[index].status !== FORM_FILL_STATUS.COMPLETE) {
        setCurrentFormIndex(index);
        return;
      }
      index++;
    }
    onBackPress();
  };

  /**
   * It updates master state present in phoenixReviewPage and also marks
   * incomplete data as unfilled with empty fieldValues
   **/
  const markIncompleteDataAsUnfilledAndSubmit = () => {
    const completedTravellersList = currentTravellerList.map((item, index) => {
      if (isConfirmClick.current) {
        return travellerList[index];
      }
      return item;
    });
    onFormFilled(completedTravellersList);
  };

  const onBackPress = () => {
    markIncompleteDataAsUnfilledAndSubmit();
    props.onBackPress();
    captureClickEvents({ name: PDTConstants.CLOSE_TRAVELLER_FORM });
  };

  let fieldSectionWise = {};
  let fieldObj = dynamicFormList[currentFormIndex]?.fields;
  getCompleteFieldList(dynamicFormList[currentFormIndex]?.sections, fieldSectionWise);

  const onEmptyCardClick = useCallback((index) => {
    captureClickEvents({ name: PDTConstants.ADD_TRAVELLER_FORM, suffix: `_${index}_form` });
    if (currentTravellerList[currentFormIndex].status === FORM_FILL_STATUS.FILLED) {
      currentTravellerList[currentFormIndex].status = FORM_FILL_STATUS.INCOMPLETE;
    }
    setCurrentFormIndex(index);
  });

  const handleErrorAlertSubmit = () => {
    setErrorToast(true);
  };

  const fieldValues = currentTravellerList[currentFormIndex]?.fieldValues;

  const submitFormValues = (fields) => {
    handleAlertSubmit();
    fillCurrentFormIndex({ status: FORM_FILL_STATUS.COMPLETE, fields: fields });
    updateNextIndex();
  };

  // Handle submit and add traveller functionality
  const handleSubmit = () => {
    isConfirmClick.current=false
    captureClickEvents({ name: PDTConstants.SAVE_TRAVELLER, suffix: `_${currentFormIndex}` });
    let validateValues = validateForm(fieldValues,setErrors,fieldSectionWise,fieldObj,layoutMap,scroll.current?.scrollTo);
    if (!(validateValues)) {
      Object.keys(errors).forEach(errorField => {
        if (!showAccordian.includes(fieldSectionWise[errorField])) { handleAccordian(fieldSectionWise[errorField]); }
      });
      return;
    }
    if (dynamicFormData?.validateSinglePax) {
      let request = initializeTravellerFormData(dynamicFormData);
      let leafObj = { ...currentTravellerList[currentFormIndex] };
      addTravellerFormLeaf(request, leafObj);
      validateSinglePaxRequest(request).then(res => {
        const { statusCode, success, modifiedPaxDetail, validationDetail } = res || {};
        if (statusCode === 1 && success && modifiedPaxDetail && validationDetail) {
          setValidateObj(res);
          setComp(FIELD_CONFIRMATION);
          handleTogglePopup();
        }
        else if (statusCode === 1 && success && !modifiedPaxDetail && validationDetail) {
          const fieldMessageMap = res?.validationDetail?.fieldMessageMap;
          const isResponseNonEmpty = fieldMessageMap && Object.keys(fieldMessageMap).length > 0;
          if (isResponseNonEmpty) {
            const keys = Object.keys(fieldMessageMap);
            const gotoField = keys.length > 0 ? keys[0] : null;
            if (layoutMap[gotoField]?.y || layoutMap[gotoField]?.y === 0) { scroll.current?.scrollTo({ y: layoutMap[gotoField].y, animated: true }); }
            setErrors(fieldMessageMap);
          }
          else { handleErrorAlertSubmit(); }
        }
        else if (statusCode === 1 && success) {
          submitFormValues(null);
          scroll?.current?.scrollTo({ y: 0, animated: true });
        }
        else { handleErrorAlertSubmit(); }
      });
    }
    else {
      scroll?.current?.scrollTo({ y: 0, animated: true });
      submitFormValues(null);
    }
  };
  const openUnchangedModal = () => {
    const incompleteTravellers = currentTravellerList ? currentTravellerList.find(e => (e.status === FORM_FILL_STATUS.INCOMPLETE || e.status === FORM_FILL_STATUS.FILLED)) : null;
    if (!isConfirmClick.current) { onBackPress(); }
    else { setModalVisible(true); }
  };
  closeTravellerDetails.current = openUnchangedModal;

  const handleAlertSubmit = () => {
    setSubmitAlert(true);
  };

  const closePopup = useCallback(() =>
    setOnCrossPressState(CROSS_BUTTON_COMPONENT_STATE.HIDE)
  );
  const height = savedTravellersSet.size > 3 ? 300 : ((3) * 50) + 120;
  const hideErrorToast = () =>  setErrorToast(false)
  const hideSubmitAlert = () =>  setSubmitAlert(false)
  return (
    <View style={styles.wrapper}>
      <PageHeader
        showBackBtn
        title={'Add New Traveller'}
        iconSource={CLOSE_GREY_ICON}
        containerStyles={styles.header}
        onBackPressed={openUnchangedModal}
      />
      <KeyboardAwareScrollView
        innerRef={ref => { scroll.current = ref; }}
        enableOnAndroid={true}
      >
        <TouchableOpacity activeOpacity={1} style={styles.scrollOffset}>

          {/* select form list */}
          <DropDownList
            savedTravellersSet={savedTravellersSet}
            currentTravellerText={DROP_DOWN_TEXT}
            handleTogglePopup={() => {
              handleTogglePopup();
              setComp(SAVED_TRAVELLER);
            }}

          />

          {/* room selection scroller */}
          <RenderRoomsAndAdults
            currentFormIndex={currentFormIndex}
            travellerList={currentTravellerList}
            onEmptyCardClick={onEmptyCardClick}
            onCrossClick={clearCurrentFormIndex} />

          {onCrossPressState === CROSS_BUTTON_COMPONENT_STATE.SHOW ?
            <CrossButtonToastMessage closePopup={closePopup} /> : []}

          {/* Traveller Form */}
          <AddTravellerFormSection
            errors={errors}
            setErrors={setErrors}
            currentTravellerList={currentTravellerList}
            options={options}
            dynamicFormList={dynamicFormList}
            currentFormIndex={currentFormIndex}
            fillCurrentFormIndex={param => {
              isConfirmClick.current = true
              fillCurrentFormIndex(param)
            }}
            showAccordian={showAccordian}
            handleAccordian={handleAccordian}
            scroll={scroll}
            layoutMap={layoutMap}
            onToggleField={onToggleField}
          />

        </TouchableOpacity>
      </KeyboardAwareScrollView>

      <View style={styles.footerButton}>
        {submitAlert ?
        <SnackBar
          isVisible={true}
          bgColor={[holidayColors.gray]}
          content="Traveller Added"
          startIcon={iconBlueTick}
          onDismiss={hideSubmitAlert}
          barPosition={50}
          customStyle={styles.submitSnackBarStyle}
        />
        : []}
        {errorToast ?
        <SnackBar
          isVisible={true}
          bgColor={[holidayColors.gray]}
          content="Something went wrong. Please try again later."
          onDismiss={hideErrorToast}
          startIcon={errorRed}
          barPosition={50}
          customStyle={styles.submitSnackBarStyle}
        />
        : []}

        <PrimaryButton
          buttonText={'CONFIRM DETAILS'}
          handleClick={handleSubmit}
          btnContainerStyles={styles.addTravellerFooterBtn}
        />
      </View>

      <ReviewPopup
        content={
          <PopupContent
            closeModal={onBackPress}
            closePopup={() => setModalVisible(false)}
            heading={'Are you sure you want to leave?'}
            desc={`Any unsaved changes will be lost, to save changes please ensure you select ‘Add
            Traveller’ after inputing traveller details.`}
            btnText={'LEAVE'}
            subBtnText={'Continue Editing'}
          />
        }
        closePopup={() => setModalVisible(false)}
        modalVisible={modalVisible}
      />
      {showPopup &&
      <BottomSheetOverlay
        toggleModal={() => setShowPopup(!showPopup)}
        title={'Saved Traveller List'}
        visible={showPopup}
        containerStyles={styles.containerStyles}
      >
        {(comp === SAVED_TRAVELLER) &&
          <View style={{ height: height }}>
            <SavedTravellerList
              savedTravellersSet={savedTravellersSet}
              onSavedTravellerClick={onSavedTravellerClick}
              closeModal={() => handleTogglePopup()}
            />
          </View>
        }
        {(comp === FIELD_CONFIRMATION) &&
          <FieldConfirmation
            closeModal={() => handleTogglePopup()}
            setErrors={setErrors}
            submitValues={(value) => {
              isConfirmClick.current=true
              fillCurrentFormIndex({ status: FORM_FILL_STATUS.INCOMPLETE, fields: value })
            }}
            validateObj={validateObj}
            fields={dynamicFormList[currentFormIndex]?.fields}
            trackReviewLocalClickEvent={trackReviewLocalClickEvent}
            scroll={scroll}
            layoutMap={layoutMap}
            fieldValues={fieldValues}
          />
        }
      </BottomSheetOverlay>
      }
    </View>
  );
};

const RenderRoomsAndAdults = ({ currentFormIndex, travellerList, onEmptyCardClick, onCrossClick }) => {
  if (travellerList && travellerList.length > 0) {
    return (
      <RoomsComponent
        travellerList={travellerList}
        onEmptyCardClick={onEmptyCardClick}
        currentFormIndex={currentFormIndex}
        onCrossClick={onCrossClick}
      />
    );
  }
  return [];
};

const styles = StyleSheet.create({
  wrapper: {
    width: "100%",
    height: '100%',
    backgroundColor: holidayColors.white,
    marginTop: Platform.select({
      ios: 0,
      android: 0,
    }),
  },
  header: {
    marginTop: Platform.select({
      ios: 0,
      android: 0,
    }),
  },
  scrollOffset: {
    paddingBottom: Platform.select({
      ios: 50,
      android: 50,
    }),
  },
  titleTraveller: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  addTravellerFooterBtn: {
    ...holidayBorderRadius.borderRadius8,
    width: getScreenWidth() - 35,

    ...marginStyles.mh16,
    ...paddingStyles.pv12,
  },
  submitSnackBarStyle :{

    wrapperStyle: {
      alignItems:'center',
      paddingHorizontal:15,
      paddingVertical:15,
      borderRadius:10

    },

    startIconHolderStyle:{
      marginRight:8,

    },

    startIconStyle: {
      width: 16,
      height: 16,
      resizeMode: 'contain',
      ...marginStyles.ml10,
      marginRight:0,
    },
  },
  btnTxt: {
    color: holidayColors.white,
    ...fontStyles.labelBaseBlack,
  },
  scanWrapper: {
    backgroundColor: holidayColors.green,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderColor: holidayColors.green,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  scanContent: {
    width: '70%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  scanImgHolder: {
    width: 40,
    height: 40,
    ...holidayBorderRadius.borderRadius4,
    overflow: 'hidden',
    backgroundColor: holidayColors.lightGray,
    marginRight: 10,
  },
  travellerFormHdr: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    borderBottomWidth: 1,
    borderColor: holidayColors.midLightBlue,
  },
  roomCategory: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roomGuest: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    height: 80,
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    borderStyle: 'dashed',
    ...holidayBorderRadius.borderRadius4,
    marginRight: 10,
  },
  iconGuest: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  selectedRoomGuest: {
    backgroundColor: holidayColors.lightBlueBg,
    borderStyle: 'solid',
  },
  roomName: {
    width: 22,
    height: 80,
    backgroundColor: holidayColors.black,
    borderTopLeftRadius: borderRadiusValues.br4,
    borderBottomLeftRadius: borderRadiusValues.br4,
    flexDirection: 'row',
    flexWrap: 'nowrap',
    position: 'relative',
  },
  roomNameText: {
    color: holidayColors.white,
    ...fontStyles.labelSmallBold,
    transform: [{ rotate: '-90deg' }],

  },
  roomNameTxtWrap: {
    position: 'absolute',
    left: -11,
    top: 32,
  },
  roomGuestCanel: {
    position: 'absolute',
    right: 6,
    top: 6,
  },
  iconCloseBlue: {
    width: 8,
    height: 8,
  },

  accordianWrap: {
    borderTopWidth: borderRadiusValues.br4,
    borderColor: holidayColors.lightGray2,
  },

  radioWrapList: {
    ...holidayBorderRadius.borderRadius4,
    paddingHorizontal: 10,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: holidayColors.lightGray2,
    marginBottom: 10,
  },
  radioListSelected: {
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
  },
  iconEdit: {
    width: 24,
    height: 24,
    resizeMode: 'cover',
  },
  iconTickBlue: {
    width: 12,
    height: 12,
    marginRight: 5,
  },
  childInfoTxt: {
    position: 'absolute',
    bottom: -12,
  },
  footerButton: {
    width: '100%',
    bottom: Platform.select({
      ios: statusBarBootomHeightForIphone,
      android: getFooterBottom(),
      web: 10,
    }),
  },
  containerStyles: {
    ...paddingStyles.pa16
  }
});

export default TravellerDetails;
