import React from 'react';
import fecha from 'fecha';
import { Text, View, StyleSheet, Platform, Dimensions } from 'react-native';
import Dropdown from '@Frontend_Ui_Lib_App/Dropdown';
import BottomSheetOverlay from 'mobile-holidays-react-native/src/Common/Components/BottomSheetOverlay';;    
import DrumRollCalendar from '@Frontend_Ui_Lib_App/DrumRollCalendar';    
import PropTypes from 'prop-types';
import { isRawClient } from '../../../../Utils/HolidayReviewUtils';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { MONTH_ARR_CAMEL } from '../../../../../HolidayConstants';
import LinearGradient from 'react-native-linear-gradient';
import { borderRadiusValues } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { DATE_FORMAT_OMNI } from 'mobile-holidays-react-native/src/SearchWidget/SearchWidgetConstants';

export default class DatePickerComponent extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            date: props.selectedDate,
            isOpen: false,
            bottomsheetVisible: false,
        };
    }
  
    getCustomStyles = () => {
        return {
            dateIcon: {
                display: 'none',
            },
            dateInput: {
                textAlign: 'left',
                borderWidth: 0,
                borderBottomWidth: 1,
                alignItems: 'flex-start',
                borderColor: holidayColors.grayBorder,
                height: 30,
                ...paddingStyles.pb6,
                width: '100%',
            },
            btnTextConfirm: {
                color: holidayColors.primaryBlue,
            },
            dateText: {
                ...fontStyles.labelMediumBlack,
            },
        };
    };

  handleCalendarBottomsheet = () => {
    this.setState(prevState => ({
      bottomsheetVisible: !prevState.bottomsheetVisible,
    }));
  }

  render() {
    const {
      label,
      customLabelStyle,
      handleChange,
      maxYear,
      minYear,
      errorState,
      errorText,
      isMandatory,
      selectedDate,
    } = this.props;
    const customStyles = this.getCustomStyles();
    if (errorState) {
      customStyles.dateInput.borderColor = '#d0021b';
    }
    if (this.state.date && isRawClient() && this.state.date instanceof Date) {
      this.selectedDate = fecha.format(this.state.date, 'DD/MM/YYYY');
    } else {
      this.selectedDate = this.state.date;
    }

    const iconArrowDown = require('@mmt/legacy-assets/src/arrow_downGrey.webp');
    const dropDownCustomStyles = {
      labelStyle: customLabelStyle,
      dropdownValueStyle: styles.dropdownValueStyle,
      endIconStyle: styles.iconArrowDown,
    };
    const handleDateSave = (date, month, year) => {
      const indexOfMonth = MONTH_ARR_CAMEL.indexOf(month);
      const combinedDate = new Date(year, indexOfMonth, date);
      const dob = fecha.format(new Date(combinedDate), DATE_FORMAT_OMNI)
      handleChange(dob);
      this.setState({ bottomsheetVisible: false })

    }
    return (
      <View style={styles.wrapper}>
            <Dropdown
                label={label}
                requiredText={isMandatory && <Text style={styles.mandatory}> *</Text>}
                isError = {errorState}
                errorMessage = {errorText}
                value={selectedDate}
                onSelect={this.handleCalendarBottomsheet}
                endIcon = {iconArrowDown}
                onEndIconPress={this.handleCalendarBottomsheet}
                isFloating={true}
                customStyle={dropDownCustomStyles}
                
            />
            {this.state.bottomsheetVisible && <BottomSheetOverlay
              visible={this.state.bottomsheetVisible}
              toggleModal={this.handleCalendarBottomsheet}
              onDismiss={this.handleCalendarBottomsheet}
              containerStyles={styles.bottomsheetContainer}
              headingContainerStyles={styles.headingContainerStyles}
              title= "Select Date"
            >
              <DrumRollCalendar
                  showWrapStyle={false}
                  txtStyleActive={{color: holidayColors.primaryBlue}}
                  onSave={handleDateSave}
                  initialYear={maxYear}
                  initialScroll={false}
                  showInfiniteScroll={false}
                  scrlContainer={{
                    width: (Dimensions.get('window').width/3) - 20,  
                    height: 80,
                  }}
                  
                  maxYear={maxYear}
                  minYear={minYear}
                  children = {
                    <LinearGradient 
                      colors={['#53B2FE', '#065AF3']}
                      start={{ x: 0, y: 1 }}
                      end={{ x: 1, y: 1 }}
                      style={styles.buttonContainer}
                    > 
                      <Text style={{color: "#fff", ...fontStyles.labelMediumBlack}}>SAVE</Text>
                    </LinearGradient>
                  }
                >
              </DrumRollCalendar>
            </BottomSheetOverlay>}
      </View>
    );
  }
}

DatePickerComponent.propTypes = {
  label: PropTypes.string.isRequired,
  selectedDate: PropTypes.string,
  maxDate: PropTypes.instanceOf(Date),
  minDate: PropTypes.instanceOf(Date),
  handleChange: PropTypes.func.isRequired,
  errorState: PropTypes.bool,
  errorText: PropTypes.string,
  toggleDropDown: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
  },
  mandatory: {
    color: holidayColors.red,
  },
  bottomsheetContainer: {
    alignItems: "center", 
    justifyContent: "center",
    ...paddingStyles.pa16
  },
  dropdownValueStyle: {
    color: holidayColors.black,
    ...fontStyles.labelMediumBlack,
    ...Platform.select({
      ios: {...marginStyles.mt8},
      android:{...marginStyles.mt0}
    }),  
  },
  headingContainerStyles: {
    ...paddingStyles.pb16
  },
  iconArrowDown: {
    width: 20,
    height: 20,
    tintColor: holidayColors.lightGray
  },
  buttonContainer: {
    ...paddingStyles.ph20,
    ...paddingStyles.pv8,
    ...marginStyles.mb20,
    borderRadius: borderRadiusValues.br8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: "100%",
  }
});
