import React from 'react';
import {Text,StyleSheet} from 'react-native';
import FloatingInput from '@Frontend_Ui_Lib_App/FloatingInput';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';

const InputTextField = (props) => {

    const {
        error,
        label,
        onChangeText,
        value,
        icon,
        iconPosition,
        isMandatory,
        onFocus,
        onBlur,
    } = props;

    const getFlexDirection = () => {
        if (icon && iconPosition) {
          if (iconPosition === 'left') {
            return 'row';
          } else if (iconPosition === 'right') {
            return 'row-reverse';
          }
        }
    };
    
    const getLabel = (label, isMandatory) => {
        return (
            <Text style={styles.label}>
                {label}    
                {isMandatory && <Text style={styles.mandatory}> *</Text>}
            </Text>
        )
    }
    return (
        <FloatingInput
            label={getLabel(label, isMandatory)}
            value={value}
            onChangeText={onChangeText}
            isError={!!error}
            errorMessage={error}
            customStyle={{
                labelStyle: styles.label,
                inputFieldWrapperStyle: [styles.inputWrapper, {flexDirection: getFlexDirection()}],
                inputFieldStyle: [styles.input],
                errorMessageStyle: [styles.errorWrap, styles.errorText]
            }}
            onFocus={onFocus}
            onBlur={onBlur}
        />
    );
};

const styles = StyleSheet.create({
    inputWrapper: {
        alignItems: 'center',
        padding:0,
    },
    label: {
        ...fontStyles.labelSmallBold,
        color:holidayColors.darkGray2,
    },
    input:{
        ...holidayBorderRadius.borderRadius8,
        borderWidth: 1,
        borderColor: holidayColors.lightGray6,
        height: 47,
        width: '100%',
        backgroundColor: holidayColors.lightGray7,
        color: holidayColors.black,
        ...fontStyles.labelBaseBlack,
        padding: 5,
        marginVertical: 0,
        flex: 1,
    },
    mandatory: {
        color: holidayColors.red,
    },
    errorWrap: {
        marginTop: 5,
        minHeight: 22,
    },
    errorText: {
        color: holidayColors.red,
        ...fontStyles.labelSmallRegular,
    },
});

export default InputTextField;
