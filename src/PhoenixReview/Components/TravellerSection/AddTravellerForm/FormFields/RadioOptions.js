import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { isEmptyString } from '../../../../../utils/HolidayUtils';

const RadioOptions = (props) => {
  const { options, label, widthPercentage, isMandatory, errorText, value, handleChange } = props;
  return (
    <View style={styles.radioOptions}>
      {!isEmptyString(label) &&
        <Text style={styles.heading}>{label} {isMandatory && <Text style={styles.mandatory}>*</Text>}</Text>
      }
      <View style={{ marginTop: 6, flexDirection: 'row', width: '100%', flexWrap: 'wrap' }}>
        {
          options && options.length && options.map((item, index) => {
            let selected = value === item?.value;
            return <TouchableOpacity
              key={item?.value}
              style={[styles.radioBtn, { minWidth: widthPercentage ? `${widthPercentage}%` : '100%' }]}
              onPress={() => {
                handleChange(item?.value);
              }}
            >
              <View style={[styles.Radio, selected ? styles.activeRadio : '']}>
                <View style={[selected ? styles.RadioInside : '']} />
              </View>
              <View style={[styles.radioText, { marginLeft: 10 }]}>
                <Text style={styles.boldText}>{item?.label}</Text>
              </View>
            </TouchableOpacity>;
          })
        }
      </View>
      <Text style={styles.errorText}>{errorText}</Text>
    </View>

  );
};

const styles = StyleSheet.create({

  Radio: {
    width: 20,
    height: 20,
    backgroundColor: '#fff',
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#9b9b9b',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },

  RadioInside: {
    width: '100%',
    height: '100%',
    backgroundColor: '#008cff',
    borderWidth: 1.5,
    borderColor: 'white',
    borderRadius: 18,
  },
  activeRadio: {
    borderColor: '#008cff',
  },
  radioText: {
    marginTop: 7,
    marginRight: 24,
    flexDirection: 'row',
  },
  radioBtn: {
    marginBottom: 10,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  boldText: {
    fontFamily: 'Lato-Regular',
    color: '#000',
    fontSize: 12,
  },
  normalText: {
    fontFamily: 'Lato-Regular',
    color: '#4a4a4a',
    fontSize: 12,
  },
  heading: {
    fontSize: 12,
    fontFamily: 'Lato-Bold',
    color: '#4a4a4a',
    marginBottom: 5,
  },
  radioOptions: {
    marginTop: 3,
  },
  mandatory: {
    color: '#eb2026',
  },
  errorWrap: {
    marginTop: 5,
    minHeight: 22,
  },
  errorText: {
    color: '#eb2026',
    fontSize: 10,
    fontFamily: 'Lato-Bold',
  },
});
export default RadioOptions;
