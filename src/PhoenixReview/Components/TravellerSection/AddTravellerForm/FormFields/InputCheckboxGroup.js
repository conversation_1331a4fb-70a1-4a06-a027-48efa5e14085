import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { isEmptyString } from '../../../../../utils/HolidayUtils';

const InputCheckboxGroup = (props) => {
  const { options, label, widthPercentage, isMandatory, errorText, values, handleChange } = props;

  const checkboxToggle = (value) => {
    if (values.includes(value)){
        var newState = values.length > 0 ? values.filter(e=>e !== value) : [];
        handleChange(newState);
    }
    else {
      handleChange([...values,value]);
    }
  };
  return (
    <View style={styles.checkBoxOptions}>
      {!isEmptyString(label) &&
        <Text style={styles.heading}>{label} {isMandatory && <Text style={styles.mandatory}>*</Text>}</Text>
      }
      <View style={{ marginTop: 6, flexDirection: 'row', width: '100%', flexWrap: 'wrap' }}>
        {
          options && options.length && options.map((item, index) => {
            let selected = values && values.includes(item?.value);
            return <TouchableOpacity
              key={item?.value}
              style={[styles.checkboxByn, { minWidth: widthPercentage ? `${widthPercentage}%` : '100%' }]}
              onPress={() => {
                checkboxToggle(item?.value);
              }}
            >
              <View style={[styles.checkbox, selected ? styles.activeCheckbox : '']}>
                {selected && <View style={styles.checkboxInside}><View style={styles.checkmark} /></View>}
              </View>
              <View style={[styles.checkboxText, { marginLeft: 10 }]}>
                <Text style={styles.boldText}>{item?.label}</Text>
              </View>
            </TouchableOpacity>;
          })
        }
      </View>
      <Text style={styles.errorText}>{errorText}</Text>
    </View>

  );
};

const styles = StyleSheet.create({
  checkbox: {
    width: 18,
    height: 18,
    backgroundColor: '#fff',
    borderRadius: 2,
    borderWidth: 1,
    borderColor: '#9b9b9b',
    marginRight: 10,
    overflow: 'hidden',
},
  activeCheckbox: {
    borderColor: '#008cff',
},
checkboxInside: {
    width: '100%',
    height: '100%',
    backgroundColor: '#008cff',
    alignItems: 'center',
    justifyContent: 'center',
},
checkboxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
},
checkmark: {
    position: 'relative',
    top: -1,
    height: 5,
    width: 10,
    borderBottomWidth: 2,
    borderLeftWidth: 2,
    borderColor: 'white',
    transform: [{ rotate: '-45deg' }],
},
  checkboxText: {
    marginTop: 7,
    marginRight: 24,
    flexDirection: 'row',
  },
  checkboxByn: {
    marginBottom: 10,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  boldText: {
    fontFamily: 'Lato-Regular',
    color: '#000',
    fontSize: 12,
  },
  normalText: {
    fontFamily: 'Lato-Regular',
    color: '#4a4a4a',
    fontSize: 12,
  },
  heading: {
    fontSize: 12,
    fontFamily: 'Lato-Bold',
    color: '#4a4a4a',
    marginBottom: 5,
  },
  checkBoxOptions: {
    marginTop: 3,
  },
  mandatory: {
    color: '#eb2026',
  },
  errorWrap: {
    marginTop: 5,
    minHeight: 22,
  },
  errorText: {
    color: '#eb2026',
    fontSize: 10,
    fontFamily: 'Lato-Bold',
  },
});
export default InputCheckboxGroup;
