import  React from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  Text,
  ScrollView,
} from 'react-native';
import { getAge } from '../../../Utils/ChooseTravellerUtil';
import iconEdit from '@mmt/legacy-assets/src/edit_blue.webp';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const SavedTravellerList = (props) => {
  const {savedTravellersSet, onSavedTravellerClick} = props;
  const onSelection = (item) => {
    onSavedTravellerClick(item);
  };

  const RenderList = () => {
    let components = [];
    for (let item of savedTravellersSet) {
      const { fields } = item;
      const { FIRST_NAME = [], LAST_NAME = [], DOB = [] } = fields || {};
      const age = (DOB && (DOB.length > 0)) ? getAge(DOB[0], 'DD/MM/YYYY') : 0;
      components.push(
        <TouchableOpacity onPress={() => onSelection(item)} style={styles.radioWrapList}>
          <Text numberOfLines={1}>
            {`${FIRST_NAME} ${LAST_NAME}`} {age > 0 ? <Text>{age}y</Text> : []}
          </Text>
          <Image source={iconEdit} style={styles.iconEdit} />
        </TouchableOpacity>,
      );
    }
    return components;
  };

  return (
    <View style={styles.contentWrap}>
      <ScrollView style={styles.scrollViewStyle}>
        {RenderList()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  radioWrapList: {
    borderRadius: 4,
    paddingHorizontal: 10,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.2)',
    marginBottom: 10,
  },
  contentWrap: {
    flex: 1,
  },
  radioListSelected: {
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
  },
  iconEdit:{
    width: 24,
    height: 24,
    resizeMode: 'cover',
  },
  iconClose: {
    width: 24,
    height: 24,
  },
  scrollViewStyle: {
    ...marginStyles.mv16,
  },
});

export default SavedTravellerList;
