import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import React, { useEffect } from 'react';
import { StyleSheet, Text, View, Image, TouchableOpacity } from 'react-native';
import { logHolidayReviewPDTClickEvents } from '../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

const PRICE_INCREASE_EVENT = 'ReviewUpdate_PriceIncrease_Persuasion_Seen';
const PRICE_DECREASE_EVENT = 'ReviewUpdate_PriceDecrease_Persuasion_Seen';

const iconClose = require('@mmt/legacy-assets/src/close-white.webp');
const PriceChangeCard = ({newPrice, onClose , persuasionData ,trackReviewLocalClickEvent}) => {
    const { description , colorCode ,iconUrl , action } = persuasionData || {};
    var textColor = colors.black;

    const isPriceIncrease = action === 'PRICE_INCREASE';
    const isPriceDecrease = action === 'PRICE_DECREASE';
    const priceChangeEvent = isPriceDecrease ? PRICE_DECREASE_EVENT : '';

    const captureClickEvents = () => {
      const event = isPriceIncrease ? PRICE_INCREASE_EVENT : priceChangeEvent;
      if (event) {
        logHolidayReviewPDTClickEvents({
          actionType: PDT_EVENT_TYPES.contentSeen,
          value: event,
          shouldTrackToAdobe:false
        });
        trackReviewLocalClickEvent(event);
      }
    };
    
    useEffect(() => {
      captureClickEvents();
    }, [isPriceIncrease, priceChangeEvent]);

    return (
        <View style={[styles.container, {backgroundColor: colorCode}]}>
            <Image source={{uri : iconUrl}} style={[styles.priceIcon]} />
            <Text style={[styles.description, { color: textColor }]}>{description}</Text>
            <TouchableOpacity onPress={onClose}>
                <Image source={iconClose} style={[styles.image, { tintColor: '#808080' }]} />
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        paddingVertical: 12,
        paddingLeft: 15,
        paddingRight: 15,
        alignItems: 'center',
    },
    image: {
        height: 24,
        width: 24,
        padding: 4,
        marginLeft: 10,
    },
    priceIcon  :{
        height:26,
        width:26,
        marginRight:15
    },
    description: {
        fontFamily: 'Lato-Regular',
        fontSize: 12,
        flex: 1,
        flexWrap: 'wrap',
    },
});

export default PriceChangeCard;
