import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions, Animated, Image, ActivityIndicator } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { isEmpty, noop } from 'lodash';
import { GIFT_CARD_STATUS } from 'mobile-holidays-react-native/src/PhoenixReview/Actions/PhoenixReviewAction';
import { ADDING_GIFT_CARDS, MMT_GIFT_CARD_SNACK_BAR_DURATION, TRY_AGAIN, } from 'mobile-holidays-react-native/src/PhoenixReview/Utils/HolidayReviewConstants';
import { getImageUrl, IMAGE_ICON_KEYS } from 'mobile-holidays-react-native/src/Common/Components/HolidayImageUrls';
import { PDTConstants } from 'mobile-holidays-react-native/src/Review/HolidayReviewConstants';
import { logHolidayReviewPDTClickEvents } from 'mobile-holidays-react-native/src/PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

const { width } = Dimensions.get('window');

const PhoenixReviewPageToastV2 = forwardRef((props, ref) => {
  const {
    attachGiftCardStatus,
    addGiftCard = null,
    trackReviewLocalClickEvent = noop,
    attachGiftCardData = null,
    retryCount = 0,
    tryAgainCount = noop,
  } = props;
  let toastTimer;
  // State variables for toast content
  const [content, setContent] = useState('');
  const [startIcon, setStartIcon] = useState('');
  const MAX_RETRY_COUNT = 2;
  const isBtnVisible= retryCount < MAX_RETRY_COUNT;
  const [visible, setVisible] = useState(false);
  const opacity = useState(new Animated.Value(0))[0];

  const captureClickEvents = ({ actionType = {}, eventName = ''}) => {
    trackReviewLocalClickEvent(eventName);
    logHolidayReviewPDTClickEvents({
      actionType,
      value: eventName,
    });
  }

  useEffect(() => {
    let timer;
    switch (attachGiftCardStatus) {
      case GIFT_CARD_STATUS.LOADING:
        setContent(ADDING_GIFT_CARDS);
        setStartIcon(getImageUrl(IMAGE_ICON_KEYS.GIFT_CARD_LOADING));
        showSnackbar(); // Show immediately for loading
        break;
      case GIFT_CARD_STATUS.SUCCESS:
        setContent(attachGiftCardData?.statusMessage);
        setStartIcon(getImageUrl(IMAGE_ICON_KEYS.GIFT_CARD_SUCCESS));
        showSnackbar();
        captureClickEvents({
          actionType: PDT_EVENT_TYPES.contentSeen,
          eventName: PDTConstants.GC_ADD_SUCCESS,
        });
        timer = setTimeout(hideSnackbar, MMT_GIFT_CARD_SNACK_BAR_DURATION);
        break;
      case GIFT_CARD_STATUS.ERROR:
        setContent(attachGiftCardData?.error?.message);
        setStartIcon(getImageUrl(IMAGE_ICON_KEYS.GIFT_CARD_ERROR));
        showSnackbar();
        captureClickEvents({
          actionType: PDT_EVENT_TYPES.contentSeen,
          eventName: PDTConstants.GC_ADD_FAILURE,
        })
        timer = setTimeout(hideSnackbar, MMT_GIFT_CARD_SNACK_BAR_DURATION);
        break;
      default:
        hideSnackbar();
    }

    return () => {
      clearTimeout(timer);
    };
  }, [attachGiftCardStatus, showSnackbar, hideSnackbar, trackReviewLocalClickEvent]);


  const showSnackbar = useCallback(() => {
    setVisible(true);
    Animated.timing(opacity, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [opacity]);

  const hideSnackbar = useCallback(() => {
    Animated.timing(opacity, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setVisible(false);
      setContent('');
      setStartIcon('');
    });
  }, [opacity]);

  useImperativeHandle(ref, () => ({
    show: showSnackbar,
  }));


  const handleActionPress = () => {
    captureClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventName: PDTConstants.GC_ADD_GC_FAILURE_TRY_AGAIN
    })
    hideSnackbar();
    toastTimer = setTimeout(() => {
    tryAgainCount();
    showSnackbar()
    addGiftCard();
    }, 300);
  };

  if (!visible) return null;

 return (
  <Animated.View style={[styles.outerContainer, { opacity }]}>
    <View style={styles.container}>
      <View style={styles.iconContainer}>
        {attachGiftCardStatus === GIFT_CARD_STATUS.LOADING ? (
          <Spinner
            size={23}
            strokeWidth={3}
            progressPercent={85}
            speed={1.5}
            color={holidayColors.primaryBlue}
          />
        ) : (
          <HolidayImageHolder style={styles.icon} imageUrl={startIcon} />
        )}
      </View>
      <Text style={styles.message}>{content}</Text>
      {isBtnVisible && attachGiftCardStatus === GIFT_CARD_STATUS.ERROR && (
        <TouchableOpacity onPress={handleActionPress} style={styles.actionButton}>
          <Text style={styles.actionText}>{TRY_AGAIN}</Text>
        </TouchableOpacity>
      )}
    </View>
  </Animated.View>
);
});

const styles = StyleSheet.create({
  outerContainer: {
    position: 'absolute',
    bottom: 120,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: holidayColors.gray,
    paddingVertical: 20,
    paddingHorizontal: 16,
    borderRadius: 8,
    width: width - 32,
    marginHorizontal: 16,
  },
  iconContainer: {
    marginRight: 12,
  },
  icon: {
    width: 24,
    height: 24,
  },
  message: {
    flex: 1,
    color: holidayColors.white,
    fontSize: 16,
    ...fontStyles.labelBaseRegular,
  },
  actionButton: {
    marginLeft: 12,
  },
  actionText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelMediumBold,
  },
});

export default PhoenixReviewPageToastV2;
