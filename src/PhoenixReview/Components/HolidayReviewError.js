import React from 'react';
import { BackHandler, Image, Platform, ScrollView, Text, View } from 'react-native';
import PropTypes from 'prop-types';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { HARDWARE_BACK_PRESS } from '../../SearchWidget/SearchWidgetConstants';
import StickyHeaderTop from '../../PhoenixDetail/Components/StickyHeaderTop';
import HelpBtn from '../../Grouping/Components/HolidayHelpBtn';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { PRESALES_EXPIRY_CODE } from '../../Review/HolidayReviewConstants';
import withBackHandler from '../../hooks/withBackHandler';

const errorIcon = require('@mmt/legacy-assets/src/detailError.webp');
const chatIcon = require('@mmt/legacy-assets/src/iconChatError.webp');

class HolidayReviewError extends BasePage {
  static navigationOptions = {
    header: null,
  };

  onBackClick=()=>{
    this.props.onBackPressed();
    return true;
  }

  render() {
    const {
      duration, onBackPressed, packageName, startChat, errorMessage,codeValue
    } = this.props;
    const iconChat = {
      width: 22,
      height: 19,
      marginRight: 5,
    };
    return (
      <View style={[AtomicCss.flex1, styles.container]}>
        <View style={styles.stickyHeaderContainer}>
          <StickyHeaderTop
            packageName={packageName}
            duration={duration}
            onBackPressed={onBackPressed}
            showActions={false}
          />
        </View>
        <ScrollView style={styles.pageContent}>
          <View style={styles.messageContent}>
            <Image style={styles.messageImg} source={errorIcon}/>
            <Text style={styles.messageTitle}>Uh Oh!</Text>
            <Text style={styles.messageDesc}>
              {errorMessage}
            </Text>
          </View>
          {codeValue !== PRESALES_EXPIRY_CODE  && <View style={styles.helpSection}>
            <HelpBtn
              source={chatIcon}
              iconStyle={iconChat}
              text="CHAT"
              startAction={startChat}
              isLast
            />
          </View>
          }
        </ScrollView>
      </View>

    );
  }
}

const styles = {
  pageWrapper: {
    flex: 1,
  },
  footerBtnWrapper: {
    paddingHorizontal: 50,
    paddingTop: 15,
    width: '100%',
    marginTop: 5,
  },
  messageContent: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 50,
  },
  messageImg: {
    height: 200,
    width: 200,
    resizeMode: 'contain',
    marginBottom: 40,
  },
  messageTitle: {
    fontSize: 20,
    fontFamily: 'Lato-Bold',
    color: '#282828',
    marginBottom: 10,
    textAlign: 'center',
  },
  messageDesc: {
    fontSize: 14,
    fontFamily: 'Lato-Regular',
    textAlign: 'center',
    lineHeight: 21,
    color: '#3c3c3c',
    opacity: 0.7,
    marginBottom: 20,
  },
  helpSection: {
    paddingHorizontal: 25,
  },
  pageContent: {
    paddingTop: 60,
  },
  stickyHeaderContainer: {
    width: '100%',
    position: 'relative',
    shadowColor: '#330000',
    shadowOpacity: 0.2,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    marginBottom: 2,
    backgroundColor: '#fff',
    zIndex: -2,
  },
  container: {
    ...Platform.select({
      ios: {
        marginTop: -statusBarHeightForIphone,
        paddingTop: statusBarHeightForIphone,
      },
      android: {
        marginTop: 24,
        marginVertical: 10,
        marginHorizontal: 2,
      },
    }),
  },
};

HolidayReviewError.propTypes = {
  startChat: PropTypes.func.isRequired,
  duration: PropTypes.number,
  onBackPressed: PropTypes.func.isRequired,
  packageName: PropTypes.string,
  errorMessage: PropTypes.string.isRequired,
};

HolidayReviewError.defaultProps = {
  duration: null,
  packageName: null,
};

export default withBackHandler(HolidayReviewError)
