import  React from 'react';
import {
    StyleSheet,
    View,
    Text,
  } from 'react-native';
  import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';



  const PolicyDesc = ({policyTitle, content}) => {
      return (
        <View style={AtomicCss.marginBottom15}>
            <View style={AtomicCss.marginBottom5}>
                <Text style={[AtomicCss.font14, {color: '#589231'}, AtomicCss.blackFont]}>{policyTitle}</Text>
            </View>
            <View>{content}</View>
        </View>
      );
  };

  const styles = StyleSheet.create({

    policyList: {
        flexWrap: 'wrap',
        marginTop: 15,
        padding: 15,
        borderWidth: 1,
        borderColor: '#e7e7e7',
        borderRadius: 2,
    },
  });

  export default PolicyDesc;
