import  React from 'react';
import {
    StyleSheet,
    View,
    Text,
  } from 'react-native';
  import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
  import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
  const lightPurpleColor = '#F5F5FF';

  const CancellationPolicyList = (props) => {
      return (
            <View style={styles.policyList}>
                {props.policies.map((item, index) =>
                    <View key={index} style={[AtomicCss.flexRow,AtomicCss.marginBottom8]}>
                        <Text style={styles.bulletStyle}>{'\u2022'}</Text>
                        <View style={styles.width90}>
                            <Text style={styles.policyTextStyle}>{item}</Text>
                        </View>
                    </View>
                )}
            </View>
      );
  };

  const styles = StyleSheet.create({

    policyList: {
        flexWrap: 'wrap',
        padding: 15,
        backgroundColor: lightPurpleColor,
        marginTop: 5,
    },
    bulletStyle: {
        color: colors.textGray,
        ...AtomicCss.font13,
        ...AtomicCss.blackFont,
        ...AtomicCss.marginRight10,
    },
    policyTextStyle: {
        ...AtomicCss.font11,
        ...AtomicCss.blackText,
        ...AtomicCss.regularFont,
    },
    width90: {
        width: '90%',
    },
  });

  export default CancellationPolicyList;
