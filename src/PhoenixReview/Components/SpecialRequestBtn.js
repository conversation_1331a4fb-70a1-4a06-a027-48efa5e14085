import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { StyleSheet} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import TextButton from '../../Common/Components/Buttons/TextButton';

class SpecialRequestBtn extends Component {
  render() {
    const {label, index} = this.props;
    const activeBtnOpacity = 0.7;
    const containerStyle = [styles.container];
    const textStyle = [styles.text];

    return (
      <TextButton
        buttonText={label}
        handleClick={() => this.props.updateRequest(index)}
        btnTextStyle={textStyle}
        btnWrapperStyle={AtomicCss.flexRow}
      />
    );
  }
}

SpecialRequestBtn.propTypes = {
  label: PropTypes.string.isRequired,
  updateRequest: PropTypes.func.isRequired,
  index: PropTypes.number.isRequired,
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 8,
    paddingHorizontal: 10,
    ...holidayBorderRadius.borderRadius8,
    marginVertical: 5,
    marginRight:10,
    backgroundColor: holidayColors.white,
    borderWidth:1,
    borderColor:holidayColors.grayBorder,
  },
  activeContainer: {
    backgroundColor: holidayColors.lightBlueBg,
    shadowOpacity: 0,
    elevation: 0,
  },
  text: {
    ...fontStyles.labelBaseRegular,
    color:holidayColors.gray,
    letterSpacing: 0,
  },
  buttonDisabled: {
    backgroundColor: holidayColors.white,
  },
});

export default SpecialRequestBtn;
