import React from 'react';
import { StyleSheet, View, Image, Text, Dimensions } from 'react-native';
import fecha from 'fecha';
import isEmpty from 'lodash/isEmpty';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import flexiPackage from '@mmt/legacy-assets/src/holidays/ic_flexi.webp';
import mySafetyLogo from '@mmt/legacy-assets/src/mySafetyIcon.webp';
import { PACKAGE_TYPE_FIT } from '../../HolidayConstants';
import { DATE_FORMAT } from '../../Review/HolidayReviewConstants';
import {
  getTotalTravellers,
  isFlexiPackage,
  getPackageTypeString,
} from '../Utils/HolidayReviewUtils';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { borderRadiusValues, holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../Styles/Spacing/index';
import { connect } from 'react-redux';
import { logHolidayReviewPDTClickEvents } from '../Utils/HolidayReviewPDTTrackingUtils';
import { noop } from 'lodash';
import { getMemberShipCardArray } from '../../Common/Components/Membership/utils/MembershipUtils';
import { CONTENT_TYPES } from '../../Common/Components/Membership/utils/constants';
import MembershipCarouselV2 from '../../Common/Components/Membership/Carousel/MembershipCarouselV2';

const SCREEN_WIDTH = Dimensions?.get('window')?.width || 32;


const PackageInfo = ({ reviewDetail, showReviewPopup = () => {}, mmtBlackDetail = {}, trackReviewLocalClickEvent = noop, personalizationDetail = {} }) => {
  const { metadataDetail, destinationDetail, departureDetail, roomDetail, configDetail } =
    reviewDetail || {};
  const { title, imageUrl } = configDetail?.packageType || {};
  const isSafe = metadataDetail?.safe;
  const getEndDestinationDate = () => {
    if (isEmpty(destinationDetail) || isEmpty(destinationDetail.destinations)) {
      return null;
    }
    return destinationDetail.destinations[destinationDetail.destinations.length - 1].end;
  };
  const getDurationString = () => {
    if (isEmpty(destinationDetail)) {
      return null;
    }
    return `${destinationDetail.duration + 1}D/${destinationDetail.duration}N`;
  };
  const getDestinationViews = () => {
    if (isEmpty(destinationDetail) || isEmpty(destinationDetail.destinations)) {
      return null;
    }
    // destinationDetail.destinations = [...destinationDetail.destinations, { duration: 1, name: 'Kerala' }, { duration: 1, name: 'Karnataka' }, { duration: 2, name: 'Lucknow' }, { duration: 10, name: 'Andaman nikobar' }]
    const views = [];
    destinationDetail.destinations.forEach((dest) => {
      views.push(<Text style={[styles.durationText]}>{`${dest.duration}N ${dest.name}`}</Text>);
      views.push(<View style={styles.bulletedDot} />);
    });
    return views.slice(0, views.length - 1);
  };
  const getImageURI = () => {
    return { uri: null };
  };
  const getDateViews = (dateString) => {
    if (isEmpty(dateString)) {
      return null;
    }
    const dateObj = fecha.parse(dateString, DATE_FORMAT);
    const dateArray = fecha.format(dateObj, 'D MMM YYYY dddd').split(' ');
    const date = dateArray[0];
    const month = dateArray[1];
    const year = dateArray[2];
    const day = dateArray[3];
    const views = [];
    views.push(
      <Text style={[styles.dateText]}>
        {`${month} ${date}, `}
        <Text style={[styles.dateText]}>{year}</Text>
      </Text>,
    );
    views.push(
      <View style={AtomicCss.paddingTop3}>
        <Text style={[styles.dayText]}>{day}</Text>
      </View>,
    );
    return views;
  };
  const { noOfAdults, children } = getTotalTravellers(roomDetail);
  const totalTravellers = noOfAdults + children;
  let paxLabel = '';
  if (noOfAdults === 1) {
    paxLabel += `${noOfAdults} Adult`;
  } else if (noOfAdults > 1) {
    paxLabel += `${noOfAdults} Adults`;
  }
  if (children === 1) {
    paxLabel += ` ${children} Child`;
  } else if (children > 1) {
    paxLabel += ` ${children} Children`;
  }

   const memberShipCardData = {
     cards: getMemberShipCardArray({
       mmtBlackDetail,
       personalizationDetail,
     }),
   };

  const trackMmtBlackClickEvent = ({eventName, prop1 = ''}) => {
    trackReviewLocalClickEvent(eventName,'', { prop1 })
  }
  return (
    <View style={marginStyles.mb12}>
      <View>
        <View style={[styles.packageCardLRRadius, styles.packageCardInner, paddingStyles.pa16]}>
          <View style={[AtomicCss.flexRow, { alignItems: 'flex-start' }]}>
            <Text
              style={[{ maxWidth: isSafe ? '80%' : undefined }, styles.packageName]}
              numberOfLines={2}
            >
              {reviewDetail.name}
            </Text>
            {isSafe && (
              <View style={[styles.mySafetyLogo]}>
                <Image source={mySafetyLogo} style={styles.mySafetyImg} />
                <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, paddingStyles.pl6]}>
                  <Text style={[styles.myText]}>My</Text>
                  <Text style={[styles.safetyText]}>Safety</Text>
                </View>
              </View>
            )}
          </View>
          <View style={[styles.cardRow, paddingStyles.pt8, { flexWrap: 'wrap' }]}>
            {isFlexiPackage(reviewDetail) && (
              <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, marginStyles.mr6]}>
                <Image source={{ uri: imageUrl }} style={styles.imgFlexiPackage} />
                <View style={paddingStyles.pl6}>
                  <Text style={styles.flexiTxt}>{title}</Text>
                </View>
              </View>
            )}
          </View>
          <View style={[styles.cardRow, styles.destinationView]}>{getDestinationViews()}</View>
        </View>
      </View>

      <View style={[styles.packageCardInner, styles.durationRow]}>
        <View style={[styles.divider, { top: 0 }]}>
          <View style={styles.dividerInner} />
        </View>
        {/* <View><Image source={getImageURI()} style={styles.photoImg} /></View> */}
        <View style={[styles.cardRow, styles.durationContent, styles.dateView]}>
          <View style={[AtomicCss.flex1, { alignItems: 'flex-start' }]}>
            {getDateViews(departureDetail.departureDate)}
          </View>

          <View style={styles.durationWrap}>
            <View style={[styles.divider, { top: 5, left: 0 }]}>
              <View style={styles.dividerInner} />
            </View>
            {/* <View style={styles.durationdivider}></View> */}
            <View style={styles.duration}>
              <Text style={styles.durationStringText}>{getDurationString()}</Text>
            </View>
          </View>
          <View style={[AtomicCss.flex1, { alignItems: 'flex-end' }]}>
            {getDateViews(getEndDestinationDate())}
          </View>
        </View>
      </View>

      <View style={[styles.packageCardBLRRadius, styles.packageCardInner]}>
        <View style={[styles.divider, { top: -1 }]}>
          <View style={styles.dividerInner} />
        </View>
        <View style={[styles.cardRow, paddingStyles.pa16]}>
          <Text style={[styles.totalTravellerText]}>{`${totalTravellers} Travellers:`}</Text>
          <Text style={[AtomicCss.flex1, styles.fromText]} numberOfLines={1}>
            {' '}
            {`${paxLabel} / From ${departureDetail.cityName}`}
          </Text>
        </View>
        <View />
        {memberShipCardData.cards?.length > 0 &&
            <MembershipCarouselV2
              memberShipCardData={memberShipCardData}
              onKnowMorePress={showReviewPopup}
              mmtblackPdtEvents={logHolidayReviewPDTClickEvents}
              trackMemberShipLoadEvent={trackMmtBlackClickEvent}
              contentType={CONTENT_TYPES.REVIEW}
              containerStyles={styles.membershipCardContainer}
              gradientStyle={styles.gradientStyle}
              showFullBorderGradient={memberShipCardData.cards?.length <= 1}
              cardItemStyle={{
                width: SCREEN_WIDTH - 32,
              }}
              customStyles={styles.customCardStyle}
              customCardsWprStyle={styles.customCardsWprStyle}
            />
          }
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  dividerInner: {
    position: 'absolute',
    left: 0,
    bottom: 0,
    width: '100%',
    height: 1,
    backgroundColor: holidayColors.white,
    zIndex: 1,
  },
  durationText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  packageName: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
  },
  dateText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  dayText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  dateView: {
    ...paddingStyles.pv16,
    backgroundColor: holidayColors.white,
  },
  safetyText: {
    color: holidayColors.black,
    ...fontStyles.labelBaseRegular,
  },
  myText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelBaseRegular,
  },
  totalTravellerText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  fromText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  cardRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  destinationView: {
    ...paddingStyles.pt8,
    ...fontStyles.labelBaseRegular,
    flex: 1,
    flexWrap: 'wrap',
  },
  destinationsRow: {
    flexWrap: 'wrap',
    flexDirection: 'row',
    alignItems: 'center',
  },

  packageCardInner: {
    backgroundColor: holidayColors.white,
  },

  packageCardLRRadius: {
    borderTopLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
  },

  packageCardBLRRadius: {
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderBottomRightRadius: borderRadiusValues.br16,
  },

  durationRow: {
    borderColor: holidayColors.grayBorder,
    borderStyle: 'dashed',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: -3,
    zIndex: 5,
    display: 'flex',
    flexDirection: 'column',
  },
  durationContent: {
    width: '100%',
    ...paddingStyles.ph16,
  },
  durationWrap: {},
  duration: {
    backgroundColor: holidayColors.lightGray2,
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pv4,
    ...paddingStyles.ph12,
    marginTop: -5,
  },
  durationStringText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  durationdivider: {
    height: 1,
    width: '100%',
    borderRadius: 1,
    borderWidth: 1,
    borderColor: holidayColors.lightGray,
    borderStyle: 'dashed',
    zIndex: 0,
    left: 0,
    top: 5,
  },
  divider: {
    width: '100%',
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    borderStyle: 'solid',
    zIndex: 0,
  },
  imgFlexiPackage: {
    width: 12,
    height: 12,
  },
  flexiTxt: {
    color: holidayColors.lightGray,
    ...fontStyles.labelBaseRegular,
    lineHeight: 19,
  },
  bulletedDot: {
    width: 5,
    height: 5,
    borderRadius: 5,
    ...marginStyles.mh8,
    backgroundColor: holidayColors.lightGray,
  },
  photoImg: {
    width: 40,
    height: 40,
    borderRadius: 5,
    backgroundColor: holidayColors.lightGray2,
  },
  mySafetyLogo: {
    borderWidth: 1,
    backgroundColor: holidayColors.lightBlueBg,
    borderColor: holidayColors.primaryBlue,
    borderRadius: 3,
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pr6,
    ...marginStyles.ml6,
    ...marginStyles.mt0,
  },
  mySafetyImg: {
    width: 16,
    height: 16,
  },
  gradientStyle: {
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
  },
  customCardStyle: {
    paddingBottom: 6,
  },
  membershipCardContainer: {
    ...marginStyles.mt14,
  },
  customCardsWprStyle: {
    ...paddingStyles.pb4, 
  }
});
const mapStateToProps = (state) => {
  return {
    mmtBlackDetail: state?.holidaysReview?.mmtBlackDetail,
    personalizationDetail: state?.holidaysReview?.personalizationDetail,
  };
};
export default connect(mapStateToProps)(PackageInfo);
