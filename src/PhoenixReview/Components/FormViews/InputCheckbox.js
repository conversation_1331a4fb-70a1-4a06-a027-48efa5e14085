import React, {useState} from 'react';
import { View, TouchableOpacity, StyleSheet, Text } from 'react-native';
const InputCheckbox = (props) => {
    const {list, onChange, checked} = props;
    const [selected, setSelected] = useState(checked);
    const handleCheckbox = () => {
        const newValue = !selected;
        setSelected(newValue);
        if (onChange) {
            onChange(newValue);
        }
    };
    return (
        <TouchableOpacity
            onPress={handleCheckbox}
            style={[styles.checkboxItem]}
        >
            <View style={[styles.checkbox, selected ? styles.activeCheckbox : '']}>
                {selected && <View style={styles.checkboxInside}><View style={styles.checkmark} /></View>}
            </View>
            <View><Text style={styles.text}>{list}</Text></View>
        </TouchableOpacity>
    );
};


const styles = StyleSheet.create({

    checkbox: {
        width: 18,
        height: 18,
        backgroundColor: '#fff',
        borderRadius: 2,
        borderWidth: 1,
        borderColor: '#9b9b9b',
        marginRight: 10,
        overflow: 'hidden',
    },
    activeCheckbox: {
        borderColor: '#008cff',
    },
    checkboxInside: {
        width: '100%',
        height: '100%',
        backgroundColor: '#008cff',
        alignItems: 'center',
        justifyContent: 'center',
    },
    checkboxItem: {
        flexDirection: 'row',
        alignItems: 'center',
        marginRight: 20,
    },
    checkmark: {
        position: 'relative',
        top: -1,
        height: 5,
        width: 10,
        borderBottomWidth: 2,
        borderLeftWidth: 2,
        borderColor: 'white',
        transform: [{ rotate: '-45deg' }],
    },
    text: {
        fontSize: 11,
        fontFamily: 'Lato-Regular',
        color: '#4a4a4a',
        fontWeight: '500',
    },

});
export default InputCheckbox;
