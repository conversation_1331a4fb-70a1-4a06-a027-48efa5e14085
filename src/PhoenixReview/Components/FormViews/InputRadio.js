import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import RadioButton from '@Frontend_Ui_Lib_App/RadioButton'; 
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const InputRadio = (props) => {
    const { index, isActive , handleRadioBtn, children} = props;
    const selected = index === isActive;
    return (
        <TouchableOpacity
            style={[styles.radioItem]}
        >
            <View>
                <RadioButton
                    activeColor={holidayColors.primaryBlue}
                    isSelected={selected}
                    alignMiddle
                    borderGap={8}
                    inactiveColor={holidayColors.primaryBlue}
                    label={children}
                    onPress={handleRadioBtn}
                    radioBgColor={holidayColors.white}
                    radioSize={20}
                />
            </View>

        </TouchableOpacity>
    );
};


const styles = StyleSheet.create({

    radio: {
        width: 20,
        height: 20,
        backgroundColor: '#fff',
        borderRadius: 20,
        borderWidth: 2,
        borderColor: '#9b9b9b',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight:5,
        overflow: 'hidden',
    },
    activeRadio: {
        borderColor: '#008cff',
    },
    radioInside: {
        width: '100%',
        height: '100%',
        backgroundColor: '#008cff',
        borderWidth: 1.5,
        borderColor: 'white',
        borderRadius: 18,

    },
    radioItem: {
        flexDirection: 'row',
        alignItems: 'center',

    },
});
export default InputRadio;
