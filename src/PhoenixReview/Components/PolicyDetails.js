import React from 'react';
import {
    StyleSheet,
    View,
    Text,
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { CANCELLATION_POLICY, DATE_CHANGE_POLICY, FLEXI_DESCRIPTION, FLEXI_KEY, ZC_DESCRIPTION, ZC_KEY } from '../Utils/HolidayReviewConstants';
import VerticalStepInfo from './VerticalStepInfo';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { paddingStyles, marginStyles } from '../../Styles/Spacing/index';

const headingsZC = {
    cancellation:'Current Cancellation Policy',
    zc:'Cancellation Policy with ZC',
};
const headingsFlexi = {
    cancellation:'Current Date Change Policy',
    zc:'Date Change Policy with Flexi Date',
};

const PolicyDetails = ({ penalties, zcOption, isZeroCancellation }) => {
    const selectedTab = isZeroCancellation ? CANCELLATION_POLICY : DATE_CHANGE_POLICY;
    const selectedTabDescription = isZeroCancellation ? ZC_DESCRIPTION : FLEXI_DESCRIPTION;
    return (
        <View>
             <View style={styles.columnHeader}>
                <Text style={[styles.zeroCancellation, {textAlignVertical: 'center',flex:1}]}>
                    {isZeroCancellation ? headingsZC.cancellation : headingsFlexi.cancellation}
                </Text>
                <Text style={[styles.zeroCancellation, {textAlignVertical: 'center',flex:1}]}>
                    {isZeroCancellation ? headingsZC.zc : headingsFlexi.zc }
                </Text>
            </View>
            <View   style={[{backgroundColor:holidayColors.lightGray2,display:'flex',flexDirection:'row'}, paddingStyles.pb16]}>
                {/* left */}
                {penalties?.length > 0  ?
                    <VerticalStepInfo
                        mode={selectedTab}
                        penalties={penalties}
                        zcOption={zcOption}
                        zcSelected={false}
                    />
                : []
            }
            {/* right */}
            {penalties?.length > 0 && zcOption?.available ?
                <VerticalStepInfo
                    mode={selectedTab}
                    penalties={penalties}
                    zcOption={zcOption}
                    zcSelected={true}
                /> : []
            }
        </View>
        </View>
    );
};

const styles = StyleSheet.create({

    columnHeader: {
        backgroundColor: holidayColors.lightBlueBg,
        width:'100%',
        display:'flex',
        flexDirection:'row',
        alignItems:'center',
        paddingHorizontal:5,
    },
    zeroCancellation:{
        ...fontStyles.labelSmallBlack,
        color:holidayColors.black,
        ...paddingStyles.pv8,
        ...paddingStyles.ph8,
    },
    columns: {
        ...paddingStyles.pt16,
        marginLeft: -12,
    },
    columnDivider: {
        borderColor: holidayColors.lightGray2,
        borderStyle: 'dashed',
        borderRadius: 1,

    },
    iconList: {
        width: 20,
        height: 20,
    },
    linearRods: {
        width: 5,
        height: '100%',
        borderRadius: 5,
        ...marginStyles.ml10,
    },

    iconZc: {
        width: 104,
        height: 18,
        marginTop: 3,
    },
    iconClose: {
        width: 24,
        height: 24,
    },
});

export default PolicyDetails;
