import React from 'react';
import {

    View,
    StyleSheet,
    Modal,
    TouchableWithoutFeedback,
    TouchableOpacity,
    Dimensions,
} from 'react-native';
import { borderRadiusValues, holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { holidayColors } from '../../Styles/holidayColors';

const windowHeight = Dimensions.get('window').height;
const windowWidth = Dimensions.get('window').width;

const BottomSheet = (props) => {
    const { children, closeModal, modalVisible, disableHeight } = props || {};
    return (
        <Modal
            animationType="fade"
            transparent={true}
            visible={modalVisible}
            onRequestClose={closeModal}
        >
            <TouchableOpacity
                activeOpacity={1}
                style={styles.overlay}
                onPressOut={() => {}}>
                <TouchableWithoutFeedback>
                    <View style={styles.bottomSheet} onStartShouldSetResponder={() => true}>
                        {children}
                    </View>
                </TouchableWithoutFeedback>
            </TouchableOpacity>

        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        width: windowWidth,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
        height: windowHeight,
    },
    bottomSheet: {
        width: '100%',
        backgroundColor: holidayColors.white,
        borderTopLeftRadius: borderRadiusValues.br16,
        borderTopRightRadius: borderRadiusValues.br16,
        elevation: 20,
    },
});

export default BottomSheet;
