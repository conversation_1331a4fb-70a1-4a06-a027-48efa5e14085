import React from 'react';
import PropTypes from 'prop-types';
import { View, Text, StyleSheet, Image, Platform, TouchableOpacity } from 'react-native';
import { backIcon } from '@mmt/legacy-commons/Helpers/displayHelper';
import { getPackageTypeString } from '../Utils/HolidayReviewUtils';
const MAX_NAME_CHARS = 37;
const Header = ({ reviewDetail, onBackPressed }) => {
  const getSubTitleView = () => {
    const packageType = getPackageTypeString(reviewDetail);
    const name = reviewDetail?.name;
    const { destinationDetail } = reviewDetail || {};
    const duration = destinationDetail && `${destinationDetail?.duration + 1}D/${destinationDetail?.duration}N`;
    return (<Text style={[styles.subTitleStyle]} numberOfLines={1}>
      {name}
    </Text>);
  };
  return (
    <View style={styles.headerStyleReview}>
      <TouchableOpacity onPress={onBackPressed} style={styles.backWrapperStyle}>
        <Image style={styles.iconBack} source={backIcon} />
      </TouchableOpacity>
      <View style={{ marginTop: 10 }}>
        <Text style={styles.titleStyle}>Review Page</Text>
        {getSubTitleView()}
      </View>
    </View>
  );
};

Header.propTypes = {
  reviewDetail: PropTypes.object.isRequired,
  onBackPressed: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  headerStyleReview: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    color: '#4a4a4a',
    paddingVertical: 12,
    shadowColor: '#330000',
    shadowOpacity: 0.2,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    elevation: 2,
    zIndex: 2,
    ...Platform.select({
      ios: {
        paddingRight: 50,
      },
    }),
  },
  backWrapperStyle: {
    paddingRight: 12,
    paddingLeft: 15,
    paddingVertical: 15,
  },
  titleStyle: {
    color: '#4a4a4a',
    fontFamily: 'Lato-Bold',
    letterSpacing: 0.3,
    fontSize: 16,
    marginBottom: 6,
  },
  subTitleStyle: {
    color: '#6D7278',
    fontFamily: 'Lato-Bold',
    letterSpacing: 0.3,
    fontSize: 12,
    marginRight: 40,
  },
  iconBack: {
    height: 16,
    width: 16,
  },
});
export default Header;
