import React, { useState } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Text,
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconClose from '../images/ic_closeGrey.png';
import LinearGradient from 'react-native-linear-gradient';
import { PDTConstants } from '../Utils/HolidayReviewConstants';
import { logHolidayReviewPDTClickEvents } from '../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

const FieldConfirmation = (props) => {
    const { closeModal, setErrors, submitValues, fields, validateObj, trackReviewLocalClickEvent, scroll, layoutMap } = props;

    const paxFieldMap = validateObj?.modifiedPaxDetail?.paxFieldMap;
    const fieldMessageMap = validateObj?.validationDetail?.fieldMessageMap;
    const paxFields = paxFieldMap && Object.keys(paxFieldMap);
    const fieldKeys = fields && Object.keys(fields);
    const fieldList = fieldMessageMap && Object.keys(fieldMessageMap);
    const header = fieldList && fieldList.length > 0 ? fields[fieldList[0]]?.label : '';
    
    const captureClickEvents = ({ eventName }) => {
      logHolidayReviewPDTClickEvents({
        actionType : PDT_EVENT_TYPES.buttonClicked,
        value: eventName,
      })
      trackReviewLocalClickEvent(eventName, '');
    };
    const handleClose = () => {
        const keys = Object.keys(fieldMessageMap);
        const gotoField = keys.length > 0 ? keys[0] : null;
        if (layoutMap[gotoField]?.y || layoutMap[gotoField]?.y === 0) {scroll.current?.scrollTo({y: layoutMap[gotoField].y, animated: true});}
        setErrors(fieldMessageMap);
        closeModal();
        captureClickEvents({ name: PDTConstants.CLOSE_NAME_POPUP });
    };
    const handleConfirm = () => {
        const values = {};
        paxFields.forEach(field=>{
            if (fieldKeys.includes(field)) {values[field] = [paxFieldMap[field]];}
        });
        submitValues(values);
        closeModal();
        setErrors({});
        captureClickEvents({ name: PDTConstants.CONFIRM_NAME_POPUP });
    };

    return (
        <View style={styles.contentWrap}>
            <View style={styles.headerWrap}>
                <View style={[AtomicCss.alignCenter, AtomicCss.flexRow]}>
                    <TouchableOpacity onPress={()=>handleClose()}><View style={AtomicCss.marginRight15}><Image source={iconClose} style={styles.iconClose} /></View></TouchableOpacity>
                    <Text style={[AtomicCss.font18, AtomicCss.blackFont, AtomicCss.blackText]}>{header} Confirmation</Text>
                </View>
                <TouchableOpacity onPress={()=>handleConfirm()}>
                    <Text style={[AtomicCss.azure, AtomicCss.font14, AtomicCss.blackFont]}>CONFIRM</Text>
                </TouchableOpacity>
            </View>

            <View style={styles.contentInnerWrap}>
                <View>
                    {
                        fieldList && fieldList.map(field =>
                            <Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.regularFont]}>{fieldMessageMap[field]}</Text>
                        )
                    }
                </View>
                <View style={styles.horizontalLine} />
                <LinearGradient start={{ x: 0, y: 0 }} end={{ x: 1, y: 0 }} colors={['#A9D4F6', '#0874CE']} style={styles.nameCard}>
                    <View style={[styles.whiteCircle, styles.circletop]} />
                    <View style={styles.cardContent}>
                        {
                            paxFields && paxFields.map((field,i) =>
                               {
                                   if (fieldKeys.includes(field)){
                                    return <View style={i === 0 ? AtomicCss.marginBottom10 : AtomicCss.marginTop15}>
                                             <View style={AtomicCss.marginBottom2}><Text style={[AtomicCss.font12, AtomicCss.whiteText, AtomicCss.textUpper, AtomicCss.regularFont]}>{fields[field]?.label}</Text></View>
                                             <Text style={[AtomicCss.font20, AtomicCss.whiteText, AtomicCss.textUpper, AtomicCss.blackFont]}>{paxFieldMap[field]}</Text>
                                           </View>;
                                }
                               }
                            )
                        }
                    </View>
                    <View style={[styles.whiteCircle, styles.circleBottom]} />
                </LinearGradient>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    contentWrap: {
        paddingTop: 25,
    },
    headerWrap: {
        marginBottom: 15,
        paddingHorizontal: 20,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    iconClose: {
        width: 24,
        height: 24,
    },
    nameCard: {
        borderRadius: 6,
        paddingHorizontal: 20,
        marginTop: 20,
    },
    whiteCircle: {
        width: 25,
        height: 25,
        borderRadius: 25,
        backgroundColor: 'white',
    },
    circletop: {
        marginTop: -12.5,
        marginLeft: 15,
    },
    circleBottom: {
        marginBottom: -12.5,
        marginLeft: 15,
    },
    cardContent: {
        marginLeft: 40,
        padding: 20,
    },
    contentInnerWrap: {
        padding: 15,
        marginBottom: 80,
    },
    horizontalLine: {
        width: 30,
        height: 4,
        backgroundColor: '#0055B7',
        marginTop: 15,
        marginBottom: 15,
    },
});

export default FieldConfirmation;
