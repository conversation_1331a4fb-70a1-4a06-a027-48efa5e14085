import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import { rupeeFormatterUtils } from '../../../utils/HolidayUtils';
import iconArrowUp from '../../images/ic_arrow_up_white.webp';
import PrimaryButton from '../../../Common/Components/Buttons/PrimaryButton';
import React from 'react';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { isEmpty } from 'lodash';


export const ReviewFooterV2 = props => {
const {fareBreakupResponse, handleOnButtonPress, setFareBreakupVisibilityForTcs} = props || {};
const {amountTitle, amountPayable, reservePrice = ''} = fareBreakupResponse || {};
const canProceedToPayment = !isEmpty(fareBreakupResponse) && amountPayable;

  return (
    <View style={styles.pageFooter}>
      {canProceedToPayment && <View style={styles.footerLeft}>
        {reservePrice ? (
          <Text style={styles.upperHeading}>
            Reserve for{' '}
            <Text style={AtomicCss.blackFont}>₹ {`${rupeeFormatter(reservePrice)}`}*</Text>
          </Text>
        ) : (
          []
        )}
        <View style={styles.netPriceContainer}>
          <Text style={styles.netPrice}>
            {rupeeFormatterUtils(amountPayable)}
          </Text>
          <TouchableOpacity onPress={() => setFareBreakupVisibilityForTcs(true)}>
            <View style={[styles.fareDetailsWrap]}>
              <Text style={styles.fareDetailsText}>Fare Details</Text>
              <View style={AtomicCss.paddingLeft5}><Image source={iconArrowUp} style={styles.iconArrowUp} /></View>
            </View>
          </TouchableOpacity>
        </View>
        <Text style={styles.grandTotal}>{amountTitle}</Text>
      </View>}
      <View style={styles.footerRight}>
        <PrimaryButton
          isDisable={!canProceedToPayment}
          buttonText={'CONTINUE'}
          showImage={true}
          imageStyle={styles.buttonImage}
          handleClick={handleOnButtonPress}
          btnContainerStyles={styles.footerButton}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  pageFooter: {
    backgroundColor: colors.black28,
    ...paddingStyles.pv10,
    ...paddingStyles.ph16,
    flexDirection: 'row',
  },
  footerLeft: {
  },
  footerRight: {
    marginLeft: 'auto',
  },
  footerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent:'center',
    ...paddingStyles.ph16,
    borderRadius: 30,
  },
  upperHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.white,
    opacity: 0.7,
  },
  netPriceContainer: {
    ...AtomicCss.flexRow,
    ...AtomicCss.alignCenter,
    ...AtomicCss.flexWrap,
    ...paddingStyles.pt4,
    ...paddingStyles.pb2,
  },
  netPrice: {
    ...fontStyles.headingBase,
    color: holidayColors.white,
  },
  fareDetailsWrap: {
    borderWidth: 1,
    ...paddingStyles.pv2,
    ...paddingStyles.ph8,
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: holidayColors.lightGray2,
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.ml16,
  },
  fareDetailsText: {
    color: holidayColors.white,
    ...fontStyles.labelSmallRegular,
  },
  grandTotal: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.disableGrayBg,
    opacity: 0.7,
  },
  iconArrowUp: {
    width: 8.5,
    height: 5,
    resizeMode: 'cover',
  },
  grossPrice: {
    ...fontStyles.labelSmallRegular,
    color: 'rgba(256,256,256,0.7)',
    textDecorationLine: 'line-through',
    ...marginStyles.ml4,
  },
  buttonImage:{
    width: 15,
    height: 15,
    tintColor: holidayColors.white,
    ...marginStyles.ml4
  },
});
