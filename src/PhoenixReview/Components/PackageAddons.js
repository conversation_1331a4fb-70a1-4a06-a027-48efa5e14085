import React, { useEffect, useState } from 'react';
import {
    StyleSheet,
    ScrollView,
    View,
    TouchableOpacity,
    Image,
    Text,
    TouchableWithoutFeedback,
} from 'react-native';

import iconzc from '../images/ic_zc.png';
import iconFlexiDate from '../images/ic_flexiDate.png';
import iconTickBlue from '../images/ic_tickBlue.png';
import BottomSheet from './BottomSheet';
import ZeroCancellation from './ZeroCancellation';
import { fillDateAndTime } from '../../Common/HolidaysCommonUtils';
import { PDTConstants } from '../Utils/HolidayReviewConstants';
import { addDays } from '@mmt/legacy-commons/Helpers/dateTimehelpers';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { marginStyles } from '../../Styles/Spacing';

let tabList = [];
const headings = {
    ZCFlexi :'Worried about Changes & Cancellations?',
    ZC:'Worried about Cancellations?',
    Flexi:'Worried about Changes in the Plan?',
};
const tabListContent = {
    ZC:'Zero Cancellation',
    Flexi:'Flexi Date',
    ZCFlexi:'Zero Cancellation & Flexi Date',
};
const getFormattedDate = (date) => {
    if (date) {
        const newDate = addDays(date, -1);
        return fillDateAndTime(newDate, 'MMMM DD,YYYY');
    }
    return '';
};
const types = {
    ZC:'ZC',
    FLEXI:'Flexi',
};
const PackageAddOns = (props) => {
    const { reviewData, trackReviewLocalClickEvent, penaltyDetail = {} } = props;
    const zcOptions = penaltyDetail?.zcOptions;

    const [modal, setModal] = useState({show:false});
    const [tabActive, setTabActive] = useState(0);
    const [ZCData, setZCData] = useState({});
    const [FlexiData, setFlexiData] = useState({});

    const toggleTab = (index,len) => {
        if (len < 2) {return;}
        let active = tabActive === index;
        setTabActive(active ? null : index);
    };
    const toggleZC = (selected, isZC) => {
        const type = isZC ? 'ZC' : 'FlexiDate';
        const action = selected ? 'REMOVE' : 'APPLY';
        const PDT_ACTION = selected ? PDTConstants.REMOVE : PDTConstants.SELECT;
        trackReviewLocalClickEvent(PDT_ACTION,`_${type}`);
        props.validateZC(action, type);
    };
    const showCancellationPopup = (type) => {
        trackReviewLocalClickEvent(PDTConstants.KNOW_MORE,`_${type}`);
        setModal({show:true,type:type});
    };
    const closeCancellationPopup = () => {
        setModal({show:false});
    };
    const getZCData = (zcOptions, t) => {
        for (let i = 0; i < zcOptions.length; i++) {
            const { type } = zcOptions[i];
            if (type === t) { //FlexiDate' // ZC
                return zcOptions[i];
            }
        }
        return null;
    };
    const getZCContent = () => {
        const { selected, lastFCDate, available } = ZCData;
        if (!available) {
          return null;
        }
        return (
          <View style={[styles.flexiDateWrap, selected ? styles.selectedOption : null]}>
            <View style={styles.zeroCancellationWrap}>
              <View style={[styles.container]}>
                {selected ? (
                  <View>
                    <Image
                      source={iconTickBlue}
                      style={[styles.iconTickBlue, marginStyles.mr6]}
                    />
                  </View>
                ) : (
                  <View style={[marginStyles.ml16]} />
                )}
                <Image source={iconzc} style={styles.iconzc} />
              </View>
              <TouchableOpacity onPress={() => toggleZC(selected, true)}>
                <View>
                  <Text style={styles.actionStyle}>{selected ? 'Remove' : 'Select'}</Text>
                </View>
              </TouchableOpacity>
            </View>
            <View style={styles.kowMoreWrap}>
              <Text style={styles.knowMoreText}>
                Free cancellation will be allowed till
                <Text style={styles.knowMoreTextBlack}>{` ${getFormattedDate(lastFCDate)} `}</Text>
                <Text
                  onPress={() => showCancellationPopup(types.ZC)}
                  style={styles.knowMoreActionStyle}
                >
                  Know More
                </Text>
              </Text>
            </View>
          </View>
        );
    };
    const getFlexiContent = () => {
        const { lastFCDate, selected, available } = FlexiData;
        if (!available) {
          return null;
        }
        return (
          <View
            style={selected ? [styles.flexiDateWrap, styles.selectedOption] : styles.flexiDateWrap}
          >
            <View style={styles.flexiDateTagWrap}>
              <View style={styles.container}>
                {selected ? (
                  <Image
                    source={iconTickBlue}
                    style={[styles.iconTickBlue, marginStyles.mr6]}
                  />
                ) : (
                  <View style={[marginStyles.ml16]} />
                )}
                <Image source={iconFlexiDate} style={styles.iconFlexiDate} />
              </View>
              <TouchableOpacity onPress={() => toggleZC(selected)}>
                <View>
                  <Text style={styles.actionStyle}>{selected ? 'Remove' : 'Select'}</Text>
                </View>
              </TouchableOpacity>
            </View>
            <View style={styles.kowMoreWrap}>
              <Text numberOfLines={2} style={styles.knowMoreText}>
                Free Date Change will be allowed till
                <Text style={styles.knowMoreTextBlack}>{` ${getFormattedDate(lastFCDate)} `}</Text>
                <Text
                  onPress={() => showCancellationPopup(types.FLEXI)}
                  style={styles.knowMoreActionStyle}
                >
                  Know More
                </Text>
              </Text>
            </View>
          </View>
        );
    };
    const getHeadingData = (data)=>{
    let heading = '';
        if (ZCData.available && FlexiData.available) {heading = data.ZCFlexi;}
        else {
            if (ZCData.available) {heading = data.ZC;}
            else if (FlexiData.available) {heading = data.Flexi;}
        }
        return heading;
    };
    const getTabView = () => {
        tabList = [];
        tabList.push(getHeadingData(tabListContent));
        return (
          <View style={styles.tabListWrapper}>
            {tabList.map((item, index) => {
              const active = tabActive === index;
              return (
                <TouchableWithoutFeedback onPress={() => toggleTab(index, tabList.length)}>
                  <View key={index} style={[styles.tabList, active ? styles.tabListActive : null]}>
                    <Text style={[active ? styles.tabTextActive : styles.tabText]}>{item}</Text>
                  </View>
                </TouchableWithoutFeedback>
              );
            })}
          </View>
        );
    };
    // const getSummary = () => {
    //     const heading = getHeadingData(headings);
    //     return (
    //         <View style={styles.contentPadding}>
    //             <View><Text style={[AtomicCss.font14, AtomicCss.blackFont, AtomicCss.defaultText]}>
    //                 {heading}</Text></View>
    //             <View style={styles.contentBlk}>
    //                 <Text style={[styles.contentBlkTxt, AtomicCss.blackTextFont]}>Radhika secured her Europe holiday with Flexi Date & was able to reschedule her trip for free !</Text>
    //             </View>
    //         </View>

    //     );
    // };
    const getModalContent = () => {
        return (
            <BottomSheet closeModal={() => closeCancellationPopup()}>
                <ZeroCancellation
                    closeModal={() => closeCancellationPopup()}
                    isZeroCancellation={modal.type == types.ZC ? true : false}
                    penaltyDetail={penaltyDetail}
                    zcOptions={zcOptions}
                />
            </BottomSheet>
        );
    };
    useEffect(() => {
        const { zcOptions } = penaltyDetail;
        setZCData(getZCData(zcOptions, 'ZC'));
        setFlexiData(getZCData(zcOptions, 'FlexiDate'));
    }, [penaltyDetail]);
    if (!ZCData.available || !FlexiData.Adapter) return null;
    return (
        <View style={styles.tabContent}>
            <View>
                {getTabView()}
            </View>
            {/*{getSummary()}*/}
            {ZCData.available && getZCContent()}
            {ZCData.available && FlexiData.available && <View style={styles.orTagWrap}>
                <View style={styles.orTagLine} />
                <View style={styles.orTag}><Text style={styles.orText}>OR</Text></View>
            </View>
            }
            {/* Flexi Date */}
            {FlexiData.available && getFlexiContent()}
            {/* BottomSheet */}
            {modal?.show && getModalContent()}
        </View>
    );
};

const styles = StyleSheet.create({
    tabContent: {
        backgroundColor: holidayColors.white,
        borderColor: holidayColors.grayBorder,
        borderBottomLeftRadius: 4,
        borderBottomRightRadius: 4,
        padding:16,
    },
    tabList: {
        width:'100%',
        backgroundColor: holidayColors.white,
        borderRadius: 4,
        borderWidth: 0.5,
        padding: 15,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        marginRight: 10,
        alignItems: 'center',
    },
    tabListActive: {
        backgroundColor: holidayColors.primaryBlue,
    },
    tabText: {
        ...fontStyles.labalBaseRegular,
        color: holidayColors.gray,
    },
    tabTextActive: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.white,
    },
    tabListWrapper: {
        paddingRight: 0,
        paddingTop: 0,
        paddingBottom: 10,
        flexDirection: 'row',
        alignItems: 'center',

    },
    contentPadding: {
        padding: 0,
        paddingBottom:15,
    },
    // contentBlk: {
    //     backgroundColor: '#C1F1DD',
    //     borderRadius: 2,
    //     padding: 10,
    //     marginTop: 10,
    // },
    zeroCancellationWrap: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginHorizontal: 10,
    },
    container: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    kowMoreWrap: {
        marginHorizontal: 25,
        marginTop: 10,
        marginBottom: 10,
    },
    knowMoreText: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.gray,
    },
    knowMoreTextBlack: {
        ...fontStyles.labelSmallBlack,
        color: holidayColors.black,
    },
    knowMoreActionStyle: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.primaryBlue,
    },
    flexiDateWrap: {
        paddingTop: 15,
        marginTop: 5,
    },
    selectedOption: {
        borderWidth: 1,
        borderColor: holidayColors.primaryBlue,
        backgroundColor: holidayColors.lightBlueBg,
        borderRadius: 5,
    },
    orTagWrap: {
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 0,
        marginTop: 5,
    },
    orTagLine: {
        width: '100%',
        borderWidth: 0.5,
        borderColor: holidayColors.grayBorder,
        position: 'absolute',
    },
    orTag: {
        width: 28,
        height: 28,
        borderRadius: 28,
        borderWidth: 1,
        borderColor: '#ddd',
        backgroundColor: holidayColors.grayBorder,
        justifyContent: 'center',
        alignItems: 'center',
    },
    orText: {
        color: holidayColors.lightGray,
        ...fontStyles.labelSmallBold,

    },
    // contentBlkTxt: {
    //     fontSize: 12,
    //     color: '#249995',
    //     lineHeight: 18,
    // },
    iconzc: {
        width: 136.38,
        height: 32,
        resizeMode: 'cover',
    },
    actionStyle: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.primaryBlue,
    },
    iconFlexiDate: {
        width: 100,
        height: 33,
        resizeMode: 'cover',
    },
    flexiDateTagWrap: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginHorizontal: 10,
    },
    iconTickBlue: {
        width: 15,
        height: 15,
        backgroundColor: holidayColors.primaryBlue,
        borderRadius: 13,
    },
    header: {
        paddingHorizontal: 10,
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        borderBottomWidth: 2,
        borderBottomColor: holidayColors.grayBorder,
        height: 50,
    },

});

export default PackageAddOns;
