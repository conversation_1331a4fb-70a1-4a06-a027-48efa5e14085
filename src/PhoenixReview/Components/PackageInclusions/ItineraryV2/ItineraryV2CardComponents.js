import React, { useCallback, useEffect, useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { isEmpty } from 'lodash';
import {
  itineraryUnitSubTypes,
  itineraryUnitTypes,
} from '../../../../PhoenixDetail/DetailConstants';
import { ITINERARY_COLLAPSED_STATE } from 'mobile-holidays-react-native/src/PhoenixDetail/Components/ItineraryV2/constants';
import { optimizeActivityDetailsForReview } from '../../../Utils/HolidayReviewUtils';
import { getIconForItineraryUnitType } from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/PhoenixDetailUtils';

/* Components */
import DayPlanRowHOC from 'mobile-holidays-react-native/src/PhoenixDetail/Components/ItineraryV2/Common/DayPlanRowHOC';
import { sightSeeingCitiesV2 } from '../../../../PhoenixDetail/Utils/PhoenixDetailUtils';
import ViatorMessageStrip, { VIATOR_MESSAGE_STATE } from '../../ViatorMessageStrip';
import ItineraryUnitExtraInfoMessages from '../../../../PhoenixDetail/Components/ItineraryUnitExtraInfoMessages';
import { marginStyles } from '../../../../Styles/Spacing';

export const accessRestriction = {
  changeFlightRestricted: true,
  removeFlightRestricted: true,
  changeHotelRestricted: true,
  removeHotelRestricted: true,
  addActivityRestricted: true,
  removeActivityRestricted: true,
  changeTransferRestricted: true,
  removeTransferRestricted: true,
  removeVisaRestricted: true,
};
export const viewDetailAccessRestriction = {
  hotelRestricted: true,
  flightRestricted: true,
  transferRestricted: true,
};

export const renderActivityRow = ({ item, reviewDetail }) => {
  const { itineraryUnitType, itineraryUnitSubType, activity, text = '' } = item;
  const { sellableId } = activity || {};
  const activityDetail = optimizeActivityDetailsForReview(reviewDetail);
  const { metadataDetail: { pickUpPointMessage = {} } = {} } = reviewDetail || {};
  const activityData = activityDetail.hasOwnProperty(sellableId) ? activityDetail[sellableId] : {};
  const {
    metaData: { formDataFixed } = {},
  } = activityData || {};
  return (
    <DayPlanRowHOC
      unit={item}
      hideBorder
      cardHeaderTexts={activityData?.metaData?.cardHeaderTexts || []}
      defaultCollapsedState={ITINERARY_COLLAPSED_STATE.NON_COLLAPSIBLE}
      cardSubHeader={text}
    >
      {!formDataFixed && (
        <ViatorMessageStrip state={VIATOR_MESSAGE_STATE.EXPANDED} viatorInfo={pickUpPointMessage} />
      )}
    </DayPlanRowHOC>
  );
};

export const renderMealsRow = ({ item, reviewDetail }) => {
  const {
    mealType = '',
    address = '',
    city = '',
    itineraryUnitSubType,
    itineraryUnitType,
  } = item || {};
  return (
    <DayPlanRowHOC
      unit={item}
      hideBorder
      cardHeaderTexts={[
        { text: 'MEAL', isEmphasized: true },
        ...(mealType ? [{ text: mealType, isEmphasized: false }] : []),
      ]}
      cardSubHeader={address}
      defaultCollapsedState={ITINERARY_COLLAPSED_STATE.NON_COLLAPSIBLE}
    />
  );
};

export const renderTransferRow = ({ item, reviewDetail }) => {
  const { itineraryUnitType, itineraryUnitSubType, text, shortText, shortTextnew, isLandOnly, isRemoved = false } =
    item || {};
  if(isRemoved) {
    return [];
  }
  const cardSubHeaderText = !isEmpty(shortText)
    ? isLandOnly
      ? shortText
      : `Transfer From ${shortText}`
    : !text
    ? text
    : '';
  return (
    <DayPlanRowHOC
      unit={item}
      hideBorder
      cardSubHeader={cardSubHeaderText}
      cardHeaderTexts={[{ text: 'TRANSFER', isEmphasized: true }]}
      defaultCollapsedState={ITINERARY_COLLAPSED_STATE.NON_COLLAPSIBLE}
    />
  );
};

export const renderSightSeeing = ({ item, reviewDetail }) => {
  const { itineraryUnitType, itineraryUnitSubType, cityName, carContents, sellableId = '', carExtraInfo } = item || {};

  const getSightseeingData = () => {
    let duration = 0;
    const citySet = new Set();
    item.locations.forEach((item, index) => {
      duration += item.durationInHours;
      citySet.add(item.cityName);
    });
    return {
      duration,
      citySet,
    };
  };

  const { duration, citySet } = getSightseeingData();

  return (
    <DayPlanRowHOC
      unit={item}
      hideBorder
      cardHeaderTexts={[
        { text: 'SIGHTSEEING', isEmphasized: true },
        ...(duration ? [{ text: `${duration} hrs` }] : []),
        ...(citySet ? [{ text: `In ${sightSeeingCitiesV2(citySet)}` }] : []),
      ]}
      defaultCollapsedState={ITINERARY_COLLAPSED_STATE.NON_COLLAPSIBLE}
    >
      <ItineraryUnitExtraInfoMessages extraInfo={carExtraInfo} containerStyles={marginStyles.mt0} />
    </DayPlanRowHOC>
  );
};
