import React from 'react';
import {
    StyleSheet,
    View,
    Image,
    Text,
} from 'react-native';
import { getHotelObjectReview, getHotelData } from '../../../../../PhoenixDetail/Utils/PhoenixDetailUtils';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import blackStar from '@mmt/legacy-assets/src/star.webp';
import greyStar from '@mmt/legacy-assets/src/greyStar.webp';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import { createHotelDayImgList } from '../../../../../PhoenixDetail/Utils/HotelUtils';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';

const MAX_STAR_COUNT = 5;
const PhoenixHotelCard = (props) => {
    const { itineraryUnit, hotelDetail ,roomCount} = props || {};
    const hotelSellableId = itineraryUnit?.hotel?.hotelSellableId;
    const hotelObject = getHotelObjectReview(hotelDetail?.hotels, hotelSellableId);

    if (hotelObject) {
        const hotelData = getHotelData(hotelObject,true);
        const images = createHotelDayImgList(hotelObject,hotelObject?.roomType);

        const starImages = [];
        for (let i = 0; i < MAX_STAR_COUNT; i++) {
            starImages.push(i < hotelData?.rating ? blackStar : greyStar);
        }

        const roomType = hotelObject?.roomType;
        const ratePlan = roomType?.ratePlan;
        return (
            <View style={styles.itineraryRowContent}>
                <View><Text style={styles.hotelTitle}><Text style={AtomicCss.blackFont}>{hotelData?.propertyType} Stay</Text> | {hotelData?.days} Night Stay</Text></View>
                <View style={[AtomicCss.flexRow, AtomicCss.marginTop10]}>
                    <View style={AtomicCss.marginRight15}>
                        <HolidayImageHolder
                            imageUrl={images?.[0]?.fullPath}
                            style={styles.imgHotel} />
                    </View>
                    <View style={styles.hotelContent}>
                        {hotelData.rating > 0 &&
                            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                                {starImages.map((item, index) =>
                                    <Image
                                        style={styles.starRating}
                                        source={item}
                                    />)}
                            </View>}

                        <View style={{width:'80%'}}><Text style={[styles.hotelName,styles.wraptext, AtomicCss.paddingTop3]}>{hotelData?.name}</Text></View>
                        <View><Text style={styles.hotelArea}>{hotelObject?.locationInfo?.areaName}</Text></View>
                        <View style={[AtomicCss.marginTop10,{width:'80%',justifyContent:'space-between',display:'flex'}]}>
                            <Text style={styles.hotelType}>{roomType?.name}
                            {roomCount && <Text> x{roomCount}</Text>}
                            </Text>
                       </View>
                        <View style={AtomicCss.paddingTop3}><Text style={styles.hotelCheckout}>{hotelData?.chkInDate} - {hotelData?.chkOutDate}</Text></View>
                        <View style={AtomicCss.paddingTop3}><Text style={styles.hotelMeal}>{(ratePlan && ratePlan?.mealName) ? ratePlan?.mealName : 'No Meals'} Included</Text></View>
                    </View>
                </View>
            </View>
        );
    }
    return [];
};

const styles = StyleSheet.create({
    hotelTitle: {
        ...fontStyles.labelBaseRegular,
        color:holidayColors.gray,
        marginBottom:8,
    },
    hotelName:{
        ...fontStyles.labelMediumBold,
        color:holidayColors.black,
    },
    hotelArea: {
        ...fontStyles.labelSmallRegular,
        color:holidayColors.gray,
    },
    hotelType:{
        ...fontStyles.labelBaseBold,
        color:holidayColors.gray,
    },
    hotelCheckout:{
        ...fontStyles.labelSmallRegular,
        color:holidayColors.gray,
    },
    hotelMeal:{
        ...fontStyles.labelSmallRegular,
        color:holidayColors.gray,
    },
    imgHotel: {
        width: 72,
        height: 90,
        resizeMode: 'cover',
        borderRadius: 16,
    },
    hotelContent: {
        marginTop: -2,
        flex: 1.
    },
    starRating: {
        width: 9,
        height: 8,
    },
    itineraryRowContent: {
        width: '97%',
        marginLeft: 10,
       paddingBottom: 15,
    },
    wraptext:{
        flex: 1,
        flexWrap: 'wrap',
    },
});

export default PhoenixHotelCard;
