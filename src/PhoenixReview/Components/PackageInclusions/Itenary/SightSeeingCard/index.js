import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconActivities from '@mmt/legacy-assets/src/ic_activityInclIcon.webp';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { marginStyles } from '../../../../../Styles/Spacing';
import ItineraryUnitExtraInfoMessages from '../../../../../PhoenixDetail/Components/ItineraryUnitExtraInfoMessages';

const PhoenixSightSeeing = (props) => {
  const { itineraryUnit, cityName, carContents } = props || {};
  const getDetails = (carContents, itineraryUnit, callback) => {
    const carContentForSightSeeing = carContents.find(carContent => carContent.sellableId === itineraryUnit.sellableId);
    const sightSeeingListForTheDay = carContentForSightSeeing?.carDayWiseContents.find(dayWiseContent => dayWiseContent.dayNumber === itineraryUnit.day);
    return callback(sightSeeingListForTheDay?.carSightseeingDetails || []);
  };

  const getCarExtraInfo = () => {
    const carContentForSightSeeing = carContents.find(carContent => carContent.sellableId === itineraryUnit.sellableId);
    const dayWiseContentForTheDay = carContentForSightSeeing?.carDayWiseContents.find(dayWiseContent => dayWiseContent.dayNumber === itineraryUnit.day);
    return dayWiseContentForTheDay?.carExtraInfo || [];
  };

  const getDuration = () => getDetails(carContents, itineraryUnit, sightSeeingList => {
    let duration = sightSeeingList.reduce((total, sightSeeing) => total + sightSeeing.durationInHours, 0);
    return duration > 0 ? `${duration} ${duration > 1 ? 'Hours' : 'Hour'}` : '';
  });

  const getCityName = () => getDetails(carContents, itineraryUnit, sightSeeingList => {
    let nameCity = sightSeeingList.reduce((cityNames, sightSeeing) => {
      if (!cityNames.includes(sightSeeing.cityName)) {
        cityNames.push(sightSeeing.cityName);
      }
      return cityNames;
    }, []).join(',');
    return nameCity || cityName;
  });

  return (
    <View style={styles.itineraryRowContent}>
      <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
        <Text style={styles.sightSeeingTitle}>Sightseeing</Text>
        <View style={AtomicCss.paddingLeft10}>
          <Image source={iconActivities} style={styles.iconActivities} />
        </View>
      </View>

      <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, marginStyles.mt14]}>
        <Text style={styles.sightSeeingDescription}>
          Sightseeing in {getCityName()} : {getDuration()}{' '}
        </Text>
      </View>

      <View
        style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.flexWrap, marginStyles.mt4]}
      >
        {itineraryUnit?.locations?.map((list, index) => (
          <View style={styles.sightSeeingPointContainer} key={index}>
            <Text style={styles.bulletBlueTxt}>
              &#x2B24;
            </Text>
            <Text style={styles.sightSeeingPoint}>{list?.name}</Text>
          </View>
        ))}
      </View>
      <ItineraryUnitExtraInfoMessages extraInfo={getCarExtraInfo()}/>
    </View>
  );
};

const styles = StyleSheet.create({
  itineraryRowContent: {
    width: '99%',
    marginLeft: 5,
    paddingBottom: 15,
  },
  bulletBlueTxt: {
    color: holidayColors.lightBlueBg,
    ...fontStyles.labelSmallRegular,
    ...marginStyles.mr6,
  },
  iconActivities: {
    width: 10,
    height: 16,
    resizeMode: 'cover',
  },
  sightSeeingTitle: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  sightSeeingDescription: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  sightSeeingPointContainer: {
    ...AtomicCss.flexRow,
    ...AtomicCss.alignCenter,
    ...marginStyles.mr10,
    ...marginStyles.mb10,
  },
  sightSeeingPoint: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
});

export default PhoenixSightSeeing;
