import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LEISURE_DAY } from '.';
import {
  itineraryUnitSubTypes,
  itineraryUnitTypes,
} from '../../../../PhoenixDetail/DetailConstants';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';

/* Icons */
import iconBreakfast from '../../../../PhoenixDetail/Components/images/ic_breakfast.png';
import iconHotel from '@mmt/legacy-assets/src/ic_hotelInclIcon.webp';

/* Components */
import PackageFlightCard from './FlightCards';
import PhoenixHotelCard from './HotelCard';
import PhoenixTransfer from './TransferCard';
import PhoenixActivityCard from './ActivityCard';
import PhoenixSightSeeing from './SightSeeingCard';
import PhoenixDayMeals from './DayMealsCard';
import { marginStyles } from '../../../../Styles/Spacing';

const ItineraryCardContainer = ({
  data,
  roomCount,
  carContents,
  baggageInfo,
  reviewDetail,
  getLeisureDay,
  getSummaryCard,
  activityItinary,
  optimizePackageReviewdetail,
}) => {
  const { flightDetail, departureDetail } = optimizePackageReviewdetail || {};
  const { hotelDetail, activityDetail, itineraryDetail, destinationDetail, metadataDetail } =
    reviewDetail || {};

  if (data.type === LEISURE_DAY) {
    return getLeisureDay();
  }

  const renderItineraryCard = (item, index, length) => {
    const { itineraryUnitType, itineraryUnitSubType } = item;
    switch (itineraryUnitType) {
      case itineraryUnitTypes.HOTEL:
        switch (itineraryUnitSubType) {
          case itineraryUnitSubTypes.BREAKFAST:
          case itineraryUnitSubTypes.DINNER:
            return getSummaryCard(iconBreakfast, item?.text);
          case itineraryUnitSubTypes.CHECKIN:
            return (
              <View>
                {getSummaryCard(iconHotel, item?.text)}
                <PhoenixHotelCard
                  itineraryUnit={item}
                  hotelDetail={hotelDetail}
                  roomCount={roomCount}
                />
              </View>
            );
          case itineraryUnitSubTypes.CHECKOUT:
            return getSummaryCard(iconHotel, item?.text);
          default:
            return null;
        }

      case itineraryUnitTypes.MEALS:
        return <PhoenixDayMeals item={item} />;
      case itineraryUnitTypes.FLIGHT:
        switch (itineraryUnitSubType) {
          case itineraryUnitSubTypes.FLIGHT_ARRIVE:
          case itineraryUnitSubTypes.FLIGHT_DEPART:
            return (
              <PackageFlightCard
                flightDetail={flightDetail}
                itineraryUnit={item}
                baggageInfo={baggageInfo}
              />
            );

          default:
            return null;
        }
      case itineraryUnitTypes.TRANSFERS:
      case itineraryUnitTypes.CAR:
        return <PhoenixTransfer itineraryUnit={item} />;
      case itineraryUnitTypes.ACTIVITY:
        return (
          <PhoenixActivityCard activityMap={activityItinary?.activityMap} itineraryUnit={item} />
        );
      case itineraryUnitTypes.SIGHTSEEING:
        return (
          <PhoenixSightSeeing
            itineraryUnit={item}
            cityName={item.cityName}
            carContents={carContents}
          />
        );

      default:
        return null;
    }
  };
  const cards = data?.map((item, index) => {
    const lastItem = index < data?.length - 1 ? false : true;

    const renderCard = ({ index, item, data = []}) => {
      if(item?.isRemoved) {
        return null;
      }
      return (
        <View style={[styles.itineraryInnerBlock, marginStyles.mt10]}>
          <View style={[styles.itineraryRow, lastItem ? { paddingBottom: 0 } : {}]}>
            <View style={[styles.bulletDot]}>
              <View style={index % 2 === 1 ? styles.bulletGrayTxt : styles.bulletBlueTxt} />
            </View>
            <View style={[{ flex: 20 }, !lastItem ? styles.borderBottomSummary : []]}>
              {renderItineraryCard(item, index, data.length)}
            </View>
          </View>
        </View>
      );
    };
    
    if (item.itineraryUnitType === itineraryUnitTypes.COMMUTE) {
      const cardsCommute = item?.commute?.map((itemCommute, indexCommute) => {
        return (
          renderCard({ index: indexCommute, item: itemCommute })
        );
      });
      return cardsCommute;
    }
    return (
      renderCard({ item, index, data })
    );
  });
  return cards;
};

const styles = StyleSheet.create({
  itineraryInnerBlock: {
    borderLeftWidth: 1.4,
    borderColor: holidayColors.grayBorder,
    borderRadius: 1,
    position: 'relative',
    marginLeft: 5,
  },
  bulletBlueTxt: {
    backgroundColor: holidayColors.midLightBlue,
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  bulletGrayTxt: {
    backgroundColor: holidayColors.midLightBlue,
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  itineraryRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    display: 'flex',
    paddingVertical: 7,
    paddingHorizontal: 6,
  },
  bulletDot: {
    marginTop: 3,
    marginLeft: -11,
  },
  borderBottomSummary: {
    borderBottomWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  viewItineraryContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    justifyContent: 'center',
  },
  viewPackageText: {
    display: 'flex',
    alignSelf: 'center',
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
  },
  minusIcon: {
    width: 15,
    height: 1.5,
    backgroundColor: holidayColors.primaryBlue,
    marginLeft: 10,
  },
  ctaIcon: {
    width: 8,
    height: 8,
    marginLeft: 10,
  },
});

export default ItineraryCardContainer;
