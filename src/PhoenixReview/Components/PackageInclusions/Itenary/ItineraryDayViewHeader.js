import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { paddingStyles } from '../../../../Styles/Spacing';
import LinearGradient from 'react-native-linear-gradient';
import { getFormattedDate } from '../../../Utils/HolidayReviewUtils';

const ItineraryDayViewHeader = (props) => {
  const { itineraryDetail, day, packageStartDate, showNewActivityDetail } = props;
  const itineraryStartDay = itineraryDetail?.dayItineraries?.[0]?.day || 0;
  const formattedDate = ` ${getFormattedDate(
    packageStartDate,
    day - itineraryStartDay,
    'ddd, MMM DD, YYYY',
  )} `;

  return showNewActivityDetail ? (
    <View style={styles.dayPlanContainer}>
      <LinearGradient
        start={{ x: 1.0, y: 0.0 }}
        end={{ x: 0.0, y: 1.0 }}
        colors={[holidayColors.orangeGradient, holidayColors.orangeGradientDark]}
        style={styles.dayPlanNumber}
      >
        <Text style={styles.dayPlanNumberText}>Day {day}</Text>
      </LinearGradient>
      <Text style={styles.dateText}>{formattedDate?.toUpperCase()}</Text>
    </View>
  ) : (
    <View style={styles.rowJustify}>
      <Text style={styles.dayText}>
        Day {day}
        <Text style={styles.dateText}>{formattedDate?.toUpperCase()}</Text>
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  rowJustify: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dayText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.gray,
  },
  dateText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  dayPlanContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...paddingStyles.pv8,
  },
  dayPlanNumber: {
    minHeight: 28,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 15,
    marginHorizontal: 10,
    borderRadius: 20,
  },
  dayPlanNumberText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
  },
});
export default ItineraryDayViewHeader;
