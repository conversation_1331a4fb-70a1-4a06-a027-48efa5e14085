import React, { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { isEmpty } from 'lodash';
import ErrorIcon from '@mmt/legacy-assets/src/errorIcon_visa.webp';
import CrossIcon from '../../../../PhoenixDetail/Components/images/cross.png';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import { getBaggageInfoForFlight } from '../../../../PhoenixDetail/Components/FlightDetailPage/FlightListing/FlightsUtils';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import BottomSheet from '../../../../Common/Components/BottomSheet';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';

const BaggageInfoDetails = ({ baggageInfo = {}, flight, flightMetadataDetail = {} }) => {
  const [openPopup, setOpenPopup] = useState(false);
  const baggageInfoDetail = getBaggageInfoForFlight(baggageInfo, flight?.flightId)?.[0] || {};
  const { checkInBaggage = true, baggageMessage = '' } = flightMetadataDetail || {};
  const handleOpenPopup = () => {
    setOpenPopup(true);
  };
  const handleClosePopup = () => {
    setOpenPopup(false);
  };
  return (
    <>
      {!isEmpty(baggageInfoDetail) && (
        <View style={styles.baggageInfoContainer}>
          <Text style={styles.baggageTitle}>Cabin Baggage : </Text>
          <Text style={[{ color: baggageInfoDetail.cabinColor}, fontStyles.labelBaseRegular]}>
            {baggageInfoDetail.cabin}
          </Text>
          <Text style={styles.baggageTitle}> | Check-in : </Text>
          {checkInBaggage ? (
            <Text style={[{ color: baggageInfoDetail.checkInColor}, fontStyles.labelBaseRegular]}>
              {baggageInfoDetail.checkIn}
            </Text>
          ) : (
            <TouchableOpacity onPress={handleOpenPopup}>
              <View style={styles.baggageNotIncluded}>
                <Text style={[{ color: baggageInfoDetail.checkInColor }, fontStyles.labelBaseRegular]}>
                  Not Included
                </Text>
                <HolidayImageHolder
                  defaultImage={ErrorIcon}
                  resizeMode="contain"
                  style={{ width: 10, height: 10, marginLeft: 5 }}
                />
              </View>
            </TouchableOpacity>
          )}
        </View>
      )}
      <BottomSheet
        isOpen={openPopup}
        onBackPressed={handleClosePopup}
        containerStyle={styles.bottomSheetContainer}
      >
        <View style={styles.infoTitleContainer}>
          <Text style={styles.infoTitle}>Baggage Info</Text>
          <TouchableOpacity
            onPress={handleClosePopup}
            style={{ marginLeft: 'auto' }}
            activeOpacity={0}
          >
            <HolidayImageHolder defaultImage={CrossIcon} style={styles.crossIcon} />
          </TouchableOpacity>
        </View>
        <Text style={styles.infoMessage}>{baggageMessage}</Text>
      </BottomSheet>
    </>
  );
};
const styles = StyleSheet.create({
  baggageInfoContainer: {
    paddingTop: 10,
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  baggageTitle: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallBold,
  },
  baggageNotIncluded: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoTitle: {
    fontSize: 22,
    fontWeight: '900',
    fontFamily: fonts.regular,
  },
  infoMessage: {
    ...fontStyles.labelBaseRegular,
  },
  crossIcon: {
    width: 10,
    height: 10,
    tintColor: holidayColors.white,
    backgroundColor: holidayColors.lightGray,
    padding: 10,
    borderRadius: 15,
  },
  bottomSheetContainer: {
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 50,
  },
  infoTitleContainer: {
    paddingVertical: 20,
    alignItems: 'center',
    flexDirection: 'row',
  },
});
export default BaggageInfoDetails;
