import React, {useMemo, useState, useEffect, useRef } from 'react';
import {
    StyleSheet,
    ScrollView,
    View,
    TouchableOpacity,
    Image,
    Text,
    Dimensions,
    FlatList,
} from 'react-native';
import _ from 'lodash';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { itineraryUnitSubTypes, itineraryUnitTypes } from '../../../../PhoenixDetail/DetailConstants';
import { getOptimizedReviewDetail } from '../../../../PhoenixDetail/Utils/PhoenixDetailUtils';
import { trackReviewLocalClickEvent } from '../../../../Review/Utils/HolidayReviewUtils';
import { PDTConstants } from '../../../Utils/HolidayReviewConstants';
import MinusIcon from '@mmt/legacy-assets/src/minusIcon.webp';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';
import { getShowNewActivityDetail } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

/* Components */
import HTMLView from 'react-native-htmlview';
import { HtmlHeading } from '../../../../PhoenixDetail/Components/DayPlan/HtmlHeading';
import ItineraryDayViewHeader from './ItineraryDayViewHeader';
import ItineraryCardContainer from './ItineraryCardContainer';
import ItineraryV2CardContainer from '../ItineraryV2/ItineraryV2CardContainer';
import ItineraryHeader from './ItineraryHeader';
import { logHolidayReviewPDTClickEvents } from '../../../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import {isMobileClient} from "../../../../utils/HolidayUtils";

const { width } = Dimensions.get('screen');
export const LEISURE_DAY = 'LEISURE';
const PackageItinerary = (props) => {
    let flatList = useRef();
    const HTMLView = isMobileClient() ? require('react-native-htmlview').default : require('../../../../Common/Components/HTML').default;
    const { reviewDetail } = props?.reviewData || {};
    const { carContents, baggageInfo = {} } = props || {};
    const optimizePackageReviewdetail = getOptimizedReviewDetail(reviewDetail);
    const { departureDetail } = optimizePackageReviewdetail || {};
    const { activityDetail, itineraryDetail, destinationDetail } = reviewDetail || {};
    const { dayItinerariesV2: dayItineraries } = itineraryDetail || {};
    const showNewActivityDetail = getShowNewActivityDetail();
    const [showLeftOverDays, setShowLeftOverDays] = useState(false);

    const MemoizedCount = useMemo(() => {
        let flightCount = 0;
        let hotelCount = 0;
        let activityCount = 0;
        let transferCount = 0;
        let mealCount = 0;
        dayItineraries.forEach((item) => {
            const {itineraryUnits} = item;
            itineraryUnits.forEach((itinerary) => {
                const {itineraryUnitType = '', itineraryUnitSubType = ''} = itinerary;
                switch (itineraryUnitType) {
                    case itineraryUnitTypes.COMMUTE: 
                    {
                        itinerary?.commute?.forEach((itineraryCommmute) => {
                            const { itineraryUnitType = '', isRemoved = false } = itineraryCommmute || {}
                            switch (itineraryUnitType) {
                                case itineraryUnitTypes.FLIGHT:
                                    if(!isRemoved) {
                                        flightCount += 1;
                                    }
                                  break;
                                case itineraryUnitTypes.TRANSFERS:
                                case itineraryUnitTypes.CAR:
                                    if(!isRemoved) {
                                        transferCount += 1;
                                    }
                                    break;
                                default: break;
                            }
                        });
                        break;
                    }
                    case itineraryUnitTypes.FLIGHT:
                        flightCount += 1;

                        break;
                    case itineraryUnitTypes.HOTEL:
                        if (itinerary.itineraryUnitSubType === itineraryUnitSubTypes.CHECKIN)
                            {hotelCount += 1;}
                        break;
                    case itineraryUnitTypes.TRANSFERS:
                    case itineraryUnitTypes.CAR:
                        transferCount += 1;
                        break;
                    case itineraryUnitTypes.SIGHTSEEING:
                        activityCount += 1;
                        break;
                    case itineraryUnitTypes.MEALS:
                        mealCount+=1;
                        break;
                    case itineraryUnitTypes.ACTIVITY:
                        if(itineraryUnitSubType === itineraryUnitSubTypes.ACT_MEALS) {
                            mealCount += 1;
                        } else if(itineraryUnitSubType === itineraryUnitSubTypes.ACT_TRANSFER) {
                            transferCount += 1;
                        } else {
                            activityCount+=1;
                        }
                        break;
                    default: break;
                }
            });
        });
        return {
            flightCount,
            hotelCount,
            activityCount,
            transferCount,
            mealCount,
        };
    }, [dayItineraries]);

    useEffect(() => {
        if (flatList && flatList.current) {
            flatList.current.scrollToIndex({animated: true, index: 0});
        }
    }, []);

     const MemoizedActivity = useMemo(() => {
               let activityMap = {};
                activityDetail?.cityActivities?.map((activity) => {
                    activity?.activities?.map((activityObj) => {
                        if (!_.isEmpty(activityObj?.additionalData?.shortDescription)) {
                            activityMap[activityObj?.sellableId] = activityObj?.additionalData?.shortDescription;
                        }
                    });
                });
                return {
                    activityMap: activityMap,
                };
         }, [activityDetail]);

    const itineraryCount = MemoizedCount;
    const activityItinary = MemoizedActivity;
    const packageStartDate = departureDetail?.packageDate;

    const getSummaryCard = (icon, text) => {
        return (
            <View style={[styles.summary]}>
                <Image source={icon} style={styles.iconImage} />
                {!_.isEmpty(text) && <HTMLView value={`<p>${text}</p>`} stylesheet={HtmlStyle}/>}
            </View>
        );
    };

    const getDayViseView = (day, data) => {
        const ItineraryCardContainerComponent = showNewActivityDetail
          ? ItineraryV2CardContainer
          : ItineraryCardContainer; 
        return (
          <View
            style={[
              styles.itineraryBlock,
              showNewActivityDetail
                ? {}
                : {
                    ...paddingStyles.pa16,     
                    borderBottomWidth: 1,
                    borderColor: holidayColors.grayBorder,
                  },
            ]}
          >
            <ItineraryDayViewHeader
              itineraryDetail={itineraryDetail}
              day={day}
              packageStartDate={packageStartDate}
              showNewActivityDetail={showNewActivityDetail}
            />
            <ItineraryCardContainerComponent
              {...props}
              day={day}
              data={data}
              getLeisureDay={getLeisureDay}
              getSummaryCard={getSummaryCard}
              reviewDetail={reviewDetail}
              activityItinary={activityItinary}
              optimizePackageReviewdetail={optimizePackageReviewdetail}
            />
          </View>
        );
      };

    const getData = (cityId)=>{
        return  destinationDetail?.destinations?.filter((dest) => {
            if (dest?.cityId == cityId) {
                return dest;
            }
        });
    };

    const getDataWithCityName = (cityName)=>{
        return  destinationDetail?.destinations?.filter((dest) => {
            if (dest?.name == cityName) {
                return dest;
            }
        });
    };

    const getDestinationData = (cityId,lastCityId,itineraryUnits,index) => {
        const dest = getData(cityId);
        if (dest?.length) {return {
            dest:dest,
            lastCityId:cityId,
        };}
        else {
            if (lastCityId){
                return {
                    dest:getData(lastCityId),
                    lastCityId:lastCityId,
                };
            }
            else {
                for (let i = index; i < itineraryUnits?.length; i++){
                    const data = itineraryUnits?.[i];
                    const { cityId:newCityid } = data;
                    if (newCityid !== cityId){
                        const destinationData = getData(newCityid);
                        if (destinationData?.length > 0){
                            return {
                                dest:destinationData,
                                lastCityId:newCityid,
                            };
                        }
                    }
                }
            }
        }
    };

    const checkisMealOrSightseeing = (unit) => {
      if (
        (unit?.itineraryUnitType == itineraryUnitTypes.MEALS &&
            // this is to identify is this new or old meal flow
          unit?.itineraryUnitSubTypeMeta !== itineraryUnitTypes.MEALS) ||
        unit?.itineraryUnitType == itineraryUnitTypes.SIGHTSEEING
      ) {
        return true;
      } else {
        false;
      }
    };

    const checkIsHotelBreakfastOrDinner = (unit)=>{
        if (unit?.itineraryUnitType == itineraryUnitTypes.HOTEL && (unit?.itineraryUnitSubType == itineraryUnitSubTypes.BREAKFAST ||
          unit?.itineraryUnitSubType == itineraryUnitSubTypes.DINNER)
        ) {return true;}
        else {false;}
    };

    const getLeisureDay = ()=>{
        return <View style={styles.itineraryRow}>
            <View style={[styles.bulletDot]}>
              <View style={styles.bulletBlueTxt}  />
          </View>
          <View style={[{flex:20}]}>
              <Text style={[marginStyles.ml4, styles.dayatLeisure]}>Day At Leisure</Text>
          </View>
      </View>;
    };

    const getTabsData = () => {
        if (!dayItineraries) {return;}
        const tabsData = [];
        const tabs = {};
        let lastCityid = '';
        let lastCityName = '';
        let lastDay = '';
        let lastCityIdWithDestinationData = '';
        const mapData = {};
        dayItineraries?.map((item,index) => {
            item?.itineraryUnits.forEach((unit) => {
                let cityId = unit?.cityId;
            if (!checkIsHotelBreakfastOrDinner(unit)) {
                if (checkisMealOrSightseeing(unit)) {
                    if (unit?.itineraryUnitType != itineraryUnitTypes.MEALS) {
                        let cityName = unit?.cityName;
                        let cityDataList = getDataWithCityName(cityName);
                        if (cityDataList) {
                            let cityData  =  {...cityDataList[0]};
                            if (cityData.cityId != lastCityid) {
                                tabs[lastCityName].endDay = lastDay;
                                tabsData.push({ city: lastCityName, ...tabs[lastCityName] });
                                lastDay = item?.day;
                                    const dataDay = { [lastDay]: [unit] };
                                    const obj = {
                                        data: dataDay,
                                        startDay: item?.day,
                                    };
                                    const dayData = tabs[cityData?.name]?.data[item?.day];
                                    if (!tabs[cityData.name])
                                     {tabs[cityData.name] = obj;}
                                    else if (dayData)
                                     {dayData.push(unit);}


                                lastCityid = cityData?.cityId;
                                lastCityName = cityData?.name;
                            } else {
                            const dayData = tabs[cityName] ? (tabs[cityName]?.data  ? tabs[cityName]?.data[item?.day] : undefined) :  undefined;
                            if (dayData)
                                {dayData.push(unit);}
                            else {
                                const data = tabs?.[cityName]?.data;
                                if (data) {
                                    tabs[cityName].data[item.day] = [unit];
                                } else {
                                    const dataDay = { [item?.day]: [unit] };
                                    const obj = {
                                        data: dataDay,
                                        startDay: item?.day,
                                    };
                                    tabs[cityName] = obj;
                                 }
                            }
                            }
                        } else
                            if (lastCityName) {
                            const dayData = tabs[lastCityName].data[item?.day];
                            if (dayData)
                                {dayData.push(unit);}
                            else {
                                tabs[lastCityName].data[item?.day] = [unit];
                            }
                        }
                    } 
                    else {
                        Object.keys(unit?.mealSummary || {})?.forEach((key) => {
                            let meal = unit?.mealSummary[key];
                            let destinationData = getData(key)[0];
                            if (meal) {
                                const DayData = tabs[destinationData?.name]?.data[item?.day];
                                if (!DayData) {
                                    const data = tabs[destinationData?.name]?.data;
                                    if (data) {
                                        tabs[destinationData?.name].data[item.day] = [{ ...unit, mealCityId: key }];
                                    } else {
                                        const dataDay = { [item?.day]: [{ ...unit, mealCityId: key }] };
                                        const obj = {
                                            data: dataDay,
                                            startDay: item?.day,
                                        };
                                       if (!lastCityid) {
                                            lastCityid = cityId;
                                            lastCityName = destinationData?.name;
                                            lastDay = item?.day;
                                            const dataDay = { [lastDay]: [{ ...unit, mealCityId: key }] };
                                            const obj = {
                                                startDay: item?.day,
                                                data: dataDay,
                                            };
                                            tabs[destinationData?.name] = obj;
                                        }
                                        tabs[destinationData?.name] = obj;
                                    }
                                } else {
                                    DayData.push({ ...unit, mealCityId: key });
                                }
                            }
                        });
                    }
                } else {
                    const Data = getDestinationData(cityId, lastCityid, item?.itineraryUnits, index);
                    const DestinationData = Data?.dest?.[0];
                    lastCityIdWithDestinationData = Data?.lastCityId;
                    mapData[cityId] = lastCityIdWithDestinationData;
                    cityId = mapData[cityId];
                    if (DestinationData) {
                        if (!lastCityid) {
                            lastCityid = cityId;
                            lastCityName = DestinationData?.name;
                            lastDay = item?.day;
                            const dataDay = { [lastDay]: [unit] };
                            const obj = {
                                startDay: item?.day,
                                data: dataDay,
                            };
                            tabs[DestinationData?.name] = obj;
                        } else if (lastCityid != cityId)
                        {
                            tabs[lastCityName].endDay = lastDay;
                            tabsData.push({ city: lastCityName, ...tabs[lastCityName] });
                            lastDay = item?.day;
                            const DayData =  tabs[DestinationData.name] ? (tabs[DestinationData.name]?.data  ? tabs[DestinationData.name]?.data[item?.day] : undefined) : undefined;
                            if (!DayData) {
                                const dataDay = { [lastDay]: [unit] };
                                const obj = {
                                    data: dataDay,
                                    startDay: item?.day,
                                };
                                tabs[DestinationData.name] = obj;
                            } else {DayData.push(unit);}
                            lastCityid = cityId;
                            lastCityName = DestinationData?.name;
                        } else {
                            lastDay = item?.day;
                            const DayData = tabs[DestinationData.name].data[item.day];
                            if (!DayData) {
                                tabs[DestinationData.name].data[item.day] = [unit];
                            } else {DayData.push(unit);}
                        }
                    }
                }
            }
            });
            if (item?.itineraryUnits?.length == 0){
                tabs[lastCityName].data[item.day] = {type:LEISURE_DAY};
            }

        });

        tabs[lastCityName].endDay = lastDay;
        tabsData.push({ city: lastCityName, ...tabs[lastCityName] });
        return tabsData;
    };

    const DayPlanData = React.useMemo(() =>
        getTabsData()
    );

    const getHeader = () => {
        return <ItineraryHeader itineraryCount={itineraryCount}/>
      };
    const captureClickEvents = ({ eventName, suffix }) => {
      logHolidayReviewPDTClickEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: eventName + suffix,
      });
      trackReviewLocalClickEvent(eventName, suffix);
    };
    const toggleShowLeftOverDays = () => {
        captureClickEvents({
          eventName: 'Itinerary_View_',
          suffix: showLeftOverDays ? 'Less' : 'Full',
        });
        setShowLeftOverDays(!showLeftOverDays);
    };
    
    const daysData = {};
    const noOfDaysToShow = 3;
    DayPlanData.map((item)=>{
        Object.keys(item.data).map((dayNumber)=>{
            if (daysData[dayNumber]) {
                daysData[dayNumber] = [
                    ...daysData[dayNumber],
                    ...item.data[dayNumber],
                ];
            } else {
                daysData[dayNumber] = item.data[dayNumber];
            }
        });
    });

    return (
        <View>
            {getHeader()}
           {Object.keys(daysData)
            .slice(0, noOfDaysToShow)
            .map((day) => {
            return getDayViseView(day, daysData[day]);
            })}
            {(Object.keys(daysData)).length <= noOfDaysToShow || showLeftOverDays ? null : (
            <TextButton
                buttonText={`View ${(Object.keys(daysData)).length - noOfDaysToShow} More Days Itinerary +`}
                handleClick={toggleShowLeftOverDays}
                btnTextStyle={styles.viewPackageText}
                btnWrapperStyle={styles.viewItineraryContainer}
            />
            )}
            {showLeftOverDays ? (
                <View>
                {Object.keys(daysData)
                    .slice(noOfDaysToShow, Object.keys(daysData).length)
                    .map((day) => {
                    return getDayViseView(day, daysData[day]);
                    })}
                    <TextButton
                        buttonText="View Less"
                        handleClick={toggleShowLeftOverDays}
                        btnTextStyle={styles.viewPackageText}
                        btnWrapperStyle={styles.viewItineraryContainer}
                        endIcon={MinusIcon}
                        endIconStyle={styles.ctaIcon}
                    />
                </View>
            ) : null}
        </View>
    );
};

const styles = StyleSheet.create({
    itineraryContainer: {
        paddingHorizontal: 16,
    },

    itineraryTitle: {
        borderBottomWidth:1,
        borderBottomColor:holidayColors.grayBorder,
        paddingVertical: 12,
    },

    itineraryCard: {
        width: width - 30,
    },
    itineraryBlock: {
        // paddingHorizontal:16,
    },
    dayatLeisure:{
        ...fontStyles.labelBaseBlack,
        color:holidayColors.black,
    },
    itineraryInnerBlock: {
        borderLeftWidth: 1.4,
        borderColor:holidayColors.grayBorder,
        borderRadius: 1,
        position: 'relative',
        marginLeft:5,
    },
    bulletBlueTxt: {
        backgroundColor: holidayColors.midLightBlue,
        width: 10,
        height: 10,
        borderRadius: 5,
    },
    bulletGrayTxt: {
        backgroundColor: holidayColors.midLightBlue,
        width: 10,
        height: 10,
        borderRadius: 5,
    },
    itineraryRow: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        display:'flex',
        paddingVertical:7,
        paddingHorizontal:6,
    },
    itineraryRowContent: {
        width: '90%',
        marginLeft: 15,
        paddingBottom: 15,
        borderBottomWidth: 1,
        borderColor: holidayColors.grayBorder,
    },
    itineraryRowContentLast: {
        borderBottomWidth: 0,
    },

    bulletDot: {
        marginTop: 3,
        marginLeft:-11,
    },

    dashedLine: {
        position: 'absolute',
        left: 15,
        padding: '5%',
        height: 100,
        overflow: 'hidden',
    },

    bulletGreyTxt: {
        color: holidayColors.grayBorder,
    },
    iconImage: {
        width: 18,
        height: 18,
        resizeMode: 'contain',
        marginRight: 10,
    },

    summary:{
        display: 'flex',
        flexDirection: 'row',
         marginLeft: 10,
         marginBottom:15,
         ...fontStyles.labelBaseBlack,
    },
    borderBottomSummary:{
        borderBottomWidth: 1,
        borderColor: holidayColors.grayBorder,
    },
    viewItineraryContainer: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 10,
        justifyContent: 'center',
        width:'100%'
    },
    viewPackageText: {
        display: 'flex',
        alignSelf: 'center',
        ...fontStyles.labelBaseBold,
        color: holidayColors.primaryBlue,
    },
    minusIcon: {
        width: 15,
        height: 1.5,
        backgroundColor: holidayColors.primaryBlue,
        marginLeft: 10,
    },
    ctaIcon: {
        width: 20,
        height: 20,
        tintColor: holidayColors.primaryBlue,
    },
});

const HtmlStyle = {
    p: {
        ...fontStyles.labelBaseBlack,
        color: holidayColors.black,
        marginTop:2,
    },
    b: {
        ...fontStyles.labelBaseBlack,
        color: holidayColors.gray,
    },
};

export default PackageItinerary;
