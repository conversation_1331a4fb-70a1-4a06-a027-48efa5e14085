import  React from 'react';
import {
    StyleSheet,
    View,
    Text,

  } from 'react-native';

  import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayColors } from '../../../../../Styles/holidayColors';

  const PackageItineraryTabs = (props) => {

     const {active, place, days} = props || {};
     return (
            <View style={AtomicCss.makeRelative}>
                <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, styles.tabList]}>
                    <Text style={[AtomicCss.font12, AtomicCss.blackFont, active ? AtomicCss.azure : AtomicCss.defaultText]}>{place} </Text>
                    <Text style={[AtomicCss.font12, AtomicCss.regularFont, active ? AtomicCss.azure : AtomicCss.defaultText]}>- Day {days}</Text>
                </View>
                <View style={active ? styles.tabSelectUnderline : styles.tabUnderline} />
            </View>
      );
  };

  const styles = StyleSheet.create({
    tabList: {
        backgroundColor: 'white',
        borderRadius: 2,
        padding: 15,
        shadowColor: 'rgba(0, 0, 0, 0.5)',
          shadowOffset: {
              width: 0,
              height: 2,
          },
          shadowOpacity: 0.25,
          shadowRadius: 3.84,
          elevation: 3,
          marginRight: 10,
      marginTop:5,
      marginBottom:5,
    },


    tabUnderline: {
        backgroundColor: 'white',
        height: 3,
        width: '90%',
        position: 'absolute',
        bottom: -10,
        zIndex: 5,
    },
    tabSelectUnderline: {

      backgroundColor: holidayColors.primaryBlue,
      height: 3,
      width: '93%',
      position: 'absolute',
      bottom: -10,
      zIndex: 5,
    },
  });

  export default PackageItineraryTabs;
