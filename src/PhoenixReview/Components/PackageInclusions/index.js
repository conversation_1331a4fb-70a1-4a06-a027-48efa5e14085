import React, { useState } from 'react';
import { StyleSheet, View } from 'react-native';
import _, { isEmpty } from 'lodash';
import PackageItinerary from './Itenary';
import PackageExclusions from './PackageExclusions';
import FeatureList from '../../../PhoenixDetail/Components/FDFeatureEdit/FeatureList';
import FDFeatureEditOverlay from '../../../PhoenixDetail/Components/FDFeatureEditOverlay';
import ActivityInclusionSection from '../../../DetailMimaComponents/ActivityInclusionsSection';
import { holidayColors } from '../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { getFilteredPackageFeatures } from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/PhoenixDetailUtils';

const PackageInclusions = (props) => {
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [activeFeature, setActiveFeature] = useState(-1);
  const toggleFDFeatureBottomSheet = (visibility, index) => {
    setShowDetailModal(visibility);
    setActiveFeature(index);
  };
  const {
    reviewData,
    carContents,
    roomCount,
    baggageInfo = {},
    additionalDetail = {},
  } = props || {};
  const { reviewDetail } = reviewData || {};
  const { exclusions = '', inclusions = [] } = additionalDetail || {};
  const filteredPackageFeatures = getFilteredPackageFeatures(reviewDetail?.features || [])
  return (
    <View style={styles.tabContent}>
      <View style={styles.content}>
        {showDetailModal && (
          <FDFeatureEditOverlay
            packageFeatures={filteredPackageFeatures}
            isReview
            toggleFDFeatureBottomSheet={toggleFDFeatureBottomSheet}
            isOverlay={true}
            dynamicPackageId={reviewDetail?.dynamicId}
            activeIndex={activeFeature}
          />
        )}
        {!isEmpty(inclusions) && <ActivityInclusionSection inclusionDetails={inclusions} />}
        {filteredPackageFeatures && filteredPackageFeatures.length > 0 && (
          <FeatureList
            packageFeatures={filteredPackageFeatures}
            isReview
            isOverlay={false}
            editable={false}
            toggleFDFeatureBottomSheet={(show, index) => toggleFDFeatureBottomSheet(show, index)}
          />
        )}
      </View>
      <PackageItinerary
        reviewData={reviewData}
        carContents={carContents}
        roomCount={roomCount}
        baggageInfo={baggageInfo}
      />
      {!_.isEmpty(exclusions) ? <PackageExclusions data={exclusions} /> : null}
    </View>
  );
};

const styles = StyleSheet.create({
  tabContent: {
    backgroundColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius16,
  },
  content: { paddingHorizontal: 16 },
});

export default PackageInclusions;
