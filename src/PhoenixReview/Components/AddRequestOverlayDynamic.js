import React from 'react';
import PropTypes from 'prop-types';

import {
  Animated,
  BackHandler,
  Easing,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import SpecialRequestBtn from './SpecialRequestBtn';
import { PDTConstants } from '../../Review/HolidayReviewConstants';
import { trackReviewLocalClickEvent } from '../../Review/Utils/HolidayReviewUtils';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { borderRadiusValues, holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import PageHeader from '../../Common/Components/PageHeader';
import TextButton from '../../Common/Components/Buttons/TextButton';
import { logHolidayReviewPDTClickEvents } from '../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import FloatingInput from '@Frontend_Ui_Lib_App/FloatingInput';
import withBackHandler from '../../hooks/withBackHandler';
import { SUB_PAGE_NAMES } from '../../HolidayConstants';

class AddRequestOverlayDynamic extends React.Component {

  constructor(props) {
    super(props);
    const { form, data, options } = this.props;
    const formValue = data[form.sectionId];
    const { fields } = form;
    const field = fields[Object.keys(fields)[0]];
    const optionsList = options[field.optionIdentifier];
    const isUserInput = optionsList.some(option => option.value === formValue);

    this.state = {
      overlayPosition: new Animated.Value(0),
      formValue,
      userValue: isUserInput ? '' : formValue,
    };
  }

  static navigationOptions = { header: null };

  componentDidMount() {
    const bottom = 700;
    const delay = 200;
    const duration = 600;
    this.startAnimate(bottom, duration, delay);
  }
  onBackClick=()=>{
    this.props.closePopUp();
    return true;
  }

  startAnimate(bottom, duration, delay) {
    Animated.timing(this.state.overlayPosition, {
      toValue: bottom,
      easing: Easing.easeInOut,
      duration: duration,
      delay: delay,
    }).start();
  }

  updateUserRequest = userValue => {
    this.setState({ userValue });
  };
  captureClickEvents = ( eventName = '' ) =>{
    logHolidayReviewPDTClickEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName:SUB_PAGE_NAMES.SPECIAL_REQUESTS,
    })
    trackReviewLocalClickEvent(eventName);
  }
  updateUserAndFormData = formValue => {
    const { updateCollapsedData, form } = this.props;
    this.setState({ formValue });
    updateCollapsedData(formValue, form.sectionId);
    this.captureClickEvents(PDTConstants.SPECIAL_REQUEST_SAVE);
  };

  updateRequest = formValue => {
    const { updateCollapsedData, form } = this.props;
    this.setState({ formValue, userValue: '' });
    updateCollapsedData(formValue, form.sectionId);
  };

  editRequest = () => {
    const { updateCollapsedData, form } = this.props;
    this.setState({ formValue: '' });
    updateCollapsedData('', form.sectionId);
    this.captureClickEvents(PDTConstants.SPECIAL_REQUEST_EDIT);
  };

  removeRequest = () => {
    const { updateCollapsedData, form } = this.props;
    this.setState({ formValue: '', userValue: '' });
    updateCollapsedData('', form.sectionId);
    this.captureClickEvents(PDTConstants.SPECIAL_REQUEST_REMOVE);
  };

  render() {
    const { form, options } = this.props;
    const { fields, description, displayName } = form;
    const field = fields[Object.keys(fields)[0]];
    const optionsList = options[field.optionIdentifier];

    return (
      <View style={styles.overlayContainer}>
        <Animated.View
          style={[
            styles.overlayContent,
            { bottom: this.state.overlayPosition },
          ]}
        >
          <PageHeader
            title={displayName}
            showShadow
            showBackBtn
            onBackPressed={() => this.props.closePopUp('')}
          />
          <ScrollView style={styles.travellerInfo}>
            {!this.state.formValue ? (
              <View style={styles.formWrapper}>
                <Text style={styles.formInfo}>{description}</Text>
                <View style={styles.optionsSection}>
                  {optionsList.map(option => (
                    <SpecialRequestBtn
                      label={option.label}
                      key={option.value}
                      index={option.value}
                      updateRequest={this.updateRequest}
                    />
                  ))}
                </View>
                <View>
                  <FloatingInput
                    customStyle={styles.customStyle}
                    inputProps={{
                      multiline: true,
                      maxLength: 140
                    }}
                    label="Something else on your mind?"
                    labelAnimationLeftValue={32}
                    labelAnimationTopValue={35}
                    onChangeText={this.updateUserRequest}
                    startIconVerticalOffset={-14}
                    value={this.state.userValue}
                  />
                  <TextButton
                    buttonText="SAVE"
                    handleClick={() => this.updateUserAndFormData(this.state.userValue)}
                    btnTextStyle={styles.btnText}
                    btnWrapperStyle={styles.savedBtn}
                  />
                </View>
              </View>
            ) : null}
            {this.state.formValue ? (
              <View style={styles.formWrapper}>
                <Text style={styles.thanksText}>
                  Thank you! Your request has been noted and our agent will
                  connect with you soon.
                </Text>
                <View style={styles.requestSection}>
                  <Text style={styles.requestText}>{this.state.formValue}</Text>
                </View>
              </View>
            ) : null}
          </ScrollView>
          {this.state.formValue && this.state.userValue ? (
            <View style={styles.btnSection}>
              <TextButton
                buttonText="REMOVE REQUEST"
                handleClick={this.removeRequest}
                btnTextStyle={styles.btnText}
                btnWrapperStyle={styles.requestBtns}
              />
              <TextButton
                buttonText="EDIT REQUEST"
                handleClick={this.removeRequest}
                btnTextStyle={styles.btnText}
                btnWrapperStyle={[styles.requestBtns, styles.borderLeft]}
              />
            </View>
          ) : null}
          {this.state.formValue && !this.state.userValue ? (
            <View style={styles.btnSection}>
              <TextButton
                buttonText="REMOVE REQUEST"
                handleClick={this.removeRequest}
                btnTextStyle={styles.btnText}
                btnWrapperStyle={styles.requestBtns}
              />
            </View>
          ) : null}
        </Animated.View>
      </View>
    );
  }
}

AddRequestOverlayDynamic.propTypes = {
  closePopUp: PropTypes.func.isRequired,
  form: PropTypes.object.isRequired,
  data: PropTypes.object.isRequired,
  options: PropTypes.object.isRequired,
};

const styles = StyleSheet.create({
  overlayContainer: {
    justifyContent: 'flex-end',
    position: 'absolute',
    bottom: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 15,
    elevation: 5,
  },
  customStyle: {
    inputFieldStyle: {
    fontFamily: 'Lato-Black',
    fontWeight: '900',
    borderBottomLeftRadius: borderRadiusValues.br2,
    borderBottomRightRadius: borderRadiusValues.br2,
    },
    labelStyle: {
    fontFamily: 'Lato-Bold',
    fontWeight: '700'
    },
  },
  overlayBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 3,
    elevation: 3,
  },
  overlayContent: {
    backgroundColor: holidayColors.white,
    zIndex: 4,
    position: 'absolute',
    bottom: 0,
    marginBottom: -700,
    width: '100%',
    height: '100%',
  },
  formWrapper: {
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  formInfo: {
    ...fontStyles.labelBaseRegular,
    color:holidayColors.gray,
    lineHeight: 18,
    marginBottom: 14,
  },
  optionsSection: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 18,
  },
  heading: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    marginBottom: 14,
  },
  textarea: {
    height: 100,
    width: '100%',
    ...holidayBorderRadius.borderRadius8,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    flexWrap: 'wrap',
    padding: 10,
    ...fontStyles.labelBaseBold,
    lineHeight: 18,
  },
  savedBtn: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 40,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    borderWidth: 1,
    borderTopWidth: 0,
    borderColor: holidayColors.grayBorder,
    ...holidayBorderRadius.borderRadius8,
    marginBottom: 20,
    width:'100%'
  },
  btnText: {
    ...fontStyles.labelBaseBold,
    color:holidayColors.primaryBlue,
  },
  btnSection: {
    flexDirection: 'row',
    paddingTop: 7,
    paddingBottom: 7,
    borderTopWidth: 1,
    borderColor: holidayColors.grayBorder,
    marginHorizontal: 15,
  },
  requestBtns: {
    height: 39,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  borderLeft: {
    borderLeftWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  thanksText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    marginBottom: 25,
  },
  requestText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  requestSection: {
    position: 'relative',
  },
  iconQuotes: {
    height: 30,
    width: 30,
    zIndex: 1,
    position: 'absolute',
    top: -4,
  },
  text: {
    ...fontStyles.labelBaseRegular,
    letterSpacing: 0,
  },
});
export default withBackHandler(AddRequestOverlayDynamic)
