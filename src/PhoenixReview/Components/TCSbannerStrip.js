import React, { useEffect, useState } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { getPokusforReviewTcsV2Section } from '../../utils/HolidaysPokusUtils';
import TCSbannerBox from './TCSbannerBox';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { borderRadiusValues } from '../../Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import CheckBox from '@Frontend_Ui_Lib_App/CheckBox';
import { logHolidayReviewPDTClickEvents } from '../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { isEmpty } from 'lodash';

const TCSbannerStrip = ({
  handleTcsBannerAckSelection,
  showTCSErrorStrip,
  handleTcsBannerToggle,
  onReferenceAvailable,
  isSelected,
  showAckSection, // need to remove ACK section in NEW Version for TCS Section
  trackReviewLocalClickEvent,
  tcsSection,
}) => {
  const { header, notifications, footer, description, acknowledgement = {} } = tcsSection || {};
  const { title: ackTitle, description: ackDescription} = acknowledgement || {};
  const [showMore, setShowMore] = useState(false);
  const { iconUrl, title } = header || {};
  const handleCheckBoxSelection = (value) => {
    handleTcsBannerAckSelection(value);
  };
  const showNewTcs = getPokusforReviewTcsV2Section();
  const showAlertText = !!showTCSErrorStrip && !showMore && !showNewTcs;
  const captureClickEvents = ({ event = '', actionType = {} }) => {
    logHolidayReviewPDTClickEvents({
      actionType: !isEmpty(actionType) ? actionType : PDT_EVENT_TYPES.buttonClicked,
      value: event,
    });
    trackReviewLocalClickEvent(event);
  };
  useEffect(() => {
    captureClickEvents({ event: 'TCS_shown', actionType: PDT_EVENT_TYPES.contentSeen });
  }, []);

  const knowMore = () => {
    setShowMore(!showMore);
    handleTcsBannerToggle(!showMore);
    captureClickEvents({ event: 'TCS_KnowMore' });
  };

  return (
    <View ref={(ref) => ref && onReferenceAvailable && onReferenceAvailable(ref)}>
      <View style={[styles.container, !showAlertText && styles.containerBorder]}>
        <View style={styles.cardContainer}>
          <View style={[AtomicCss.flexRow, AtomicCss.flex1, AtomicCss.alignCenter]}>
            <Image source={{ uri: iconUrl }} style={styles.idIcon} />
            <Text style={[styles.titleText, AtomicCss.flex1, AtomicCss.lineHeight18]}>{title}</Text>
          </View>
        </View>
        <View style={marginStyles.mt8}>
          {notifications?.map((el) => {
            return <Text style={styles.bulletList}>{`\u2022 ${el}`}</Text>;
          })}
        </View>

        {showAckSection && (
          <View style={styles.checkboxContainer}>
            <CheckBox
              activeColor={holidayColors.primaryBlue}
              isChecked={isSelected}
              alignMiddle
              borderColor={holidayColors.lightGray}
              boxRadius={4}
              customContainerStyle={isSelected ? styles.checkBoxSelected : styles.checkBoxUnselected}
              customStyles={styles.customStyles}
              onPress={() => {handleCheckBoxSelection(!isSelected);}}
              size={18}
              tickSize={10}
              children={
                <Text style={styles.ack}>
                  <Text style={[AtomicCss.blackText, AtomicCss.boldFont]}>{ackTitle}</Text>{' '}
                  {(!!ackTitle) && <Text style={[AtomicCss.redText, AtomicCss.boldFont]}>*</Text>}
                  {(!!ackTitle && !!ackDescription) && <Text>: </Text>}
                  {ackDescription}
                </Text>
              }
            />
          </View>
        )}
        {!showMore && ((!showNewTcs && !!description) || (showNewTcs && (!!description || !!footer))) && (
          <TouchableOpacity onPress={knowMore}>
            <Text style={styles.knowMore}>KNOW MORE</Text>
          </TouchableOpacity>
        )}

        {!!showMore && !!showTCSErrorStrip && showAckSection && (
          <Text style={styles.ackError}>Please select the above acknowledgement to proceed</Text>
        )}
        {!!showMore && (
          <TCSbannerBox
            fromStrip={true}
            footer={showNewTcs ? footer : {}}
            showLess={() => setShowMore(false)}
            description={description}
          />
        )}
      </View>
      {showAlertText && (
        <View style={styles.tcsErrorStripContainer}>
          <Text style={[marginStyles.mt12, styles.stripError, AtomicCss.flex1]}>
            Please select the above acknowledgement to proceed
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...paddingStyles.ph16,
    ...paddingStyles.pv6,
    backgroundColor: holidayColors.fadedRed,
    overflow: 'hidden',
  },

  checkBoxSelected: { 
    backgroundColor: holidayColors.primaryBlue,
    top: 3
  },

  checkBoxUnselected: {
    backgroundColor: holidayColors.white,
    top: 3
  },

  containerBorder: {
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderBottomRightRadius: borderRadiusValues.br16,
  },
  customStyles: {
    alignItems: 'flex-start'
  },
  cardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pt10,
    ...paddingStyles.pb6,
  },
  checkboxContainer: {
    flexDirection: 'row',
    ...marginStyles.mb2,
  },
  titleText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  stripError: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.red,
    lineHeight: 18,
  },
  checkbox: {
    alignSelf: 'center',
  },
  descriptionTextStyle: {
    color: holidayColors.gray,
    ...paddingStyles.pb12,
    ...fontStyles.labelBaseRegular,
  },
  tcsErrorStripContainer: {
    backgroundColor: holidayColors.fadedRed,
    height: 45,
    width: '100%',
    borderBottomEndRadius: borderRadiusValues.br16,
    borderBottomStartRadius: borderRadiusValues.br16,
    alignItems: 'center',
    overflow: 'hidden',
  },
  tcsInfoText: {
    ...paddingStyles.ph20,
  },
  idIcon: {
    width: 34,
    height: 34,
    ...marginStyles.mr6,
  },
  ArroIcon: { width: 24, height: 24 },
  bulletList: {
    color: holidayColors.gray,
    ...marginStyles.mr10,
    lineHeight: 15,
    textAlign: 'left',
    ...AtomicCss.paddingBottom12,
    ...fontStyles.labelSmallRegular,
  },
  knowMore: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBlack,
    ...marginStyles.mb12,
  },
  ack: {
    color: holidayColors.gray,
    marginRight: 50,
    ...marginStyles.ml10,
    lineHeight: 19,
    ...paddingStyles.pb12,
    ...fontStyles.labelBaseRegular,
  },
  ackError: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.red,
    ...marginStyles.mh16,
    ...marginStyles.mb10,
    ...AtomicCss.lineHeight18,
  },
  checkBtn: {
    ...paddingStyles.pr18,
  },
});

export default TCSbannerStrip;
