import  React from 'react';
import {
    StyleSheet,
    View,
    Text,
  } from 'react-native';

  import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';


  const CancellationPolicyList = ({policies}) => {

     if (policies)
      {return (
            <View style={styles.policyList}>
                {policies?.map((item, index) =>
                    <View key={item.text} style={styles.wrapper}>
                        <Text style={styles.bullet}>{'\u2022'}</Text>
                        <View style={styles.width90}><Text style={styles.policy}>{item.text}</Text></View>
                    </View>
                )}
            </View>
      );}
      else {return null;}
  };

  const styles = StyleSheet.create({

    policyList: {
        flexWrap: 'wrap',
        marginTop: 15,
        padding: 15,
        borderWidth: 1,
        borderColor: '#e7e7e7',
        borderRadius: 2,
        minHeight:100,
    },
    width90: {
        width: '90%',
    },
    wrapper:{
        ...AtomicCss.flexRow,
        ...AtomicCss.marginBottom8,

    },
    bullet:{
        ...AtomicCss.greenText,
        ...AtomicCss.font13,
        ...AtomicCss.blackFont,
        ...AtomicCss.marginRight10,
    },
    policy:{
        ...AtomicCss.font11,
        ...AtomicCss.blackText,
        ...AtomicCss.regularFont,
    },
  });

  export default CancellationPolicyList;
