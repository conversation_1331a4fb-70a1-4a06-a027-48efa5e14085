import React from 'react';
import { StyleSheet, Text, View, Image, TouchableOpacity } from 'react-native';
import editIcon from '../images/ic_edit.png';
import greenTickIcon from '../images/ic_tickGreen2.png';
import redTickIcon from '@mmt/legacy-assets/src/redalerticon1.webp';
import { PDTConstants } from '../Utils/HolidayReviewConstants';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { borderRadiusValues, holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../Styles/Spacing/index';
import TextButton from '../../Common/Components/Buttons/TextButton';
import { logHolidayReviewPDTClickEvents } from '../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

export const STATUS_INCOMPLETE = 'incomplete';
export const STATUS_MANDATORY = 'mandatory';
export const STATUS_SKIP = 'skip';
export const STATUS_DONE = 'done';
export const STATUS_COMPLETE = 'Complete';

const STATUS_TEXT_SKIP_TO_NEXT = 'Skip to Next';
class ReviewSection extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      expanded: props.expanded || false,
    };
  }
  componentDidUpdate(prevProps) {
    if (prevProps.expanded !== this.props.expanded) {
      this.setState({ expanded: this.props.expanded });
    }
  }
  expand = () => {
    this.setState(
      {
        expanded: true,
      },
      () => {
        if (this.props.onExpand) {
          this.props.onExpand(true);
        }
      },
    );
  };
  collapse = () => {
    this.setState(
      {
        expanded: false,
      },
      () => {
        if (this.props.onExpand) {
          this.props.onExpand(false);
        }
      },
    );
  };
  captureClickEvents = (eventName = '', prop1 = '') => {
    logHolidayReviewPDTClickEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value : eventName + prop1,
    });
    this.props.trackReviewLocalClickEvent(eventName, prop1);
  }
  toggle = () => {
    if (this.state.expanded) {
      if (!this.props.isSectionsExpanded) {
        this.collapse();
      }
      
      this.captureClickEvents(PDTConstants.COLLAPSE_SECTION,
        `_${this.props.stepId}_${this.props.title}`)
    } else {
      // this.props.closeAll() {/* commented to remove toggle effect in accordian  */}
      if (this.props.stepId != 1) {
        this?.props?.updateSectionStatus();
      }
      this.expand();
      this.captureClickEvents(PDTConstants.EXPAND_SECTION,
        `_${this.props.stepId}_${this.props.title}`);
    }
  };
  getStatusIcon = () => {
    let { status } = this.props;
    status = status ? status.toLowerCase() : status;
    if (status === STATUS_DONE) {
      return greenTickIcon;
    }
    if (status === STATUS_INCOMPLETE) {
      return redTickIcon;
    }
    return null;
  };
  getStatusColor = () => {
    let { status } = this.props;
    status = status ? status.toLowerCase() : status;
    if (status === STATUS_DONE) {
      return '#007E7D';
    }
    if (status === STATUS_INCOMPLETE || status === STATUS_MANDATORY) {
      return '#EC2127';
    }
    return '#008CFF';
  };
  getBorderColor = () => {
    let { status } = this.props;
    status = status ? status.toLowerCase() : status;
    if (status === STATUS_DONE) {
      return '#26B5A9';
    }
    if (status === STATUS_INCOMPLETE) {
      return '#F57C7E';
    }
    return '#FFFFFF';
  };
  getStatusString = () => {
    let { status } = this.props;
    const { expanded } = this.state;
    if (!status) {
      if (expanded) {
        return STATUS_TEXT_SKIP_TO_NEXT;
      }
      return null;
    }
    status = status.toLowerCase();
    if (status === STATUS_DONE) {
      return 'Done';
    }
    if (status === STATUS_INCOMPLETE) {
      return 'Incomplete';
    }
    if (status === STATUS_MANDATORY) {
      return 'Mandatory';
    }
    return STATUS_TEXT_SKIP_TO_NEXT;
  };
  handleSkip = () => {
    if (!this.props.onSkip) {
      return;
    }
    const statusText = this.getStatusString();
    if (statusText === STATUS_TEXT_SKIP_TO_NEXT && this.props.stepId !== this.props.maxSteps) {
      this.toggle();
      this.props.onSkip(this.props.stepId);
    } else {
      this.toggle();
    }
  };
  render() {
    const statusIcon = this.getStatusIcon();
    let statusText = this.getStatusString();
    const { stepId = '', maxSteps = '', subTitle = '', title = '', renderSubComponent = () => { } } = this.props;
    statusText =
      statusText === STATUS_TEXT_SKIP_TO_NEXT && stepId === maxSteps
        ? null
        : statusText;
    return (
      <View style={[styles.container, { borderColor: this.getBorderColor() }]}>
        <TouchableOpacity onPress={this.toggle}>
          <View style={[styles.titleContainer, { paddingBottom: subTitle ? 0 : 16 }]}>
            {statusIcon && <Image style={styles.statusIcon} source={statusIcon} />}
            <Text style={styles.stepNumber}>
              {stepId}
              <Text style={styles.maxSteps} numberOfLines={1}>
                /{maxSteps}
              </Text>
            </Text>
            <Text style={styles.title} numberOfLines={1}>
              {title}
            </Text>
            <TextButton
              buttonText={statusText}
              handleClick={this.handleSkip}
              btnTextStyle={[styles.infoText, { color: this.getStatusColor() }]}
            />
            {!this.state.expanded && <Image style={styles.editIcon} source={editIcon} />}
          </View>
          {subTitle ? (
            <Text style={[styles.subTitle, { paddingLeft: statusIcon ? 38 : 18 }]}>
              {subTitle}
            </Text>
          ) : null}
          {!this.state.expanded && renderSubComponent()}
        </TouchableOpacity>
        {this.state.expanded ? this.renderContent() : null}
      </View>
    );
  }
  renderContent = () => (
    <View style={[styles.content, this.props.contentStyle]}>{this.props.children}</View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.white,
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius16,
    borderColor: holidayColors.white,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.ph16,
    ...paddingStyles.pt20,
  },
  stepNumber: {
    color: holidayColors.black,
    ...marginStyles.mr4,
    ...marginStyles.mt4,
    ...fontStyles.labelLargeBlack,
  },
  maxSteps: {
    color: holidayColors.black,
    ...fontStyles.labelLargeRegular,
  },
  title: {
    flex: 1,
    color: holidayColors.black,
    ...fontStyles.headingBase,
  },
  subTitle: {
    color: holidayColors.black,
    ...fontStyles.labelBaseRegular,
    paddingLeft: 38,
    ...marginStyles.mt4,
    ...marginStyles.mb14,
    ...paddingStyles.pr12,
  },
  statusIcon: {
    width: 14,
    height: 14,
    ...marginStyles.mr6,
  },
  editIcon: {
    width: 18,
    height: 18,
    ...marginStyles.ml12,
  },
  infoText: {
    color: holidayColors.black,
    ...fontStyles.labelBaseBold,
    ...marginStyles.ml8,
    textAlign: 'center',
  },
  content: {
    ...paddingStyles.pa0,
    borderTopWidth: 1,
    borderTopColor: holidayColors.grayBorder,
  },
});
export default ReviewSection;
