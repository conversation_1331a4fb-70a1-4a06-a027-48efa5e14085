import React, { useState } from 'react';
import { Platform, StyleSheet, View, ScrollView } from 'react-native';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import BottomSheet from '../../../Common/Components/BottomSheet';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import PageHeader from '../../../Common/Components/PageHeader';
import { fontStyles } from '../../../Styles/holidayFonts';
import ReviewFareBreakup from '../FareBreakup';
import { ReviewFooterV2 } from '../PageFooter/FooterV2';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import { getOpitimsedImageUrl, IMAGE_ICON_KEYS } from '../../../Common/Components/HolidayImageUrls';
import { holidayColors } from '../../../Styles/holidayColors';
import TcsPanWidget from './TcsPanWidget';
import { fetchFareBreakUp } from '../../../utils/HolidayNetworkUtils';
import { showLongToast } from '@mmt/core/helpers/toast';
import { FARE_BREAKUP_ERROR, TAX_COLLECT_TCS_TYPES } from '../../../HolidayConstants';
import { TCS_AMOUNT, TCS_AMOUNT_CALCULATED } from './TcsPanConstants';

const HolidaysReviewTcs = (props) => {
  const {
    onBackPressed,
    handlePrePayment,
    setTcsBottomSheetVisibility,
    setFareBreakupVisibilityForTcs,
    fareBreakupVisibilityForTcs,
    listData,
    validateZC,
    holidayReviewData,
    gotoSection,
    pricingDetail,
    emiDetail,
    trackReviewLocalClickEventInComponent,
  } = props || {};

  const { dynamicPackageId } = holidayReviewData || {};
  const { tcsMetadata } = pricingDetail || {};
  const { tcsApplicableEnum } = tcsMetadata || {};
  const TITLE = 'PAN Required';
  const [fareBreakupData, setFareBreakupData] = useState(null);


  const handleError = (error) => {
    const { message, code } = error || {};
    if (code === FARE_BREAKUP_ERROR.SESSION_EXPIRED) {
      showLongToast('Session has been expired. Please search again.');
    } else {
      showLongToast(message || 'Failed to fetch fare breakup ');
    }
    onBackPressed();
  };

  const getFareBreakup = () => {
    fetchFareBreakUp({ dynamicPackageId })
      .then((response) => {
        const { success, statusCode, error } = response || {};
        if (statusCode !== 1 || !success) {
          handleError(error);
          return;
        }
        setFareBreakupData(response);
      })
      .catch((error) => {
        console.error('Error fetching fare breakup', error);
        handleError(error);
      });
  };

  const clearFareBreakup = () => {
    setFareBreakupData(null);
  };

  const handleWidgetDetails = (event) => {
    const { pan_tcs_tracking_data, tcsCollected } = event || {};
    // tcsCollected is used in Mweb and pan_tcs_tracking_data is used in Mobile
    if (pan_tcs_tracking_data || tcsCollected) {
      getFareBreakup();
    } else {
      clearFareBreakup();
    }
  };

  const handleTrackingData = (event) => {
    const { pan_tcs_tracking_data = '' } = event || {};
    if (pan_tcs_tracking_data.startsWith(TCS_AMOUNT)) {
      trackReviewLocalClickEventInComponent(TCS_AMOUNT_CALCULATED, '', { prop1: pan_tcs_tracking_data });
      return;
    }
    trackReviewLocalClickEventInComponent(pan_tcs_tracking_data, '');
  };

  const handleOnButtonPress = () => {
    handlePrePayment();
    setTcsBottomSheetVisibility(false);
  };

  const hideFareBreakPopup = () => setFareBreakupVisibilityForTcs(false);

  const handleGotoSection = () => {
    hideFareBreakPopup();
    setTcsBottomSheetVisibility(false);
    gotoSection();
  };

  return (
    <View style={styles.container}>
      <View style={styles.upperContainer}>
        <PageHeader
          showBackBtn={true}
          showShadow={false}
          title={TITLE}
          onBackPressed={onBackPressed}
          titleStyles={styles.title}
          headingContainerStyles={styles.titleContainer}
          containerStyles={styles.headerContainer}
        />

        <HolidayImageHolder
          imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.PAN_CARD_WITH_TRAVEL)}
          style={styles.panImage} />
        <ScrollView>
          <TcsPanWidget
            handleTrackingData={handleTrackingData}
            handleWidgetDetails={handleWidgetDetails}
            type={tcsApplicableEnum || TAX_COLLECT_TCS_TYPES.PAN_WITH_TCS}
            containerStyle={{ ...marginStyles.ma16 }}
            pricingDetail={pricingDetail}
          />
        </ScrollView>
      </View>

      {/* This View is necessary to prevent UI issues induced due to native TCS widiget. Do not remove it. */}
      <View />

      <View style={styles.footerContainer}>
        <ReviewFooterV2
          fareBreakupResponse={fareBreakupData}
          handleOnButtonPress={handleOnButtonPress}
          setFareBreakupVisibilityForTcs={setFareBreakupVisibilityForTcs}
          fareBreakupVisibilityForTcs={fareBreakupVisibilityForTcs}
        />
      </View>

      <BottomSheet
        isOpen={fareBreakupVisibilityForTcs}
        modalVisible={fareBreakupVisibilityForTcs}
        closeModal={hideFareBreakPopup}
        disableHeight={true}>
        <ReviewFareBreakup
          closeModal={hideFareBreakPopup}
          fareBreakUp={fareBreakupData?.fareBreakUp}
          listData={listData}
          gotoSection={handleGotoSection}
          validateZC={validateZC}
          emiDetail={emiDetail}
          holidayReviewData={holidayReviewData}
          isCouponEditable={false}
        />
      </BottomSheet>
    </View>
  );
};


const styles = StyleSheet.create({
  bottomSheetContainer: {
    maxHeight: '100%',
    paddingHorizontal: 0,
    borderTopRightRadius: 0,
    borderTopLeftRadius: 0,
    padding: 0,
    backgroundColor: holidayColors.white,
  },
  container: {
    height: '100%',
    width: '100%',
    backgroundColor: holidayColors.white,
    position: 'absolute',
    elevation: 15,
    zIndex: 15,
  },
  footerContainer: {
    justifyContent: 'flex-end',
  },
  titleContainer: {
    ...marginStyles.mr30,
  },
  title: {
    ...fontStyles.labelLargeBold,
  },
  panImage: {
    width: 195,
    height: 104,
    alignSelf: 'center',
    ...marginStyles.mt40,
  },
  upperContainer: {
    flex: 1,
  },
  headerContainer: {
    ...paddingStyles.pl16,
    ...paddingStyles.pr16,
    ...Platform.select({
      android: {
        ...paddingStyles.pt8,
        ...paddingStyles.pb16,
      },
    }),
  }
});

export default HolidaysReviewTcs;
