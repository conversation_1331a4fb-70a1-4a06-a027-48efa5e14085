import  React, { useEffect } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Text,
    ScrollView,
  } from 'react-native';

  import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
  import iconClose from '../images/ic_closeGrey.png';
  import CancellationPolicyList from './CancellationPolicyList';
  import PolicyDetails from './PolicyDetails';
  import PolicyDesc from './Cancellation/PolicyDesc';
  import { fillDateAndTime } from '../../Common/HolidaysCommonUtils';
import { FLEXI_KEY, ZC_KEY } from '../Utils/HolidayReviewConstants';
import { DEVICE_WINDOW } from '../../utils/HolidayUtils';
import fecha from 'fecha';
import { addDays } from '@mmt/legacy-commons/Helpers/dateTimehelpers';
const deviceHeight = DEVICE_WINDOW.height;

  const zeroCancellation = {
      header:'Zero Cancellation (ZC)',
      subHeader:'Package Cancellation Policy',
      note:'Cancellation Possible till',
      summary:'After that, package is Non-Refundable please view Package Cancellation Policy for more details',
  };
  const flexiDateChange = {
    header:'Flexi Date',
    subHeader:'Package Date Change Policy',
    note:'Date Change Possible till',
    summary:'After that, package date is fixed please view Package Date Change Policy for more details',
};
  const ZeroCancellation = ({closeModal,isZeroCancellation,penaltyDetail,zcOptions}) => {

    const data = isZeroCancellation ? zeroCancellation : flexiDateChange;
    const penaltiesObject = isZeroCancellation ? penaltyDetail?.cancellationPenalty : penaltyDetail?.dateChangePenalty;
    const {penalties,policies} = penaltiesObject;
    const zcOption = zcOptions?.length ? zcOptions.find(e =>
      isZeroCancellation ?
          e.type === ZC_KEY :
          e.type === FLEXI_KEY
      ) : {};
    const getDate = (penalties)=>{
        if (!penalties) {return '';}
        let date;
        for (let i = penalties?.length - 1; i >= 0; i--){
            const item = penalties?.[i];
           if (!item?.nonRefundable){
             date = item?.fromDate;
             break;
           }
        }
        let newDate = addDays(date, -1);
        const dateObj = fillDateAndTime(newDate,'D MMMM');
        let day = dateObj?.split(' ')?.[0];
        return day + ' ' + dateObj?.split(' ')?.[1];

    };
      return (
          <View style={styles.contentWrap}>
             <ScrollView contentContainerStyle={AtomicCss.flexRow} showsVerticalScrollIndicator={false}>
            <TouchableOpacity activeOpacity={0.8} style={{width:'100%'}}>
              <View style={[AtomicCss.marginBottom15, AtomicCss.flexRow]}>
                  <TouchableOpacity onPress={closeModal}><View style={AtomicCss.marginRight15}><Image source={iconClose} style={styles.iconClose} /></View></TouchableOpacity>
                  <Text style={[AtomicCss.font18, AtomicCss.blackFont, AtomicCss.blackText]}>{data.header}</Text>
              </View>
              <View style={AtomicCss.marginBottom15}>
                  <Text style={[AtomicCss.font18, AtomicCss.blackFont, AtomicCss.blackText]}>{data.subHeader}</Text>
              </View>
              {penalties?.length > 0 && !(penalties?.length == 1 && penalties[penalties.length - 1]?.nonRefundable) ?
                <PolicyDesc
                policyTitle ={data.note + ' ' + getDate(penalties) + '*'}
                content = {
                    <Text style={[AtomicCss.font12, AtomicCss.regularFont, AtomicCss.greyText]}>{data.summary}</Text>
                }
              /> : []
              }
              <PolicyDetails penalties={penalties} zcOption={zcOption} isZeroCancellation={isZeroCancellation}/>
              {/* list */}
              <CancellationPolicyList policies={policies}/>
              </TouchableOpacity>
        </ScrollView>
          </View>
      );
  };

  const styles = StyleSheet.create({
    contentWrap: {
        paddingHorizontal: 25,
        paddingTop: 25,
        paddingBottom: 0,
        height:deviceHeight - 50,
    },

    iconClose: {
        width: 24,
        height: 24,
    },
  });

  export default ZeroCancellation;
