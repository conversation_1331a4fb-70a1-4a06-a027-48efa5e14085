import React, { useEffect, useState } from 'react';
import { FlatList, Image, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import CouponCard from '../../../Common/Components/CouponOffers/CouponCard';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import { COUPON_APPLY, COUPON_REMOVE, PDTConstants } from '../../Utils/HolidayReviewConstants';
import iconBlueDown from '@mmt/legacy-assets/src/ic_arrow_blue_down.webp';
import iconBlueUp from '@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp';
import EMIOption from '../EMIOptions';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { borderRadiusValues, holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import EmiTag from './EmiTag';
import BottomSheetOverlay from '../../../Common/Components/BottomSheetOverlay';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing';
import { COUPON_BOX_PLACEHOLDER_TEXT, INVALID_COUPON_MESSAGE, SELECT_ACTIONS, SUCCESSFULLY_APPLIED_MESSAGE } from '../../../PhoenixDetail/DetailConstants';
import { logHolidayReviewPDTClickEvents } from '../../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { capitalizeText } from '../../../../src/utils/textTransformUtil';

const COUPONS_MAX_LENGTH = 4;

const CouponsOffers = (props) => {
    const { dealDetail, validateCoupon, emiDetails, couponData, getEmiOptions, emiOptions, reviewAdditionalPricingDetail, fromPresalesDetail, fromAmendment = false } = props || {};
    const { couponDetails = [] } = dealDetail || {};
    const { bankName = '' } = emiDetails || {};
    const discountsApplied = reviewAdditionalPricingDetail?.discountsApplied?.length > 0 ? reviewAdditionalPricingDetail?.discountsApplied[0] : {};

    const couponCode = discountsApplied?.couponCode;
    const additionalDiscountDetail = discountsApplied?.additionalDiscountDetail;
    const recommendationMessage = additionalDiscountDetail?.recommendationMessage;
    const hasRecommendationMessage = recommendationMessage?.length > 1;
    // const couponDesc = hasRecommendationMessage ? recommendationMessage : 'Coupon applied successfully';   // @todo - not in use now 
    // const offerPrice = discountsApplied?.amount?.price;
    const offerPricePrefix = '- ₹';

    const [ownCoupon, setOwnCoupon] = useState(false);
    const [couponVal, setCouponVal] = useState(ownCoupon ? couponData?.couponCode : '');
    const [couponErr, setCouponErr] = useState(false);
    const [viewMore, setViewMore] = useState(false);
    const [emiModal, setEmiModal] = useState(false);
    const [focusedState, setFocusedState] = useState(false);

    useEffect(() => {
        if (!couponData) {
            return;
        }

        const { error } = couponData;
        if (error) {
            setCouponErr(true);
            return;
        }

        const selected = couponDetails?.find(coupon => coupon.selected);
        if (couponCode && !selected) {
            setCouponVal(couponCode);
            setOwnCoupon(true);
            return;
        }
        setOwnCoupon(checkOwnCoupon());
    }, [couponData]);
    const handleOnFocus = () => {
        setFocusedState(true);
    }
    const handleOnBLur = () => {
        setFocusedState(false);
    }
    const checkOwnCoupon = () => {
        let selected = true;
        couponDetails?.forEach(element => {
            if (element?.selected) {
                selected = false;
                return;
            }
        });

        if (selected && couponData?.selected) {
            return true;
        }
        else {
            setCouponVal('');
            return false;
        }
    };
    const validateOwnCoupon = (remove) => {
        setCouponErr(false);
        if (couponVal) {
            validateCoupon(couponVal?.toUpperCase(), !!remove, remove ? COUPON_REMOVE : COUPON_APPLY, true, fromPresalesDetail);
            setFocusedState(false);
        }
    };
    const changeText = (val) => {
        if (!val) {
            setCouponErr(false);
        }
        setCouponVal(val);
    };
    const captureClickEvents = ({ eventName = '', suffix = '' }) => {
      logHolidayReviewPDTClickEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: eventName + suffix,
      });
      props.trackReviewLocalClickEvent(eventName, suffix);
    };
    const toggleViewMore = () => {
        if (!viewMore) {
          captureClickEvents({ eventName: PDTConstants.VIEW_MORE_COUPONS });
        }
        setViewMore(!viewMore);
    };

    const openEmiModal = () => {
        setEmiModal(!emiModal);
    };
    const placeholderText = !focusedState ? COUPON_BOX_PLACEHOLDER_TEXT.UNFOCUSED : COUPON_BOX_PLACEHOLDER_TEXT.FOCUSED
    const textBoxButtonText = ownCoupon ? SELECT_ACTIONS.REMOVE : SELECT_ACTIONS.APPLY
    const couponTyping = focusedState || couponVal
    const inputBoxStyle = couponErr ? styles.errorTxtBox : couponTyping ? styles.focusedTextBox : styles.textboxWrap
    const couponTextBox = () => {
        return (
            <View style={styles.couponContainer}>
                <View style={[styles.textBox, inputBoxStyle]}>
                    <TextInput
                        autoCapitalize={'characters'}
                        placeholder={placeholderText}
                        value={couponVal}
                        onChangeText={(val) => {
                            changeText(val);
                        }}
                        style={[
                            styles.couponTxtbox,
                            ownCoupon ? styles.selectedText : null,
                        ]}
                        editable={!ownCoupon}
                        onFocus={handleOnFocus}
                        onBlur={handleOnBLur}
                    />
                    {!fromAmendment ? (
                        !couponTyping ? (
                            <TouchableOpacity onPress={handleOnFocus}>
                                <Text style={[styles.applyTextValue]}>Enter Code</Text>
                            </TouchableOpacity>
                        ) : (
                            <TextButton
                                buttonText={capitalizeText(textBoxButtonText)}
                                handleClick={() => validateOwnCoupon(ownCoupon)}
                                btnTextStyle={[styles.applyTextValue, couponVal ? {} : styles.applyTxt]}
                                disabled={!couponVal}
                            />
                        )
                    ) : null}
                    {fromAmendment && <Text>{''}</Text>}
                </View>
            </View>
        );
    };
    const couponError = () => {
        return (
            couponErr &&
            <View style={styles.alertWrapper}>
                <Text style={styles.invalidText}>{INVALID_COUPON_MESSAGE}</Text>
            </View>
        );
    };
    const applyText = () => {
        return (
            ownCoupon &&
            <View style={styles.alertWrapper}>
                <Text style={styles.applyText}>{SUCCESSFULLY_APPLIED_MESSAGE}</Text>
            </View>
        );
    };

    const CouponList = ({ data }) => {
        const arrowImage = viewMore ? iconBlueDown : iconBlueUp;
        const slicedData = data.slice(0, COUPONS_MAX_LENGTH);
        const remainingData = data.slice(COUPONS_MAX_LENGTH);

        const viewMoreText = viewMore
            ? 'SHOW LESS'
            : `+${data?.length - COUPONS_MAX_LENGTH} MORE`;

        return (
            <View style={styles.couponCardWrap}>
                {getCouponList(slicedData)}
                {viewMore && getCouponList(remainingData)}
                {data?.length > COUPONS_MAX_LENGTH && (
                    <TouchableOpacity onPress={toggleViewMore}>
                        <View style={styles.viewTagWrap}>
                            <View style={styles.viewTagHolder}>
                                <Text style={styles.viewMoreText}>{viewMoreText}</Text>
                            </View>
                        </View>
                    </TouchableOpacity>
                )}
            </View>
        );
    };

    const renderCouponItem = ({ item, index }) => {
        const { couponCode, recommendationMessage, description, discountAmount, selected, tncUrl } = item || {};
        const action = selected ? COUPON_REMOVE : COUPON_APPLY;
        const couponToBeRemoved = true;
        const isLastItem = couponDetails.slice(0, COUPONS_MAX_LENGTH).length - 1 === index;
        const validate = () => {
            setCouponErr(false);
            const PDT_ACTION = action === COUPON_APPLY ? PDTConstants.APPLY_COUPON : PDTConstants.REMOVE_COUPON;
            captureClickEvents({ eventName: PDT_ACTION, suffix: `_${couponCode}` });
            validateCoupon(couponCode, selected, action, couponToBeRemoved, fromPresalesDetail);
            setFocusedState(false);
        };
        return (
            <CouponCard
                key={couponCode}
                couponCode={couponCode}
                couponDesc={selected ? description : recommendationMessage}
                offerPrice={offerPricePrefix + rupeeFormatter(discountAmount)}
                selected={selected}
                validateCoupon={validate}
                fromPresalesDetail={fromPresalesDetail}
                tncUrl={tncUrl}
                {...(isLastItem && { cardStyles: styles.lastCouponCard })}
            />
        );
    };

    const getCouponList = (list) => {
        return (
            <FlatList
                data={list}
                keyExtractor={(item) => item.couponCode}
                renderItem={renderCouponItem}
                contentContainerStyle={styles.flatListContainer}
                showsVerticalScrollIndicator={false}
            />
        );
    };

    return (
        <View style={styles.tabContent}>
            <EmiTag
                emiDetails={emiDetails}
                openEmiModal={openEmiModal}
                trackReviewLocalClickEvent={props.trackReviewLocalClickEvent}
            />
            <View style={{ paddingHorizontal: 0 }}>
                {/* textbox */}
                {couponTextBox()}
                {/* invalid coupon */}
                {couponError()}
                {applyText()}

                {emiModal && (
                    <BottomSheetOverlay
                        title={'EMI Options'}
                        toggleModal={() => setEmiModal(!emiModal)}
                        visible={emiModal}
                        containerStyles={styles.emiOverlay}
                        headingContainerStyles={styles.headingContainerStyles}
                    >
                        <EMIOption
                            defaultBankName={bankName}
                            emiOptions={emiOptions}
                            getEmiOptions={getEmiOptions}
                            closeModal={() => setEmiModal(false)}
                        />
                    </BottomSheetOverlay>
                )}
                <CouponList data={couponDetails} />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    tabContent: {
        ...holidayBorderRadius.borderRadius16,
        paddingTop: 0,
    },
    couponContainer: {
        ...paddingStyles.pt16,
        ...paddingStyles.pb8,
        borderLeftWidth: 1,
        borderLeftColor: holidayColors.white,
        borderRightWidth: 1,
        borderRightColor: holidayColors.white,
    },
    viewMoreWrapper: {
        display: 'flex',
        alignItems: 'center',
    },
    couponCardWrap: {
        padding: 0,
    },
    orTagWrap: {
        alignItems: 'center',
        justifyContent: 'center',
    },
    viewTagWrap: {
        justifyContent: 'center',
        ...marginStyles.mh10,
        ...marginStyles.mb10,
    },
    orTagLine: {
        width: '100%',
        borderWidth: 0.5,
        borderColor: holidayColors.grayBorder,
        position: 'absolute',
    },
    viewTagLine: {
        width: '100%',
        borderWidth: 0.5,
        borderColor: holidayColors.grayBorder,
        position: 'absolute',
    },
    viewTagHolder: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    orTag: {
        width: 28,
        height: 28,
        borderRadius: 28,
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
        backgroundColor: holidayColors.grayBorder,
        justifyContent: 'center',
        alignItems: 'center',
    },
    orText: {
        ...fontStyles.labelSmallBlack,
        color: holidayColors.lightGray,
    },
    rediconTick: {
        width: 15,
        height: 15,
        resizeMode: 'cover',
        transform: [{ rotate: '180deg' }],
    },
    viewMoreIcon: {
        width: 18,
        height: 18,
        paddingHorizontal: 10,
        backgroundColor: holidayColors.white,
    },
    applyTextValue: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.primaryBlue,
    },
    applyTxt: {
        opacity: 0.3,
        color: holidayColors.black,
    },
    textBox: {
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
        borderBottomLeftRadius: 8,
        borderBottomRightRadius: 8,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    textboxWrap: {
        ...paddingStyles.ph16,
        ...paddingStyles.pv12,
    },
    errorTxtBox: {
        ...holidayBorderRadius.borderRadius8,
        ...paddingStyles.pa10,
        borderWidth: 1,
        borderColor: holidayColors.red,
        ...marginStyles.mh16,
        backgroundColor: holidayColors.lightRedBg,
    },
    focusedTextBox: {
        ...paddingStyles.pa10,
        borderWidth: 1,
        borderColor: holidayColors.primaryBlue,
        ...marginStyles.mh16,
        backgroundColor: holidayColors.lightBlueBg,
    },
    iconTick: {
        width: 18,
        height: 18,
        resizeMode: 'cover',
    },
    couponTxtbox: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.black,
        borderWidth: 0,
        paddingVertical: 5,
        width: '70%',
    },
    notAvailableText: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.gray,
        borderWidth: 0,
        paddingVertical: 5,
        textAlign: 'center',
    },
    selectedText: {
        color: holidayColors.green,
    },
    alertWrapper: {
        ...AtomicCss.flexRow,
        ...AtomicCss.alignCenter,
        paddingHorizontal: 25,
        width: '100%',
        paddingBottom: 10,
    },
    invalidText: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.red,
    },
    applyText: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.green,
    },
    header: {
        paddingHorizontal: 10,
        alignItems: 'center',
        display: 'flex',
        flexDirection: 'row',
        borderBottomWidth: 2,
        borderBottomColor: holidayColors.grayBorder,
        height: 50,
    },
    headerText: {
        ...fontStyles.labelLargeBold,
        flex: 2,
        color: holidayColors.black,
    },
    viewMoreText: {
        color: holidayColors.primaryBlue,
        ...fontStyles.labelBaseBold,
        backgroundColor: holidayColors.white,
        ...paddingStyles.pa10,
    },
    lastCouponCard: {
        borderBottomLeftRadius: borderRadiusValues.br16,
        borderBottomRightRadius: borderRadiusValues.br16,
    },
    emiOverlay: {
        borderTopLeftRadius: borderRadiusValues.br16,
        borderTopRightRadius: borderRadiusValues.br16,
        backgroundColor: holidayColors.white,
        paddingVertical: 20,
        paddingHorizontal: 20
    },
    headingContainerStyles: {
        paddingBottom: 10
    }
});

export default CouponsOffers;
