import React, {memo} from 'react';
import {Image, Text, TouchableOpacity, View} from 'react-native';
import {isEmpty} from 'lodash';
import iconEmiTag from '../../images/ic_emitag.png';
import {NO_COST, PDTConstants} from '../../Utils/HolidayReviewConstants';
import {rupeeFormatter} from '@mmt/legacy-commons/Helpers/currencyUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { logHolidayReviewPDTClickEvents } from '../../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const EMP_TYPE_NO_COST = 'NO_COST';

const EmiTag = (props) => {
    const {emiDetails, openEmiModal, trackReviewLocalClickEvent} = props || {};
    if (isEmpty(emiDetails)) {
        return null;
    }

    const { emiType, emiAmount } = emiDetails || {};
    const emiText = emiType === EMP_TYPE_NO_COST ? NO_COST : null;
    const formattedEmiAmount = rupeeFormatter(emiAmount);

    const captureClickEvents = (event) => {
      logHolidayReviewPDTClickEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: event,
      });
      trackReviewLocalClickEvent(event, '');
    };
    const handleClick = () => {
        openEmiModal();
        captureClickEvents(PDTConstants.EMI_OPTIONS);
    };

    return (
        <View style={styles.emiTag}>
            <Image source={iconEmiTag} style={styles.iconEmiTag}/>
            <View style={styles.emiContainer}>
                <Text style={styles.emi}>{emiText} EMI @ ₹{formattedEmiAmount}</Text>
                <Text style={styles.emiOptions}>
                    <Text style={styles.emiOptionsText}>Book your holidays with Easy</Text>
                    <Text style={styles.emiOptionsLink} onPress={handleClick}>
                        {' '}EMI options
                    </Text>
                </Text>
            </View>
        </View>
    );
};

const styles = {
    emiTag: {
        backgroundColor: holidayColors.fadedYellow,
        flexDirection: 'row',
        alignItems: 'center',
        ...paddingStyles.pv10,
        ...paddingStyles.ph16,
    },
    iconEmiTag: {
        width: 64,
        height: 22,
        resizeMode: 'cover',
        ...marginStyles.mr20,
    },
    emiContainer:{
        flexDirection: 'column',
        flex: 1,
    },
    emi: {
        ...fontStyles.labelMediumBlack,
        color: holidayColors.black,
    },
    emiOptions: {
        ...paddingStyles.pt4,
        flexDirection: 'row',
        alignItems: 'flex-start',
        flexWrap: 'wrap',
    },
    emiOptionsText: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.black,
    },
    emiOptionsLink: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.primaryBlue,
    },
};

export default memo(EmiTag);
