import React from 'react';
import {
    StyleSheet,
    ScrollView,
    View,
    TouchableOpacity,
    Image,
    Text,
} from 'react-native';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconBlackTick from '../../images/ic_blackTick.png';
import { COUPON_APPLY, COUPON_REMOVE, PDTConstants } from '../../Utils/HolidayReviewConstants';
import {isEmpty} from 'lodash';

const CouponCard = (props) => {
    const { couponCode, couponDesc, offerPrice, selected, validateCoupon,tncUrl,removerError, trackReviewLocalClickEvent, fromPresalesDetail } = props || {};
    const action = selected ? COUPON_REMOVE : COUPON_APPLY;
    const couponToBeRemoved  = true;

    const selectCoupon = () => {
        removerError();
        const PDT_ACTION = action === COUPON_APPLY ? PDTConstants.APPLY_COUPON : PDTConstants.REMOVE_COUPON;
        trackReviewLocalClickEvent(PDT_ACTION,`_${couponCode}`);
        validateCoupon(couponCode, selected, action, couponToBeRemoved, fromPresalesDetail);
    };

    return (
        <View style={[styles.couponCard, selected ? styles.selectedCard : null]}>
            <View style={styles.selectIconWrap}>
                {selected && <Image source={iconBlackTick} style={styles.iconBlackTick} /> }
            </View>
            <View style={styles.cardDesc}>
                <View><Text style={styles.couponCode}>{couponCode}</Text></View>
                {couponDesc ? <View ><Text style={styles.couponDesc}>{couponDesc}</Text></View> : []}
                <View style={[AtomicCss.marginTop5,{display:'flex',flexDirection:'row'}]}>
                    <Text style={[styles.offerPrice, {height: 16}]}>{offerPrice}</Text>
                    {!isEmpty(tncUrl)  && <TouchableOpacity onPress={() => this.openUrl(coupon.tncUrl)} style={AtomicCss.flex2}>
                        <Text style={styles.viewInfo}>Terms & Conditions</Text>
                    </TouchableOpacity>
                    }
                </View>
            </View>
            <TouchableOpacity onPress={selectCoupon} style={styles.linkText}>
                <View ><Text style={styles.apply}>{action}</Text></View>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    couponCard: {
        backgroundColor: '#F2F2F2',
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#d8d8d8',
        padding: 15,
        flexDirection: 'row',
        marginVertical: 5,
       flex:1,
    },
    selectedCard: {
        backgroundColor: '#DDFFFC',
        borderColor: '#B5F1EC',

    },
    selectIconWrap: {
        width: '7%',
    },
    selectIcon: {
        width: 15,
        height: 15,
        resizeMode: 'cover',
    },
    linkText: {

        width: '20%',
    },
    cardDesc: {
        flexWrap: 'wrap',
        width: '73%',
    },
    iconBlackTick: {
        backgroundColor: 'black',
        width: 14,
        height: 14,
        resizeMode: 'cover',
        borderRadius: 7,
    },
    couponCode:{
        ...AtomicCss.font12,
        ...AtomicCss.boldFont,
        ...AtomicCss.blackText,
    },
    couponDesc:{
        ...AtomicCss.font12,
        ...AtomicCss.regularFont,
        ...AtomicCss.defaultText,
    },
    offerPrice:{
        ...AtomicCss.font14,
        ...AtomicCss.blackFont,
        ...AtomicCss.blackText,
        ...AtomicCss.flex1,
    },
    apply:{
        ...AtomicCss.font11,
        ...AtomicCss.blackFont,
        ...AtomicCss.azure,
        ...AtomicCss.pushRight,
    },
});

export default CouponCard;
