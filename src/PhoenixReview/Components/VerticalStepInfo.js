import React from 'react';
import {
    StyleSheet,
    View,
    Image,
    Text,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { addDays } from '@mmt/legacy-commons/Helpers/dateHelpers';
import iconTickGreen from '../images/ic_tickGreen.png';
import iconTickYellow from '../images/ic_tickYellow.png';
import iconCloseRed from '../images/ic_closeRed.png';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import iconZc from '../images/ic_zcSmall.png';
import iconFlexiDate from '../images/ic_flexiDate.png';
import { CANCELLATION_POLICY, FLEXI_DESCRIPTION, ZC_DESCRIPTION, ZC_FULL_DESCRIPTION, ZC_KEY } from '../Utils/HolidayReviewConstants';
import { DATE_FORMAT } from '../../Review/HolidayReviewConstants';
import fecha from 'fecha';
import {isEmpty} from 'lodash';
import { getNewDate } from '@mmt/legacy-commons/Common/utils/DateUtils';
import { CANCELLATION_MESSAGES } from '../../HolidayConstants';
const { ONLY_NON_REFUNDABLE_TEXT, ONLY_NO_DATE_CHANGE_TEXT, NON_REFUNDABLE_TEXT, NO_DATE_CHANGE_TEXT } = CANCELLATION_MESSAGES;

const priceViewGenerator = ( penalties, mode, zcSelected, icons, stepperCount, onlyNonRefundablePackage ) => {
    let viewList = [];
    if (penalties) {
        let dateToShow = '';
        let yearToShow = '';
        penalties.forEach((e, i) => {
            const { fromDate, nonRefundable } = e;
            const dateObj = fecha.parse((fromDate), DATE_FORMAT);
            let newDate = addDays(dateObj, 0);

            let completeDate = '';
            const dateArray = fecha.format(newDate, 'D MMM YYYY dddd').split(' ');
            const onlyDate = dateArray[0];
            const onlyMonth = dateArray[1];
            const onlyYear = dateArray[2];
            completeDate = onlyDate + ' ' + onlyMonth;

            if (penalties.length == 1 && nonRefundable) {
                completeDate = mode === CANCELLATION_POLICY ? 'Non Refundable' : 'Date Change not allowed';
            }

            if (isEmpty(dateToShow)) {
                dateToShow = completeDate;
                yearToShow = onlyYear;
            } else if (!nonRefundable) {
                dateToShow = completeDate;
                yearToShow = onlyYear;
            }

            const nonRefundableText =
              mode === CANCELLATION_POLICY
                ? onlyNonRefundablePackage
                  ? ONLY_NON_REFUNDABLE_TEXT
                  : NON_REFUNDABLE_TEXT
                : onlyNonRefundablePackage
                ? ONLY_NON_REFUNDABLE_TEXT
                : NO_DATE_CHANGE_TEXT;

            let node = <View style={[AtomicCss.marginBottom15, AtomicCss.flexRow]}>
                <View style={AtomicCss.marginRight5}><Image source={icons[i].icon} style={styles.iconList} /></View>
                <View style={styles.columnWidth}>
                    <View>
                        {penalties.length == 1 && nonRefundable ?
                        <Text style={[AtomicCss.font12, AtomicCss.regularFont, {color: icons[i].color, marginTop: 2}]}>
                            {completeDate}
                        </Text> :
                        <Text style={[AtomicCss.font12, AtomicCss.regularFont, {color: icons[i].color,flex:1}]}>
                            {e.nonRefundable ? 'After' : 'Till'}
                        <Text style={AtomicCss.blackFont}> {dateToShow}</Text> {onlyYear}
                        </Text>
                        }
                    </View>
                    <View style={AtomicCss.marginTop5}>
                        {
                            nonRefundable ?
                            <View style={styles.textWrapper}>
                                <Text
                                numberOfLines={3}
                                style={[AtomicCss.font11, AtomicCss.blackText, AtomicCss.boldFont]}
                                >
                                    {nonRefundableText}
                                </Text>
                            </View>
                                :
                                <View style={styles.textWrapper}>
                                     <Text style={[AtomicCss.font11, AtomicCss.blackText, AtomicCss.regularFont,(stepperCount == 2 ? styles.width90 : {}),{flexWrap:'wrap'}]} numberOfLines={(mode === CANCELLATION_POLICY) ? 2 : 4}>
                                     <Text style={AtomicCss.blackFont} numberOfLines={2}>
                                        ₹ {rupeeFormatter(zcSelected ? e?.withZCPenalty : e?.penalty)} </Text>
                                        { (mode === CANCELLATION_POLICY) ? ' Cancellation fee' : ' \nDate Change Fee. Fare Difference will be extra.'}
                                    </Text>
                                </View>
                        }
                    </View>
                </View>
            </View>;
            viewList.push(node);
        });
    }
    return viewList;
};

const VerticalStepInfo = (props) => {
    const {penalties, zcSelected, mode ,zcOption, stepperCount } = props;
    let icons = [];
    penalties.forEach((penalty, i) => {
        if (penalty.nonRefundable) {
            icons.push({ color: '#EB2026', gradientColor: '#F9C6A9', icon: iconCloseRed });
        }
        else if (i === 0) {
            icons.push({ color: '#589231', gradientColor: '#C8E5B5', icon: iconTickGreen });
        }
        else {
            icons.push({ color: '#CF8100', gradientColor: '#FFE6AC', icon: iconTickYellow });
        }
    });
    if (penalties?.length === 1){
        if (penalties[0]?.nonRefundable) {
            icons.push({ color: '#EB2026', gradientColor: '#F9C6A9'});
        }
        else {
            icons.push({ color: '#589231', gradientColor: '#C8E5B5'});
        }
    }
    const onlyNonRefundablePackage =
    Boolean(penalties.length === 1 && penalties?.[0]?.nonRefundable) || false;
    let priceView = priceViewGenerator(penalties, mode, zcSelected, icons, stepperCount, onlyNonRefundablePackage);
    return (
            <View style={{flex:1}}>
            <View style={[AtomicCss.flexRow, AtomicCss.marginTop10]}>
                {onlyNonRefundablePackage ? (
                    <View style={styles.emptySpace} />
                    ) : (
                    <LinearGradient
                        colors={icons.map((e) => e.gradientColor)}
                        style={styles.linearRods}
                     />
                    )}
                <View style={[styles.columnDivider, styles.columns]}>
                    {priceView}
                </View>
            </View>
            {
                !(stepperCount === 1) ? (zcSelected ?
                    <View style={[AtomicCss.paddingLeft10, AtomicCss.paddingTop10]}>
                        <Text style={[AtomicCss.font11, AtomicCss.defaultText, AtomicCss.boldFont,styles.width90]}>Add {`${zcOption.type === ZC_KEY ? ZC_DESCRIPTION : FLEXI_DESCRIPTION}`} for <Text style={[AtomicCss.blackFont, AtomicCss.blackText]}>₹ {rupeeFormatter(zcOption?.amount)}</Text>
                                            </Text>
                        {mode === CANCELLATION_POLICY ?
                            <View><Image source={iconZc} style={styles.iconZc} /></View> :
                            <View><Image source={iconFlexiDate} style={styles.iconFlexi} /></View>
                        }
                    </View> :
                    <View style={[AtomicCss.paddingLeft10, AtomicCss.paddingTop10]}>
                        <Text style={[AtomicCss.font11, AtomicCss.defaultText, AtomicCss.boldFont]}>{zcOption.type === ZC_KEY ? ZC_FULL_DESCRIPTION : FLEXI_DESCRIPTION}</Text>
                        <Text style={[AtomicCss.font11, AtomicCss.blackText, AtomicCss.blackFont, {paddingBottom:8}]}>Not Available</Text>
                    </View>) : []
            }
            </View>
    );
};

const styles = StyleSheet.create({
    columnWidth:{
        width:'90%',
    },
    columnHeader: {
        backgroundColor: '#EAF5FF',
        paddingVertical: 5,
        paddingHorizontal: 20,
        alignSelf: 'flex-start',
    },
    width90:{
        width:'100%',
    },
    columns: {
        paddingTop: 15,
        marginLeft: -12,
    },
    columnDivider: {
        borderColor: '#e7e7e7',
        borderStyle: 'dashed',
        borderRadius: 1,

    },
    iconList: {
        width: 20,
        height: 20,
    },
    linearRods: {
        width: 5,
        height: '100%',
        borderRadius: 5,
        marginLeft: 5,
    },
    emptySpace : {
        marginLeft: 15,
    },
    iconZc: {
        width: 104,
        height: 18,
        marginTop: 3,
    },
    iconFlexi:{
        width: 58,
        height: 19,
        marginTop: 3,
        resizeMode: 'cover',
    },
    iconClose: {
        width: 24,
        height: 24,
    },
    textWrapper:{width:'80%',
        ...AtomicCss.marginTop5,
    },
});

export default VerticalStepInfo;
