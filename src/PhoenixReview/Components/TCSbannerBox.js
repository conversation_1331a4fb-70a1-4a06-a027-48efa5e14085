import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
const ViaMmt = require('@mmt/legacy-assets/src/holidays/tcs/viammt.webp');
const ClaimCredit = require('@mmt/legacy-assets/src/holidays/tcs/ClaimCredit.webp');
const CReciveCredit = require('@mmt/legacy-assets/src/holidays/tcs/cReciveCredit.webp');
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { borderRadiusValues, holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { sectionHeaderSpacing } from '../../Styles/holidaySpacing';

const TCSbannerBox = (props) => {
  const { showLess ,fromStrip = false, description : headerDescription} = props || {};
  const { title, infos } = props?.footer || {};
  const renderItem = (item, imageStyle) => {
    const { title: subTitle, description, iconUrl } = item;
    return (
      <View style={[AtomicCss.flexRow, paddingStyles.pb20]}>
        <View style={styles.tcsIcon}>
          <Image style={imageStyle} source={{ uri: iconUrl }} resizeMode={'contain'} />
        </View>
        <View style={styles.textTcs}>
          <Text style={[fromStrip ? styles.sectionSubHeading : styles.heading]}>{subTitle}</Text>
          <Text style={[fromStrip ? styles.sectionSubDesc : styles.subdesc]}>{description}</Text>
        </View>
      </View>
    );
  };
  return (
    <>
    <View>
        {!!headerDescription && (
          <Text style={[styles.bulletList, !fromStrip && marginStyles.ml10]}>
            {headerDescription}
          </Text>
        )}
    </View>
    <View style={styles.tcsAmountWrapper}>
      <View
        style={[
          styles.tcsInner,
          fromStrip && infos?.length ? styles.tcsCondition : styles.tcsOld,
        ]}>
         {!!title && <Text
            style={[
              fromStrip ? styles.sectionHeading :  styles.mainHeading,
              infos?.length ? sectionHeaderSpacing : [],
            ]}
          >
            {title}
          </Text>}
          {infos?.map((info) => {
            return renderItem(info, styles.viaMmtIcon);
          })}
          {fromStrip && (
            <TouchableOpacity onPress={showLess}>
              <Text style={styles.showless}>Show Less</Text>
            </TouchableOpacity>
          )}
      </View>
    </View>
    </>
  );
};

const styles = StyleSheet.create({
  tcsAmountWrapper:{
    ...holidayBorderRadius.borderRadius16,
    backgroundColor: holidayColors.fadedRed,
  },
  mainHeading: {
    color: holidayColors.black,
    ...fontStyles.labelLargeBlack,
    justifyContent: 'center',
    ...marginStyles.ml10,
  },
  sectionHeading: {
    color: holidayColors.black,
    ...fontStyles.labelMediumBlack,
    justifyContent: 'center',
  },
  tcsOld :{
  },
  tcsInner: {
    ...paddingStyles.pb0,
    justifyContent: 'center',
    ...holidayBorderRadius.borderRadius16,
    ...marginStyles.ml0,
    ...paddingStyles.pt20,
  },
  tcsCondition:{
    borderTopWidth: 1,
    borderTopColor:holidayColors.grayBorder,
    backgroundColor: holidayColors.white,
    ...paddingStyles.ph16,
  },
  heading: {
    color: holidayColors.gray,
    ...fontStyles.labelMediumBold,
    ...paddingStyles.pb4,
  },
  sectionSubHeading: {
    color: holidayColors.gray,
    ...fontStyles.labelBaseBold,
    ...paddingStyles.pb4,
  },
  subdesc:{
    color: holidayColors.gray,
    ...fontStyles.labelBaseRegular,
  },
  sectionSubDesc: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
  },
  textTcs: {
    width: 0,
    flexGrow: 1,
  },
  tcsIcon: {
    width: 40,
    marginRight: 10,
    alignItems: 'center',
    ...paddingStyles.pt6,
  },
  viaMmtIcon: {
    width: 30,
    height: 30,
  },
  ClaimCreditIcon: {
    width: 24,
    height: 27,
  },
  CReciveCreditIcon: {
    width: 32,
    height: 27,
  },
  showless: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBlack,
    ...marginStyles.mb12,
  },
  bulletList : {
    color: holidayColors.gray,
    textAlign: 'left',
     ...fontStyles.labelSmallRegular,
     ...paddingStyles.pb12,
    },
});

export default TCSbannerBox;
