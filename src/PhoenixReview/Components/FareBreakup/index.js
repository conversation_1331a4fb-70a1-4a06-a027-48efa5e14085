import React, { useState ,useEffect} from 'react';
import {
    StyleSheet,
    ScrollView,
    View,
    TouchableOpacity,
    Image,
    Text, Platform,
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import CrossIcon from '@mmt/legacy-assets/src/ic_cross__gray.webp';
import iconArrowDown from '@mmt/legacy-assets/src/ic-arrowdown-blue.webp';
import ArrowIcon from '@mmt/legacy-assets/src/Arrow.webp';
import {
    COUPON_KEY,
    FLEXI_KEY,
    ZC_KEY,
    FLEXI_ACTION_KEY,
    EMI_OPTIONS_AVAILABLE,
    EMI_TYPE,
    SECTIONS,
} from '../../Utils/HolidayReviewConstants';
import HolidayModule from '@mmt/legacy-commons/Native/HolidayModule';
import WebViewWrapper from '@mmt/legacy-commons/Common/Components/WebViewWrapperNew/WebViewWrapperNew';
import BottomSheet from '../BottomSheet';
import { isIosClient } from '../../../utils/HolidayUtils';
// import iconCloseWhite from '@mmt/legacy-assets/src/hubble/ic_close_white.webp';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { BottomSheetHeading, BottomSheetCross } from '../../../Common/Components/BottomSheetOverlay';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { sectionHeaderSpacing } from '../../../Styles/holidaySpacing';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { logHolidayReviewPDTClickEvents } from '../../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';
import FareBreakUpBottomCard from '../../../Common/Components/Membership/FareBreakUpBottomCard/FareBreakUpBottomCard';
import { noop } from 'lodash';
import {PDTConstants} from '../../../Review/HolidayReviewConstants'
import HubbleSharedModuleHolder from '@mmt/hubble-shared';
// Note: The icon for CLOSE_GREY_ICON is @mmt/legacy-assets/src/hubble/ic_close_grey.webp
// That bundled icon has been removed by Hubble/W2G team
let CLOSE_GREY_ICON = null;
try {
  // @ts-expect-error
  CLOSE_GREY_ICON =
    HubbleSharedModuleHolder.get()?.getHubbleSharedComponents()?.getCloseIconGreyIcon?.() ?? null;
} catch (error) {
  // @ts-expect-error
  CLOSE_GREY_ICON = null;
  console.warn('Error while getting RECENT_SEARCH_ICON', error);
}

const ACTION_TYPE_REMOVE = 'REMOVE';
const FARE_BREAKUP_HEADER_COLOR = holidayColors.black;
const openUrl = (url) => {
    HolidayModule.openWebView({ url });
};



const ReviewFareBreakup = ({ closeModal, fareBreakUp, listData, gotoSection, validateZC, emiDetail = {}, holidayReviewData = {}, isCouponEditable = true ,fareBreakUpCardData={},trackReviewLocalClickEvent=noop }) => {

    const [tooltipState, setTooltip] = useState({});
    const [showPopup, setShowPopup] = useState(false);
    const [urlLink, setUrlLink] = useState('');
    const emiType = emiDetail?.emiType || '';
    const isFromQuote = Boolean(holidayReviewData?.quoteRequestId) || false;

    const openUrl = (url) => {
        if (isIosClient()){
            setUrlLink(url);
            setShowPopup(true);
        }
        else {
            HolidayModule.openWebView({ url });
        }
    };
    const handlePopupClose = ()=> {
        setShowPopup(false);
    };

    const toggleTooltip = (key) => {
        if (tooltipState[key]) {
            setTooltip({ [key]: !tooltipState[key] });
        }
        else {
            setTooltip({ [key]: true });
        }
    };

    const removeZC = (isZC) => {
        const type = isZC ? ZC_KEY : FLEXI_KEY;
        validateZC(ACTION_TYPE_REMOVE, type);
    };

    const gotoCoupon = () => {
      //  let couponIndex=listData.findIndex((el)=>el.id==SECTIONS.COUPONS) || 0;
        logHolidayReviewPDTClickEvents({
          actionType: PDT_EVENT_TYPES.buttonClicked,
          value: 'edit_coupon',
        });
        gotoSection(null,SECTIONS.COUPONS);
        closeModal();
    };

    const generateFareSign = (fareSign) => {
        let result = '';
        switch (fareSign) {
            case 'ADD':
                result = '+';
                break;
            case 'SUBTRACT':
                result = '-';
                break;
            default:
                result = '';
                break;
        }
        return result;
    };
    useEffect(() => {
        logHolidayReviewPDTClickEvents({
            actionType: PDT_EVENT_TYPES.contentSeen,
            value: PDTConstants.GC_Popup_SEEN_FARE,
            shouldTrackToAdobe:false
        })
        trackReviewLocalClickEvent(PDTConstants.GC_Popup_SEEN_FARE)
    },[])

    const generateActionIcon = (action) => {
        let result = [];
        switch (action) {
            case ZC_KEY:
                result = <TouchableOpacity onPress={()=>removeZC(true)}>
                    <View style={styles.crossIconContainer}>
                        <Image source={CrossIcon} style={styles.iconCross} />
                    </View>
                </TouchableOpacity>;
                break;
            case FLEXI_ACTION_KEY:
            case FLEXI_KEY:
                result = <TouchableOpacity onPress={()=>removeZC(false)}>
                    <View>
                        <Image source={CrossIcon} style={styles.iconCross} />
                    </View>
                    </TouchableOpacity>;
                    break;
            case COUPON_KEY:
                result = isCouponEditable ? <TouchableOpacity style={paddingStyles.pl16} onPress={gotoCoupon}>
                    <Text style={styles.editText}>Edit</Text>
                </TouchableOpacity> : null;
                break;
            default:
                result = [];
                break;
        }
        return result;
    };

    const generateTooltip = (tooltipObj) => {
        if (!tooltipObj) {
          return [];
        }
        const { header, description, tncUrl } = tooltipObj || {};
        return (
          <View style={styles.tcsBlk}>
            {header && <Text style={styles.tcsHeader}>{header}</Text>}
            {description && <Text style={styles.tcsDesc}>{description}</Text>}
            {tncUrl && (
              <View style={[AtomicCss.paddingTop5, AtomicCss.flexRow]}>
                <TouchableOpacity onPress={() => openUrl(tncUrl)}>
                  <Text style={styles.tcsLink}>Terms &amp; Conditions</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        );
    };

  return (
        <View style={styles.contentWrap}>
            <View style={styles.popupHeader}>
                <BottomSheetHeading title={'Fare Breakup'}/>
                <BottomSheetCross toggleModal={closeModal}/>
            </View>
            <ScrollView showsVerticalScrollIndicator={false}>
                <TouchableOpacity activeOpacity={1}>
                    <View style={styles.fareItemList}>
                        {
                            fareBreakUp?.map((fareItem, i) =>
                              <View style={fareBreakUp.length - 1 === i ? styles.blkRowLast : styles.blkRow} key={fareItem?.title + i}>
                                    <View style={[styles.fareRow]}>
                                        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                                        <Text
                                            style={[
                                            styles.fareHeader,
                                            { color: fareItem?.colorCode || FARE_BREAKUP_HEADER_COLOR },
                                            ]}
                                        >
                                            {fareItem?.title}
                                        </Text>
                                        {fareItem?.toolTip ?
                                            <TouchableOpacity onPress={() => toggleTooltip(i)}>
                                                <Image
                                                    source={iconArrowDown}
                                                    style={tooltipState && tooltipState[i] ? styles.iconArrowUp : styles.iconArrowDown}
                                                />
                                            </TouchableOpacity>
                                            : []}
                                        </View>
                                    <View style={[{flexDirection: 'row', alignItems: 'center'}]}>
                                    <Text
                                        style={[
                                        styles.fairItemValue,
                                        { color: fareItem?.colorCode || holidayColors.black },
                                        ]}
                                    >
                                        {generateFareSign(fareItem?.sign)} ₹{rupeeFormatter(fareItem?.value)}{' '}
                                    </Text>
                                    {fareItem?.change ?
                                        <Image
                                        source={ArrowIcon}
                                        style={fareItem?.change === -1 ? styles.priceUpArrow : styles.priceDownArrow}
                                        />
                                    : null}
                                    </View>
                                    </View>
                                    {tooltipState && tooltipState[i] ? generateTooltip(fareItem?.toolTip) : []}
                                    {
                                        fareItem?.sections?.map((section, j) => {
                                            let key = `${i} ${j}`;
                                            let tooltip = tooltipState && tooltipState[key];
                                            return (
                                                <View>
                                                  <View
                                                    style={[
                                                      styles.fareRow,
                                                      j == 0 ? AtomicCss.paddingTop10 : AtomicCss.paddingTop5,
                                                    ]}
                                                  >
                                                    <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                                                      <View style={section.action === COUPON_KEY ? styles.couponStyle : {}}>
                                                        <Text
                                                          style={[
                                                            styles.sectionTitle,
                                                            section.action === COUPON_KEY ? styles.couponTextStyle : {},
                                                          ]}
                                                        >
                                                          {section.action === COUPON_KEY &&
                                                            isFromQuote
                                                            ? 'SPECIAL DISCOUNT applied'
                                                            : section?.title}
                                                        </Text>
                                                      </View>
                                                      {section?.action
                                                      ? section.action === COUPON_KEY && isFromQuote
                                                        ? []
                                                        : generateActionIcon(section?.action)
                                                      : []}
                                                      {section?.toolTip ? (
                                                        <TouchableOpacity onPress={() => toggleTooltip(key)}>
                                                          <Image
                                                            source={iconArrowDown}
                                                            style={tooltip ? styles.iconArrowUp : styles.iconArrowDown}
                                                          />
                                                        </TouchableOpacity>
                                                      ) : (
                                                        []
                                                      )}
                                                    </View>
                                                    <Text style={styles.fareItemSecitonValue}>
                                                      {generateFareSign(section?.sign)}
                                                      {section?.value ? `₹${rupeeFormatter(section?.value)}` : ''}
                                                    </Text>
                                                  </View>
                                                  {tooltip ? generateTooltip(section?.toolTip) : []}
                                                </View>
                                              );
                                        })
                                    }
                                </View>

                            )
                        }
                    </View>
                </TouchableOpacity>
                {emiType === EMI_TYPE.NO_COST ? <View>
                    <View
                    style={styles.noEmiContainer}
                    >
                        <Text style={styles.noEmiText}>
                            {EMI_OPTIONS_AVAILABLE}
                        </Text>
                    </View>
                </View> : null}
                {fareBreakUpCardData?.fareBreakUp?.description && <FareBreakUpBottomCard fareBreakUpCardData={fareBreakUpCardData}/>}
            </ScrollView>
            <BottomSheet
              modalVisible={showPopup}
              closeModal={() => handlePopupClose()}>
                <View style={styles.contentWrapWeb}>
                    <WebViewWrapper
                      url={urlLink}
                      headerText={'Terms & Conditions'}
                      containerStyle={[{paddingBottom: 30}, styles.webViewModalStyle]}
                      closeWebView={handlePopupClose}
                      onBackPressed={handlePopupClose}
                      headerIcon={CLOSE_GREY_ICON}
                    />
                </View>
            </BottomSheet>
        </View>
    );
};

const styles = StyleSheet.create({
    contentWrap: {
        ...paddingStyles.pa16,
    },
    popupHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        ...sectionHeaderSpacing,
    },
    blkRow: {
        borderBottomWidth: 1,
        borderColor: holidayColors.grayBorder,
        ...paddingStyles.pv16,
    },
    blkRowLast: {
        ...paddingStyles.pv16,
    },
    fareRow: {
        alignItems: 'center',
        justifyContent: 'space-between',
        flexDirection: 'row',
    },
    iconClose: {
        width: 24,
        height: 24,
    },
    fareHeader: {
        ...fontStyles.labelMediumBold,
        color: FARE_BREAKUP_HEADER_COLOR,
    },
    fairItemValue: {
        ...fontStyles.labelMediumBold,
        color: holidayColors.black,
    },
    tcsBlk: {
        backgroundColor: holidayColors.lightGray2,
        ...paddingStyles.pa20,
        ...marginStyles.mt16,
        ...holidayBorderRadius.borderRadius16,
    },
    tcsHeader: {
        ...fontStyles.labelBaseBlack,
        color: holidayColors.black,
    },
    tcsDesc: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.gray,
    },
    tcsLink: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.primaryBlue,
    },
    contentWrapWeb: {
        paddingTop: Platform.select({
            ios: statusBarHeightForIphone,
            android: 0,
        }),
        height: '100%',
    },
    priceDownArrow: {
        width: 12,
        height: 12,
        tintColor: holidayColors.green,
        transform: [{rotate: '90deg'}],

    },
    priceUpArrow: {
        width:12,
        height: 12,
        tintColor: holidayColors.red,
        transform: [{rotate: '270deg'}],
    },
    iconArrowDown: {
        width: 16,
        height: 16,
        resizeMode: 'contain',
        marginLeft: 10,
    },
    webViewModalStyle: {
        position: 'relative',
        top: null,
        left: null,
        height: null,
    },
    iconArrowUp: {
        width: 16,
        height: 16,
        resizeMode: 'contain',
        marginLeft: 10,
        transform: [{ rotate: '180deg' }],
    },
    crossIconContainer: {
        backgroundColor: holidayColors.grayBorder,
        borderRadius: 30,
        padding: 6,
        marginLeft: 'auto',
        marginTop: 3,
        flex: 0,
    },
    iconCross: {
        width: 10,
        height: 10,
    },
    editText: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.primaryBlue,
    },
    sectionTitle: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.gray,
    },
    fareItemSecitonValue: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.gray,
    },
    couponStyle: {
        backgroundColor: holidayColors.fadedGreen,
        paddingVertical: 4,
        paddingHorizontal: 6,
        borderRadius: 2,
    },
    couponTextStyle: {
        color: holidayColors.green,
        fontWeight: '700',
    },
    noEmiContainer: {
        ...paddingStyles.pv10,
        ...marginStyles.mb16,
        alignItems: 'center',
        backgroundColor: holidayColors.fadedYellow,
        ...holidayBorderRadius.borderRadius8,
    },
    noEmiText: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.gray,
    },
});

export default ReviewFareBreakup;
