import React, { useEffect, useState, useCallback } from 'react';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../Common/Components/HolidayImageUrls';
import SnackBar from '@Frontend_Ui_Lib_App/SnackBar';
import { holidayColors } from '../../Styles/holidayColors';
import { StyleSheet } from 'react-native';
import {
  ADDING_GIFT_CARDS,
  GIFT_CARDS_ADDED,
  GIFT_CARDS_ERROR,
  MMT_GIFT_CARD_SNACK_BAR_DURATION,
  TRY_AGAIN,
} from '../Utils/HolidayReviewConstants';
import { fontStyles } from '../../Styles/holidayFonts';
import { PDTConstants } from '../../Review/HolidayReviewConstants';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { logHolidayReviewPDTClickEvents } from '../Utils/HolidayReviewPDTTrackingUtils';
import { noop } from 'lodash';
import { GIFT_CARD_STATUS } from '../Actions/PhoenixReviewAction';

// Main component for displaying toast notifications in the Phoenix Review Page
const PhoenixReviewPageToast = (props) => {
  // Destructure props with default values
  const { attachGiftCardStatus, addGiftCard = null, trackReviewLocalClickEvent = noop ,attachGiftCardData=null } = props;
  // State variables for toast content
  const [content, setContent] = useState('');
  const [startIcon, setStartIcon] = useState('');
  const [actionText, setActionText] = useState('');
  const [retryCount, setRetryCount] = useState(0);
  const MAX_RETRY_COUNT = 2;

  // Callback function to handle error state
  const handleErrorState = useCallback(() => {
    console.log('>>>>MAX_RETY _count', MAX_RETRY_COUNT);
    setContent('attachGiftCardData?.error?.message');
    setStartIcon(getImageUrl(IMAGE_ICON_KEYS.GIFT_CARD_ERROR));
    if (retryCount < MAX_RETRY_COUNT) {
      setActionText(TRY_AGAIN);
    } else {
      setActionText('');
    }
  }, []);

  // Function to track the "Try Again" action
  const trackTryAgainAction = useCallback(() => {
    trackReviewLocalClickEvent(PDTConstants.GC_ADD_GC_FAILURE_TRY_AGAIN);
    logHolidayReviewPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: PDTConstants.GC_ADD_GC_FAILURE_TRY_AGAIN,
    });
  }, [trackReviewLocalClickEvent]);

  // Callback function for action text press
  const onActionTextPress = useCallback(() => {
    if (addGiftCard && retryCount < MAX_RETRY_COUNT) {
      setRetryCount((prevCount) => prevCount + 1);
      addGiftCard();
      trackTryAgainAction();
    }
  }, [addGiftCard, trackTryAgainAction, retryCount]);

  // Effect to update toast content based on gift card status
  useEffect(() => {
    switch (attachGiftCardStatus) {
      case GIFT_CARD_STATUS.LOADING:
        setContent(ADDING_GIFT_CARDS);
        setStartIcon(getImageUrl(IMAGE_ICON_KEYS.GIFT_CARD_LOADING));
        setActionText('');
        break;
      case GIFT_CARD_STATUS.SUCCESS:
        setContent(attachGiftCardData?.statusMessage);
        setStartIcon(getImageUrl(IMAGE_ICON_KEYS.GIFT_CARD_SUCCESS));
        setActionText('');
        setRetryCount(0);
        trackReviewLocalClickEvent(PDTConstants.GC_ADD_SUCCESS);
        logHolidayReviewPDTClickEvents({
          actionType: PDT_EVENT_TYPES.contentSeen,
          value: PDTConstants.GC_ADD_SUCCESS,
          shouldTrackToAdobe:false
        });
        break;
      case GIFT_CARD_STATUS.ERROR:
        handleErrorState();
        trackReviewLocalClickEvent(PDTConstants.GC_ADD_FAILURE);
        logHolidayReviewPDTClickEvents({
          actionType: PDT_EVENT_TYPES.contentSeen,
          value: PDTConstants.GC_ADD_FAILURE,
          shouldTrackToAdobe:false
        });
        break;
      case GIFT_CARD_STATUS.NONE:
        setContent('');
        setStartIcon('');
        setActionText('');
        setRetryCount(0);
        break;
    }
  }, [attachGiftCardStatus, handleErrorState, trackReviewLocalClickEvent]);

  // Render null if there's no content
  if (!content) {
    return null;
  }

  // Render the SnackBar component with appropriate props
  return (
    <SnackBar
      duration={MMT_GIFT_CARD_SNACK_BAR_DURATION}
      startIcon={startIcon}
      isVisible={!!content}
      bgColor={[holidayColors.gray]}
      content={content}
      actionText={actionText}
      onActionTextPress={onActionTextPress}
      customStyle={{
        actionTextStyle: styles.actionTextStyle,
      }}
    />
  );
};

// Styles for the component
const styles = StyleSheet.create({
  actionTextStyle: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
  },
});

export default PhoenixReviewPageToast;
