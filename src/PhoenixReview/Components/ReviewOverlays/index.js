import React from 'react';
import { Modal, View } from 'react-native';
import { connect } from 'react-redux';

/* Components */
import InsuranceListingPage from '../../../Travelnsurance/listing/TravelInsuranceListing';
import InsuranceDetailPage from '../../../Travelnsurance/details/TravelInsuranceDetails';
import VppDetailPage from '../../../Common/Components/VisaProtectionPlan/VPPDetailPage'

export const REVIEW_OVERLAYS = {
  INSURANCE_LISTING_PAGE: 'insurance_listing_page',
  INSURANCE_DETAIL_PAGE: 'insurance_detail_page',
  VPP_OVERLAY: 'vpp_overlay',
}


const ComponentMap = {
  [REVIEW_OVERLAYS.INSURANCE_LISTING_PAGE]: InsuranceListingPage,
  [REVIEW_OVERLAYS.INSURANCE_DETAIL_PAGE]: InsuranceDetailPage,
  [REVIEW_OVERLAYS.VPP_OVERLAY]: VppDetailPage,
};

const PhoenixReviewOverlays = (props) => {
  const { overlayDataMap = {}, overlayMap = {}} = props || {};
  const renderComponent = (key, data) => {
    const Component = ComponentMap[key];
    return (
      <Modal visible={overlayMap[key]}>
        <Component {...data} />
      </Modal>
    );
  };

  const renderModal = (key, show) => {
    if (show) {
      const data = overlayDataMap[key];
      return renderComponent(key, data);
    }
    return [];
  };

  const overlaysArray = overlayMap ? Object.entries(overlayMap) : [];
  return <View>{overlaysArray.map(([key, value]) => renderModal(key, value))}</View>;
};

const mapStateToProps = (state) => ({
  ...state.holidaysReviewOverlays,
});

export default connect(mapStateToProps, null)(PhoenixReviewOverlays);
