import { reviewError, updateReviewLoaded, updateReviewLoading } from "../../Review/Actions/HolidayReviewActions";
import { fetchCouponErrorMessage, trackReviewLocalClickEvent } from "../../Review/Utils/HolidayReviewUtils";
import {
  attachGiftCardViaApi,
  fetchEmiOptions,
  fetchPackageContent,
  fetchValidateCoupon,
  selectInsuranceAddOnPost,
  validateAddonRequestPost,
} from '../../utils/HolidayNetworkUtils';
import { showShortToast } from "@mmt/legacy-commons/Common/Components/Toast";
import {
  COULD_NOT_PERFORM_OPERATION,
  COUPON_APPLY,
  COUPON_REMOVE,
  PDTConstants,
  reviewActionTypes,
} from "../../Review/HolidayReviewConstants";
import { MMT_GIFT_CARD_SNACK_BAR_DURATION } from '../Utils/HolidayReviewConstants';

export const GIFT_CARD_STATUS = {
  LOADING: 'LOADING',
  SUCCESS: 'SUCCESS',
  ERROR: 'ERROR',
  NONE: 'NONE',
};

export const validateCoupon = (code, isSelected, action, couponToBeRemoved, fromPresales = false) => async (dispatch, getState) => {
    try {
      if (!couponToBeRemoved) {
        return;
      }
      if (isSelected && action === COUPON_APPLY) {
        return null;
      } else if (!isSelected && action === COUPON_REMOVE) {
        return null;
      }

      dispatch(updateReviewLoading());
      const reviewData = getState().holidaysReview.reviewData;
      const dynamicId = reviewData.reviewDetail.dynamicId;

      if (!dynamicId) {
        return null;
      }

      const couponResponse = await fetchValidateCoupon(dynamicId, action, code, fromPresales);
      if (!couponResponse || couponResponse.error) {
        if (couponResponse && couponResponse.error && couponResponse.error.message) {
          const errorMessage = fetchCouponErrorMessage(couponResponse.error);
          dispatch(showCouponError(errorMessage));
          showShortToast(errorMessage);
        } else {
          showShortToast(COULD_NOT_PERFORM_OPERATION);
        }
        dispatch(updateReviewLoaded());
        return null;
      }
      trackReviewLocalClickEvent(PDTConstants.COUPON_SUCCESS, '', reviewData);
      dispatch(updatePhoenixCouponStatus(couponResponse, code));
    } catch (e) {
      showShortToast('Could not perform operation');
      dispatch(updateReviewLoaded());
      return null;
    }
  };

  export const validateTravellerForm = (formObject) => async (dispatch, getState) => {
    try {

    } catch (e) {

    }
  };

  export const fetchPackageContentReview = (body,isWG)=>async(dispatch)=>{
    try {
        dispatch(ReviewPackageLoading(true));
        const packageContentResponse = await fetchPackageContent(body,isWG);
        dispatch(ReviewPackageContent(packageContentResponse));
    }
    catch (e){
        dispatch(reviewError());
        showShortToast('Could not perform operation');
        return null;
    }
  };

/**
 * Action to attach a gift card.
 *
 * @async
 * @function updateGiftCardStatus
 * @returns {Function} A Redux thunk action that dispatches various states of the attached gift card process.
 */
export const attachGiftCard = () => async (dispatch) => {
  const RESET_TIMEOUT = MMT_GIFT_CARD_SNACK_BAR_DURATION + 50;
  // Dispatch action to indicate loading state
  dispatch(updateGiftCardStatus(GIFT_CARD_STATUS.LOADING));
  try {
    // Call the API to attach the gift card
    const response = await attachGiftCardViaApi();
    if (response?.success) {
      dispatch(updateGiftCardData(response));
      dispatch(updateGiftCardStatus(GIFT_CARD_STATUS.SUCCESS));
    } else {
      dispatch(updateGiftCardData(response));
      dispatch(updateGiftCardStatus(GIFT_CARD_STATUS.ERROR));
    }
  } catch (error) {
    dispatch(updateGiftCardStatus(GIFT_CARD_STATUS.ERROR));
  } 
};
  export const clearGiftCardData = () => async (dispatch) => {
      dispatch(removeGiftCardData(null));
  };

  export const selectInsurance = (selectInsuranceRequestBody) => async(dispatch) =>{
    try {
      dispatch(updateReviewLoading());
      const packageContentResponse = await selectInsuranceAddOnPost(selectInsuranceRequestBody);
      dispatch(updateReviewLoaded());
      dispatch(updateSelectInsurance(packageContentResponse))
    }
    catch (e){
      showShortToast('Could not perform operation');
      dispatch(updateReviewLoaded());
      return null;
    }
  }

export const validateAddOns = (validateAddonRequest) => async(dispatch) =>{
  try {
    dispatch(updateReviewLoading());
    const packageContentResponse = await validateAddonRequestPost(validateAddonRequest);
    dispatch(updateReviewLoaded());
    dispatch(updateValidateAddOn(packageContentResponse))
  }
  catch (e){
    showShortToast('Could not perform operation');
    dispatch(updateReviewLoaded());
    return null;
  }
}

  export const getEmiOptions = (dynamicId) =>async (dispatch) => {
    try {
      const emiOptions = await fetchEmiOptions(dynamicId,'REVIEW');
      dispatch(EmiOptions(emiOptions));
    }
    catch (e){
      showShortToast('Could not perform operation');
    }
  };
  export const ReviewPackageLoading = (state)=>({
    type: reviewActionTypes.STATE_REVIEW_PACKAGE_LOADING,
    reviewPackageLoading :state,
  });
  export const EmiOptions = (emiOptions)=>({
    type: reviewActionTypes.STATE_REVIEW_EMI_OPTIONS,
    emiOptions,
  });

  export const ReviewPackageContent = (packageContent) => ({
    type: reviewActionTypes.STATE_REVIEW_PACKAGE_CONTENT,
    packageContent,
  });
  export const showCouponError = (error) => ({
    type: reviewActionTypes.COUPON_ERROR,
    error,
  });

  export const updatePhoenixCouponStatus = (couponResponse, code) => ({
    type: reviewActionTypes.PHOENIX_STATE_COUPON_SUCCESS,
    couponResponse,
    code,
  });

export const updateSelectInsurance = (selectInsuranceData) => ({
  type: reviewActionTypes.STATE_INSURANCE_SELECTED,
  selectInsuranceData
});

export const updateValidateAddOn = (validateApiResponseData) => ({
  type: reviewActionTypes.STATE_VALIDATE_API_SUCCESS,
  validateApiResponseData
});
export const removeMmtBlackReviewData = () => ({
  type: reviewActionTypes.CLEAR_MMTBLACK_REVIEW_DATA
});
export const removeGiftCardStatus = (response) => ({
  type: reviewActionTypes.CLEAR_GIFT_CARD_STATUS,
  payload: response,
});

const updateGiftCardStatus = (status) => ({
  type: reviewActionTypes.ATTACH_GIFT_CARD_STATUS,
  payload: status,
});
const updateGiftCardData = (response) => ({
  type: reviewActionTypes.ATTACH_GIFT_CARD_DATA,
  payload: response,
});
const removeGiftCardData = (response) => ({
  type: reviewActionTypes.CLEAR_GIFT_CARD_DATA,
  payload: response,
});

