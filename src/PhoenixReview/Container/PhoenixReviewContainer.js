import React from 'react';
import {connect} from 'react-redux';
import {
  fetchReviewData,
  fetchPersuasionDataActions,
  selectPaymentOption,
  doPrePaymentDynamic,
  validateZC,
  toggleComponentFailure,
} from '../../Review/Actions/HolidayReviewActions';
import { attachGiftCard, clearGiftCardData, removeGiftCardStatus, removeMmtBlackReviewData, selectInsurance, validateAddOns, validateCoupon, validateTravellerForm } from '../Actions/PhoenixReviewAction';
import {fetchPackageContentReview,getEmiOptions} from '../Actions/PhoenixReviewAction';
import PhoenixReviewPage from '../Components/PhoenixReviewPage';

const mapStateToProps = state => ({
  ...state?.holidaysReview,
  packageContent:state?.holidaysDetail?.packageContent,
  attachGiftCardStatus: state?.holidaysReview?.attachGiftCardStatus,
  attachGiftCardData: state?.holidaysReview?.attachGiftCardData,
});

const mapDispatchToProps = dispatch => ({
  attachGiftCard: () => dispatch(attachGiftCard()),
  fetchReviewData: (holidayReviewData, roomDetails,isFphReview, criteria, callback) => dispatch(fetchReviewData(holidayReviewData, roomDetails,isFphReview, criteria, callback)),
  fetchPackageContent:(detailRespBody, isWG)=>dispatch(fetchPackageContentReview(detailRespBody, isWG)),
  validateCoupon: (coupon, isSelected, action, couponToBeRemoved, fromPresalesDetail) => dispatch(validateCoupon(coupon, isSelected, action, couponToBeRemoved, fromPresalesDetail)),
  fetchPersuasionDataActions: () => dispatch(fetchPersuasionDataActions()),
  selectPaymentOption: option => dispatch(selectPaymentOption(option)),
  doPrePaymentDynamic: (userDetails, user,presales) => dispatch(doPrePaymentDynamic(userDetails, user,presales)),
  validateZC: (action, zcType) => dispatch(validateZC(action, zcType,true,true)),
  getEmiOptions : (dynamicId) => dispatch(getEmiOptions(dynamicId)),
  validateTravellerForm: (formObject) => dispatch(validateTravellerForm(formObject)),
  toggleComponentFailure:(componentFailureData) => dispatch(toggleComponentFailure(componentFailureData)),
  selectInsurance: (selectInsuranceRequestBody) => dispatch(selectInsurance(selectInsuranceRequestBody)),
  validateAddOns: (validateAddonRequest) => dispatch(validateAddOns(validateAddonRequest)),
  clearGiftCardData: () => dispatch(clearGiftCardData()),
  removeMmtBlackReviewData: () => dispatch(removeMmtBlackReviewData()),
  removeGiftCardStatus:() => dispatch(removeGiftCardStatus()),
});

class HolidaysReviewContainer extends React.Component {
  ReviewPageView;
  constructor(props) {
    super(props);
      this.ReviewPageView = require('../Components/PhoenixReviewPage').default;
  }
  render() {
    return <PhoenixReviewPage {...this.props}/> //<this.ReviewPageView {...this.props} />;
  }
}
export default connect(mapStateToProps, mapDispatchToProps)(HolidaysReviewContainer);
