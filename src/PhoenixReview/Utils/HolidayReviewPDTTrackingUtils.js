import { getReviewPDTObj, updateReviewPDTObj } from './HolidayReviewPDTDataHolder';
import {
  getEventDetailComponents,
  getLobCategory,
  initializeCampaignDetails,
  initializeEventTrackingContext,
  initializeSearchContext,
  logHolidaysEventToPDT,
  populateExperimentalDetails,
  populatePageContext,
  populateSearchContext,
} from '../../utils/HolidayPDTTrackingV3';
import {  NULL_VALUE } from '../../utils/HolidayPDTConstants';
import { getPaxDetails } from '../../utils/HolidayUtils';
import { createChildAgeArrayFromApi, createRoomDataFromRoomDetailsPhoenix } from '../../utils/RoomPaxUtils';
import { isEmpty } from 'lodash';
import {
  DEFAULT_PASSPORT_ISSUANCE_COUNTRY_CODE,
  FEMALE_TITLE_MRS,
  MALE,
  MALE_TITLE,
  REVIEW_PDT_PAGE_NAME,
} from '../../Review/HolidayReviewConstants';
import { getAge } from './ChooseTravellerUtil';
import { DATE_FORMAT_OMNI } from '../../SearchWidget/SearchWidgetConstants';
import { FORM_SECTION_NAME, FORM_SECTION_TYPES } from './HolidayReviewConstants';
import { calculateToDateTime } from '../../PhoenixDetail/Utils/PhoenixDetailUtils';

export const logHolidayReviewPDTClickEvents = ({ actionType, value, errorDetails  = {}, travellerInfo = [], subPageName = '' , compData={},addonsDetails={},queryDetail={},shouldTrackToAdobe=true}) => {
  const pdtObj = {
    ...getReviewPDTObj(),
    ...(isEmpty(errorDetails) || isEmpty(errorDetails?.code) ? {} : { error_details_list: [errorDetails] }),
    traveller_info : travellerInfo, 
  };
  logHolidaysEventToPDT({
    pdtObj,
    actionType,
    value,
    subPageName,
    compData,
    addonsDetails,
    queryDetail,
    shouldTrackToAdobe
  });
};



export const initReviewPDTObj = ({ reviewData }) => {
  const { branch = '', reviewDetail = {} } = reviewData || {};
  const pdtObj = initializeReviewPDTObj({ branch, reviewDetail });
  updateReviewPDTObj({ pdtObj });
};

export const updateReviewComponentsOnApiSuccess = ({ reviewDetail }) => {
  const { components } = getEventDetailComponents({ packageDetail: reviewDetail });
  const pdtObj = getReviewPDTObj();
  updateReviewPDTObj({
    pdtObj: {
      ...pdtObj,
      event_detail: {
        components,
      },
    },
  });
}

export const initializeReviewPDTObj = ({ branch, reviewDetail }) => {
  const pdtObj = getReviewPDTObj();
  return {
    ...JSON.parse(JSON.stringify(pdtObj)),
    experiment_details: populateExperimentalDetails(),
    page_context: setReviewPageContextData({ branch }),
    event_tracking_context: initializeEventTrackingContext(),
    search_context: populateReviewSearchContext({ reviewDetail }),
    campaign_details: initializeCampaignDetails(),
  };
};

export const setReviewPageContextData = ({ branch }) => {
  const lobCategory = getLobCategory({ branch });
  return populatePageContext({
    funnelStep: REVIEW_PDT_PAGE_NAME,
    pageName: REVIEW_PDT_PAGE_NAME,
    lobCategory,
  });
};

export const populateReviewSearchContext = ({ reviewDetail }) => {
  const {departureDetail = {}, tagDestination = {}, roomDetail = {},destinationDetail={}} = reviewDetail || {};
  const {rooms = []} = roomDetail;
  const {
    cityId = '',
    cityName = '',
    locusDetails: depLocusDetails = {},
    departureDate = '',
    locusDetailsV2={},
    locationDetails={},
  } = departureDetail || {};
  const {destinations=[]}=destinationDetail
  const { id = '', name = '', locusDetails: destLocusDetails = {} } = tagDestination;
  const deptCity = {
    id: cityId,
    name: cityName,
    locusId: depLocusDetails?.locusCode,
    type: depLocusDetails?.locusType,
    LocationDetails:locationDetails,
    locus_v2:locusDetailsV2,
    countryName:depLocusDetails?.countryName
  };
  const destCity = {
    id,
    name,
    locusId: destLocusDetails?.locusCode,
    type: depLocusDetails?.locusType,
    LocationDetails:destinations[0]?.locationDetails,
    locus_v2:destinations[0]?.locusDetailsV2,
    countryName:destinations[0]?.locusDetails?.countryName
  };
  const pdtObj = getReviewPDTObj();
  const { search_context = {} } = pdtObj;
  let childAgeArray = [];
  if (rooms) {
    childAgeArray = createChildAgeArrayFromApi(rooms);
  }
  const paxDetails = {
    ...getPaxDetails({ roomDetails : rooms }),
    roomData: createRoomDataFromRoomDetailsPhoenix(rooms, childAgeArray),
  };
  const prevSearchContext = isEmpty(search_context) ? initializeSearchContext() : search_context
  return populateSearchContext({
    paxDetails,
    destCity,
    deptCity,
    packageDate: departureDate,
    prevSearchContext,
    toDataTime: calculateToDateTime(
      departureDetail?.departureDate,
      destinationDetail?.duration
    ),
  });
};


export const getTravellerInfo = (params) => {
  const { formSections, userDetailsDynamic, travellerFormData, collapsedForm, collapsedFormData } =
    params || {};
  let travellerInfo = [];
  const { formSectionResponses = [] } = travellerFormData;
  const contactDetail  = getContactDetails(formSections, userDetailsDynamic)
  formSectionResponses.forEach((item, index) => {
    if (item?.formSectionName === FORM_SECTION_NAME.TRAVELLER_FORM) {
      item?.formSections.forEach((formSection, index) => {
        const fieldValues = extractFieldValues(formSection);
        travellerInfo.push(getTravellerObj(fieldValues, index, contactDetail));
      });
    }
  });
  return travellerInfo;
};

export const getTravellerObj = (fieldValues, index, contactDetail) => {
  const {
    FIRST_NAME = [],
    LAST_NAME = [],
    GENDER = [],
    DOB = [],
    PASSPORT_DATE_OF_EXPIRY = [],
    PASSPORT_ISSUING_COUNTRY = [],
    PASSPORT_NUMBER = [],
    NATIONALITY = [],
  } = fieldValues[0] || {};
  const { EMAIL = [], MOBILE_CODE = [], MOBILE_NUMBER = [] } = contactDetail || {};

  const passport_details = {
    passport_expiry_date: PASSPORT_DATE_OF_EXPIRY[0],
    passport_issue_place: PASSPORT_ISSUING_COUNTRY[0],
    passport_number: PASSPORT_NUMBER[0],
  };
  
  const communicationDetail = {
    m_cty_cd: index === 0 ? MOBILE_CODE[0] : NULL_VALUE,
    moblie_com_id: index === 0  ? MOBILE_NUMBER[0] : NULL_VALUE,
    email_com_id: index === 0  ? EMAIL[0] : NULL_VALUE,
    email_id : NULL_VALUE,
    mob_id : NULL_VALUE,
  }
  const traveller = {
    is_primary: index === 0,
    fname: FIRST_NAME[0],
    lname: LAST_NAME[0],
    title: GENDER[0] === MALE ? MALE_TITLE : FEMALE_TITLE_MRS,
    date_of_birth: DOB[0],
    gender: GENDER[0],
    age: DOB && DOB.length > 0 ? getAge(DOB[0], DATE_FORMAT_OMNI) : 0,
    nationality: NATIONALITY[0] || DEFAULT_PASSPORT_ISSUANCE_COUNTRY_CODE,
    passport_details,
    ...communicationDetail,
  };
  return traveller;
};

export const getContactDetails = (formSections, userDetailsDynamic) => {
  const contactDetails = {};
  formSections?.map((form) => {
    const { sectionId, fields, formSectionName } = form || {};
    if (formSectionName === FORM_SECTION_NAME.CONTACT) {
      for (let name in fields) {
        contactDetails[name] = userDetailsDynamic[`${sectionId}#${name}`]
          ? [userDetailsDynamic[`${sectionId}#${name}`]]
          : [];
      }
    }
  });
  return contactDetails;
};

export const extractFieldValues = (section) => {
  if (section.formSectionType === FORM_SECTION_TYPES.LEAF && section.fieldValues) {
    return section.fieldValues;
  }

  if (section.formSections) {
    return section.formSections.flatMap((subSection) => extractFieldValues(subSection));
  }

  return {};
};

export const  createPDTProductObject = (props) => {
  const { reviewData, pricingDetail , dealDetail={}} = props;
  const { reviewDetail = {} ,mmtBlackDetail = {} ,personalizationDetail = {}} = reviewData || {};
  const { metadataDetail = {}, id, name, dynamicId, tagDestination = {}  , packageAddonsDetail = {}} = reviewDetail;
  const { 
    vppAddonDetail: { addons: vppAddonDetails = [] } = {}, 
    insuranceAddonDetail: { addons: insuranceAddonDetails = [] } = {} 
  } = packageAddonsDetail || {};
  const allAddons = [...vppAddonDetails, ...insuranceAddonDetails];

  const { couponDetails = [] } = dealDetail || {};
  const { branch, packageType, premium = false } = metadataDetail;
  const category = premium ? 'Premium' : '';
  const { price, serviceTax = 0, discountedPrice } = pricingDetail || {};

  const allPreSelectAddons = [
    ...(props.vppAddonDetail?.addons || []), 
    ...(props.insuranceAddonDetail?.addons || [])
  ];

  const preSelectDealData = props.reviewData?.reviewDetail?.dealDetail?.couponDetails || [];
  const applicableCoupons = couponDetails?.map((item, index) => ({
    discount: item?.discountAmount,
    code: item?.couponCode,
    is_preapplied: preSelectDealData[index]?.selected,
    is_applied: item.selected
  })) || [];
  const sections = [
    ...(Object.keys(mmtBlackDetail || {}).length ? [{ id: '', type: 'MMT Black', name: '', section_code: 'BLACK',position:{v:1} }] : []),
    ...(Object.keys(personalizationDetail || {}).length ? [{ id: '', type: 'PZN', name: '', section_code: 'PZN' ,position:{v:1}}] : []),
  ];
  return {
    event_detail: {
      components: {
        product_list: [{
          id: id || 'null',
          name: name,
          type: packageType,
          branch: branch,
          category: [category],
          dynamic_id: dynamicId,
          tagged_destinations: [{name:tagDestination?.name}],
          price: {
            display_price: discountedPrice,
            base_price: price,
            total_tax: serviceTax,
            per_person_price: price,
            per_child_price: null,
            discounts: {
              applicable_coupons: applicableCoupons
            }
          }
        }],
        content_details:sections
      }
    },
    addon_details: {
    addon_details: allAddons.map((addon, index) => ({
      addontype: addon.addonType,
      id: addon.id,
      currency: 'INR',
      includedunits: addon.addonUnits,
      unitprice: addon.addonPrice,
      unittype: addon.addonPriceUnit,
      heading: addon?.cardDetails?.cardHeader?.heading,
      selected: allPreSelectAddons[index]?.isSelected,
      available: true,
      is_preselected: addon.isSelected,
    }))
    }
  };
  };