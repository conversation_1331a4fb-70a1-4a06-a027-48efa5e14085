import isEmpty from 'lodash/isEmpty';
import { PACKAGE_TYPE_FIT, RAW_PLATFORM_NAME } from '../../HolidayConstants';
import { DATE_FORMAT } from '../../Review/HolidayReviewConstants';
import fecha from 'fecha';
import { AUTO_FILLER_TYPES, FORM_FILL_STATUS, FORM_SECTION_NAME, FORM_SECTION_TYPES } from './HolidayReviewConstants';
import { Platform } from 'react-native';
import {flightDetailTypes, itineraryUnitSubTypes} from '../../PhoenixDetail/DetailConstants';


// TODO: remove this function
export const isRawClient = () => Platform.OS === RAW_PLATFORM_NAME;

export const getTotalTravellers = roomDetail => {
  if (!roomDetail || isEmpty(roomDetail.rooms)) {
    return null;
  }
  let noOfAdults = 0;
  let noOfChildrenWOB = 0;
  let noOfChildrenWB = 0;
  let noOfInfants = 0;

  for (const roomIndex in roomDetail.rooms) {
    const room = roomDetail.rooms[roomIndex];
    noOfAdults += room.noOfAdults;
    noOfChildrenWOB += room.noOfChildrenWOB;
    noOfChildrenWB += room.noOfChildrenWB;
    noOfInfants += room.noOfInfants;
  }
  return {
    noOfAdults,
    noOfChildrenWB,
    noOfChildrenWOB,
    noOfInfants,
    children: noOfChildrenWB + noOfChildrenWOB + noOfInfants,
  };
};
export const isFlexiPackage = (reviewDetail) => reviewDetail.metadataDetail && reviewDetail.metadataDetail.packageType === PACKAGE_TYPE_FIT;
export const getPackageTypeString = (reviewDetail) => {
  if (isFlexiPackage(reviewDetail)) {
    return 'Flexi Package';
  }
  return null;
};

export const getFormattedDate = (date,day,format) => {
  if (date && date?.length > 0) {
    let dateObj = fecha.parse(date, DATE_FORMAT);
    if (day && dateObj){
      dateObj = dateObj.setDate(dateObj.getDate() + day);
    }
    const formattedDate = fecha.format(dateObj, format);
    return formattedDate;
  }
  return '';
};

/**
 * Main function to create an empty traveller form
 * */
export const createEmptyTravellerForm = (formSections) => {
  let emptyFormData = [];
  if (formSections && formSections.length > 0) {
    formSections.forEach((section) => {
      const sectionObject = createSection(section);
      emptyFormData.push(sectionObject);
    });
  }
  return emptyFormData;
};

/**
 * Functions which separates between a group and leaf node
**/
const createSection = (section) => {
  const {formSectionType} = section;
  switch (formSectionType) {
    case FORM_SECTION_TYPES.GROUP:
      return createGroupObject(section);
    case FORM_SECTION_TYPES.LEAF:
      return createLeafObject(section);
    default: return {};
  }
};

/**
 * Since group can contain another group
 * Did a recursive call to createSection function to create another group or leaf object.
 **/
const createGroupObject = (section) => {
  const {formSections, formSectionType, sectionId, displayName, formSectionName} = section;
  const formSectionList = [];
  formSections.forEach((section2) => {
    formSectionList.push(createSection(section2));
  });
  return {
    formSectionType,
    sectionId,
    displayName,
    formSectionName,
    formSections: formSectionList,
  };
};

const createLeafObject = (section) => {
  const {
    formSectionType,
    sectionId,
    fields,
    displayName,
    roomSectionId,
    formSectionName,
    age,
  } = section;
  const fieldValues = {};
  const keys = Object.keys(fields);
  keys.forEach((item) => {
    fieldValues[item] = [];
  });
  return {
    age,
    formSectionType,
    sectionId,
    displayName,
    formSectionName,
    status: FORM_FILL_STATUS.UNFILLED,
    fieldValues,
    roomSectionId,
  };
};

/**
 * Creates saved traveller object with complete status
 * */
export const createSavedTravellerInformation = (travellerFormAutofillerDetail) => {
  const traveller_data = travellerFormAutofillerDetail[AUTO_FILLER_TYPES.TRAVELLER];
  const {fieldValues = []} = traveller_data || {};
  let data = [];
  if (fieldValues && fieldValues.length > 0) {
    data = fieldValues.map((item) => {
      return {
        status: FORM_FILL_STATUS.FILLED,
        fields: item.fieldValues,
      };
    });
  }
  return data;
};

export const getTravellerObject = (travellerForm) => {
  if (travellerForm && travellerForm.length > 0) {
    for (let i = 0; i < travellerForm.length; i++) {
      if (travellerForm[i].formSectionName === FORM_SECTION_NAME.TRAVELLER_FORM) {
        return travellerForm[i];
      }
    }
  }
  return {};
};

export const createTravellerList = (travellerForm, showRooms = true) => {
  const travellerObject = getTravellerObject(travellerForm);
  let travellerList = flattenTravellerForm(travellerObject?.formSections, showRooms);
  return showRooms ? travellerList : sortByAdults(travellerList);
};

export const createDynamicFormList = (dynamicFormData, showRooms = true) => {
  const formObject = getTravellerObject(dynamicFormData?.formSections);
  let dynamicFormList = dynamicTravellerForm(formObject?.formSections);
  return showRooms ? dynamicFormList : sortByAdults(dynamicFormList);

};

export const getCompleteFieldList = (sections, fieldSectionObj) => {
  sections && sections.length > 0 && sections.forEach((section,i) => {
   if (section?.fields && section.fields.length > 0) {
    section?.fields.forEach(field=> fieldSectionObj[field] = i);
   }
   if (section?.sections && section.sections.length > 0){
     section.sections.map(innerSection =>
      {
        if (innerSection?.fields && innerSection.fields.length > 0) {
          innerSection?.fields.forEach(field=>fieldSectionObj[field] = i);
        }
      }
    );}});
};

export const initializeTravellerFormData = (dynamicFormData) => {
  let result = {};
  result.travellerFormId = dynamicFormData?.travellerFormId;
  let formSections = dynamicFormData?.formSections;
  if (formSections && formSections.length > 0){
    let travellerFormObj = formSections.find(e=>e.formSectionName === FORM_SECTION_NAME.TRAVELLER_FORM);
    if (travellerFormObj){
      result.formSectionResponses = [{
      formSectionType: travellerFormObj?.formSectionType,
      sectionId: travellerFormObj?.sectionId,
      formSections: [],
      formSectionName: FORM_SECTION_NAME.TRAVELLER_FORM,
    }];}
    else {
      result.formSectionResponses = [];
    }
  }
  else {result.formSectionResponses = [];}
  return result;
};

export const addTravellerFormLeaf = (requestObj, leaf) => {
  const index = requestObj?.formSectionResponses?.findIndex(e=>e?.formSectionName === FORM_SECTION_NAME.TRAVELLER_FORM);
  if (index !== -1){
    let groupIndex = requestObj.formSectionResponses[index]?.formSections.findIndex(e=> e.sectionId === leaf.roomSectionId);
    const leafObj = { formSectionType: leaf?.formSectionType, sectionId: leaf?.sectionId, fieldValues: leaf?.fieldValues };
    if (groupIndex !== -1){
      requestObj.formSectionResponses[index].formSections[groupIndex]?.formSections.push(leafObj);
    }
    else {
      const groupObj = { formSectionType: FORM_SECTION_TYPES.GROUP, sectionId: leaf?.sectionId, formSections: [leafObj] };
      requestObj.formSectionResponses[index].formSections.push(groupObj);
    }
  }
};

export const getDate = (date) => {
  const dateArr = date.split('/');
  return new Date(dateArr[2], dateArr[1] - 1, dateArr[0]);
};

const flattenTravellerForm = (travellerFormSection, showRooms) => {
  let travellerList = [];
  if (travellerFormSection && travellerFormSection.length > 0) {
    for (let i = 0; i < travellerFormSection.length; i++) {
      if (travellerFormSection[i].formSections) {
        for (let j = 0; j < travellerFormSection[i].formSections.length; j++) {
          const temp_obj = {
            roomId: i,
            roomSectionId: travellerFormSection[i].sectionId, // Also included in Lead Node.
            roomDisplayName: travellerFormSection[i].displayName,
            showRoomTag: showRooms && (j === 0 ? true : false),
            travellerId: j,
            ...travellerFormSection[i].formSections[j],
          };
          temp_obj.roomDisplayName = temp_obj.roomDisplayName.replace('-', '');
          temp_obj.displayName = temp_obj.displayName.replace('-', '');
          travellerList.push(temp_obj);
        }
      }
    }
  }
  return travellerList;
};

const dynamicTravellerForm = (dynamicFormSection) => {
  let dynamicFormList = [];
  if (dynamicFormSection && dynamicFormSection.length > 0) {
    for (let i = 0; i < dynamicFormSection.length; i++) {
      if (dynamicFormSection[i].formSections) {
        for (let j = 0; j < dynamicFormSection[i].formSections.length; j++) {
          const temp_obj = dynamicFormSection[i].formSections[j];
          temp_obj.displayName = temp_obj.displayName.replace('-', '');
          dynamicFormList.push(temp_obj);
        }
      }
    }
  }
  return dynamicFormList;
};

const sortByAdults = (objectList) => {
  if (objectList && objectList.length > 0) {
    let counts = [1, 1, 1]; // ADULT, CHILD, INFANT
    for (let i = 0; i < objectList.length; i++) {
      const formSecName = objectList[i].formSectionName;
      if (formSecName === 'PRIMARY_TRAVELLER' || formSecName ===  'ADULT') {
        objectList[i].displayName = `ADULT ${counts[0]++}`;
      } else if (formSecName === 'CHILD') {
        objectList[i].displayName = `CHILD ${counts[1]++}`;
      } else {
        objectList[i].displayName = `INFANT ${counts[2]++}`;
      }
    }
    objectList.sort((a, b) => {
      return a.displayName.localeCompare(b.displayName);
    });
  }
  return objectList;
};

export const formDynamicApiDataContact = params => {
  const {
    formSections,
    userDetailsDynamic,
    travellerFormData,
    collapsedForm,
    collapsedFormData,
  } = params || {};

  const data = {};
  const formSubmit = formSections?.map(form => {
    const {formSectionType, sectionId, fields} = form || {};
    const fieldValues = {};
    for (let name in fields) {
      fieldValues[name] = userDetailsDynamic[`${sectionId}#${name}`] ? [userDetailsDynamic[`${sectionId}#${name}`]] : [];
    }
    return {
      formSectionType,
      sectionId,
      fieldValues,
    };
  });

  const formCollapsedSubmit = collapsedForm?.map(form => {
    const {formSectionType, sectionId, fields} = form;
    const fieldValues = {};
    for (let name in fields) {
      fieldValues[name] = collapsedFormData[`${sectionId}`] ? [collapsedFormData[`${sectionId}`]] : [];
    }
    return {
      formSectionType,
      sectionId,
      fieldValues,
    };
  });


  let newSections = travellerFormData?.formSectionResponses?.concat(formSubmit);
  let allSections = newSections?.concat(formCollapsedSubmit);
  travellerFormData.formSectionResponses = allSections;
  return travellerFormData;

};

export const getFlightObjectDetails = (flightDetail, sellableFlightId) => {
  let obj = {};
  if (flightDetail && flightDetail?.flightDetailType === flightDetailTypes.OBT) {
    const { obtFlightGroup } = flightDetail;
    const { flights } = obtFlightGroup || {};
    if (flights && flights.length > 0) {
      obj = flights.find((flight)=> flight.sellableId === sellableFlightId);
    }
  } else if (flightDetail && flightDetail?.flightDetailType === flightDetailTypes.DOM_RETURN) {
    const { departureFlight, returnFlight } = flightDetail || {};
    if (departureFlight && departureFlight.sellableId === sellableFlightId) {
      obj = departureFlight;
    }
    if (returnFlight && returnFlight.sellableId === sellableFlightId) {
      obj = returnFlight;
    }
  } else if (flightDetail?.flightDetailType === flightDetailTypes.DOM_ONWARDS) {
    const { flightGroup, onwardFlights = []} = flightDetail;
    const { flights = [] } = flightGroup || {};
    if (flights && flights.length > 0) {
      obj = flights.find((flight)=> flight.sellableId === sellableFlightId);
    }
    if (onwardFlights && onwardFlights.length > 0) {
      obj = onwardFlights.find((flight)=> flight.sellableId === sellableFlightId);
    }
  }

  return obj;
};

export const checkIfFlightIsOvernight = ({ detailData = {} } = {}) => {
  const sellableFlightId =
    detailData?.itineraryDetail?.dayItineraries?.[0]?.itineraryUnits?.[0]?.flight?.sellableId;
  const flightObject = getFlightObjectDetails(detailData?.flightDetail, sellableFlightId);
  return flightObject?.isOvernight;
};


export const optimizeHotelDetailsForReview = (packageDetail) => {
  const { hotelDetail = {} } = packageDetail || {};
  const { hotels = [] } = hotelDetail || {};
  const obj = {};
  if (hotels && hotels.length > 0) {
    hotels.forEach((hotel, index) => {
      obj[hotel.sellableId] = hotel;
    });
  }
  return obj;
};

export const optimizeFlightDetailsForReview = (newPackageDetail) => {
  const { flightDetail } = newPackageDetail;
  if (flightDetail && flightDetail.flightDetailType === 'OBT') {
    const { obtFlightGroup } = flightDetail;
    const { flights } = obtFlightGroup || {};
    const obj: any = {};
    if (flights && flights.length > 0) {
      flights.forEach((item, index) => {
        obj[item.sellableId] = item;
      });
      obtFlightGroup.flights = obj;
    }
    return {
      ...flightDetail,
      obtFlightGroup: {
        ...obtFlightGroup,
        flights: obj,
      }
    }
  } else if (flightDetail && flightDetail.flightDetailType === 'DOM_RETURN') {
    const { departureFlight, returnFlight } = flightDetail || {};
    const obj: any = {};
    if (departureFlight) {
      departureFlight.type = itineraryUnitSubTypes.FLIGHT_DEPART;
      obj[departureFlight.sellableId] = departureFlight;
    }
    if (returnFlight) {
      returnFlight.type = itineraryUnitSubTypes.FLIGHT_ARRIVE;
      obj[returnFlight.sellableId] = returnFlight;
    }

    return {
      ...flightDetail,
      flights: obj,
    }
  }

};

export const optimizeFlightDetailsForReviewV2 = (newPackageDetail) => {
  const { flightDetail } = newPackageDetail;
  if (flightDetail && flightDetail.flightDetailType === 'OBT') {
    const { obtFlightGroup } = flightDetail;
    const { flights } = obtFlightGroup || {};
    const obj: any = {};
    if (flights && flights.length > 0) {
      flights.forEach((item, index) => {
        obj[item.sellableId] = item;
      });
    }
    return {
      ...flightDetail,
      obtFlightGroup: {
        ...obtFlightGroup,
        flights: obj,
      }
    }
  } else if (flightDetail && flightDetail.flightDetailType === 'DOM_RETURN') {
    const { departureFlight, returnFlight } = flightDetail || {};
    const obj: any = {};
    if (departureFlight) {
      obj[departureFlight.sellableId] = {...departureFlight, type : itineraryUnitSubTypes.FLIGHT_DEPART};
    }
    if (returnFlight) {
      obj[returnFlight.sellableId] = {...returnFlight, type : itineraryUnitSubTypes.FLIGHT_ARRIVE};
    }
    return {
      ...flightDetail,
      flights: obj,
    }
  }

};

export const optimizeActivityDetailsForReview = (newPackageDetail) => {
  const { activityDetail } = newPackageDetail;
  const { cityActivities } = activityDetail || {};
  const obj: any = {};
  if (cityActivities && cityActivities.length > 0) {
    cityActivities.forEach((item, index) => {
      const { activities } = item;
      if (activities && activities.length > 0) {
        activities.forEach((item2, index2) => {
          obj[item2.sellableId] = item2;
        });
      }
    });
  }
  return obj;
};

export const getPackageAddonsProp1 = (arg1, arg2, arg3) => {
  const args = [arg1, arg2, arg3];
  const argNames = ['ZC', 'INSURANCE', 'VPP'];
  return args
    .map((arg, index) => arg !== undefined && arg !== '' ? `${argNames[index]}_${index}` : '')
    .filter(Boolean)
    .join('_');
};