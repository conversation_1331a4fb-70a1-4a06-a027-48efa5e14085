import { isEmpty } from 'lodash';
import {
  FAILURE_TOAST_BOTTOM,
  FAB_BOTTOM_DEFAULT,
  FAB_BOTTOM_WITH_INFORMATION_STRIP,
  FAB_BOTTOM_ADDITIONAL_WITH_TRAVELLER_PILL,
} from './HolidayReviewConstants';

/**
 * Calculates the bottom position for FAB based on current UI state
 * This determines proper spacing when multiple UI elements stack in the footer area
 *
 * @param {Object} options - Options for calculating FAB position
 * @param {boolean} options.showComponentFailureToast - Whether a component failure toast is shown
 * @param {boolean} options.showNewUpdateSection - Whether the new update section is shown
 * @param {boolean} options.showPriceChange - Whether price change banner should be shown
 * @param {boolean} options.priceChange - Whether a price change has occurred
 * @param {boolean} options.isConfigAvailable - Whether configuration is available
 * @param {Object} options.packageReviewDifferenceDetail - Package review difference details
 * @param {boolean} options.travellerPill - Whether traveller pill is shown
 * @returns {Object} Object containing calculated bottom position style for the FAB
 */
export const calculateFabPosition = ({
  showComponentFailureToast,
  showNewUpdateSection,
  showPriceChange,
  priceChange,
  isConfigAvailable,
  packageReviewDifferenceDetail,
  travellerPill,
}) => {
  // Determine which UI elements are currently active/visible
  const isPriceChangeCardActive = showNewUpdateSection && showPriceChange && priceChange;
  const isInformationStripActive =
    !showNewUpdateSection &&
    isConfigAvailable &&
    Boolean(
      packageReviewDifferenceDetail?.headerInfo?.text &&
      !isEmpty(packageReviewDifferenceDetail?.sectionInfo?.messages),
    );

  let baseBottom = FAB_BOTTOM_DEFAULT;

  // Set base bottom position based on priority of UI elements
  // Higher bottom values push elements further up from the bottom of the screen
  switch (true) {
    case showComponentFailureToast:
      baseBottom = baseBottom + FAILURE_TOAST_BOTTOM;
      break;
    case isPriceChangeCardActive:
      baseBottom =  baseBottom + FAB_BOTTOM_ADDITIONAL_WITH_TRAVELLER_PILL;
      break;
    case isInformationStripActive:
      baseBottom =  baseBottom + FAB_BOTTOM_WITH_INFORMATION_STRIP;
      break;
    default:
      baseBottom = FAB_BOTTOM_DEFAULT;
  }
  return { baseBottom };
};

/**
 * Determines if an information strip is active based on page state
 *
 * @param {boolean} showNewUpdateSection - Whether the new update section is shown
 * @param {boolean} isConfigAvailable - Whether configuration is available
 * @param {Object} packageReviewDifferenceDetail - Package review difference details
 * @returns {boolean} Whether an information strip is active
 */
export const isInformationStripActive = (
  showNewUpdateSection,
  isConfigAvailable,
  packageReviewDifferenceDetail
) => {
  return !showNewUpdateSection &&
    isConfigAvailable &&
    Boolean(
      packageReviewDifferenceDetail?.headerInfo?.text &&
      !isEmpty(packageReviewDifferenceDetail?.sectionInfo?.messages)
    );
};
