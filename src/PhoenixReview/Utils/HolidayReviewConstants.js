export const COUPON_APPLY = 'APPLY';
export const COUPON_REMOVE = 'REMOVE';
export const NO_COST = 'No cost';
export const CANCELLATION_POLICY = 'Cancellation Policy';
export const DATE_CHANGE_POLICY = 'Date Change Policy';
export const FLEXI_KEY = 'FlexiDate';
export const FLEXI_ACTION_KEY = 'FLEXI_DATE';
export const ZC_KEY = 'ZC';
export const COUPON_KEY = 'COUPON';
export const FLEXI_DESCRIPTION = 'Flexi Date';
export const ZC_DESCRIPTION = 'ZC';
export const ZC_FULL_DESCRIPTION = 'Zero Cancellation';
export const PART_PAYMENT = 'PART_PAYMENT';
export const FULL_PAYMENT = 'FULL_PAYMENT';
export const PART_DESC = 'Book Now Pay Later';
export const FULL_DESC = 'Pay full amount now';
export const EMI_OPTIONS_AVAILABLE = 'EMI Available on all payment modes';
export const PRICE = 'PRICE';
export const FORM_SECTION_TYPES = {
  GROUP: 'GROUP',
  LEAF: 'LEAF',
};
export const FIELD_FIRSTNAME = 'FIRST_NAME';
export const FIELD_TYPE_TEXT = 'TEXT';
export const FIELD_TYPE_DATE = 'DATE';
export const FIELD_TYPE_CHECKBOX = 'CHECKBOX';
export const FIELD_TYPE_SINGLE_SELECT = 'SINGLE_SELECT';
export const FIELD_TYPE_RADIO = 'RADIO_SINGLE_SELECT';
export const ADD_TRAVELLER_ERROR = 'ADD_TRAVELLER';
export const IMP_INFO = 'Important Information';
export const ADDING_GIFT_CARDS = 'Adding MMTBlack gift cards..';
export const GIFT_CARDS_ADDED = 'Gift cards added to your account. You can apply it at the time of payment';
export const GIFT_CARDS_ERROR = 'Oops! Failed to add gift cards to your account. Please try';
export const TRY_AGAIN = 'TRY AGAIN';
export const MMT_BLACK_ATTACH_CARD_CALL = 'MMT_BLACK_ATTACH_CARD_CALL';
export const ALREADY_ADDED = 'ALREADY ADDED';

export const FAILURE_TOAST_BOTTOM = 20;
export const MMT_GIFT_CARD_SNACK_BAR_DURATION = 2200;

/**
 * FAB Positioning Constants
 *
 * These constants define the bottom positioning (in pixels) of the Floating Action Button (FAB)
 * under different UI states. Proper spacing prevents the FAB from overlapping with other
 * UI elements at the bottom of the screen.
 *
 * The positioning strategy follows a stacking model where UI elements at the bottom of the
 * screen can appear in various combinations, requiring the FAB to adjust its position accordingly.
 */

// Default position when no additional UI elements are present
export const FAB_BOTTOM_DEFAULT = 100;

// Position when an information strip is visible, giving more space at the bottom
export const FAB_BOTTOM_WITH_INFORMATION_STRIP = 50;

// Additional offset to apply when the traveller pill is visible
// This is added to either the default or information strip position
export const FAB_BOTTOM_ADDITIONAL_WITH_TRAVELLER_PILL = 50;

export const AUTO_FILLER_TYPES = {
  TRAVELLER: 'TRAVELLER',
  CONTACT: 'CONTACT',
  GST: 'GST',
};

export const FORM_FILL_STATUS = {
  UNFILLED: 'unfilled',
  INCOMPLETE: 'Incomplete',
  COMPLETE: 'Complete',
  FILLED: 'Filled',
};

export const FORM_SECTION_NAME = {
  TRAVELLER_FORM: 'TRAVELLER_FORM',
  CONTACT: 'CONTACT',
  EXTRA: 'EXTRA',
};

export const PDTConstants = {
  FARE_BREAKUP: 'fare_breakup',
  EXPAND: '_expand',
  COLLAPSE: '_collapse',
  PURCHASE_ADDON: 'purchase_add_on',
  EXPAND_SECTION: 'Expand_section',
  COLLAPSE_SECTION: 'Collapse_section',
  CONFIRM_NAME_POPUP: 'Confirm_name_popup',
  CLOSE_NAME_POPUP: 'Close_name_popup',
  DATE_CHANGE_POLICY: 'date_change_policy',
  CANCELLATION_POLICY: 'cancellation_policy',
  KNOW_MORE: 'Know_more',
  PAYMENT_FULL: 'paymode_full',
  PAYMENT_PARTIAL: 'paymode_partial',
  VIEW_MORE_COUPONS: 'view_more_coupons',
  EMI_OPTIONS:'EMI_Options',
  SELECT: 'select',
  REMOVE: 'remove',
  BOOK: 'book',
  CLOSE_TRAVELLER_FORM: 'pax_popup_close',
  APPLY_COUPON: 'coupon_select',
  REMOVE_COUPON: 'coupon_remove',
  SAVE_TRAVELLER: 'save_pax',
  ITENARY_TABS: 'Itinerary',
  BOOKING_OPTIONS:'booking_options',
  INITIATE_PAYMENT:'initiate_payment',
  CHANGE_PAX:'change_pax',
  ADD_TRAVELLER_FORM:'addTraveller',

};
export const PDTConstantsNew = {
  UPDATE_PAX:'update_pax',
  ADD_TRAVELLER:'addTraveller',
};

export const REVIEW_FOOTER_BTN_TEXT = {
  CONTINUE: 'Pay Now',
  BOOKING_OPTIONS: 'Continue',
};
export const REVIEW_FOOTER_COMPONENTS = {
  fareBreakUp: 'fareBreakup',
  bookingOption: 'bookingOption',
};
export const EMI_TYPE = {
  NO_COST: 'NO_COST',
};

export const SECTIONS = {
  TRAVELLERS: 0,
  ADD_ONS: 1,
  ITENERARY: 2,
  CANCELLATION: 3,
  COUPONS: 4,
  TCS:5,
  BOOKING_INFORMATION: 6, // This will always be the last section
};

export const SECTIONS_DATA = [
  {
    id: SECTIONS.TRAVELLERS,
    title: 'Traveller Details',
    expanded: false,
    status:'mandatory',
  },
  {
    id: SECTIONS.ADD_ONS,
    title: 'Package Add - Ons',
    expanded: false,
  },

  {
    id: SECTIONS.ITENERARY,
    title: 'Package Inclusions',
    expanded: false,
  },
  {
    id: SECTIONS.CANCELLATION,
    title: 'Cancellation & Date Change',
    expanded: false,
  },
  {
    id: SECTIONS.COUPONS,
    title: 'Coupons & Offers',
    expanded: false,
  },
];

export const TCS_VERSIONS = {
  V1: 'v1',
  V2: 'v2',
};
