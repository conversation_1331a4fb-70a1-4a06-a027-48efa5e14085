import fecha from 'fecha';


export const getAge = (birthDate,format) => {
  const today = new Date();
  if (format) {
    birthDate = fecha.parse(birthDate,format);
  } else {
    birthDate = new Date(birthDate);
  }
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
};
