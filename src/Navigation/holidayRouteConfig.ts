import { RouteConfig } from '@mmt/navigation';
import { HOLIDAY_ROUTE_KEYS } from './holidayPageKeys';
import { ReduxOptions } from '@mmt/navigation/src/types';
// Replace the main store import with the holidays store
// import store from '@mmt/legacy-commons/AppState/Store';
import holidaysStore from '../holidaysStore';
import { FUNNEL_ENTRY_TYPES } from '../HolidayConstants';

/**
 * Callback whenever there is any Navigation change happens
 * @param args react-navigation state object
 * @returns void
 */
const onNavigationStateChange = (args: any) => {
  const currentScreenIndex = args?.data?.state?.index;
  const HolidayDataHolder = require('../utils/HolidayDataHolder').default;
  HolidayDataHolder.getInstance().setCurrentPageNameV2(args?.data?.state?.routes[currentScreenIndex].name);
  if (!currentScreenIndex) {
    return;
  }
  if (args?.data?.state?.routes[currentScreenIndex]?.name) {
    HolidayDataHolder.getInstance().setCurrentPage(args.data.state.routes[currentScreenIndex].name);
  }
};
const reduxOptions: ReduxOptions = {
  // Use the holidays-specific store
  // @ts-ignore
  storeFactory: () => holidaysStore,
};

const holidayRouteConfig: RouteConfig[] = [
  {
    key: HOLIDAY_ROUTE_KEYS.LANDING_NEW,
    component: () => require('../LandingNew/Containers/HolidayLandingContainerNew').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.TRIP_INSURANCE_LISTING,
    component: () => require('../Travelnsurance/listing/TravelInsuranceListing').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.TRIP_INSURANCE_DETAILS,
    component: () => require('../Travelnsurance/details/TravelInsuranceDetails').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.SME_DETAIL,
    component: () => require('../SmeDetails/SmeDetailsContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.MAP,
    component: () => require('../Map/Redux/HolidayMapContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.GROUPING,
    component: () => require('../Grouping/Containers/HolidayGroupingContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.GROUPING_FILTERS,
    component: () => require('../Grouping/Containers/HolidayGroupingFilterContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.DETAIL,
    component: () => require('../PhoenixDetail/Containers/PhoenixDetailCommonContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.HOTEL_LISTING_OLD,
    component: () => require('../ListingHotelNew/HolidaysHotelListingNew').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.HOTEL_FILTER_LIST,
    component: () => require('../FilterHotels/FilterHotelList/FilterList').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.HOTEL_POPULAR_SECTION,
    component: () => require('../FilterHotels/FilterHotelList/PopularSection').default,
    reduxOptions,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.HOTEL_PROPERTY_TYPE,
    component: () =>
      require('../FilterHotels/FilterHotelList/PropertyType/PropertyTypeList').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.HOTEL_PRICE_RANGE,
    component: () => require('../FilterHotels/FilterHotelList/PriceRange/PriceRangeList').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.QUOTES_LISTING_PAGE,
    component: () => require('../MimaPreSales/QuotesListing').default,
    reduxOptions,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.COMPARE_QUOTES_PAGE,
    component: () => require('../MimaPreSales/CompareQuotes').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY,
    component: () => require('../PhoenixDetail/Components/Gallery/GridGallery').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY_V2,
    component: () => require('../PhoenixDetail/Components/Gallery/GridGalleryV2').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY_FULL_PAGE,
    component: () => require('../PhoenixDetail/Components/Gallery/FullPageGallery').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.PHOENIX_DETAIL_OVERLAY,
    component: () => require('../PhoenixDetail/Containers/PhoenixDetailOverlayContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.FLIGHT_LISTING,
    component: () => require('../PhoenixDetail/Containers/HolidaysFlightOverlayContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.FLIGHT_DETAIL,
    component: () => require('../PhoenixDetail/Components/FlightDetailPage/FlightDetail').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.ACTIVITY_LISTING,
    component: () => require('../PhoenixDetail/Containers/PhoenixActivityOverlayContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.TRAVEL_TIDBITS,
    component: () => require('../PhoenixDetail/Containers/TravelTidbitsOverlayContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.PACKAGE_ADD_ONS,
    component: () => require('../PhoenixDetail/Containers/PackageAddOnsOverlayContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL,
    component: () => require('../PhoenixDetail/Components/ActivityOverlay/ActivityDetail').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_V2,
    component: () =>
      require('../PhoenixDetail/Components/ItineraryV2/Activity/ActivityDetailPage').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_RATE_PLAN_V2,
    component: () =>
      require('../PhoenixDetail/Components/ItineraryV2/Activity/ActivityDetailPage/ActivityOptionRatePlan')
        .default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.SIGHTSEEING_DETAIL,
    component: () =>
      require('../PhoenixDetail/Components/SightSeenDetail/SightSeeingDetail').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.TRANSFER_LISTING,
    component: () => require('../PhoenixDetail/Components/TransferListingPage').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.TRANSFER_DETAIL,
    component: () => require('../PhoenixDetail/Components/TransferDetailPage').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.HOTEL_LISTING,
    component: () =>
      require('../PhoenixDetail/Components/PhoenixHotelsListing/PhoenixHotelsListing').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.HOTEL_LISTING_SEARCH,
    component: () =>
      require('../PhoenixDetail/Components/PhoenixHotelsListing/HotelListing/SearchAutoComplete')
        .default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.HOTEL_DETAIL,
    component: () =>
      require('../PhoenixDetail/Containers/PhoenixDetailHotelFullPageContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.HOTEL_REVIEWS_PAGE,
    component: () => require('../PhoenixDetail/Components/ReviewRating/index.js').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.HOTEL_HOUSE_RULES_PAGE,
    component: () =>
      require('../PhoenixDetail/Components/HotelDetailPage/HouseRules/HouseRulesExpand').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.COMBO,
    component: () => require('../PhoenixDetail/Components/combo/index').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.COMMUTE_DETAIL_PAGE,
    component: () => require('../PhoenixDetail/Components/combo/CommuteDetailsPage/index').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.PHOENIX_DETAIL_MAP_PAGE,
    component: () => require('../PhoenixDetail/Containers/PhoenixDetailMapPageContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.REVIEW,
    component: () => require('../Review/Containers/HolidayReviewContainer').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.THANK_YOU,
    component: () => require('../PostPayment/Components/HolidayThankYou').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.QUERY_FORM,
    component: () => require('../Query/Components/HolidayQueryForm').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.CALENDAR,
    component: () => require('../Calender/MmtHolidayCalender').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.WEB_VIEW,
    component: () => require('@mmt/legacy-commons/Common/Components/WebViewWrapper').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.MIMA_PRESALES_EDIT_DETAIL_PAGE,
    component: () => require('../MimaPreSales/DetailsPage/MimaPreSalesEditDetailPage').default,
    reduxOptions,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.GALLERY,
    component: () => require('../Gallery/GalleryPage').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.REFER_AND_EARN_PAGE,
    component: () => require('../ReferAndEarn').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.HOLIDAYS_REFERRALS_PAGE,
    component: () => require('../ReferAndEarn/HolidaysReferral').default,
    reduxOptions,
    onNavigationStateChange,
  },
  {
    key: HOLIDAY_ROUTE_KEYS.VISA_PROTECTION_DETAILS,
    component: () => require('../Common/Components/VisaProtectionPlan/VPPDetailPage').default,
    reduxOptions,
    onNavigationStateChange,
  }
];

export default holidayRouteConfig;
