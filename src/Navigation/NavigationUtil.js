import {HOLIDAY_ROUTE_KEYS} from './holidayPageKeys';

export default class NavigationUtil {
  static instance = null;

  static getInstance() {
    if (NavigationUtil.instance == null) {
      NavigationUtil.instance = new NavigationUtil();
    }
    return NavigationUtil.instance;
  }

  /**
   * returns URL and STATE.
   * */
  resolveUrlAndState(key, params) {
    switch (key) {
      case HOLIDAY_ROUTE_KEYS.REVIEW:
        return this.getReviewPageUrlAndState(params);
      case HOLIDAY_ROUTE_KEYS.THANK_YOU:
        return this.getThankYouPageUrlAndState(params);
      case HOLIDAY_ROUTE_KEYS.LISTING:
        return this.getListingPageUrlAndState(params);
      case HOLIDAY_ROUTE_KEYS.GROUPING:
        return {
          url: '/holidays/international/group',
          state: params,
        };
      case HOLIDAY_ROUTE_KEYS.QUOTES_LISTING_PAGE:
        return {
          url: '/holidays/psm/quotes/listing',
          state: params,
        };
      case HOLIDAY_ROUTE_KEYS.COMPARE_QUOTES_PAGE:
        return {
          url: '/holidays/psm/quotes/compare',
          state: params,
        };
      case HOLIDAY_ROUTE_KEYS.DETAIL:
        return this.getDetailPageUrlAndState(params);
      case HOLIDAY_ROUTE_KEYS.QUERY_FORM:
        return this.getQueryFormUrlAndState(params);
      case HOLIDAY_ROUTE_KEYS.GROUPING_FILTERS:
        return {
          url: '/holidays/international/searchWidget',
          state: params,
        };
      default: return {url: '', state: {}};
    }
  }

  getListingPageUrlAndState(params) {
    const {holidaysListingData, holidaysDeepLinkListingData} = params;
    if (holidaysListingData) {
      const {packageIds, destinationCity, departureCity, defDuration, defPkgId, showFab, cmp, lastPage, pt, metaTags, aff, dest}
          = holidaysListingData;
      let query = '';
      if (dest) {
        query = `dest=${dest}`;
      } else if (destinationCity) {
        query += `dest=${destinationCity}`;
      }
      if (packageIds) {
        query += `&packageIds=${packageIds}`;
      }
      if (departureCity) {
        query += `&depCity=${departureCity}`;
      }
      if (defDuration) {
        query += `&defDuration=${defDuration}`;
      }
      if (defPkgId) {
        query += `&defPackageId=${defPkgId}`;
      }
      if (showFab) {
        query += `&showfab=${showFab}`;
      }
      if (cmp) {
        query += `&cmp=${cmp}`;
      }
      if (lastPage) {
        query += `&lastPage=${lastPage}`;
      }
      if (metaTags) {
        let metaTagQueryParam = '';
        for (let i = 0; i < metaTags.length; i += 1) {
          if (i !== 0) {
            metaTagQueryParam += ',';
          }
          metaTagQueryParam += `theme_${metaTags[i]}`;
        }
        query += `&METATAG=${metaTagQueryParam}`;
      }
      if (pt) {
        query += `&pt=${pt}`;
      }
      if (aff) {
        query += `&aff=${aff}`;
      }
      return {
        url: `/holidays/international/search?${query}`,
        state: params,
      };
    } else {
      const {packageIds, destinationCity, departureCity, defDuration, defPkgId, showFab, cmp, lastPage, pt, metaTags, aff}
        = JSON.parse(holidaysDeepLinkListingData);
      let query = '';
      if (destinationCity) {
        query += `&dest=${destinationCity}`;
      }
      if (packageIds) {
        query += `&packageIds=${packageIds}`;
      }
      if (departureCity) {
        query += `&depCity=${departureCity}`;
      }
      if (defDuration) {
        query += `&defDuration=${defDuration}`;
      }
      if (defPkgId) {
        query += `&defPackageId=${defPkgId}`;
      }
      if (showFab) {
        query += `&showfab=${showFab}`;
      }
      if (cmp) {
        query += `&cmp=${cmp}`;
      }
      if (lastPage) {
        query += `&lastPage=${lastPage}`;
      }
      if (metaTags) {
        let metaTagQueryParam = '';
        for (let i = 0; i < metaTags.length; i += 1) {
          if (i !== 0) {
            metaTagQueryParam += ',';
          }
          metaTagQueryParam += `theme_${metaTags[i]}`;
        }
        query += `&METATAG=${metaTagQueryParam}`;
      }
      if (pt) {
        query += `&pt=${pt}`;
      }
      if (aff) {
        query += `&aff=${aff}`;
      }
      return {
        url: `/holidays/international/search?${query}`,
        state: params,
      };
    }
  }
  getDetailPageUrlAndState(params) {
    const {openMimaPreSales = false, openMimaPreSalesEditDetail = false } = params;

    if (openMimaPreSales || openMimaPreSalesEditDetail){
      return this.getDetailUrlAndStateForPsm(params);
    }

    const {packageId, categoryId, departureDetail, cmp, pt, aff, fromSeo} = params?.holidaysDetailData || {};
    const {departureCity, departureDate} = departureDetail;
    let query = `id=${packageId}`;
    if (departureCity) {
      query += `&fromCity=${departureCity}`;
    } else {
      query += '&fromCity=New Delhi';
    }
    if (categoryId) {
      query += `&category=${categoryId}`;
    }
    if (departureDate) {
      query += `&depDate=${departureDate}`;
    }
    if (cmp) {
      query += `&cmp=${cmp}`;
    }
    if (pt) {
      query += `&pt=${pt}`;
    }
    if (aff) {
      query += `&aff=${aff}`;
    }
    if (fromSeo) {
      query += '&fromSeo=true';
    }
    query += '&pkgType=FIT';
    return {
      url: `/holidays/international/package?${query}`,
      state: {holidaysDetailData: params.holidaysDetailData},
    };
  }

  getDetailUrlAndStateForPsm = params => {
    const {openMimaPreSales = false, openMimaPreSalesEditDetail = false } = params;
    const {quoteRequestId} = params?.holidaysDetailData || {};
    let query = `quoteRequestId=${quoteRequestId}`;
    const url = openMimaPreSales
        ? `/holidays/psm/quotes/detail?${query}`
        : `/holidays/psm/quotes/edit?${query}`;

    return {
      url,
      state: params,
    };
  }

  getReviewPageUrlAndState(params) {
    const {requestId, pt, aff} = params;
    if (requestId) {
      let query = `requestId=${requestId}`;
      if (pt) {
        query += `&pt=${pt}`;
      }
      if (aff) {
        query += `&aff=${aff}`;
      }
      return {
        url: `/holidays/reArchBookingReviewAndPaymentAction!openReviewPage?${query}`,
        state: params,
      };
    } else {
      return {
        url: '/holidays/reArchBookingReviewAndPaymentAction',
        state: params,
      };
    }
  }
  getThankYouPageUrlAndState(params) {
    const {payId} = params;
    if (payId) {
      return {
        url: `/holidays/onlineBookingPaymentThankyouAction?PayId=${payId}`,
        state: params,
      };
    } else {
      return {
        url: '/holidays/onlineBookingPaymentThankyouAction',
        state: params,
      };
    }
  }
  getQueryFormUrlAndState(params) {
    const {destination, branch, query, pageName} = params;
    let queryParam = query;
    if (!queryParam) {
      if (destination) {
        queryParam = `dest=${destination}`;
      }
      if (pageName) {
        queryParam += `&pageName=${pageName}`;
      }
      if (branch) {
        queryParam += `&branch=${branch}`;
      }
      queryParam += '&nodl=true';
    }
    if (queryParam) {
      return {
        url: `/holidays/sendQuery30!packageSendQueryForm?${queryParam}`,
        state: params,
      };
    } else {
      return {
        url: '/holidays/sendQuery30!packageSendQueryForm',
        state: params,
      };
    }
  }
}


