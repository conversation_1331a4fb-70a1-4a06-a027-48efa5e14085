import { createStore, combineReducers, applyMiddleware, compose } from 'redux';
import thunk from 'redux-thunk';
import holidaysReducers from './holidaysReducers';
import { Observable } from 'redux';

// Optional: Add any middleware specific to Holidays
const middlewares = [thunk];

// Create a root reducer that combines all the holiday-specific reducers
const rootReducer = combineReducers({
  ...holidaysReducers
});

// Set up store enhancers (middleware, dev tools, etc.)
const composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;
const enhancer = composeEnhancers(applyMiddleware(...middlewares));

// Create the store
const holidaysStore = createStore(rootReducer, enhancer);

// Add Symbol.observable method for compatibility with react-redux Provider
if (typeof Symbol === 'function' && Symbol.observable) {
  holidaysStore[Symbol.observable] = function () {
    return {
      subscribe: (observer) => {
        observer.next(this.getState());
        const unsubscribe = this.subscribe(() => {
          observer.next(this.getState());
        });
        return { unsubscribe };
      },
      [Symbol.observable]() {
        return this;
      }
    };
  };
}

export default holidaysStore; 