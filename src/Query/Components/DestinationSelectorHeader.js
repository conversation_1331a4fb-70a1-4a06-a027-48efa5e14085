import React, { useRef } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import PropTypes from 'prop-types';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import SearchBar from '@Frontend_Ui_Lib_App/SearchBar';
import AnchorBtn from 'mobile-holidays-react-native/src/Common/Components/AnchorBtn';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss.js';
import { CLEAR } from '../../SearchWidget/SearchWidgetConstants';
import iconBack from '@mmt/legacy-assets/src/img/iconBack.webp';
import { marginStyles, paddingStyles } from '../../Styles/Spacing';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

const destinationSelectorHeaderDefaultPropBackHandler = () => { };

const DestinationSelectorHeader = (props) => {
  props = {
    ...props,
    placeholder: typeof props.placeholder === "undefined" ? '' : props.placeholder,
    showLoading: typeof props.showLoading === "undefined" ? false : props.showLoading,
    backHandler: typeof props.backHandler === "undefined" ? destinationSelectorHeaderDefaultPropBackHandler : props.backHandler
  };

  const inputRef = useRef(null);
  const { onChangeText, placeholder, showLoading, backHandler, onClear } = props;

  const onClearHandler = () => {
    if (inputRef.current) {
      inputRef.current.clear();
    }
    onClear();
  };
  const renderLoaderView = () => (
    <View style={[AtomicCss.flexRow, { ...marginStyles.mr12 }]}>
      {showLoading &&
        <Spinner
          size={25}
          strokeWidth={2}
          progressPercent={85}
          speed={1.5}
          color={colors.black}
        />
      }
      <View style={[AtomicCss.pushRight]}>
        <AnchorBtn label={CLEAR} handleClick={onClearHandler} />
      </View>
    </View>
  );

  const renderBackButton = () => (
    <TouchableOpacity style={styles.backWrapper} onPress={backHandler}>
      <Image style={styles.iconBack} source={iconBack} />
    </TouchableOpacity>
  )
  return (
    <SearchBar
      onChangeText={onChangeText}
      placeholder={placeholder}
      placeholderTextColor={holidayColors.disableGrayBg}
      leftComponent={renderBackButton()}
      rightComponent={renderLoaderView()}
      inputRef={inputRef}
      isEditable
      keyboardType="default"
      maxLength={50}
      inputProps={{ autoFocus: true, autoCorrect: false }}
      customStyles={{
        leftIconStyle: styles.iconBack,
        containerStyle: styles.contentContainer,
        inputStyle: styles.inputStyle,
      }}
    />
  );
}


DestinationSelectorHeader.propTypes = {
  onChangeText: PropTypes.func.isRequired,    // Function to handle text change
  placeholder: PropTypes.string,              // Placeholder text
  showLoading: PropTypes.bool,                // Whether to show a loading indicator
  backHandler: PropTypes.func,                // Function to handle back button press
  onClear: PropTypes.func.isRequired,         // Function to handle clear action
};

const styles = StyleSheet.create({
  contentContainer: {
    backgroundColor: colors.activeBg,
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.lightBlue2,
    ...marginStyles.mh16,
    height: 50,
    ...Platform.select({ android: { ...marginStyles.mt10 } }),
    ...paddingStyles.ph0,
    flex: 1,
  },
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
    justifyContent: 'center',
  },
  textInput: {
    flex: 1,
  },
  clearIcon: {
    position: 'relative',
    right: 10,
    height: 20,
    width: 20,
  },
  clearText: {
    color: colors.black,
    fontFamily: fonts.bold,
    fontSize: 10,
    paddingRight: 10,
  },
  clearContainer: {
    marginTop: -10,
    paddingTop: 12,
  },
  inputStyle: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
  },
  backWrapper: {
    ...paddingStyles.pa16,
  },
  iconBack: {
    height: 16,
    width: 16,
    tintColor: holidayColors.black,
  },
});

export default DestinationSelectorHeader;
