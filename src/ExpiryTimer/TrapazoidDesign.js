import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { borderRadiusValues } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import timerIcon from '@mmt/legacy-assets/src/timerIcon.webp';

const TrapazoidDesign = ({ label, days, hours, minutes, seconds }) => {
  return (
    <View style={styles.timerBox}>
      <Image source={timerIcon} style={styles.timerIconStyle} />
      <Text style={styles.textGray}>{label}</Text>
      <View style={styles.container}>
        <Text style={styles.timerText}>{days}d:</Text>
        <Text style={styles.timerText}>{hours}h:</Text>
        <Text style={styles.timerText}>{minutes}m:</Text>
        <Text style={styles.timerText}>{seconds}s</Text>
      </View>
      <View style={styles.triangle} />
    </View>
  );
};

const styles = StyleSheet.create({
  timerBox: {
    backgroundColor: holidayColors.fadedRed,
    width: '75%',
    borderBottomLeftRadius: borderRadiusValues.br8,
    ...paddingStyles.pv8,
    ...paddingStyles.pl12,
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1,
  },
  textGray: {
    ...fontStyles.labelSmallRegular,
    ...marginStyles.mr6,
    color: holidayColors.gray,
    lineHeight: 15,
  },
  timerIconStyle: {
    width: 12,
    height: 12,
    ...marginStyles.mr8,
  },
  triangle: {
    width: 0,
    height: 0,
    borderLeftWidth: 15,
    borderRightWidth: 15,
    borderTopWidth: 33,
    borderStyle: 'solid',
    borderLeftColor: holidayColors.transparent,
    borderRightColor: holidayColors.transparent,
    borderTopColor: holidayColors.fadedRed,
    position: 'absolute',
    right: -16,
    top: 0,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timerText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.red,
    lineHeight: 15,
    ...marginStyles.mr4,
  },
});

export default TrapazoidDesign;
