import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import { StyleSheet, Text } from 'react-native';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

const ChipDesign = ({label, days, hours, minutes, seconds}) => {

  return (
    <LinearGradient
      style={styles.container}
      colors={['#FF7F3F', '#FF3E5E']}
      start={{
        x: 0.0,
        y: 0.0,
      }}
      end={{
        x: 1.0,
        y: 0.0,
      }}>
      <Text style={styles.timer}> {label} </Text>
      {days > 0 ? <Text style={styles.timer}>{days}d : </Text> : []}
      {hours > 0 ? <Text style={styles.timer}>{hours}h : </Text> : []}
      {minutes > 0 ? <Text style={styles.timer}>{minutes}m : </Text> : []}
      <Text style={styles.timer}>{seconds}s</Text>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 14.4,
    flexDirection: 'row',
    height: 22,
    paddingHorizontal: 10,
  },
  timer: {
    ...fontStyles.labelSmallBold,
    lineHeight: 19,
    color: 'rgb(255,255,255)',
  },
});

export default ChipDesign;
