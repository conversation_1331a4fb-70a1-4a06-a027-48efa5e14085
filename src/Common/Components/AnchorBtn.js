import React from 'react';
import { StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import TextButton from './Buttons/TextButton';

const AnchorBtn = ({ label, handleClick, disable }) => {
  const activeBtnOpacity = 0.7;
  const btnStyle = [styles.text];
  if (disable) {
    btnStyle.push(styles.disable);
  }

  return (
    <TextButton
      buttonText={label}
      handleClick={handleClick}
      btnTextStyle={btnStyle}
      btnWrapperStyle={styles.linkContainer}
    />
  );
};

AnchorBtn.propTypes = {
  label: PropTypes.string.isRequired,
  handleClick: PropTypes.func.isRequired,
  disable: PropTypes.bool,
};

const styles = StyleSheet.create({
  linkContainer: {
    paddingLeft: 10,
  },
  text: {
    ...fontStyles.labelSmallBold,
    letterSpacing: 0.23,
    color: holidayColors.primaryBlue,
  },
  disable: {
    color: '#b7b7b7',
  },
});

export default AnchorBtn;
