import React from 'react';
import { View, TouchableOpacity, Text, Image, StyleSheet } from 'react-native';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

const FullWidthSearchBox = ({ onPress, icon, title, value, containerStyles, titleStyles, iconStyles, children = null }) => {
  return (
    <TouchableOpacity onPress={onPress} style={[styles.searchSectionRow, containerStyles]}>
        <Image style={[styles.image, iconStyles]} source={icon} />
        <View style={AtomicCss.flex1}>
          <Text style={[styles.searchHeader, titleStyles]}>{title}</Text>
          <Text style={styles.searchValue} numberOfLines={1} ellipsizeMode="tail">
            {value}
          </Text>
        </View>
        {children}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  searchSectionRow: {
    width: '100%',
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    backgroundColor: holidayColors.lightGray2,
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.mb8,
    ...paddingStyles.ph10,
    ...paddingStyles.pv6,
    ...marginStyles.mt2,
    ...AtomicCss.flexRow,
    ...AtomicCss.alignCenter,
  },
  searchHeader: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.lightGray,
  },
  searchValue: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  image: {
    width: 24,
    height: 24,
    ...marginStyles.mr10,
  },
});

export default FullWidthSearchBox;
