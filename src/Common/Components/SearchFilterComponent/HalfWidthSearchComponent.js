import React from 'react';
import { TouchableOpacity, View, Image, Text, StyleSheet } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const HalfWidthSearchBox = ({ onPress, title, value, icon, titleStyles, iconStyles, containerStyles}) => {
  return (
    <TouchableOpacity style={[AtomicCss.flex1]} onPress={onPress}>
      <View
        style={[styles.searchSection, AtomicCss.flexRow, AtomicCss.alignCenter, containerStyles]}
      >
        <Image style={[styles.image, iconStyles]} source={icon} />
        <View style={AtomicCss.flex1}>
          <Text style={[styles.searchHeader, titleStyles]}>{title}</Text>
          <Text style={styles.searchValueSmall} numberOfLines={1} ellipsizeMode="tail">
            <Text>{value}</Text>
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  image: {
    width: 20,
    height: 20,
    ...marginStyles.mr10,
  },
  searchSection: {
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    backgroundColor: holidayColors.lightGray2,
    ...marginStyles.mb8,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.ph12,
    ...paddingStyles.pv6,
  },
  searchHeader: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.lightGray,
    ...marginStyles.mb4,
  },
  searchValue: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  searchValueSmall : {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
});

export default HalfWidthSearchBox;
