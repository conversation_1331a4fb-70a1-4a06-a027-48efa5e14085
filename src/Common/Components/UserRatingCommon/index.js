import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';

const UserRating = ({ratingValue = 0, containerStyle = {}}) => {
  let rating = 0;
  if (ratingValue && typeof ratingValue === 'string') {
    rating = Number(ratingValue);
  } else {
    rating = ratingValue;
  }

  return (
    <View style={[styles.ratingTag, {...containerStyle}]}>
      <Text style={styles.ratingValue}>{rating}</Text>
      <Text style={styles.ratingOutOf}> / 5</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  ratingTag: {
    ...paddingStyles.pa4,
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.mr8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: holidayColors.darkBlue,
  },
  ratingValue: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
  },
  ratingOutOf: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.white,
  },
});

export default UserRating;
