import { overlayActionTypes } from './CommonOvelayAction';

interface OverlayState {
  overlayMap: { [key: string]: boolean };
  overlayDataMap: { [key: string]: any };
}

interface OverlayAction {
  type: string;
  key?: string;
  data?: any;
  overlays?: string | string[];
}

const initialState: OverlayState = {
  overlayMap: {},
  overlayDataMap: {},
};

const holidaysCommonOverlays = (state = initialState, action: OverlayAction) => {
  let newOverlayMap = { ...state.overlayMap };
  let newOverlayDataMap = { ...state.overlayDataMap };
  switch (action.type) {
    case overlayActionTypes.SHOW_OVERLAYS:
      if (action.key) {
        newOverlayMap[action.key] = true;
        newOverlayDataMap[action.key] = action.data;
      }
      return {
        overlayMap: newOverlayMap,
        overlayDataMap: newOverlayDataMap,
      };
    case overlayActionTypes.HIDE_OVERLAYS:
      if (action.overlays) {
        const overlaysToHide = Array.isArray(action.overlays) ? action.overlays : [action.overlays];
        overlaysToHide.forEach((item: string) => {
          if (item) {
            delete newOverlayMap[item];
            delete newOverlayDataMap[item];
          }
        });
      }
      return {
        overlayMap: newOverlayMap,
        overlayDataMap: newOverlayDataMap,
      };
    case overlayActionTypes.CLEAR_OVERLAYS:
      return initialState;
    default:
      return state;
  }
};

export default holidaysCommonOverlays;
