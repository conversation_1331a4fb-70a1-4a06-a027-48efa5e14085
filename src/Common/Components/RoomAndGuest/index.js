import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Image,
  Platform,
  TouchableOpacity,
} from 'react-native';
import PropTypes from 'prop-types';
import cloneDeep from 'lodash/cloneDeep';
import { fonts, statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { isEmpty } from 'lodash';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import {
  calculateInfantsForPhoenix,
  createRoomDetailsFromRoomDataForPhoenix,
} from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/HolidayDetailUtils';
import { MAX_ROOM_TRAVELLER_LIMIT, MAX_TRAVELLER_COUNT } from 'mobile-holidays-react-native/src/PhoenixDetail/DetailConstants';
import { showAlert } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import { validateGuest } from 'mobile-holidays-react-native/src/utils/HolidayNetworkUtils';

/* Components */
import PageHeader from 'mobile-holidays-react-native/src/Common/Components/PageHeader';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';
import RoomCards from './RoomCards';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';
import { showNewRoomAndGuest } from '../../../utils/HolidaysPokusUtils';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';


export default class TravellerPage extends React.PureComponent {
  // static navigationOptions = { header: null };

  constructor(props) {
    super(props);
    this.state = {
      activeRoom: 0,
      roomData: this.props.roomData,
      initialRooms: cloneDeep(this.props.roomData),
      showError: false,
      errorText: '',
      showLoader: false,
    };
    this.scrollViewRef = null;
  }

  increaseRoom = () => {
    if (this.validateAges() === false) {
      return;
    }
    let totalCount = this.getTravellerCount();
    if (totalCount < this.state.travellerCount) {
      totalCount = this.state.travellerCount;
    }
    {
      /* removed as these limits checks will be handled by backend */
    }
    this.trackClickEvents();
    this.updateRoomData();
  };

  trackClickEvents = () => {
    this.props?.trackPDTV3Event?.({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: 'add_room',
    });
    this.props.trackClickEvent('add_room');
  };

  setLoaderStatus = (showError, errorText, showLoader) => {
    this.setState({ showError, errorText, showLoader });
  };

  updateRoomData() {
    const tempRoomData = cloneDeep(this.state.roomData);
    tempRoomData.push({
      adultCount: 0,
      childCount: 0,
      infantCount: 0,
      childAgeArray: [],
    });
    this.setState({
      activeRoom: tempRoomData.length - 1,
      roomData: tempRoomData,
    });
  }

  closedCardClick = (index) => {
    this.setState({ activeRoom: index });
  };
  removeRoom = (index) => {
    const tempRoomData = cloneDeep(this.state.roomData);
    tempRoomData.splice(index, 1);
    this.setState({
      roomData: tempRoomData,
      activeRoom: tempRoomData.length - 1,
    });
    this.props?.trackPDTV3Event?.({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: 'remove_room',
    });
  };
  changeCount = (obj, index) => {
    const tempRoomData = cloneDeep(this.state.roomData);
    tempRoomData[index] = obj;
    this.setState({ roomData: tempRoomData });
  };

  goBack = () => {
    const { initialRooms } = this.state || {};
    this.props.handleTravellerChange(initialRooms, null, true);
  };

  increaseAdultCount = () => {
    const index = this.state.activeRoom;
    const tempRoomData = cloneDeep(this.state.roomData);
    tempRoomData[index].adultCount++;
    const room = tempRoomData[index];
    if(showNewRoomAndGuest()) {
      if (this.validateRoomData([room])) {
        this.setState({ roomData: tempRoomData });
      }
    } else {
    if (this.validateRoomData(tempRoomData)) {
      this.setState({ roomData: tempRoomData });
    }
  }
  };

  decreaseAdultCount = () => {
    const index = this.state.activeRoom;
    const tempRoomData = cloneDeep(this.state.roomData);
    tempRoomData[index].adultCount--;
    const room = tempRoomData[index];
    if(showNewRoomAndGuest()) {
      if (this.validateRoomData([room],true)) {
        this.setState({ roomData: tempRoomData });
      }
    } else {
       if (this.validateRoomData(tempRoomData, true)) {
      this.setState({ roomData: tempRoomData });
    }
  }
  };

  increaseChildCount = () => {
    const index = this.state.activeRoom;
    const tempRoomData = cloneDeep(this.state.roomData);
    const room = tempRoomData[index];
    room.childCount++;
    room.childAgeArray.push(!this?.props?.fromLanding ? { age: 0, bedRequired: true } : { age: 0 });
    room.infantCount = calculateInfantsForPhoenix(room.childAgeArray);
    if(showNewRoomAndGuest()) {
      if (this.validateRoomData([room])) {
        this.setState({ roomData: tempRoomData });
      }
    } else {
      if (this.validateRoomData(tempRoomData)) {
        this.setState({ roomData: tempRoomData });
      }
    }
    
  };

  decreaseChildCount = () => {
    const index = this.state.activeRoom;
    const tempRoomData = cloneDeep(this.state.roomData);
    const room = tempRoomData[index];
    room.childCount--;
    room.childAgeArray.pop();
    room.infantCount = calculateInfantsForPhoenix(room.childAgeArray);
    if (showNewRoomAndGuest()) {
      if (this.validateRoomData([room], true)) {
        this.setState({ roomData: tempRoomData });
      }
    } else {
      if (this.validateRoomData(tempRoomData, true)) {
        this.setState({
          roomData: tempRoomData,
        });
      }
    }
  };

  setAge = (childIndex, age, bedRequired) => {
    const index = this.state.activeRoom;
    const tempRoomData = cloneDeep(this.state.roomData);
    const room = tempRoomData[index];
    room.childAgeArray[childIndex].age = age;
    if (!this.props?.fromLanding) {
      room.childAgeArray[childIndex].bedRequired = bedRequired;
    }
    room.infantCount = calculateInfantsForPhoenix(room.childAgeArray);
    if(showNewRoomAndGuest()) {
      if (this.validateRoomData([room])) {
        this.setState({ roomData: tempRoomData });
      }
    } else {
      if (this.validateRoomData(tempRoomData)) {
        this.setState({ roomData: tempRoomData });
      }
    }
  };

  validateRoomData = (roomData, decreasing = false) => {
    const { packagePaxDetail } = this.props || {};
    let totalTravellers = 0;
    const { maxPaxAllowed, maxAdultAllowed = 4, maxChildAllowed = 4 } = packagePaxDetail || {};
    const maximumGuestsAllowedPerRoom =
      maxPaxAllowed && maxPaxAllowed > 0 ? maxPaxAllowed : MAX_ROOM_TRAVELLER_LIMIT;

      /* Validating each room in the array roomData */
    for (let i = 0; i < roomData.length; i++) {
      const room = roomData[i];
      if (room.adultCount <= 0 || room.childCount < 0 || room.infantCount < 0) {
        return false;
      }

      if (this.props.flightCount > 0 && room.adultCount < room.infantCount) {
        showAlert('Infants cannot be more than adults');
        return false;
      }

      const { adultCount, childCount } = room || {};
      const travellers = adultCount + childCount;
      if (
        adultCount > maxAdultAllowed ||
        childCount > maxChildAllowed ||
        travellers > maximumGuestsAllowedPerRoom
      ) {
        return false;
      }
      totalTravellers += travellers;
    }

    return true;
  };

  validateAges = () => {
    for (let i = 0; i < this.state.roomData.length; i += 1) {
      const room = this.state.roomData[i];
      if (room.adultCount === 0) {
        showAlert('Select atleast 1 Adult in room ' + (i + 1));
        return false;
      }
      if (
        room.childAgeArray.length < room.childCount ||
        room.childAgeArray.some((item) => item.age === 0)
      ) {
        showAlert(`Select Age in room ${i + 1}`);
        return false;
      }
    }
    return true;
  };

  getErrorView = () => {
    const { showError, errorText } = this.state || {};
    if (showError && !isEmpty(errorText)) {
      setTimeout(() => {
        this.scrollViewRef.scrollTo({ x: 0, y: 9800, animated: true });
      }, 50);
      setTimeout(() => {
        this.setState({ showError: false });
      }, 3000);
      return (
        <View style={styles.errorView}>
          <Text style={styles.errorViewText}>{errorText}</Text>
        </View>
      );
    } else {
      return [];
    }
  };

  render() {
    const changeCallbacks = {
      increaseAdultCount: this.increaseAdultCount,
      increaseChildCount: this.increaseChildCount,
      decreaseAdultCount: this.decreaseAdultCount,
      decreaseChildCount: this.decreaseChildCount,
      setAge: this.setAge,
    };
    const { packagePaxDetail } = this.props || {};
    this.validToApplyOrAdd();
    return (
      <View style={styles.container}>
        <PageHeader
          showBackBtn
          showShadow
          title={'Select Rooms & Guests'}
          onBackPressed={this.goBack}
          containerStyles={styles.travelHeader}
        />
        <ScrollView
          ref={(ref) => (this.scrollViewRef = ref)}
          onContentSizeChange={() => this.scrollViewRef.scrollToEnd({ animated: true })}
          style={styles.roomsInfo}
        >
          {this.state.roomData?.map((room, index) => {
            return (
              <RoomCards
                removeRoom={this.removeRoom}
                adultCount={room.adultCount}
                childCount={room.childCount}
                childAgeArray={room.childAgeArray}
                closedCardClick={this.closedCardClick}
                changeCallbacks={changeCallbacks}
                totalTravellerCount={this.getTravellerCount()}
                active={this.state.activeRoom}
                changeCount={this.changeCount}
                index={index}
                key={index}
                flightCount={this.props.flightCount}
                packagePaxDetail={packagePaxDetail}
                trackClickEvent={this.props.trackClickEvent}
                roomCount={this?.state?.roomData?.length}
                trackPDTV3Event={this.props?.trackPDTV3Event}
              />
            );
          })}
        </ScrollView>
        {this.state.showError && this.getErrorView()}
        {this.canAddAnotherRoom() &&
          (this.validToApplyOrAdd() ? (
            <TouchableOpacity style={styles.addRoomContainer} onPress={this.increaseRoom}>
              <Text style={styles.addRoom}>+ ADD ANOTHER ROOM </Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.addRoomContainer}>
              <Text style={styles.addRoomDisabled}>+ ADD ANOTHER ROOM </Text>
            </View>
          ))}
        {this.state.showLoader ? (
          <View style={styles.spinnerWrap}>
            <Spinner
            size={25}
            strokeWidth={2}
            progressPercent={90}
            speed={1.5}
            color={holidayColors.lightGray}
            />
          </View>
      
        ) : (
          <PrimaryButton
            isDisabled={!this.validToApplyOrAdd()}
            buttonText={'APPLY'}
            handleClick={this.submitTravellerContent}
            btnContainerStyles={marginStyles.mh10}
          />
        )}
      </View>
    );
  }

  validToApplyOrAdd = () => {
    return this.state.roomData?.every((item) => item && item.adultCount);
  };
  canAddAnotherRoom = () => {
    if (this.props?.packagePaxDetail?.maxRoomCount) {
      if (this.props?.packagePaxDetail?.maxRoomCount <= this.state.roomData.length) {
        return false;
      }
    }
    return true;
  };

  getTravellerCount = () => {
    let totalCount = 0;
    for (let index = 0; index < this.state.roomData.length; index += 1) {
      totalCount += this.state.roomData[index].childCount;
      totalCount += this.state.roomData[index].adultCount;
    }
    return totalCount;
  };

  submitTravellerContent = () => {
    // Clear and hide error message from screen any
    this.setLoaderStatus(false, '', true);

    if (this.validateAges() === false) {
      this.setLoaderStatus(false, '', false);
      return;
    }
    const { packagePaxDetail, packageId, selectedDate } = this.props || {};
    const { roomData } = this.state || {};

    const roomDetails = createRoomDetailsFromRoomDataForPhoenix(roomData, packagePaxDetail);
    if (packageId) {
      validateGuest(roomDetails, packageId, selectedDate)
        .then((res) => {
          const { statusCode, success, error } = res || {};
          if (statusCode === 1 && success) {
            //Handle success case.
            this.props.handleTravellerChange(roomData, packagePaxDetail);
          } else {
            const { message } = error || {};
            this.setLoaderStatus(true, message, false);
          }
        })
        .catch((err) => {
          this.setLoaderStatus(true, err, false);
        });
    } else {
      this.props.handleTravellerChange(roomData, packagePaxDetail);
    }
  };
}

TravellerPage.propTypes = {
  flightCount: PropTypes.number.isRequired,
  handleTravellerChange: PropTypes.func.isRequired,
  roomData: PropTypes.array.isRequired,
};

const styles = StyleSheet.create({
  travelHeader: {
    zIndex: 2,
    elevation: 3,
    ...Platform.select({
      ios: {
        ...marginStyles.mt30,
      },
    }),
  },
  container: {
    flex: 1,
    backgroundColor: holidayColors.white,
  },
  addRoom: {
    ...fontStyles.labelBaseBold, 
    color: holidayColors.primaryBlue,
    ...marginStyles.mv16,
  },
  addRoomDisabled: {
    ...fontStyles.labelBaseBold, 
    color: holidayColors.lightGray,
    ...marginStyles.mv16,
  },
  backWrapper: {
    paddingRight: 15,
    paddingLeft: 18,
    paddingVertical: 15,
  },
  addRoomContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  roomsInfo: {
    paddingHorizontal: 15,
    flex: 1,
    paddingTop: 15,
  },
  errorView: {
    backgroundColor: '#ffd3d4',
    flexDirection: 'row',
    height: 44,
    alignItems: 'center',
    marginBottom: 0,
    elevation: 2,
    shadowColor: '#330000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: {
      width: 1,
      height: 1,
    },
    borderRadius: 4,
    justifyContent: 'center',
    paddingHorizontal: 10,
  },
  errorViewText: {
    color: holidayColors.red,
    fontSize: 12,
    fontFamily: fonts.regular,
    marginEnd: 40,
  },
  spinnerWrap: {
    alignItems:"center",
  }
});
