import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Image,
  FlatList,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

const ChildAgeSection = (props) => {
  const { bedRequired, activeSorter } = props || {};
  const currentChildIndex = props.index;
  const pad = (n) => (n < 10 ? `0${n}` : n);

  const handleChange = (value) => {
    const { maximumChildAge, packagePaxDetail = {} } = props || {};
    const { childWithBed = false } = packagePaxDetail || {};
    const selectedChildAge = parseInt(value);
    const bedRequired = childWithBed && (selectedChildAge >= maximumChildAge || props.bedRequired); // bed to required true if age is more than max age (getting from backend as minChildAgeForBedCompulsory)
    props.handleChange(currentChildIndex, selectedChildAge, bedRequired);
  };

  const getDefaultIndex = () => (activeSorter > 0 ? activeSorter - 1 : -1);

  const ageOptions = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11'];

  const renderAgeOptions = ({ item, index }) => {
    const handleOnPress = () => {
      handleChange(item);
    };
    const isActive = props.activeSorter - 1 === index;
    return (
      <TouchableOpacity onPress={handleOnPress} key={`item-${index}`}>
        <View
          style={[styles.childlist, isActive ? { backgroundColor: holidayColors.primaryBlue } : []]}
        >
          <Text style={[styles.childlistText, isActive ? { color: holidayColors.white } : []]}>
            {item}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {ageOptions?.map((item, index) => renderAgeOptions({ item, index }))}
      </ScrollView>
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    height: 45,
  },
  childlist: {
    height: 40,
    width: 40,
    marginRight: 5,
    marginLeft: 2,
    marginTop: 2,
    backgroundColor: 'white',
    justifyContent: 'center',
    borderRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    elevation: 10,
  },
  childlistText: {
    textAlign: 'center',
    alignItems: 'center',
    ...fontStyles.labelSmallRegular,
  },
});

export default ChildAgeSection;
