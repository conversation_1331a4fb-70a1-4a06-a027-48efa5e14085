import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import PropTypes from 'prop-types';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { getPluralString } from '@mmt/legacy-commons/Common/utils/StringUtils';

const ClosedCard = (props) => {
  const pad = (n) => (n < 10 ? `0${n}` : n);

  const activeBtnOpacity = 0.7;
  const { index, adultCount, childCount, removeRoom, closedCardClick } = props;
  return (
    <TouchableOpacity activeOpacity={1} style={styles.roomCardClosed}>
      <View style={[styles.marginBottom4, styles.flexRow]}>
        <Text style={styles.roomHeading}>ROOM {index + 1}</Text>
        <TouchableOpacity onPress={() => removeRoom(index)} activeOpacity={activeBtnOpacity}>
          <Text style={styles.removeBtn}>REMOVE</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.infoContainer}>
        <Text style={styles.count}>{adultCount}{' '}</Text>
        <Text style={[styles.label, styles.marginRight5]}>
          {getPluralString({ singular: 'Adult', plural: 'Adults', none: 'Adult' })(adultCount)}
          {childCount > 0 && ','}
        </Text>
        {childCount > 0 && <Text style={styles.count}> {childCount}</Text>}
        {childCount > 0 && <Text style={styles.label}> Child{''}{childCount > 1 ? 'ren' : ''}</Text>}
        <TouchableOpacity onPress={() => closedCardClick(index)} style={styles.marginEdit}>
          <Text style={[styles.label, styles.edit]}>Edit</Text>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

ClosedCard.propTypes = {
  index: PropTypes.number.isRequired,
  adultCount: PropTypes.number.isRequired,
  childCount: PropTypes.number.isRequired,
  closedCardClick: PropTypes.func.isRequired,
  removeRoom: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  marginEdit: { padding: 5 },
  edit: { color: holidayColors.primaryBlue },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  roomCardClosed: {
    backgroundColor: holidayColors.white,
    margin: 1,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    paddingHorizontal: 22,
    paddingVertical: 20,
    borderRadius: 4,
    elevation: 2,
    shadowColor: '#330000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: {
      width: 1,
      height: 1,
    },
    marginBottom: 25,
  },
  roomHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
    marginBottom: 10,
    marginRight: 'auto',
  },
  removeBtn: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    marginTop: 2,
  },
  count: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  label: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  flexRow: { flexDirection: 'row' },

  marginRight5: { marginRight: 5 },
});

export default ClosedCard;
