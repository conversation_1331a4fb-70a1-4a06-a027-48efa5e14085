import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import React, { useEffect, useState } from 'react';
import { Image, StyleSheet, Text, TextInput, TouchableOpacity, View, Platform } from 'react-native';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import Header from '@Frontend_Ui_Lib_App/Header';
import SearchBar from '@Frontend_Ui_Lib_App/SearchBar';
import { getLeftBackIcon, isIosClient } from '../../../utils/HolidayUtils';
import { CLEAR } from '../../../SearchWidget/SearchWidgetConstants';
import AnchorBtn from '../AnchorBtn';
import iconBack from '@mmt/legacy-assets/src/img/iconBack.webp';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { isGiAffiliate } from '../../../theme';
import { capitalizeText } from 'mobile-holidays-react-native/src/utils/textTransformUtil';

export const pageHeaderBackArrow = require('@mmt/legacy-assets/src/backArrowAndroid.webp');

export const PageHeaderBackButton = ({ onBackPressed, iconSource, iconStyles = {} }) => {
  return (
    <TouchableOpacity onPress={onBackPressed} style={styles.backIconContainer}>
      <Image
        style={[styles.iconBack, iconStyles]}
        source={iconSource ? iconSource : pageHeaderBackArrow}
      />
    </TouchableOpacity>
  );
};
export const PageHeaderTitle = (props) => {
  const { title, titleStyles, numberOfLines = 1 } = props || {};
  return (
    !!title && (
      <Text style={[styles.title, titleStyles]} numberOfLines={numberOfLines}>
        {capitalizeText(title)}
      </Text>
    )
  );
};
const PageHeader = ({
  showBackBtn = false,
  showShadow = false,
  iconSource = '',
  title = '',
  subTitle = '',
  onBackPressed = () => {},
  titleStyles = {},
  subTitleStyles = {},
  containerStyles = {},
  SubTitleComponent = null,
  numberOfLines = 1,
  headingContainerStyles = {},
  leftComponent = null,
  showSearch = false,
  onTextChange = () => { },
  searchTextPlaceholder = 'Search...',
  activeTabIndex,
  headerWrapperStyle
}) => {
  const leftIcon = getLeftBackIcon({ onBackPress: onBackPressed });
  const [isSearchBoxVisible, setSearchBoxVisible] = useState(false);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    clearSearch();
    setSearchBoxVisible(false);
  }, [activeTabIndex]);

  useEffect(() => {
    onTextChange(searchText);
  }, [searchText]);

  const clearSearch = () => {
    setSearchText('');
  };

  const toggleSearch = () => {
    if (isSearchBoxVisible) {
      clearSearch();
    }
    setSearchBoxVisible(!isSearchBoxVisible);
  };
  const rightIcons = [
    {
      icon: require('@mmt/legacy-assets/src/search.webp'),
      onPress: toggleSearch,
      customStyles: {
        iconStyle: styles.iconSearch
      }
    }
  ];

  const renderBackButton = () => (
    <TouchableOpacity style={styles.searchBackWrapper} onPress={toggleSearch}>
      <Image style={styles.searchBackIcon} source={iconBack} />
    </TouchableOpacity>
  )
  const renderClearButton = () => {
    return (
      <>
      {searchText &&
        <View style={{...marginStyles.mr12 }}><AnchorBtn label={CLEAR} handleClick={clearSearch} /></View>
      }
      </>
    )
  }
  

  return (
    <View style={[styles.titleContainer, containerStyles]}>
      {/* Search box */}
      {isSearchBoxVisible ? (
        <SearchBar
        inputValue={searchText}
        onChangeText={setSearchText}
        placeholder={searchTextPlaceholder}
        placeholderTextColor={holidayColors.disableGrayBg}
        leftComponent={renderBackButton()}
        rightComponent={renderClearButton()}
        isEditable
        keyboardType="default"
        maxLength={50}
        inputProps={{ autoFocus: true }}
        customStyles={{
          containerStyle: styles.searchContainer,
          inputStyle: styles.searchInput,
        }}
      />
      ) : (
        <Header
          customStyles={{
            descriptionStyle: [styles.subTitle, subTitleStyles],
            titleStyle: [styles.title, titleStyles],
              wrapperStyle: [styles.wrapperStyle, headerWrapperStyle],
            iconStyle: styles.iconSearch,
            leftWrapperStyle: { marginLeft : showBackBtn ? 0 : 16 },
            bodyContainerStyle:isIosClient() ? styles.btnContainerStyle : {},
          }}
          description={!!subTitle && <Text>{subTitle}</Text>}
          leftIcon={showBackBtn && leftIcon}
          rightIcons={showSearch && !isSearchBoxVisible && (rightIcons)}
          title={capitalizeText(title)}
          titleNoOfLines={numberOfLines}
          leftComponent={leftComponent}
        />
      )}
    </View>
  );
};

export const shadowStyle = StyleSheet.create({
  shadow: {
    shadowColor: holidayColors.black,
    backgroundColor: holidayColors.white,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 3,
  },
});

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  wrapperStyle: {
    width: '100%',
  },
  backIconContainer: {
    ...paddingStyles.pr20,
  },
  backWrapper: {
    ...paddingStyles.pr20,
    ...paddingStyles.pv16,
  },
  title: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
  },
  subTitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  iconBack: {
    height: 24,
    width: 24,
    tintColor: holidayColors.lightGray,
  },
  headingContainer: {
    flex: 1,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.primaryBlue,
    padding: Platform.OS === 'android' ? 1 : 5,
    ...marginStyles.mh10,
    ...marginStyles.mv4,
    height: 50,
    backgroundColor: holidayColors.lightBlueBg,
    ...paddingStyles.ph0,
  },
  searchInput: {
    flex: 1,
    color: holidayColors.black,
    ...fontStyles.labelMediumBold,
    ...paddingStyles.pv0,
    ...marginStyles.mr12,
  },
  iconSearch: {
    height: 24,
    width: 24,
    tintColor: holidayColors.primaryBlue,
  },
  iconClear: {
    height: 20,
    width: 20,
    tintColor: holidayColors.primaryBlue,
  },
  iconCross: {
    height: 20,
    width: 20,
  },
  searchIconContainer: {
    paddingRight: 20,
  },
  searchBackWrapper: {
    ...paddingStyles.pa16,
  },
  searchBackIcon: {
    height: 16,
    width: 16,
    tintColor: holidayColors.black,
  },
  btnContainerStyle:{
    alignItems:'center',
    marginRight:'10%'
  }
});
export default PageHeader;
