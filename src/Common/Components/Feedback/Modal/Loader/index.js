import React from 'react';
import { SafeAreaView, View , Text } from 'react-native';
import PropTypes from 'prop-types';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

import styles from './styles';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const Loader = ({ loaderText = '' }) => {
  return (
      <View style={styles.container}>
        <View style={styles.loaderContainer}>
        
          <Spinner
            size={25}
            strokeWidth={2}
            progressPercent={85}
            speed={1.5}
            color={holidayColors.darkBlue5}
        />
          {!!loaderText && <Text style={styles.loaderText}>{loaderText}</Text>}
        </View>
      </View>
  );
};

export default Loader;

Loader.propTypes = {
  loaderText: PropTypes.string,
};
