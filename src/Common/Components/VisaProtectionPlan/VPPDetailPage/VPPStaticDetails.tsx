import React from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import InfoCards from './InfoCards';
import { useApi } from 'mobile-holidays-react-native/src/CustomHooks';
import { getAddonStaticDetail } from 'mobile-holidays-react-native/src/utils/HolidayNetworkUtils';
import FilterLoader from 'mobile-holidays-react-native/src/SearchWidget/Components/FilterLoader';
import { CTAS_KEY, VPP_SECTIONS_ON_KEY } from '../VPPConstant';
import { VPPStaticDetailsProps, InfoCardProps } from './VPPStaticDetailType';

const renderItemSeperator = () => {
  return <View style={styles.itemSeperator} />;
};

const VPPStaticDetails = (props: VPPStaticDetailsProps) => {
  const { addonType, addonSubType, trackLocalClickEvent ,trackEvent } = props;
  const additionalRequestObj = {
    addonType,
    addonSubType,
    sections: VPP_SECTIONS_ON_KEY[CTAS_KEY.KNOW_MORE],
  };
  const { data, success, loading } = useApi({
    getUrlAndRequestObj: getAddonStaticDetail,
    additionalRequestParams: additionalRequestObj,
  });

  const filterLoaderStyle = { backgroundColor: 'rgba(0,0,0,0)' };
  if (loading) {
    return (
      <FilterLoader
        showCenterLoader={true}
        loadingFirstTime={false}
        show={loading}
        style={filterLoaderStyle}
      />
    );
  }

  const renderItem = ({ item }: { item: InfoCardProps }) => (
    <InfoCards {...item} trackLocalClickEvent={trackLocalClickEvent} isBottomSheet={false} trackEvent={trackEvent}/>
  );

  return (
    <View style={styles.contentContainer}>
      {success && !loading && (
        <FlatList
          data={data?.addonStaticDetails}
          renderItem={renderItem}
          keyExtractor={(item, index) => `${item.title}_${index}`}
          ItemSeparatorComponent={renderItemSeperator}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    ...marginStyles.ma16,
  },
  itemSeperator: {
    height: 16,
  },
  cardContainer: {
    ...holidayBorderRadius.borderRadius8,
    elevation: 1,
    ...paddingStyles.pa16,
    backgroundColor: holidayColors.white,
    overflow: 'hidden',
  },
});

export default VPPStaticDetails;
