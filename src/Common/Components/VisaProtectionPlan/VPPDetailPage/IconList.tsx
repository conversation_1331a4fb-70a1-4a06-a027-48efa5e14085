import React from 'react';
import { FlatList, TextStyle } from 'react-native';
import { StyleSheet, View } from 'react-native';
import IconPoints from '../IconPoints';
import { holidayColors } from '../../../../Styles/holidayColors';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { ListItemProps, listItems } from './VPPStaticDetailType';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

const getFontStyleforBottomSheet = (isBottomSheet: boolean) => {
  return isBottomSheet
    ? {
        ...fontStyles.labelBaseRegular,
      }
    : {
        ...fontStyles.labelSmallRegular,
      };
}


const IconList = (props: listItems) => {
  const renderItem = ({ item }: { item: ListItemProps }) => (
    <IconPoints {...item} textStyle={{ ...styles.textStyle, ...getFontStyleforBottomSheet(!!props.isBottomSheet) } as TextStyle} />
  );
  return (
    <View style={styles.wrapper}>
      <FlatList data={props.listItems} renderItem={renderItem} />
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    ...paddingStyles.pt4,
    ...paddingStyles.ph16,
    ...paddingStyles.pb12,
  },
  textStyle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  } as TextStyle,
});

export default IconList;
