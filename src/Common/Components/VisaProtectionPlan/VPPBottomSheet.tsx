import React, { useCallback } from 'react';
import BottomSheetOverlay from 'mobile-holidays-react-native/src/Common/Components/BottomSheetOverlay';
import { Dimensions, FlatList, StyleSheet, View } from 'react-native';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import InfoCards from './VPPDetailPage/InfoCards';
import { getAddonStaticDetail } from 'mobile-holidays-react-native/src/utils/HolidayNetworkUtils';
import { useApi } from 'mobile-holidays-react-native/src/CustomHooks';
import FilterLoader from 'mobile-holidays-react-native/src/SearchWidget/Components/FilterLoader';
import { VPPBottomSheetProps } from './VPPTypes';
import {
  CTAS_KEY,
  VPP_SECTIONS_ON_KEY,
  VPP_TRACKING_ACTIONS,
  VPP_TRACKING_EVENT,
} from './VPPConstant';
import PrimaryButton from '../Buttons/PrimaryButton';
import { removeKeyFromData, vppCaptureReviewClickEvents } from './VPPUtils';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';

const VPPBottomSheet = (props: VPPBottomSheetProps) => {
  const { toggleBottomSheet = () => {}, addonSubType, addonType, trackLocalClickEvent } = props;
  const { data, loading, error } = useApi({
    getUrlAndRequestObj: getAddonStaticDetail,
    additionalRequestParams: {
      sections: VPP_SECTIONS_ON_KEY[CTAS_KEY.READ_BEFORE_YOU_BUY],
      addonSubType,
      addonType,
    },
  });

  const onToggleModal = useCallback(() => {
    vppCaptureReviewClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      trackLocalClickEvent,
      eventName: VPP_TRACKING_EVENT + VPP_TRACKING_ACTIONS.CLOSE,
      suffix: CTAS_KEY.READ_BEFORE_YOU_BUY,
    });
    toggleBottomSheet(false);
  }, [toggleBottomSheet, trackLocalClickEvent]);
  const { addonStaticDetails = [] } = data || {};
  const title = error ? 'Oops! Something went wrong.' : addonStaticDetails?.[0]?.title;
  return (
    <BottomSheetOverlay
      title={title}
      toggleModal={onToggleModal}
      visible={true}
      onDismiss={onToggleModal}
      childStyle={styles.childStyle}
      containerStyles={styles.bottomSheetStyle}
    >
      {loading ? (
        <FilterLoader
          showCenterLoader={true}
          loadingFirstTime={false}
          show={loading}
          style={styles.loaderStyle}
        />
      ) : error ? (
        <View style={styles.errorStyle}>
          <PrimaryButton
            buttonText={'Try Again Later'}
            handleClick={onToggleModal}
            isDisable={false}
          />
        </View>
      ) : (
        <FlatList
          data={removeKeyFromData(addonStaticDetails, 'title')}
          renderItem={({ item }) => (
            <InfoCards
              footer={item.footer}
              listConfig={item.listConfig}
              listItems={item.listItems}
              trackLocalClickEvent={trackLocalClickEvent}
              isBottomSheet={true}
              onToggleModalBottomSheet={onToggleModal}
            />
          )}
        />
      )}
    </BottomSheetOverlay>
  );
};

const styles = StyleSheet.create({
  bottomSheetStyle: {
    ...paddingStyles.pa16,
  },
  errorStyle: {
    ...paddingStyles.pa20,
  },
  childStyle: {
    maxHeight: Dimensions.get('window').height - 200,
  },
  loaderStyle: {
    backgroundColor: 'rgba(0,0,0,0)',
  }
});

export default VPPBottomSheet;
