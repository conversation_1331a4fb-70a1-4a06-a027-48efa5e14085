import React from 'react';
import { View, Text, StyleSheet, TextStyle, ImageStyle, ViewStyle } from 'react-native';
import { HeaderProps, TagProps } from './VPPTypes';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import LinearGradient from 'react-native-linear-gradient';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { VPP_COLORS } from './VPPConstant';
import HolidayImageHolder from '../HolidayImageHolder';

const Tag: React.FC<TagProps> = ({ tag }) => {
  const linearGradientColor = VPP_COLORS.tagLinearGradientColor;
  return (
    <LinearGradient
      useAngle
      angle={30}
      colors={linearGradientColor}
      style={styles.tagLinearGradientStyle}
    >
      <Text style={styles.tagStyle}>{tag}</Text>
    </LinearGradient>
  );
};

interface IconFillProps { 
  icon: string;
  iconStyle?: ViewStyle;
  linearGradientColors?: string[];
}

const IconFill = (props: IconFillProps) => {
  return (
    <LinearGradient
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
      colors={props.linearGradientColors || ['#3023AE', '#C86DD7']}
      style={styles.linearGradient}
    >
      <HolidayImageHolder
        imageUrl={props.icon}
        style={props.iconStyle}
        defaultImage={null}
        resizeMode="cover"
        containerStyles={{}}
      />
    </LinearGradient>
  );
};

const Header: React.FC<HeaderProps> = ({
  heading,
  subHeading,
  icon,
  tag,
  headingStyle,
  subHeadingStyle,
  iconStyle,
  linearGradientColors,
  isIconFill = false,
}) => {
  return (
    <View style={styles.wrapper}>
      {isIconFill ? (
        <IconFill icon={icon || ''} iconStyle={iconStyle} linearGradientColors={linearGradientColors}/>
      ) : (
        <HolidayImageHolder
          imageUrl={icon}
          style={iconStyle}
          defaultImage={null}
          resizeMode="cover"
          containerStyles={{}}
        />
      )}
      <View style={styles.headerDetails}>
        <View style={styles.upperWrapper}>
          {heading && <Text style={headingStyle}>{heading}</Text>}
          {tag && <Tag tag={tag} />}
        </View>
        {subHeading && <Text style={subHeadingStyle}>{subHeading}</Text>}
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
  },
  linearGradient: {
    height: 20,
    width: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  upperWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...marginStyles.mb4,
  },
  headerDetails: {
    flex: 1,
    ...paddingStyles.pl12,
  },
  tagLinearGradientStyle: {
    ...holidayBorderRadius.borderRadius16,
    alignItems: 'center',
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
  },
  iconStyleNew: {
    height: 16,
    width: 16,
  } as ImageStyle,
  tagStyle: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
    textAlign: 'center',
    ...paddingStyles.ph8,
  } as TextStyle,
});

export default Header;
