import GenericModule from "@mmt/legacy-commons/Native/GenericModule";
import { CTA_ACTIONS, DESELECT_BOTTOMSHEET_DATA, LIST_CONFIG_TYPE, UPDATE_BOTTOMSHEET_DATA, VPP_POPUPS, VISA_PROTECTION_PLAN_DATA, INSURANCE_DATA } from "./VPPConstant";
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from "mobile-holidays-react-native/src/Navigation";
import { ctaItemProps, VPPDetailPageProps, VPPBottomSheetProps } from "./VPPTypes";
import { VPPDataProps } from "./VPPDetailPage/VPPStaticDetailType";
import { logHolidayReviewPDTClickEvents } from "mobile-holidays-react-native/src/PhoenixReview/Utils/HolidayReviewPDTTrackingUtils";
import { logHolidaysGroupingPDTEvents } from "mobile-holidays-react-native/src/PhoenixGroupingV2/Utils/PhoenixGroupingV2PDTTrackingUtils";
import { PDT_EVENT_TYPES } from "mobile-holidays-react-native/src/utils/HolidayPDTConstants";
import HolidayDataHolder from "../../../utils/HolidayDataHolder";
import { PHOENIX_GROUPING_V2_PAGE_NAMES } from "mobile-holidays-react-native/src/PhoenixGroupingV2/Contants";
import { holidayNavigationPush } from "mobile-holidays-react-native/src/PhoenixDetail/Utils/DetailPageNavigationUtils";
import { openGenericDeeplink } from "mobile-holidays-react-native/src/utils/HolidayUtils";
import { COMMON_OVERLAYS } from "../CommonOverlay";
import { REVIEW_PDT_PAGE_NAME } from "mobile-holidays-react-native/src/Review/HolidayReviewConstants";
import { isEmpty } from "lodash";

export interface handleCtaClickProps {
    vppBottomSheetProps?: VPPBottomSheetProps;
    vppDetailPageProps?: VPPDetailPageProps;
    item?: ctaItemProps;
}

export const handleCtaClick = {
    [CTA_ACTIONS.OPEN_MODAL]: ({ vppBottomSheetProps }: handleCtaClickProps) => {
        vppBottomSheetProps?.toggleBottomSheet?.(true);
    },
    [CTA_ACTIONS.OPEN_OVERLAY]: ({ vppDetailPageProps }: handleCtaClickProps) => {
        holidayNavigationPush({
              overlayKey: [COMMON_OVERLAYS.VPP_OVERLAY],
              pageKey: HOLIDAY_ROUTE_KEYS.VISA_PROTECTION_DETAILS,
              showOverlay: vppDetailPageProps?.showOverlay,
              hideOverlays: vppDetailPageProps?.hideOverlays,
              props: vppDetailPageProps,
        });
    },
    [CTA_ACTIONS.OPEN_LINK]: ({ item, }: handleCtaClickProps) => {
        const link = item?.link || '';
        openGenericDeeplink({url: link});
    }
};

export const handlePopUP = (prevSelectionStatus: boolean, currentSelectionStatus: boolean) => {
    switch (true) {
        case prevSelectionStatus && !currentSelectionStatus:
            return VPP_POPUPS.DESELECT_BOTTOMSHEET;
        case !prevSelectionStatus && currentSelectionStatus:
            return VPP_POPUPS.PAGE_FOOTER;
        default:
            return '';
    }
}

export const ActionBottomsheetData = {
    [VPP_POPUPS.UPDATE_BOTTOMSHEET]: UPDATE_BOTTOMSHEET_DATA,
    [VPP_POPUPS.DESELECT_BOTTOMSHEET]: DESELECT_BOTTOMSHEET_DATA,
    [VPP_POPUPS.VISA_PROTECTION_PLAN]: VISA_PROTECTION_PLAN_DATA,
    [VPP_POPUPS.INSURANCE]: INSURANCE_DATA
}

interface vppCaptureClickEventsProps {
    trackLocalClickEvent: (name: string, suffix: string, props: { prop1: string }) => void;
    eventName: string;
    suffix?: string;
    prop1?: string;
    actionType?: {
        [key: string]: string;
    };
    subPageName?: string;

}

const vppPDTClickEvents = (props: vppCaptureClickEventsProps) => {
    const instance = HolidayDataHolder.getInstance() as HolidayDataHolder | null;
    const currentPage = instance && 'getCurrentPage' in instance ? instance.getCurrentPage() : undefined;
    const prevPageName = instance ? instance.getPrevPageName(currentPage) : '';
    if (prevPageName === PHOENIX_GROUPING_V2_PAGE_NAMES.PAGE_NAME) {
        logHolidaysGroupingPDTEvents({
            value: props.eventName + props.suffix,
            actionType: props.actionType || PDT_EVENT_TYPES.buttonClicked,
            subPageName: props.subPageName,
            shouldTrackToAdobe:isEmpty(props.actionType)
        });
    } else {
        logHolidayReviewPDTClickEvents({
            actionType: props.actionType || PDT_EVENT_TYPES.buttonClicked,
            value: props.eventName + props.suffix,
            subPageName: props.subPageName,
            shouldTrackToAdobe:isEmpty(props.actionType)
        });
    }
}

export const vppCaptureReviewClickEvents = (props: vppCaptureClickEventsProps) => {
    try {
        const { trackLocalClickEvent = () => {}, eventName = '', suffix = '', prop1 = '' } = props;
        vppPDTClickEvents(props);
        trackLocalClickEvent(eventName, suffix, { prop1 });
    } catch (e) {
        console.error('Something went wrong while tracking logging data in vppCaptureReviewClickEvents ' + e);
    }
};
export interface removeKeyFromDataProps {
    data: VPPDataProps[];
    [key: string]: any;
}
export const removeKeyFromData = (data: removeKeyFromDataProps[], key: string) => {
    if (data?.length === 0) return [];
    return data.map((item, index) => {
        if (index === 0) {
            const { [key]: _, ...rest } = item;
            return rest;
        }
        return item;
    });
};
