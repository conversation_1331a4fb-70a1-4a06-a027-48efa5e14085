import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import React from 'react';

import { ImageStyle, StyleSheet, TextStyle, View, Text } from 'react-native';
import { CardDetailProps } from '../VPPTypes';
import Header from '../Header';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import BenefitsDetail from './BenefitsDetails';
import ActionDetails from './ActionDetails';

const CardDetails: React.FC<CardDetailProps> = (props: CardDetailProps) => {
  const { cardHeader, benefitsDetail, showCtas, ctas, handleCtaClick } = props;
  return (
    <View style={styles.wrapper}>
      <Header
        {...cardHeader}
        headingStyle={styles.headingStyle}
        iconStyle={styles.iconStyle}
        subHeadingStyle={styles.subHeadingStyle}
      />
      <BenefitsDetail {...benefitsDetail} />
      {showCtas && <ActionDetails ctas={ctas} handleCtaClick={handleCtaClick} />}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'column',
    ...marginStyles.ma16,
  },
  headingStyle: {
    color: holidayColors.black,
    ...fontStyles.labelBaseBlack,
  } as TextStyle,
  iconStyle: {
    height: 20,
    width: 16,
    resizeMode: 'contain',
  } as ImageStyle,
  subHeadingStyle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  } as TextStyle,
});

export default CardDetails;
