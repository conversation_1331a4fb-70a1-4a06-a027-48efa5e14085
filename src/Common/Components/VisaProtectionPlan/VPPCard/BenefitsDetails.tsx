import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { BenefitsDetailProps } from '../VPPTypes';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import IconPoints from '../IconPoints';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const BenefitsDetail: React.FC<BenefitsDetailProps> = ({ benefits }) => {
  return (
    <View style={styles.wrapper}>
      {benefits.map((benefit, index) => {
        const { heading, icon } = benefit;
        return <IconPoints key={index} text={heading} icon={icon} textStyle={styles.textStyle} />;
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    ...marginStyles.mt8,
  },
  textStyle: {
    color: holidayColors.gray,
  },
});

export default BenefitsDetail;
