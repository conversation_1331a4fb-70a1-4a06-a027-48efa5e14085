import React from 'react';
import { StyleSheet, View, ImageStyle, TextStyle, Text } from 'react-native';
import { CardHeaderProps } from '../VPPTypes';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import Header from '../Header';

const CardHeader: React.FC<CardHeaderProps> = (props: CardHeaderProps) => {
  const {inclusionMessage=''}=props
  return (
    <View style={styles.wrapper}>
      <Header
        {...props}
        headingStyle={styles.headingStyle}
        subHeadingStyle={styles.subHeadingStyle}
        iconStyle={styles.iconStyle}
        isIconFill={true}
        linearGradientColors={['#3023AE', '#C86DD7']}
      />
      {!!inclusionMessage && <View style={styles.includedWrapper}>
        <View style={styles.includedContainer}>
          <Text style={styles.includedTitle}>{inclusionMessage}</Text>
        </View>
      </View>
      }
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    ...marginStyles.ma16,
    ...marginStyles.mb12,
  },
  headingStyle: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  } as TextStyle,
  iconStyle: {
    height: 16,
    width: 16,
  } as ImageStyle,
      subHeadingStyle: {
      ...fontStyles.labelSmallRegular,
      color: holidayColors.gray,
    } as TextStyle,
    includedWrapper: {
      alignItems: 'center',
      marginTop: 10,
      paddingLeft:26
    },
    includedContainer: {
      backgroundColor: holidayColors.greenlight,
      paddingVertical: 5,
      paddingHorizontal: 10,
      borderRadius: 24,
      alignSelf: 'flex-start',
    },
    includedTitle: {
      color: holidayColors.greenDark,
      ...fontStyles.labelSmallBlack,
    } as TextStyle,
});

export default CardHeader;
