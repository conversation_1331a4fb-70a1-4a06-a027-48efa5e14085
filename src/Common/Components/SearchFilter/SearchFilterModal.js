import { isEmpty, noop } from 'lodash';
import React, { Fragment, useMemo } from 'react';
import { StyleSheet, View, Text, Platform, TouchableOpacity } from 'react-native';
import {
  getFormattedDate,
} from '../../../Grouping/Components/ModifySearch/DateUtil';
import { createQuickFilterList } from '../../../SearchWidget/Components/PhoenixSearchPage/Utils';
import {
  FROM,
  TO,
} from '../../../SearchWidget/SearchWidgetConstants';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors, iconColors } from '../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';
import { RESIZE_MODE_IMAGE, SEARCH_FILTER_SECTIONS } from '../../../HolidayConstants';
import { IMAGE_ICON_KEYS, getOpitimsedImageUrl } from '../HolidayImageUrls';


/* Components */
import FiltersViewLanding from './FiltersViewLanding';
import FullWidthSearchComponent from 'mobile-holidays-react-native/src/Common/Components/SearchFilterComponent/FullWidthSearchComponent';
import HalfWidthSearchComponent from 'mobile-holidays-react-native/src/Common/Components/SearchFilterComponent/HalfWidthSearchComponent';
import PrimaryButton from '../Buttons/PrimaryButton';
import HolidayImageHolder from '../HolidayImageHolder';
import { enableSearchByImage } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';
import MembershipCarousel from '../Membership/Carousel/MembershipCarousel';
import { CDN_IMAGES } from '../../../utils/HolidayImageUrls';
const startlocation = { uri: CDN_IMAGES.START_LOCATION }
const tolocation = { uri: CDN_IMAGES.TO_LOCATION }

/* Icons */
const locationImg = require('@mmt/legacy-assets/src/holidays/locationIcon.webp');
const calendarImg = require('@mmt/legacy-assets/src/calendar.webp');
const paxImg = require('@mmt/legacy-assets/src/ic_user_gray.webp');

const SearchFilterModal = ({
  userDepCity,
  metaData,
  onFilterClicked,
  isFilterCTAExpanded,
  selectedQuickFilterIndex,
  openDepartureCity,
  selectedFilter,
  toggleCalendar,
  fromDateObj,
  destinationCity,
  search,
  paxDetails,
  togglePax,
  contentType,
  isFilter,
  isDefaultRoomDetail,
  isHeaderHide,
  searchFilterModalError,
  openImageSearch = () => {},
  onKnowMorePressMmtBlackMembership = noop,
  logHolidaysLandingPDTEvents = noop,
  trackMmtBlackClickEvent = noop,
  landingMMTBlackData
}) => {
  const { filters } = metaData || {};
  const openDepartureCityLocal = () => {
    openDepartureCity(FROM);
  };
  const openDestinationCity = () => {
    openDepartureCity(TO);
  };
  const toggleCalendarLocal = () => {
    toggleCalendar();
  };
  const getDisplayDate = () => {
    if (!fromDateObj) {
      return 'Add Travel Date';
    }
    return getFormattedDate(fromDateObj);
  };
  const getRoomDetails = () => {
    const { adult = 0, child: noOfChildren = 0, noOfInfants = 0, noOfRooms = 0} = paxDetails || {};
    const child = noOfChildren + noOfInfants;
    const adultText = `${adult} ${adult > 1 ? 'Adults' : 'Adult'}`;
    const childText = child > 1 ? 'Children' : 'Child';
    const childTextWithCount = child > 0 ? `, ${child} ${childText}` : '';
    const roomText = `, ${noOfRooms} ${noOfRooms > 1 ? 'Rooms' : 'Room'}`;
    return (isDefaultRoomDetail || adult === 0) ? 'Add Guests' : `${adultText}${childTextWithCount}${roomText}`;
  };

  const getRequiredFieldError = () => {
    return !isEmpty(metaData) && searchFilterModalError  && (
      <View style={styles.requiredField}>
        <Text style={styles.requiredFieldsText}>*Required Field</Text>
      </View>
    );
  };
  const togglePaxLocal = () => {
    togglePax();
  };
  const filtersList = useMemo(() =>  createQuickFilterList(filters, [], 'LANDING'), [filters]);
  const { landingSearchDest = false } = enableSearchByImage();
  return (
    <Fragment>
      <View style={[styles.wrapper, (isHeaderHide ? {marginTop: 10} : {})]}>
        <MembershipCarousel
          {...{
            onKnowMorePressMmtBlackMembership,
            logHolidaysLandingPDTEvents,
            trackMmtBlackClickEvent,
            landingMMTBlackData,
            contentType,
          }}
        />
        <FullWidthSearchComponent
          icon={startlocation}
          containerStyles={styles.startingFromContainer}
          onPress={openDepartureCityLocal}
          title={SEARCH_FILTER_SECTIONS.STARTING_FROM}
          value={!isEmpty(userDepCity) ? userDepCity : 'Select'}
        />
        <FullWidthSearchComponent
          icon={tolocation}
          onPress={openDestinationCity}
          title={SEARCH_FILTER_SECTIONS.TRAVELLING_TO}
          value={destinationCity && destinationCity.trim().length ? destinationCity : 'Select'}
          containerStyles={{ flexDirection: 'row' }}
        >
          {landingSearchDest && (
            <TouchableOpacity
              style={styles.cameraIconContainer}
              onPress={openImageSearch}
            >
              <HolidayImageHolder
                imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.CAMERA_ICON)}
                style={styles.cameraIcon}
                resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
              />
            </TouchableOpacity>
          )}
        </FullWidthSearchComponent>
        {!isEmpty(metaData) && (
          <View style={styles.wrapperDatePax}>
            <View style={styles.datePaxWrapper}>
              <View>
                <HalfWidthSearchComponent
                  onPress={toggleCalendarLocal}
                  value={getDisplayDate()}
                  title={SEARCH_FILTER_SECTIONS.STARTING_DATE}
                  icon={calendarImg}
                  iconStyles={{ tintColor: iconColors.searchFilterIcons }}
                  titleStyles={
                    searchFilterModalError && !fromDateObj ? { color: holidayColors.red } : {}
                  }
                />
              </View>
              {!fromDateObj && getRequiredFieldError()}
            </View>
            <View style={marginStyles.mh4} />
            <View style={{ flex: 1 }}>
              <HalfWidthSearchComponent
                onPress={togglePaxLocal}
                value={getRoomDetails()}
                title={SEARCH_FILTER_SECTIONS.ROOM_GUESTS}
                icon={paxImg}
                titleStyles={
                  searchFilterModalError && isDefaultRoomDetail ? { color: holidayColors.red } : {}
                }
              />
              {isDefaultRoomDetail && getRequiredFieldError()}
            </View>
          </View>
        )}

        <FiltersViewLanding
          showFilter={!isEmpty(metaData) && isFilter && filtersList.length > 0}
          onFilterClicked={onFilterClicked}
          isFilterCTAExpanded={isFilterCTAExpanded}
          selectedFilter={selectedFilter}
          selectedQuickFilterIndex={selectedQuickFilterIndex}
          filters={filters}
        />
        <PrimaryButton
          buttonText={'SEARCH'}
          btnContainerStyles={styles.btnContainer}
          handleClick={() => search(true)}
        />
      </View>
    </Fragment>
  );
};

export const styles = StyleSheet.create({
  wrapper: {
    zIndex:11,
    // marginTop: 66,
    width: '100%',
    backgroundColor:holidayColors.white,
    ...paddingStyles.ph16,
  },
  startingFromContainer: {
    marginTop: Platform.select({
      android: 2,
      ios: 4,
      default: 2,
    }),
  },
  searchSectionRow: {
    width: '100%',
    borderWidth: 1,
    borderColor: holidayColors.lightGrayBorder,
    backgroundColor: holidayColors.lightGray2,
    ...marginStyles.mb8,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.ph10,
    ...paddingStyles.pv10,
  },
  searchValue: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  locationImg: {
    width: 24,
    height: 24,
    ...marginStyles.mr10,
  },
  wrapperDatePax: {
    display: 'flex',
    flexDirection: 'row',
  },
  requiredField: {
    height: 20,
    width: '100%',
  },
  requiredFieldsText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.red,
    ...marginStyles.ml6,
  },
  btnContainer: {
    ...marginStyles.mt10,
    ...marginStyles.mb16,
  },
  datePaxWrapper:{
    display:'flex',
    flexDirection:'column',
    flex:1,
  },
  cameraIconContainer: {
    width: '15%',
    alignItems: 'center',
  },
  cameraIcon: {
    width: 30,
    height: 30,
  },
});
export default SearchFilterModal;
