import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { fontStyles } from '../../../Styles/holidayFonts';
import QuickFiltersHorizontalList from '../../../SearchWidget/Components/PhoenixSearchPage/QuickFiltersHorizontalList';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const FiltersViewLanding = ({
  filters,
  selectedFilter,
  onFilterClicked,
  isFilterCTAExpanded,
  selectedQuickFilterIndex,
  showFilter,
}) => {
  if (!showFilter) {
    return [];
  }

  return (
    <View>
      {filters?.length > 0 && (
        <Text style={filterStyles.filters}>
          CHOOSE FILTERS <Text style={fontStyles.labelSmallRegular}>(OPTIONAL)</Text>
        </Text>
      )}
      {filters?.length > 0 && (
        <View style={[marginStyles.mb6, marginStyles.mt2]}>
          <QuickFiltersHorizontalList
            fromLandingPage={true}
            landingFilters={{
              filters: selectedFilter,
              masterListingFilters: filters,
              filtersSize: selectedFilter?.length,
              filterType: 'QUICK_LANDING_FILTER',
            }}
            onQuickFilterClicked={onFilterClicked}
            onAllFilterClicked={onFilterClicked}
            isExpanded={isFilterCTAExpanded}
            selectedQuickFilterIndex={selectedQuickFilterIndex}
          />
        </View>
      )}
    </View>
  );
};

const filterStyles = StyleSheet.create({
  filters: {
    color: holidayColors.lightGray,
    ...fontStyles.labelSmallBold,
    ...marginStyles.mb6,
    ...paddingStyles.pl6,
    ...marginStyles.mt4,
  },
});

export default FiltersViewLanding;
