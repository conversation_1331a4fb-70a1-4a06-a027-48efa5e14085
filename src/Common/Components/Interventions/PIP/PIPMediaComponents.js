import React from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Text,
  Platform,
  StatusBar,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { isEmpty } from 'lodash';
import {
  borderRadiusValues,
  holidayBorderRadius,
} from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { MEDIA_TYPE, TEMPLATE_TYPES } from './constants';


/* Components */
import HolidayImageHolder from '../../HolidayImageHolder';
import VideoPlayer from '@mmt/legacy-commons/Common/Components/VideoPlayer';
import StoryContainer from '../../StoryComponent/StoryContainer';
import { RESIZE_MODE_IMAGE } from 'mobile-holidays-react-native/src/HolidayConstants';

export const PipGifComponent = ({ mediaDetails = {}, mediaStyle = {} }) => {
  const { thumbnailUrl = '', mediaUrl = '', redirectionUrl = '' } = mediaDetails;
  return <HolidayImageHolder imageUrl={thumbnailUrl || mediaUrl} style={mediaStyle} />;
};

export const PipImageComponent = ({ mediaDetails = {}, mediaStyle = {} }) => {
  const { thumbnailUrl = '', mediaUrl = '', redirectionUrl = '' } = mediaDetails;
  const url = thumbnailUrl || mediaUrl;
  return <HolidayImageHolder imageUrl={url} style={mediaStyle} from={'pip'} />;
};

export const PipVideoComponent = ({
  mediaStyle = {},
  mediaDetails = '',
  textDetails = {},
  templateType = '',
  ...rest
}) => {
  const { thumbnailUrl = '', mediaUrl = '', videoFrameUrl = '' } = mediaDetails;
  const { header = '' } = textDetails || {};
  if (!isEmpty(thumbnailUrl)) {
    return <PipImageComponent mediaDetails={{ thumbnailUrl }} mediaStyle={mediaStyle} {...rest} />;
  }

  const isVideoLandscapeWithText = !!header && templateType === TEMPLATE_TYPES.LANDSCAPE;
  const isVideoPortrait = templateType === TEMPLATE_TYPES.PORTRAIT;
  const containerBorderStyle = {
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    ...(!isVideoLandscapeWithText && {
      borderBottomRightRadius: 16,
      borderBottomLeftRadius: 16,
    }),
  };
  const parentStyle = isVideoLandscapeWithText
    ? { flex: 0, width: 140 }
    : isVideoPortrait
    ? { width: mediaStyle.width, height: 249 }
    : { width: mediaStyle.width, height: mediaStyle.height };

  return (
    <View style={[parentStyle, { ...holidayBorderRadius.borderRadius16, overflow: 'hidden' }]}>
      <VideoPlayer
        source={{ uri: mediaUrl }}
        videoPaused={false}
        disableSeekbar={true}
        renderBottomControls={false}
        resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
        poster={videoFrameUrl}
        posterResizeMode={RESIZE_MODE_IMAGE.CONTAIN}
        repeat={true}
        videoStyle={{
          width: '100%',
          height: '100%',
          ...containerBorderStyle,
        }}
        style={{
          ...containerBorderStyle,
          ...(isVideoLandscapeWithText && { height: 80 }),
          ...(videoFrameUrl && { backgroundColor: 'transparent' }),
        }}
        {...rest}
      />
      {!!header && (
        <PipMediaTextContent {...rest} textDetails={textDetails} templateType={templateType} />
      )}
    </View>
  );
};

export const PipStoryComponent = ({ mediaStyle = {}, mediaDetails = '', ...rest }) => {
  const { thumbnailUrl = '', mediaUrl = '', media = [], videoFrameUrl = '' } = mediaDetails;
  if (!isEmpty(thumbnailUrl)) {
    return <PipImageComponent mediaDetails={{ thumbnailUrl }} mediaStyle={mediaStyle} {...rest} />;
  }
  const { url, type, videoFrameUrl: mediaVideoFrameUrl = '' } = media?.[0] || {};

  switch (type) {
    case MEDIA_TYPE.IMAGE:
      return (
        <PipImageComponent mediaDetails={{ mediaUrl: url }} mediaStyle={mediaStyle} {...rest} />
      );
    case MEDIA_TYPE.VIDEO:
      return (
        <PipVideoComponent
          mediaDetails={{ mediaUrl: url, videoFrameUrl: videoFrameUrl || mediaVideoFrameUrl || '' }}
          mediaStyle={mediaStyle}
          {...rest}
        />
      );
  }
};

export const PipMediaTextContent = ({ textDetails, templateType }) => {
  const { header = '', headerColour = '' } = textDetails || {};
  if (isEmpty(textDetails) || isEmpty(header)) {
    return [];
  }

  const start = templateType === TEMPLATE_TYPES.PORTRAIT ? { x: 0.0, y: 1.0 } : { x: 0.0, y: 0.0 };
  const end = templateType === TEMPLATE_TYPES.PORTRAIT ? { x: 0.0, y: 0.0 } : { x: 1.0, y: 0.0 };
  const colors =
    templateType === TEMPLATE_TYPES.PORTRAIT
      ? [holidayColors.black, holidayColors.transparent]
      : [holidayColors.gray, holidayColors.black];
  const containerStyle =
    templateType === TEMPLATE_TYPES.PORTRAIT
      ? {
          position: 'absolute',
          bottom: 0,
          zIndex: 1,
        }
      : {};
  return (
    <LinearGradient
      start={start}
      end={end}
      colors={colors}
      style={[styles.textContainer, containerStyle]}
    >
      <Text
        numberOfLines={3}
        style={[styles.mediaText, { color: headerColour || holidayColors.white }]}
      >
        {header}
      </Text>
    </LinearGradient>
  );
};

export const PipMediaStoryPage = ({
  templateType,
  handleCloseStoryPage,
  openStoryPage,
  storyDetails,
  knowMoreSection,
  ...rest
}) => {
  return (
    openStoryPage && (
      <View style={storyPageStyles.parentContainer}>
        <View style={storyPageStyles.bottomSheetContainer}>
          <TouchableOpacity
            activeOpacity={1}
            style={storyPageStyles.overlay}
            onPress={handleCloseStoryPage}
          />
          <View style={[storyPageStyles.container]}>
            <StoryContainer
              {...rest}
              visible={true}
              storyDetails={storyDetails}
              knowMoreSection={knowMoreSection}
              onComplete={handleCloseStoryPage}
              containerStyle={{
                width: '100%',
                height: '100%',
                backgroundColor: holidayColors.black,
              }}
              templateType={templateType}
            />
          </View>
        </View>
      </View>
    )
  );
};
const styles = StyleSheet.create({
  container: {
    position: 'absolute',
  },
  image: {
    flex: 1,
    resizeMode: 'cover',
  },
  iconContainer: {
    position: 'absolute',
    top: 5,
    zIndex: 1,
    flexDirection: 'row',
    width: '100%',
  },
  iconContainerStyle: {
    backgroundColor: holidayColors.gray,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.pa2,
    ...marginStyles.ml6,
  },
  iconStyle: {
    width: 24,
    height: 24,
  },
  expandIconContainer: {
    ...marginStyles.mr6,
    marginLeft: 'auto',
  },
  textContainer: {
    width: '100%',
    justifyContent: 'center',
    ...paddingStyles.pa8,
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderBottomRightRadius: borderRadiusValues.br16,
  },
  mediaText: {
    ...fontStyles.labelBaseBold,
    ...marginStyles.mh4,
  },
  videoFrameImage: {
    width: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
    backgroundColor: 'black',
  },
});

const storyPageStyles = StyleSheet.create({
  parentContainer: {
    height: '100%',
    width: '100%',
    zIndex: 1,
    position: 'absolute',
  },
  bottomSheetContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: holidayColors.black,
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    flex: 1,
    justifyContent: 'flex-end',
    position: 'absolute',
    bottom: 0,
    top: 0,
    left: 0,
    right: 0,
  },
  container: {
    flex: 1,
    backgroundColor: holidayColors.black,
    borderTopRightRadius: borderRadiusValues.br16,
    borderTopLeftRadius: borderRadiusValues.br16,
  },
});
