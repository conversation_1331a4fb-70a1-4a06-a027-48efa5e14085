import React, { useCallback, useEffect, useRef, useState } from 'react';
import { getInterventionData } from '../../../utils/HolidayNetworkUtils';
import HolidayDataHolder from '../../../utils/HolidayDataHolder';
import { interventionTypes } from './InterventionConstants';
import { trackInterventionPDTClickEvents, trackLocalClickEvent } from './InterventionUtils';
import { DOM_BRANCH } from '../../../HolidayConstants';
import { getExcludedInterventionTypes } from '../../../utils/HolidaysPokusUtils';

/* Components */
import RoadBlock from './Roadblock';
import Feedback from './Feedback';
import GenericIntervention from './GenericIntervention';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

export default (WrappedComponent, pageName, trackingPageName = '') => {
  let response;
  let session;
  let savedPageName = '';
  return (props) => {
    // Create a ref to store the FabAnimationContainer reference
    const fabAnimationRef = useRef(null);
    const [type, setType] = useState();
    const [interventionData, setInterventionData] = useState({});
    const [branch, setBranch] = useState(DOM_BRANCH);
    const [isLeaveIntent, setLeaveIntent] = useState(null);
    const [isOpen, setIsOpen] = useState(false);
    const [isHandleBack, setHandleBack] = useState(false);
    const [hideIntervetion,setHideIntervention] = useState(false);
    const [uniqueKey, setUniqueKey] = useState('');
    const [fromWrappedComponent, setFromWrappedComponent] = useState(false);
    // const [filterData, setFilterData] = useState(null);
    const [interventionResponseData, setInterventionResponseData] = useState({});

    // Callback for a wrapped component to register its FabAnimationContainer ref
    const registerFabAnimationRef = useCallback((ref) => {
      fabAnimationRef.current = ref;
    }, []);

    // Callback to trigger TravelPlex from roadblock
    const triggerTravelPlexFromRoadblock = useCallback((url) => {
      if (fabAnimationRef.current) {
        fabAnimationRef.current.triggerTravelPlex(url);
      } else {
        console.error('❌ withIntervention - fabAnimationRef.current is null!');
      }
    }, []);

    const timerRef = useRef(null);
    const routeslengthRef = useRef(null);
    const [showIntervention, setShowIntervention] = useState(true);
    const [delay, setDelay] = useState(true);
    useEffect(() => {
      props.navigation?.navigationRef?.addListener('state', (state) => {
        if (routeslengthRef.current && routeslengthRef.current !== state.data.state.routes.length) {
          if (routeslengthRef.current === state.data.state.routes.length + 1) {
            savedPageName = '';
          }
          unmountIntervention();
        } else {
          routeslengthRef.current = state.data.state.routes.length;
        }
      });
    }, [props.navigation]);

    // {
    //   /* To be set by component wrapped*/
    // }

    const trackClickEvent = ({ eventName, suffix = '', pageName, ...rest }) => {
      trackLocalClickEvent(eventName, suffix, pageName, {
        omniTrackingPageName: trackingPageName,
        pdtData: interventionData?.pdtData,
        ...rest,
      });
    };
    const unmountIntervention = () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      setIsOpen(false);
      setLeaveIntent(null);
    };
    const pauseIntervention = () => {
      setHideIntervention(true);
      setShowIntervention(false);
    };
    const playIntervention = () => {
      setHideIntervention(false);
      setShowIntervention(true);
    };
    const updateInterventionData = ({
      destinationCity,
      packageId,
      fromCity,
      ticketId,
      agentUserName,
      quoteRequestId,
      branch: branchValue,
      cmp,
      departureDate,
      roomData = {},
      campaign = '',
      pdtData,
      fromReferralLink = false,
      referralCode = '',
    }) => {
      if (branchValue) {
        setBranch(branchValue);
      }
      setInterventionData({
        tagDestination: destinationCity,
        packageId: packageId,
        fromCity: fromCity,
        ticketId: ticketId,
        agentUserName: agentUserName,
        quoteRequestId: quoteRequestId,
        cmp: cmp,
        departureDate: departureDate,
        roomData,
        campaign,
        pdtData,
        fromReferralLink,
        referralCode,
      });
    };

    const captureClickEvents = () => {
      if (response?.id) {
        trackInterventionPDTClickEvents({
          value: `${response?.id}_${response.type}_Seen`,
          actionType: PDT_EVENT_TYPES.contentSeen,
          pageName,
          shouldTrackToAdobe:false
        });
        trackClickEvent({ eventName: `${response?.id}_${response.type}_Seen`, pageName, prop1: response?.id || ''});
      }
    }

    const close = () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      captureClickEvents();
      setFromWrappedComponent(true);
      setIsOpen(true);
      const holidayDataHolder = HolidayDataHolder.getInstance();
      holidayDataHolder.setInterventionList(response, pageName);
      setLeaveIntent(null);
    };
    const handleBack = () => {
      if (fromWrappedComponent) {
        setHandleBack(true);
        setFromWrappedComponent(false);
      }
    };
    const isEmpty = (obj = {}) => {
      return Object?.values(obj)?.every((x) => x === null || x === '');
    };
    const fetchData = async () => {
      if (!isEmpty(interventionData)) {
        const holidayDataHolder = HolidayDataHolder.getInstance();
        const newSession = await holidayDataHolder.setSessionInStorage();
        if (!isEmpty(newSession)) {
          session = newSession;
        }
        const {
          tagDestination,
          cmp,
          departureDate,
          packageId,
          roomData = [],
          campaign = '',
        } = interventionData || {};
        const data = {
          displayedInterventions: session?.displayedInterventions,
          pageName: pageName,
          tagDestination,
          sessionCount: session.sessionCount,
          departureDate,
          cmp,
          packageId,
          roomData,
          campaign,
        };
        const interventionDataUpdated = await getInterventionData(data);
        response = interventionDataUpdated?.applicableIntervention;
        const excludedInterventionTypesPokus = getExcludedInterventionTypes();
        const interventionType = interventionDataUpdated?.applicableIntervention?.type;
        if (excludedInterventionTypesPokus.includes(interventionType)) {
          return;
        }
        const { interactionDelaySec, leaveIntent, metadata } = response || {};
        if (metadata) {
          setUniqueKey(JSON.parse(metadata));
        }
        setLeaveIntent(leaveIntent);
        setType(interventionDataUpdated?.applicableIntervention?.type);
        timerRef.current = setTimeout(() => {
          setDelay(false);

          if (
            interventionDataUpdated?.applicableIntervention?.type === interventionTypes.FILTER ||
            interventionDataUpdated?.applicableIntervention?.type === interventionTypes.PIP
          ) {
            setInterventionResponseData(response);
            return;
          }
        }, interactionDelaySec * 1000);
      }
    };
    useEffect(() => {
      try {
        if (!isEmpty(interventionData?.referralCode)) {
          // START REFERRAL LINK FLOW
          setType(interventionTypes.HOL_REFFERAL);
        } else if (!isEmpty(interventionData) && pageName !== savedPageName) {
          fetchData();
          savedPageName = pageName;
        }
      } catch (e) {
        console.log(e);
      }
    }, [interventionData]);

    const getWrappedcomponent = () => {
      let filterData = {};
      if (type === interventionTypes.FILTER) {
        filterData = interventionResponseData;
      }
      return (
        <WrappedComponent
          {...props}
          leaveIntent={isLeaveIntent}
          updateInterventionData={updateInterventionData}
          close={close}
          isHandleBack={isHandleBack}
          unmountIntervention={unmountIntervention}
          pauseIntervention={pauseIntervention}
          playIntervention={playIntervention}
          filterData={filterData}
          registerFabAnimationRef={registerFabAnimationRef} // Pass callback to register FabAnimationContainer ref
        />
      );
    };
    const wrappedComponent = getWrappedcomponent();

    if (!delay && showIntervention) {
      setShowIntervention(false);
      setIsOpen(true);
      setLeaveIntent(null);
      captureClickEvents();
      const holidayDataHolder = HolidayDataHolder.getInstance();
      holidayDataHolder.setInterventionList(response, pageName).then((updatedSession) => {
        session = updatedSession;
      });
    }
    return <RoadBlock
        {...response}
        handleBack={handleBack}
        trackLocalClickEvent={trackClickEvent}
        trackInterventionPDTClickEvents={trackInterventionPDTClickEvents}
        pageName={pageName}
        isOpen={isOpen}
        type={type}
        branch={branch}
        hideIntervention={hideIntervetion}
        unmountIntervention={unmountIntervention}
        interventionResponseData={interventionResponseData}
        interventionData={interventionData}
        triggerTravelPlex={triggerTravelPlexFromRoadblock} // Pass callback created in withIntervention
      >
        <Feedback
          interventionData={interventionData}
          branch={branch}
          metaData={uniqueKey}
          type={type}
          isOpen={isOpen}
        >
          {wrappedComponent}
        </Feedback>
      </RoadBlock>
  };
};
