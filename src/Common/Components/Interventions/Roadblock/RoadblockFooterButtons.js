import { isEmpty } from 'lodash';
import React from 'react';
import { View,StyleSheet, Linking } from 'react-native';
import { handleDeepLinkUrl } from '../../../../Grouping/Components/PhoenixSectionCardClickHandler';
import PropTypes from 'prop-types';
import { DEFAULT_CLOSE_BUTTON } from '../InterventionConstants';
import HolidayDataHolder from "../../../../utils/HolidayDataHolder";
import PrimaryButton from '../../../../Common/Components/Buttons/PrimaryButton';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import TextButton from '../../Buttons/TextButton';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { QUERY_DETAIL_CONSTANTS } from '../../../../HolidayConstants';
import { showTravelPlex, travelPlexRollbackEnabled } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';
import {CALL_NOW_TEXT} from '../../../../../src/utils/CtaUtils'
export const CTA_NAME = {
  PRIMARY: 'primary',
  SECONDARY: 'secondary',
  CLOSE: 'close',
  };

export default RoadblockFooterButtons = (props) => {
  const {
    primaryCtaText, primaryCtaUrl, secondaryCtaText, secondaryCtaUrl,
    trackLocalClickEvent, id, type = '', pageName, branch, unmountIntervention,
    trackInterventionPDTClickEvents = () => {}, triggerTravelPlex,
  } = props || {};

  const isDismiss = isEmpty(primaryCtaText) && isEmpty(secondaryCtaText);

  const captureClickEvents = (text = '', prop66 = '',queryDetail={}) => {
    const eventName = `${id}_${type}_Click_${text}`;
    trackInterventionPDTClickEvents({
      value : eventName,
      actionType : PDT_EVENT_TYPES.buttonClicked,
      pageName,
      queryDetail :  {query_details:queryDetail}
    });
    trackLocalClickEvent({ eventName, pageName, branch, prop66 });
  };

  // Helper function to check if URL is a chat URL
  const isChatUrl = url => url && url.includes('https://myra.makemytrip.com/chat');

  const openUrl = (deeplink, text) => {
    if (deeplink) {
      // Check if this is a chat URL and TravelPlex trigger is available
      if (isChatUrl(deeplink) && triggerTravelPlex && (showTravelPlex() || !travelPlexRollbackEnabled())) {
        // Handle chat URL with TravelPlex
        let propValue = '';
        if (deeplink.includes('rb_track_chatid')) {
          const chatId = createChatID(false);
          propValue = `HLD:${chatId}`;
        }

        const queryDetail = {
          id: propValue,
          contact_type: QUERY_DETAIL_CONSTANTS.CONTACT_TYPE.CHAT,
          intervention_type: QUERY_DETAIL_CONSTANTS.INTERVENTION_TYPE.ROADBLOCK,
          intervention_id: `${id}`,
        };

        captureClickEvents(text, propValue, queryDetail);
        unmountIntervention(); // Close roadblock first

        // Trigger TravelPlex on main page with delay
        setTimeout(() => {
          triggerTravelPlex(deeplink);
        }, 200);
      } else {
        // Handle non-chat URLs with original web view logic
        let updatedDeeplink = deeplink;
        // Replace rb_track_pagename with pageName and "_roadblock" suffix (if pageName is provided)
        updatedDeeplink = updatedDeeplink.replaceAll('rb_track_pagename', !isEmpty(pageName) ? `${pageName}_roadblock` : 'NA');
        const cmp = HolidayDataHolder.getInstance().getCmp();
        // Replace rb_track_cmp with cmp (if cmp is provided)
        updatedDeeplink = updatedDeeplink.replaceAll('rb_track_cmp', !isEmpty(cmp) ? cmp : 'NA');

        let propValue = '';
        if (updatedDeeplink.includes('rb_track_chatid')) {
          const chatId = createChatID(false);
          propValue = `HLD:${chatId}`;
          updatedDeeplink = updatedDeeplink.replace('rb_track_chatid', chatId);
        }

        const queryDetail = {
          id: propValue,
          contact_type: QUERY_DETAIL_CONSTANTS.CONTACT_TYPE.CHAT,
          intervention_type: QUERY_DETAIL_CONSTANTS.INTERVENTION_TYPE.ROADBLOCK,
          intervention_id: `${id}`
        };

        captureClickEvents(text, propValue, queryDetail);
        unmountIntervention();

        setTimeout(() => {
          handleDeepLinkUrl(updatedDeeplink, '', false, pageName);
        }, 1000);
      }
    } else {
      props.close();
    }
  };

  const handleOpenChat = () => {
    if (
      primaryCtaText?.toUpperCase() === CALL_NOW_TEXT &&
      typeof primaryCtaUrl === 'string' &&
      primaryCtaUrl.startsWith('tel://')
    ) {
      unmountIntervention()
      const phoneNumber = primaryCtaUrl.replace('tel://', '');
      // Use React Native Linking to open the dialer
      Linking.openURL(`tel:${phoneNumber}`).catch((err) => {
        // Handle error opening dialer
        console.error('Failed to open dialer:', err);
      });
      
      return;
    }
    openUrl(primaryCtaUrl, CTA_NAME.PRIMARY);
  };

  return (
    <View style={[styles.wrapper, secondaryCtaText && primaryCtaText ? [] : styles.cta ]}>
      {!!secondaryCtaText && (
        <TextButton
          buttonText={secondaryCtaText}
          handleClick={() => openUrl(secondaryCtaUrl,CTA_NAME.SECONDARY)}
          btnTextStyle={styles.close}
          btnWrapperStyle={styles.secondaryCtaWrapper}
        />
      )}
      <PrimaryButton
        buttonText={primaryCtaText}
        handleClick={handleOpenChat}
        btnContainerStyles={styles.explore}
      />
      {isDismiss && (
        <TextButton
          buttonText={DEFAULT_CLOSE_BUTTON}
          handleClick={props.close}
          btnTextStyle={styles.close}
        />
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  wrapper: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...marginStyles.mv16,
  },
  cta:{justifyContent:'flex-end'},
  explore: {
    ...paddingStyles.ph16,
  },
  close: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
  },
  secondaryCtaWrapper:{
    alignSelf:'center'
  }
});
RoadblockFooterButtons.propTypes = {
  primaryCtaText: PropTypes.string,
  primaryCtaUrl: PropTypes.string,
  secondaryCtaText:PropTypes.string,
  secondaryCtaUrl:PropTypes.string,
};
