import React, { Fragment, useState } from 'react';
import { View, Image, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import BottomSheet from '../../BottomSheet';
import { Interventions, interventionTypes } from '../InterventionConstants';
import RoadblockContent from './RoadblockContent';
import RoadblockSquareImageview from './RoadblockSquareImageview';
import RoadblockFooterButtons, { CTA_NAME } from './RoadblockFooterButtons';
import RoadblockCurvedImage from './RoadblockCurvedImage';
import { holidayColors } from '../../../../Styles/holidayColors';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const getTemplate1 = (props) => {
  return (
    <View>
      <RoadblockContent {...props} imgUrl={props?.imgUrl} />
      {!!props?.imgUrl && <RoadblockSquareImageview url={props?.imgUrl} />}
    </View>
  );
};
const getTemplate2 = (props) => {
  return (
    <View>
      {!!props?.imgUrl && <RoadblockCurvedImage url={props?.imgUrl} />}
      <RoadblockContent {...props} />
    </View>
  );
};

const TemplateRoadblock = (props) => {
  const [isVisible, setVisibility] = useState(true);
  const RoadBlockEnum = {
    [Interventions.TEMPLATE1]: getTemplate1(props),
    [Interventions.TEMPLATE2]: getTemplate2(props),
  };

  const captureClickEvents =({eventName = '',  pageName , branch, trackLocalClickEvent = () => {}, trackInterventionPDTClickEvents = () => {} }) => {
    trackInterventionPDTClickEvents({
      value : eventName,
      actionType : PDT_EVENT_TYPES.buttonClicked,
      pageName,
    })
    trackLocalClickEvent({ eventName, pageName, branch });
  }

  const changeVisibility = () => {
    const { trackLocalClickEvent, id, type = '', pageName, branch, trackInterventionPDTClickEvents = () => {} } = props || {};
    captureClickEvents({ eventName: `${id}_${type}_Click_${CTA_NAME.CLOSE}`, pageName, branch, trackLocalClickEvent, trackInterventionPDTClickEvents });
    setVisibility(false);
    props.handleBack();
  };

  const getTemplateContent = () => {
    const {
      templateType = Interventions.TEMPLATE1,
      iconUrl,
      gradientColour = holidayColors.white,
      backgroundColour = holidayColors.white,
    } = props || {};
    {
      /* to-do remove default value from template type */
    }
    return props.type === interventionTypes.ROADBLOCK ? (
      <BottomSheet
        isOpen={isVisible && props.isOpen}
        containerStyle={styles.container}
        onBackPressed={changeVisibility}
      >
        <LinearGradient
          colors={[gradientColour, backgroundColour]}
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 0.0, y: 1.0 }}
          style={styles.linearGradient}
        >
          <View style={styles.gradientView} />
        </LinearGradient>
        {!!iconUrl && (
          <View style={styles.imageWrapper}>
            <Image source={{ uri: iconUrl }} style={styles.image} />
          </View>
        )}
        <View style={[styles.content, { backgroundColor: backgroundColour }]}>
          {RoadBlockEnum[templateType]}
          <RoadblockFooterButtons close={changeVisibility} {...props} />
        </View>
      </BottomSheet>
    ) : null;
  };

  return <Fragment>{getTemplateContent()}</Fragment>;
};
const styles = StyleSheet.create({
  linearGradient: {
    zIndex: 1,
    borderRadius: 10,
  },
  imageWrapper: {
    position: 'absolute',
    top: -40,
    left: 25,
    zIndex: 10,
  },
  image: {
    height: 80,
    width: 80,
    resizeMode: 'cover',
    borderRadius: 40,
  },
  content: {
    paddingHorizontal: 25,
  },
  gradientView: {
    height: 70,
    zIndex: 1,
  },
  container: { padding: 0 },
});
export default TemplateRoadblock;