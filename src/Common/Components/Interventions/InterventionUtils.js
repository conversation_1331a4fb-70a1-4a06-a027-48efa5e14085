import { PDT_RAW_EVENT } from '../../../HolidayConstants';
import {
  trackHolidaysRoadMapClickEvent,
} from '../../../PhoenixDetail/Utils/PhoenixDetailTracking';
import { createRandomString } from '../../../utils/HolidayUtils';
import { HOLIDAYS_BRANCH_NONE } from '../../../utils/HolidayTrackingUtils';
import { logHolidaysLandingPDTEvents } from 'mobile-holidays-react-native/src/LandingNew/Utils/HolidayLandingPdtTrackingUtils';
import { LANDING_PAGE_NAME } from 'mobile-holidays-react-native/src/LandingNew/LandingConstants';
import { PHOENIX_GROUPING_V2_PAGE_NAMES } from 'mobile-holidays-react-native/src/PhoenixGroupingV2/Contants';
import { DETAIL_TRACKING_PAGE_NAME } from 'mobile-holidays-react-native/src/PhoenixDetail/DetailConstants';
import { logHolidaysGroupingPDTEvents } from 'mobile-holidays-react-native/src/PhoenixGroupingV2/Utils/PhoenixGroupingV2PDTTrackingUtils';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { REVIEW_PDT_PAGE_NAME } from 'mobile-holidays-react-native/src/Review/HolidayReviewConstants';
import { logHolidayReviewPDTClickEvents } from 'mobile-holidays-react-native/src/PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PRESALES_DETAIL_PAGE } from 'mobile-holidays-react-native/src/MimaPreSales/constants/preSalesMimaConstants';

export const trackLocalClickEvent = (eventName, suffix = '',pageName ,{ omniTrackingPageName, pdtData = {}, prop1 = '', prop66 = '', branch = HOLIDAYS_BRANCH_NONE } = {}) => {
  trackHolidaysRoadMapClickEvent({
      omniEventName: eventName + suffix,
      prop1,
      prop66,
      pdtData: {
        pageDataMap:{},
        interventionDetails: {},
        eventType: PDT_RAW_EVENT,
        activity: eventName + suffix,
        requestId: createRandomString(),
        branch,
        ...pdtData,
      },
      omniTrackingPageName,
      pageName,
    });
  };


export const trackInterventionPDTClickEvents = ({ value, pageName, actionType ,queryDetail={},shouldTrackToAdobe=true}) => {
  if (!shouldTrackToAdobe) return;
  switch (pageName) {
    case LANDING_PAGE_NAME:
      logHolidaysLandingPDTEvents({ value, actionType ,queryDetail});
      break;
    case PHOENIX_GROUPING_V2_PAGE_NAMES.LISTING__PAGE_NAME:
      logHolidaysGroupingPDTEvents({ value, actionType ,queryDetail });
      break;
    case DETAIL_TRACKING_PAGE_NAME || PRESALES_DETAIL_PAGE:
      logPhoenixDetailPDTEvents({ value, actionType ,queryDetail});
      break;
    case REVIEW_PDT_PAGE_NAME: 
      logHolidayReviewPDTClickEvents({actionType, value ,queryDetail});
      break;
    default: 
      break;
  }
};