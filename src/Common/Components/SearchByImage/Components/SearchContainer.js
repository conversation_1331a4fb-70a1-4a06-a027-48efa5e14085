import React, { useRef, useState } from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { isEmpty } from 'lodash';
import { RESIZE_MODE_IMAGE } from '../../../../HolidayConstants';
import { getScreenWidth, isEmptyString } from '../../../../utils/HolidayUtils';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { IMAGE_SEARCH_STATES } from '../constants';

/* Components */
import UploadImagePlaceholder from './UploadImagePlaceholder';
import HolidayImageHolder from '../../HolidayImageHolder';
import Carousel from 'react-native-snap-carousel';
import LinearGradient from 'react-native-linear-gradient';
import HolidaysMessageStrip from '../../HolidaysMessageStrip';
import Divider from '../../Divider';
import { getResultList } from '../utils';
import { IMAGE_SEARCH_EVENT_PRE_FIX } from '../UploadImage';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

const SCREEN_WIDTH = getScreenWidth();
const IMAGE_CARD_CONTAINER_RATIO = 290 / 280;
const CARD_WIDTH = SCREEN_WIDTH - 100;
const CARD_HEIGHT = CARD_WIDTH / IMAGE_CARD_CONTAINER_RATIO;

const ImageDestinationCard = ({ cardData, index = 0, activeCityIndex }) => {
  const { imgUrl = '', displayName = '', confidenceScore = {} } = cardData || {};
  const {
    value = '',
    displayName: confidenceScoreDisplayName = '',
    colorCode = '',
  } = confidenceScore || {};
  const confidenceScoreColor = isEmptyString(colorCode) ? holidayColors.black : colorCode;
  const isActiveCard = activeCityIndex === index;
  const cardBorderColor = isActiveCard ? holidayColors.primaryBlue : holidayColors.grayBorder;
  const gradientColors = [
    isActiveCard ? holidayColors.skyBlue : holidayColors.white,
    holidayColors.white,
    holidayColors.white,
  ];

  return (
    <View
      style={[
        searchSuccessStyle.cardContainer,
        { borderColor: cardBorderColor, height: CARD_HEIGHT },
      ]}
    >
      <LinearGradient
        colors={gradientColors}
        start={{ x: 1, y: 0 }}
        end={{ x: 0, y: 1 }}
        locations={[0.4, 0.5, 0.9]}
        style={searchSuccessStyle.cardContainerChild}
      >
        <HolidayImageHolder
          imageUrl={imgUrl}
          style={searchSuccessStyle.destImage}
          resizeMode={RESIZE_MODE_IMAGE.COVER}
          containerStyles={{ alignItems: 'center' }}
        />
        <View style={searchSuccessStyle.cardTextContainer}>
          <Text style={searchSuccessStyle.imageCardDestNameText}>{displayName}</Text>
          <Text
            style={[searchSuccessStyle.imageCardDestScoreText, { color: confidenceScoreColor }]}
          >
            {confidenceScoreDisplayName}
          </Text>
        </View>
      </LinearGradient>
    </View>
  );
};

const UserImageDetailsCard = ({ userImageDetails = {} }) => {
  const {
    title = '',
    subTitle = '',
    confidenceScore = {},
    infoMessage = '',
  } = userImageDetails || {};
  const { value, displayName, colorCode } = confidenceScore || {};
  const confidenceScoreColor = isEmptyString(colorCode) ? holidayColors.black : colorCode;

  return (
    <View style={searchSuccessStyle.userImageDetailsContainer}>
      <View style={searchSuccessStyle.userImageDetails}>
        <View>
          <Text style={searchSuccessStyle.userImageDetailsSubTitle}>{subTitle}</Text>
          <Text style={searchSuccessStyle.userImageDetailsTitle}>{title}</Text>
        </View>
        <Text style={[searchSuccessStyle.userImageDetailsScore, { color: confidenceScoreColor }]}>
          {displayName}
        </Text>
      </View>
      <HolidaysMessageStrip
        shouldShow={true}
        message={infoMessage}
        containerStyles={searchSuccessStyle.messgaeStripStyle}
        messageStyle={searchSuccessStyle.messgae}
      />
    </View>
  );
};
const ImageSearchSuccessHighResult = ({
  imageRespDetails = {},
  uploadedImageUrl = '',
  activeCityIndex,
  setActiveCityIndex,
  imageSearchState = '',
  trackClickEvent = () => {},
}) => {
  const {
    title = 'Hurray',
    titleConfig = {},
    subTitleConfig = {},
    subTitle = 'We found a perfect match for your destination',
    ctaConfig = {},
    searchDestinationList = [],
    userImageDetails = {},
    carouselTitle = '',
  } = imageRespDetails || {};
  const { SECONDARY = {} } = ctaConfig || {};
  const { displayName = '' } = SECONDARY || {};
  const imageDestList = getResultList({ searchDestinationList });
  const { secondaryCtaUrl, name } = imageDestList?.[activeCityIndex] || {};
  const titleColor = !isEmpty(titleConfig?.colorCode)
    ? titleConfig?.colorCode
    : holidayColors.black;
  const subTitleColor = !isEmpty(subTitleConfig?.colorCode)
    ? subTitleConfig?.colorCode
    : holidayColors.black;

  const onSnapToItem = (index) => {
    trackClickEvent(IMAGE_SEARCH_EVENT_PRE_FIX, 'scroll', `imagesearch_${index}`);
    setActiveCityIndex(index);
  };

  return (
    <View style={searchSuccessStyle.container}>
      <View style={searchSuccessStyle.headerContainer}>
        <View style={{ ...marginStyles.mr16, flex: 1 }}>
          <Text style={[searchSuccessStyle.headerText, { color: titleColor }]}>{title}</Text>
          <Text
            numberOfLines={2}
            style={[searchSuccessStyle.subHeaderText, { color: subTitleColor }]}
          >
            {subTitle}
          </Text>
        </View>
        <Image
          source={{ uri: uploadedImageUrl }}
          resizeMode={RESIZE_MODE_IMAGE.COVER}
          style={searchSuccessStyle.userImage}
        />
      </View>
      {!isEmpty(userImageDetails) && (
        <>
          <UserImageDetailsCard userImageDetails={userImageDetails} />
          <Divider height={1} color={holidayColors.grayBorder} style={{ ...marginStyles.mt30 }} />
        </>
      )}
      <View style={searchSuccessStyle.destinationCardContainer}>
        {!!carouselTitle && <Text style={searchSuccessStyle.carousalTitle}>{carouselTitle}</Text>}
        <Carousel
          data={imageDestList}
          renderItem={({ item, index }) => (
            <ImageDestinationCard cardData={item} index={index} activeCityIndex={activeCityIndex} />
          )}
          itemWidth={CARD_WIDTH}
          keyExtractor={(item, index) => `FilterPage-${index}`}
          sliderWidth={SCREEN_WIDTH + 10}
          contentContainerCustomStyle={{ alignItems: 'center', height: carouselTitle ? CARD_HEIGHT + 40: '100%'}}
          onSnapToItem={onSnapToItem}
        />
      </View>
    </View>
  );
};

const ImageSearchSuccessLowResult = ({ imageRespDetails, uploadedImageUrl }) => {
  const { title = '', subTitle = '' } = imageRespDetails || {};
  return (
    <View style={imageSearchLowSuccessStyles.container}>
      <Image
        source={{ uri: uploadedImageUrl }}
        resizeMode={RESIZE_MODE_IMAGE.COVER}
        style={imageSearchLowSuccessStyles.image}
      />
      <Text style={imageSearchLowSuccessStyles.headerText}>{title}</Text>
      <Text style={imageSearchLowSuccessStyles.subHeaderText}>{subTitle}</Text>
    </View>
  );
};

const ImageUploadState = ({ imageDetails, processingImage, showImage }) => {
  const { uri, base64, height = 400, width, type = '' } = imageDetails || {};

  return (
    <View style={styles.imageContainer}>
      {showImage ? (
        <Image
          source={{ uri }}
          resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
          style={{
            width: SCREEN_WIDTH,
            height,
          }}
        />
      ) : (
        <UploadImagePlaceholder />
      )}
      {processingImage && (
        <View style={[styles.analyzeImageLoaderContainer, { width: width, height }]}>
         <Spinner
              size={36}
              strokeWidth={4}
              progressPercent={90}
              speed={1.5}
              color={holidayColors.white}
          />
          <Text style={styles.analyzeImageLoaderText}>Analysing Image</Text>
        </View>
      )}
    </View>
  );
};
const ImageSearchContainer = ({
  imageSearchState,
  showImage,
  processingImage,
  imageDetails = [],
  imageRespDetails = {},
  activeCityIndex = 0,
  setActiveCityIndex = () => {},
  trackClickEvent = () => {},
}) => {
  const { uri, base64, height = 400, width, type = '' } = imageDetails || {};

  switch (imageSearchState) {
    case IMAGE_SEARCH_STATES.UPLOAD:
      return (
        <ImageUploadState
          imageDetails={imageDetails}
          showImage={showImage}
          processingImage={processingImage}
          trackClickEvent={trackClickEvent}
        />
      );
    case IMAGE_SEARCH_STATES.PARTIAL_MATCH:
    case IMAGE_SEARCH_STATES.EXACT_MATCH:
    case IMAGE_SEARCH_STATES.EXPLORE:
      return (
        <ImageSearchSuccessHighResult
          imageRespDetails={imageRespDetails}
          uploadedImageUrl={uri}
          activeCityIndex={activeCityIndex}
          setActiveCityIndex={setActiveCityIndex}
          trackClickEvent={trackClickEvent}
          imageSearchState={imageSearchState}
        />
      );
    case IMAGE_SEARCH_STATES.IMAGE_ERROR:
      return (
        <ImageSearchSuccessLowResult imageRespDetails={imageRespDetails} uploadedImageUrl={uri} />
      );
    case 'default':
      return null;
  }
};

const styles = StyleSheet.create({
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    position: 'relative',
    backgroundColor: holidayColors.white,
  },
  analyzeImageLoaderContainer: {
    position: 'absolute',
    backgroundColor: 'rgba(0,0,0,0.5)',
    width: '100%',
    justifyContent: 'center',
    alignItems:"center",
  },
  analyzeImageLoaderText: {
    ...fontStyles.labelMediumBold,
    ...marginStyles.mt10,
    color: holidayColors.white,
    textAlign: 'center',
  },
});

const searchSuccessStyle = StyleSheet.create({
  container: {
    ...marginStyles.mt10,
    ...paddingStyles.pa16,
    flex: 1,
    flexDirection: 'column',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'base-line',
  },
  headerText: {
    ...fontStyles.labelLargeBlack,
  },
  subHeaderText: {
    ...fontStyles.labelSmallRegular,
  },
  userImage: {
    height: 48,
    width: 60,
    ...holidayBorderRadius.borderRadius8,
  },
  userImageDetailsContainer: {
    ...paddingStyles.pa16,
    ...marginStyles.mt30,
    ...holidayBorderRadius.borderRadius16,
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
  },
  messgaeStripStyle: {
    justifyContent: 'center',
    ...holidayBorderRadius.borderRadius8,
  },
  messgae: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  userImageDetailsTitle: {
    ...marginStyles.mt4,
    ...fontStyles.labelLargeBold,
    color: holidayColors.gray,
  },
  userImageDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...marginStyles.mb4,
  },
  userImageDetailsSubTitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  userImageDetailsScore: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  destinationCardContainer: {
    alignItems: 'center',
    flexGrow: 1,
  },
  carousalTitle: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    ...paddingStyles.pt20,
  },
  cardContainer: {
    width: '100%',
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...holidayBorderRadius.borderRadius16,
  },
  cardContainerChild: {
    ...paddingStyles.pa12,
    ...holidayBorderRadius.borderRadius16,
  },
  cardTextContainer: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  destImage: {
    width: '100%',
    height: '92%',
    ...holidayBorderRadius.borderRadius16,
  },
  imageCardDestNameText: {
    ...fontStyles.labelLargeBold,
    color: holidayColors.gray,
  },
  imageCardDestScoreText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  secondaryCtaContainer: {
    alignItems: 'center',
    marginTop: 'auto',
  },
  secondaryCtaText: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.primaryBlue,
  },
});

const imageSearchLowSuccessStyles = StyleSheet.create({
  container: {
    ...paddingStyles.pa16,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  image: {
    width: 100,
    height: 83,
    ...marginStyles.ma16,
    ...holidayBorderRadius.borderRadius8,
  },
  headerText: {
    ...marginStyles.mv10,
    ...fontStyles.labelLargeBold,
    color: holidayColors.gray,
    textAlign: 'center',
  },
  subHeaderText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    textAlign: 'center',
  },
});
export default ImageSearchContainer;
