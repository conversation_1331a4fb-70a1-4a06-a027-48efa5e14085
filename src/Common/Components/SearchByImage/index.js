import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'react-native-linear-gradient';
import { RESIZE_MODE_IMAGE } from '../../../HolidayConstants';
import { holidayColors, searchByImageColors } from '../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';

/* Components */
import HolidayImageHolder from '../HolidayImageHolder';

/* Icons */
import ArrowIcon from '@mmt/legacy-assets/src/arrow_blue_right.webp';
import { IMAGE_ICON_KEYS, getOpitimsedImageUrl } from '../HolidayImageUrls';

const SearchByImageTextEntryPoint = ({ state = '' }) => {
  return (
    <View style={styles.container}>
      <HolidayImageHolder
        style={styles.cameraIcon}
        imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.CAMERA_ICON)}
        resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
      />
      <Text style={styles.cameraText}>Search with a image</Text>
      <View style={styles.gifContainer}>
        <HolidayImageHolder
          imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.RNE_ENTRY_POINT_GIF)}
          style={styles.gifImage}
          resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
        />
      </View>
    </View>
  );
};

const SearchByImageBannerEntryPoint = ({ handleUploadImageClick = () => {} }) => {
  const entryPointHeader = 'Upload & Unveil Your Vacation';
  const entryPointSubheader = `Upload an image and explore your next holiday destination`;
  return (
    <TouchableOpacity onPress={handleUploadImageClick}>
      <LinearGradient
        colors={[searchByImageColors.lightPink, searchByImageColors.lightGreen]}
        style={styles.bannerContainer}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
      >
        <View>
          <HolidayImageHolder
            imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.IMAGE_SEARCH_ENTRY_IMAGE)}
            style={styles.landingEntryImage}
            resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
          />
        </View>
        <View style={styles.entryPointTextContainer}>
          <Text style={styles.bannerHeading}>{entryPointHeader}</Text>
          <Text style={styles.bannerSubHeading}>{entryPointSubheader}</Text>
        </View>
        <HolidayImageHolder defaultImage={ArrowIcon} style={styles.arrowIcon} />
      </LinearGradient>
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    ...paddingStyles.pv12,
  },
  cameraIcon: {
    height: 25,
    width: 25,
    tintColor: holidayColors.primaryBlue,
    ...marginStyles.mr14,
  },
  cameraText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  gifContainer: {
    marginLeft: 'auto',
    ...marginStyles.mt4,
  },
  gifImage: {
    width: 30,
    height: 25,
  },
  landingEntryImage: {
    width: 60,
    height: 60,
  },
  bannerContainer: {
    height: 80,
    ...marginStyles.ma16,
    ...paddingStyles.ph16,
    ...holidayBorderRadius.borderRadius8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  entryPointTextContainer: {
    flex: 1,
    ...marginStyles.mh10,
  },
  bannerHeading: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  bannerSubHeading: {
    ...fontStyles.labelSmallRegular,
    ...marginStyles.mt4,
    color: holidayColors.gray,
  },
  arrowIcon: {
    width: 25,
    height: 25,
  },
});
export { SearchByImageTextEntryPoint, SearchByImageBannerEntryPoint };
