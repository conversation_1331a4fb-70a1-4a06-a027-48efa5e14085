import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import React from 'react';
import { Image, Platform, StyleSheet, Text, View } from 'react-native';
import { holidayColors } from '../../../Styles/holidayColors';
import { paddingStyles } from '../../../Styles/Spacing';
import { fontStyles } from '../../../Styles/holidayFonts';
import LinearGradient from 'react-native-linear-gradient';
import { getImageUrl, IMAGE_ICON_KEYS } from '../HolidayImageUrls';

const SelectedTagBlackFooter = () => {
  return (
    <View
      style={styles.selected}
    >
      <View style={styles.tickIconContainer}>
        <Image
          source={{ uri: getImageUrl(IMAGE_ICON_KEYS.WHITE_TICK) }}
          style={styles.selectedIcon}
        />
      </View>
      <Text style={styles.selectedText}>SELECTED</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  selected: {
    ...holidayBorderRadius.borderRadius16,
    flexDirection: 'row',
    ...paddingStyles.pa4,
    ...paddingStyles.pr10,
    alignItems: 'center',
    backgroundColor: holidayColors.white,
    borderColor: holidayColors.primaryBlue,
    borderWidth: 1,
  },
  selectedIcon: {
    height: '100%',
    width: '60%',
    resizeMode: 'contain',
  },
  selectedText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
  },
  tickIconContainer: {
    width: 16,
    height: 16,
    backgroundColor: holidayColors.primaryBlue,
    borderRadius: 50,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...marginStyles.mr4,
    ...marginStyles.ml8,
  },
});
export default SelectedTagBlackFooter;
