import React, { useState } from 'react';
import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import RadioGroupButton from '../RadioButtonGroup';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { rupeeFormatterUtils } from '../../../utils/HolidayUtils';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { trackPhoenixDetailLocalClickEvent } from '../../../PhoenixDetail/Utils/PhoenixDetailTracking';

const SelectableMealOption = ({
  day,
  city,
  options,
  updateMeal,
  packagePriceMap,
  discountedFactor,
  packageDetail,
  selectedMealCode,
}) => {
  const initialSelection = options.findIndex((item) => item.mealCode === selectedMealCode);
  const [showSelection, setShowSelection] = useState(initialSelection);

  const toggleSelection = (index, mealCode) => {
    setShowSelection(showSelection === index ? null : index);
    updateMeal(mealCode);
  };

  const getPriceDiff = (mealCode, selectedMealCode) => {
    const selectedMealPrice = packagePriceMap[selectedMealCode];
    const mealPrice = packagePriceMap[mealCode];
    return Math.ceil((mealPrice - selectedMealPrice) * discountedFactor);
  };

  const renderMealItem = ({ item, index }) => {
    const priceDiffValue = getPriceDiff(item.mealCode, selectedMealCode);

    const handleSelectBtn = () => {
      trackPhoenixDetailLocalClickEvent({ eventName: `select_packageMeal_${day}_${city}` });
      toggleSelection(index, item.mealCode);
    };
    return (
      <View style={styles.mealItem}>
        <RadioGroupButton
          optionText={item.mealName}
          textLineCount={1}
          selected={index === showSelection}
          optionTextStyle={styles.mealText}
          handleClick={handleSelectBtn}
          btnContainerStyles={{
            ...styles.optionContainer,
            ...(index === showSelection ? styles.activeOptionContainer : {}),
          }}
          optionContainerStyle={styles.optionContainerStyle}
          children={
            <View>
              {index === showSelection ? (
                <View style={styles.optionTextContainer}>
                  <Text style={[styles.optionText, styles.optionSelected]}>Included</Text>
                </View>
              ) : (
                <View style={styles.optionTextContainer}>
                  <Text
                    style={[
                      styles.optionText,
                      {
                        color: priceDiffValue >= 0 ? '#eb2026' : '#1a7971',
                      },
                    ]}
                  >
                    {`${priceDiffValue >= 0 ? '+' : ''} ${rupeeFormatterUtils(priceDiffValue)}`}
                  </Text>
                  <Text style={styles.paxText}>per adult or child</Text>
                </View>
              )}
            </View>
          }
        >
        </RadioGroupButton>
      </View>
    );
  };
  return (
    <View style={styles.container}>
      <FlatList
        data={options}
        renderItem={renderMealItem}
        numColumns={2}
        keyExtractor={(item, index) => `${item?.mealCode}_${index}`}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
  },
  mealText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
    flex: 1,
  },
  paxText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  activeOptionContainer: {
    borderColor: holidayColors.primaryBlue,
  },
  optionContainer: {
    borderWidth: 1,
    flexDirection: 'column',
    borderColor: holidayColors.grayBorder,
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pa12,
    flex: 1
  },
  optionSelected: {
    color: holidayColors.green,
  },
  optionTextContainer: {
    ...paddingStyles.pl30,
    ...paddingStyles.pt6,
  },
  optionText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  mealItem: {
    ...paddingStyles.ph10,
    ...paddingStyles.pv16,
    flexDirection: 'column',
    flexGrow: 1,
  },
  optionContainerStyle: {
    flexDirection: 'column'
  }
});

export default SelectableMealOption;
