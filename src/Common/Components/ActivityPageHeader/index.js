import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import React, { useEffect, useState } from 'react';
import { Image, StyleSheet, Text, TextInput, TouchableOpacity, View, Platform } from 'react-native';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import SearchBar from '@Frontend_Ui_Lib_App/SearchBar';
import { getLeftBackIcon } from '../../../utils/HolidayUtils';
import { CLEAR } from '../../../SearchWidget/SearchWidgetConstants';
import AnchorBtn from '../AnchorBtn';
import iconBack from '@mmt/legacy-assets/src/img/iconBack.webp';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import Header from '@Frontend_Ui_Lib_App/Header';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import {capitalizeText} from '../../../../src/utils/textTransformUtil'
export const pageHeaderBackArrow = require('@mmt/legacy-assets/src/backArrowAndroid.webp');

export const PageHeaderBackButton = ({ onBackPressed, iconSource, iconStyles = {} }) => {
  return (
    <TouchableOpacity onPress={onBackPressed} style={styles.backIconContainer}>
      <Image
        style={[styles.iconBack, iconStyles]}
        source={iconSource ? iconSource : pageHeaderBackArrow}
      />
    </TouchableOpacity>
  );
};
export const PageHeaderTitle = (props) => {
  const { title, titleStyles, numberOfLines = 1 } = props || {};
  return (
    !!title && (
      <Text style={[styles.title, titleStyles]} numberOfLines={numberOfLines}>
        {capitalizeText(title)}
      </Text>
    )
  );
};
const ActivityPageHeader = ({
  title = '',
  subTitle = '',
  onBackPressed = () => {},
  containerStyles = {},
  numberOfLines = 1,
  leftComponent = null,
  onTextChange = () => { },
  searchTextPlaceholder = 'Search...',
  activeTabIndex,
  headerWrapperStyle,
  searchedText,
  isSearchLoading = false,
}) => {
  const leftIcon = getLeftBackIcon({ onBackPress: onBackPressed });
  const [isSearchBoxVisible, setSearchBoxVisible] = useState(false); // Always start with search box hidden
  const [searchText, setSearchText] = useState(searchedText || '');

  // Update local search text when searchedText prop changes
  useEffect(() => {
    // Always sync local state with prop
    setSearchText(searchedText || '');

    // Don't automatically show search box - let user manually opens it
    // This prevents search box from appearing when switching tabs
  }, [searchedText]);

  useEffect(() => {
    // Hide search box when tab changes for cleaner UX
    // Always hide search box when switching tabs
    setSearchBoxVisible(false);
  }, [activeTabIndex]);


  const clearSearch = () => {
    setSearchText('');
  };

  const toggleSearch = () => {
    if (isSearchBoxVisible) {
      // Only clear search if user explicitly toggles off the search box via search icon
      // Don't clear when switching tabs
      clearSearch();
      onTextChange(''); // Notify parent about clearing search
      setSearchBoxVisible(false);
    } else {
      // When opening the search box, restore search text from prop if available
      if (searchedText && searchedText !== searchText) {
        setSearchText(searchedText);
      }
      setSearchBoxVisible(true);
    }
  };
  const rightIcons = [
    {
      icon: require('@mmt/legacy-assets/src/search.webp'),
      onPress: toggleSearch,
      customStyles: {
        iconStyle: styles.iconSearch
      }
    }
  ];
  const clearSearchClick = () => {
    setSearchText('');
    onTextChange('');
  };

  const handleSearchBackPress = () => {
    // Just close the search box without clearing the search text
    // This preserves the search text for when the user opens search again
    setSearchBoxVisible(false);
  };

  const renderBackButton = () => (
    <TouchableOpacity style={styles.searchBackWrapper} onPress={handleSearchBackPress}>
      <Image style={styles.searchBackIcon} source={iconBack} />
    </TouchableOpacity>
  )
  const renderClearButton = () => {
    return (
      <>
        {/* Show spinner when search is loading */}
        {isSearchLoading && (
          <View style={{ ...marginStyles.mr8 }}>
            <Spinner
              size={20}
              strokeWidth={2}
              progressPercent={85}
              speed={1.5}
              color={holidayColors.primaryBlue}
            />
          </View>
        )}
        {/* Show clear button when there's search text */}
        {searchText && (
          <View style={{ ...marginStyles.mr12 }}>
            <AnchorBtn label={CLEAR} handleClick={clearSearchClick} />
          </View>
        )}
      </>
    )
  }


  return (
    <View style={[styles.titleContainer, containerStyles]}>
      {/* Search box */}
      {isSearchBoxVisible ? (
        <SearchBar
        inputValue={searchText}
        onChangeText={(text) => {
          setSearchText(text);
          onTextChange(text);
        }}
        placeholder={searchTextPlaceholder}
        placeholderTextColor={holidayColors.disableGrayBg}
        leftComponent={renderBackButton()}
        rightComponent={renderClearButton()}
        isEditable
        keyboardType="default"
        maxLength={50}
        inputProps={{ autoFocus: true }}
        customStyles={{
          containerStyle: styles.searchContainer,
          inputStyle: styles.searchInput,
        }}
      />
      ) : (
        <Header
          customStyles={{
            descriptionStyle: styles.subTitle,
            titleStyle: styles.title,
              wrapperStyle: [styles.wrapperStyle, headerWrapperStyle],
            iconStyle: styles.iconSearch
          }}
          description={!!subTitle && <Text>{subTitle}</Text>}
          leftIcon={leftIcon}
          rightIcons={!isSearchBoxVisible && (rightIcons)}
          title={capitalizeText(title)}
          titleNoOfLines={numberOfLines}
          leftComponent={leftComponent}
        />
      )}
    </View>
  );
};

export const shadowStyle = StyleSheet.create({
  shadow: {
    shadowColor: holidayColors.black,
    backgroundColor: holidayColors.white,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 3,
  },
});

const styles = StyleSheet.create({
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  wrapperStyle: {
    width: '100%',
  },
  backIconContainer: {
    ...paddingStyles.pr20,
  },
  backWrapper: {
    ...paddingStyles.pr20,
    ...paddingStyles.pv16,
  },
  title: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    ...marginStyles.mr20
  },
  subTitle: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
  },
  iconBack: {
    height: 24,
    width: 24,
    tintColor: holidayColors.lightGray,
  },
  headingContainer: {
    flex: 1,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.primaryBlue,
    padding: Platform.OS === 'android' ? 1 : 5,
    ...marginStyles.mh10,
    ...marginStyles.mv4,
    height: 50,
    backgroundColor: holidayColors.lightBlueBg,
    ...paddingStyles.ph0,
  },
  searchInput: {
    flex: 1,
    color: holidayColors.black,
    ...fontStyles.labelMediumBold,
    ...paddingStyles.pv0,
    ...marginStyles.mr12,
  },
  iconSearch: {
    height: 24,
    width: 24,
    tintColor: holidayColors.primaryBlue,
  },
  iconClear: {
    height: 20,
    width: 20,
    tintColor: holidayColors.primaryBlue,
  },
  iconCross: {
    height: 20,
    width: 20,
  },
  searchIconContainer: {
    paddingRight: 20,
  },
  searchBackWrapper: {
    ...paddingStyles.pa16,
  },
  searchBackIcon: {
    height: 16,
    width: 16,
    tintColor: holidayColors.black,
  },
});
export default ActivityPageHeader;
