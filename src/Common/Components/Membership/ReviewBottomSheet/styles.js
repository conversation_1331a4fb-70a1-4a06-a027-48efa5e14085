import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles, getFontFamily } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { Dimensions, StyleSheet } from 'react-native';
const  {width ,height} = Dimensions.get('window');
const styles = StyleSheet.create({
  bottomSheetContainer: {
    paddingTop: 16,
    paddingLeft: 20,
    paddingRight: 20,
    maxHeight:height - 250,
  },
  memberShipBadge: {
    height: 90,
    width: 90,
    position: 'absolute',
    top: -50,
    left:18,
    zIndex:999
  },
  headerCrossIcon: {
    height: 30,
    width: 30,
    tintColor: holidayColors.lightGray,
    marginLeft: 'auto',
  },

  descHeadingCont: {
    marginBottom: 16,
  },
  rewardsBullet: {
    height: 6,
    width: 6,
    alignSelf: 'center',
  },
  myCashDesc: {
    marginTop: 16,
    display: 'flex',
    flexDirection: 'row',
  },
  myCashDescTxt: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
    marginLeft: 14,
  },
  overlayImageContainer:{
    position: 'absolute',
    left: 0,
    zIndex:999,
    alignItems: 'center',
    justifyContent: 'center',
    right:0,
    top:0,
    bottom:0,
    overflow:'hidden',
    pointerEvents: 'none',
 },
 overlayImage:{
  height: height,
  width: width,
},
withoutClaimWrapper: {
  marginBottom:15,
  flexDirection: 'row',
  justifyContent: 'center',
},
withoutClaimTxt: {
  fontSize: 18,
  color: holidayColors.primaryBlue,
  fontWeight: '700',
  lineHeight:19,
  ...getFontFamily('700')
},
crossimageContainer:{
  position:'absolute',
  right:18,
  top:18,
  zIndex:999
},
submitButtonContainer:{
  marginTop:12,
  marginBottom:15
},
bottomSheetChildHeader: {
  marginBottom:10,
  marginTop:35
}
});

export default styles;
