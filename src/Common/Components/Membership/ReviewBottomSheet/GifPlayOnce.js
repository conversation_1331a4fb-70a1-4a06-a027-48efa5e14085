import React, { useState, useEffect } from 'react';
import { View } from 'react-native';
import HolidayImageHolder from '../../HolidayImageHolder';
import { RESIZE_MODE_IMAGE } from 'mobile-holidays-react-native/src/HolidayConstants';

const GifPlayOnce = ({ source, style }) => {
  const [key, setKey] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Reset visibility when source changes
    setIsVisible(true);
    setKey(prevKey => prevKey + 1);
  }, [source]);

  const handleLoadEnd = () => {
    // Hide the GIF after it has played once
    setTimeout(() => {
      setIsVisible(false);
    }, 6000);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <View style={style}>
      <HolidayImageHolder 
        style={style}
        imageUrl={source}
        resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
      />
    </View>
  );
};

export default GifPlayOnce;
