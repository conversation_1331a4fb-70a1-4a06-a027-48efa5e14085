import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { Dimensions, StyleSheet } from 'react-native';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
  gradientBorder: {
    borderRadius: 16,
    ...paddingStyles.ph12,
    ...paddingStyles.pv12,
  },
  giftCardFlex: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...marginStyles.mt12,
  },
  giftCardIconTxtFlex: {
    flexDirection: 'row',
    width: '70%',
    alignItems: 'center',
  },
  giftCardImgStyle: {
    ...marginStyles.mr6,
    height:20,
    width:20,
  },
  subHeaderTxt: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
  },
  finalTotalHeader: {
    ...fontStyles.labelSmallRegular,
  },
  finalPrice: {
    ...paddingStyles.pt4,
  },
  lowerSectionDetailsCard: {
    width: '100%',
    backgroundColor: holidayColors.white,
    overflow: 'hidden', // Hide content outside the parent box
    position: 'relative',
    justifyContent: 'space-between',
    ...paddingStyles.pa4,
    borderRadius: 12,
    ...marginStyles.mt16,
    flexDirection:'row',
    alignItems:'center'
  },
  animatedbox: {
    width: '52%',
    backgroundColor: holidayColors.white, // Color of the moving box
  },
  animatedborderbox: {
    borderRadius: 12,
    ...paddingStyles.pa2,
  },
  animatedborderinsidebox: {
    backgroundColor: holidayColors.white,
    borderRadius: 10,
    ...paddingStyles.ph12,
    ...paddingStyles.pv8,
  },
  packageText: {
    ...fontStyles.labelSmallRegular,
  },
  packagePriceText: {
    ...paddingStyles.pt4,
  },
  imagebox: {
    position: 'absolute',
    left: 0,
    zIndex:999,
    alignItems: 'center',
    justifyContent: 'center',
    right:0,
    top:0,
    bottom:0,
  },
  imagedimension: {
    height: 200,
    width: 200,
  },
});

export default styles;
