import React, { useCallback, useEffect, useRef, useState, memo, useMemo } from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';
import Carousel, { Pagination } from 'react-native-snap-carousel';
import MembershipCard from '../MembershipCard';
import { CONTENT_TYPES } from '../utils/constants';
import { holidayColors } from '../../../../Styles/holidayColors';
import { paddingStyles } from '../../../../Styles/Spacing';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { showMMTBlack, showMMTPersonalization } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';
import WebCarousel from '../../Carousel/web';
import { isRawClient } from '../../../../utils/HolidayUtils';
import { width } from 'mobile-holidays-react-native/src/Map/MapConstants';
import { getMemberShipCardTypeWithOrder } from '../utils/MembershipUtils';

const SCREEN_WIDTH = Dimensions.get('window').width;
const CARD_TYPES = {
  MMT_BLACK: 'MMT_BLACK',
  PZN: 'PERSONALIZATION',
};

const MembershipCardMemo = memo(MembershipCard);

const MembershipCarousel = (props) => {
  const {
    onKnowMorePressMmtBlackMembership,
    logHolidaysLandingPDTEvents,
    trackMmtBlackClickEvent,
    landingMMTBlackData,
    contentType, 
    containerStyle = {},
    gradientStyle = {},
    carouselContainerStyle = {},
    paginationContainerStyle= {},
    dotContainerStyle = {},
  } = props || {};


  if (!showMMTBlack()) {
    //remove MMT black DATA
    landingMMTBlackData.cards = landingMMTBlackData?.cards?.filter(card => card.type !== CARD_TYPES.MMT_BLACK);
  }

  if (!showMMTPersonalization()) {
    //remove Personalization DATA from landingMMTBlackData
    landingMMTBlackData.cards = landingMMTBlackData?.cards?.filter(card => card.type !== CARD_TYPES.PZN);
  }

  const [activeMembershipCardIndex, setActiveMembershipCardIndex] = useState(0);
  const [itemWidth, setItemWidth] = useState(SCREEN_WIDTH - 50);
  const sectionOrder = useRef('');

  useEffect(() => {
    if (landingMMTBlackData) {
      sectionOrder.current = landingMMTBlackData.order || '';
      setItemWidth(landingMMTBlackData?.cards?.length > 1 ? SCREEN_WIDTH - 50 : SCREEN_WIDTH - 36);
    }
  }, [landingMMTBlackData]);

  const handleLoadAnalyticsEvent = (isPersonalisation, pznCardType = '') => {
    const actionType = PDT_EVENT_TYPES.contentSeenUser;
    const prop1Val = isPersonalisation ? pznCardType : sectionOrder.current;
    const value = isPersonalisation
      ? `${PDTConstants.PZN_CARD_VIEWED}`
      : `${PDTConstants.GC_CARD_SEEN}${sectionOrder.current ? `|${sectionOrder.current}` : ''}`;
    logHolidaysLandingPDTEvents({ actionType, value ,shouldTrackToAdobe:false});
    trackMmtBlackClickEvent({
      eventName: isPersonalisation ? PDTConstants.PZN_CARD_VIEWED : PDTConstants.GC_CARD_SEEN,
      prop1: prop1Val,
      mmtBlackBucketDetail: mmtBlackBucketDetail?.current,
    });
  };

  const handleCardsLoadEvent = () => {
    const eventName = `Loaded_section_${landingMMTBlackData.sectionCode}`;
    const prop1 = getMemberShipCardTypeWithOrder(landingMMTBlackData);
    trackMmtBlackClickEvent({
      eventName,
      prop1,
    });
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.contentSeen,
      value: `${eventName}|${prop1}`,
      shouldTrackToAdobe:false
    });
  };

  const handleOnLayout = () => {
    const isPersonalisation = landingMMTBlackData.cards[0]?.type === CARD_TYPES.PZN;
    const pznCardType = isPersonalisation
      ? landingMMTBlackData.cards[0]?.personalizationDetail?.section?.type
      : '';
    handleCardsLoadEvent();
    handleLoadAnalyticsEvent(isPersonalisation, pznCardType);
  };

  const handleSnapToItem = useCallback((index, MMTBlackData) => {
    const isPersonalisation = MMTBlackData.cards[index]?.type === CARD_TYPES.PZN;
    const pznCardType = isPersonalisation ? MMTBlackData.cards[index]?.personalizationDetail?.section?.type : '';
    if (!viewedItems.has(index)) {
      viewedItems.add(index);
      handleLoadAnalyticsEvent(isPersonalisation, pznCardType);
    }
    setActiveMembershipCardIndex(index)
  }, []);

  const handleKnowMorePress = useCallback((bottomSheetDetail, mmtBlackBucketDetail, ctaText) => {
    onKnowMorePressMmtBlackMembership(bottomSheetDetail, mmtBlackBucketDetail, ctaText);
  }, [onKnowMorePressMmtBlackMembership]);

  const renderMembershipCard = useCallback(( item ) => {
    const membershipCard = item;
    if (![CARD_TYPES.MMT_BLACK, CARD_TYPES.PZN].includes(membershipCard?.type)) {
      return null;
    }

    const isPersonalisation = membershipCard?.type === CARD_TYPES.PZN;
    const details = isPersonalisation
      ? membershipCard?.personalizationDetail
      : membershipCard?.mmtBlackDetail;

    const { section, bottomSheet, mmtBlackPdtData } = details || {};
    const { overlayImage, cta } = section || {};
    if (!section ) {
      return null;
    }

    const onKnowMorePress = () => {
      if (isPersonalisation && cta?.link) {
        GenericModule.openDeepLink(cta?.link);
      } else {
        handleKnowMorePress(bottomSheet, mmtBlackPdtData?.mmtBlackBucketDetail, cta?.text);
      }
    };

    return (
      <View style={[styles.cardContainer, { marginTop: overlayImage ? 16 : 0 }]}>
        <MembershipCardMemo
          sectionDetails={section}
          onKnowMorePress={onKnowMorePress}
          mmtBlackPdtEvents={logHolidaysLandingPDTEvents}
          trackMemberShipLoadEvent={trackMmtBlackClickEvent}
          sectionOrder={sectionOrder.current}
          mmtBlackBucketDetail={mmtBlackPdtData?.mmtBlackBucketDetail}
          contentType={contentType}
          containerStyles={containerStyle}
          gradientStyle={gradientStyle}
        />
      </View>
    );
  }, [handleKnowMorePress, logHolidaysLandingPDTEvents, trackMmtBlackClickEvent]);

  if (!landingMMTBlackData || !landingMMTBlackData.cards || landingMMTBlackData.cards.length === 0) {
    return <View />; // Return an empty view if data is not available
  }

  const carouselProps = {
    data: landingMMTBlackData.cards,
    renderItem: renderMembershipCard,
    sliderWidth: SCREEN_WIDTH,
    onSnapToItem: handleSnapToItem,
    activeSlideAlignment: 'start',
    containerCustomStyle: styles.carouselContainer,
    disableEdgeSwiping: isRawClient(),
    carousalContainerStyle: styles.carouselContainer,
      onLayout:handleOnLayout,



  }

  return (
    <View style={styles.container}>
      {!isRawClient() ? (
        <>
          <Carousel {...carouselProps} />
          <Pagination
            dotsLength={landingMMTBlackData.cards.length}
            activeDotIndex={activeMembershipCardIndex}
            containerStyle={[styles.paginationContainer, paginationContainerStyle]}
            dotStyle={styles.dotStyle}
            dotContainerStyle={dotContainerStyle}
            removeClippedSubviews={false}
            inactiveDotStyle={styles.inactiveDotStyle}
            inactiveDotOpacity={0.4}
            scrollEnabled={landingMMTBlackData.cards.length > 1} 
          />
        </>
      ) : (
        <WebCarousel {...carouselProps} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  cardContainer: {
    marginTop: 16,
    flex: 1,
  },
  carouselContainer: {
    ...paddingStyles.pb6,
    width: '100%'
  },
  paginationContainer: {
    paddingVertical: 8,
  },
  dotStyle: {
    width: 7,
    height: 7,
    marginHorizontal: -6,
    backgroundColor: holidayColors.primaryBlue,
  },
  inactiveDotStyle: {
    backgroundColor: holidayColors.gray,
  },
});

export default memo(MembershipCarousel);
