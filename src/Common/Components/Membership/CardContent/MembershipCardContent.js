import React from 'react';
import { View, Text, Image, Pressable } from 'react-native';
import styles from '../styles/MembershipCardStyles';
import DynamicTextWrapper from '../DynamicTextWrapper';
import { noop } from 'lodash';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { getTextStyles } from '../utils/MembershipUtils';

const MembershipCardContent = (props) => {
  const {
    onKnowMorePress = noop,
    cta = {},
    description = [],
    descriptionBulletImageUrl = '',
    headerText = [],
  } = props || {};

  const textColor = cta?.color || holidayColors.black;

  return (
    <>
      {headerText.length > 0 && (
        <View style={{marginTop: 2}}>
          <DynamicTextWrapper textData={headerText} />
        </View>
      )}

        <View style={headerText.length > 0 ? {} : {marginTop: 0}}>
          {description.map((item, index) => (
            <View key={index} style={styles.itemContainer}>
              {descriptionBulletImageUrl && (<Image style={styles.bullet} source={{ uri: descriptionBulletImageUrl }} />)}
              <View style={[styles.itemText, !descriptionBulletImageUrl ? {marginLeft: marginStyles.ml2} : {}]}>
                <DynamicTextWrapper textData={item} />
              </View>
            </View>
          ))}
        </View>
      <Pressable onPress={cta?.text ? () => onKnowMorePress(cta?.text) : noop}>
        {Object.keys(cta).length > 0 && (
          <Text style={[styles.knowMoreText, getTextStyles(cta?.textStyle)]}>{cta?.text}</Text>
        )}
      </Pressable>
    </>
  );
};

export default MembershipCardContent;
