import React, { useEffect, useState } from 'react';
import { View, Image, TouchableOpacity } from 'react-native';
import CrossIcon from '@mmt/legacy-assets/src/holidays/iconCross.webp';
import styles from './styles';
import { isEmpty } from 'lodash';
import HolidayImageHolder from '../../HolidayImageHolder';
import { RESIZE_MODE_IMAGE } from 'mobile-holidays-react-native/src/HolidayConstants';

const noop = () => {}; // Define a named no-op function

/**
 * Renders the header component for the bottom sheet.
 *
 * @component
 * @returns {JSX.Element} The rendered header component.
 * @param props
 */
const BottomSheetHeader = (props) => {
  const { headerImage = '', togglePopup = noop } = props || {};

  return (
    <View style={styles.bottomSheetHeader}>
      <TouchableOpacity onPress={togglePopup} style={styles.crossIconWrapper}>
        <Image style={styles.headerCrossIcon} source={CrossIcon} />
      </TouchableOpacity>
        <HolidayImageHolder
          imageUrl={headerImage}
          style={styles.image}
          resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
        />
    </View>
  );
};

export default React.memo(BottomSheetHeader);
