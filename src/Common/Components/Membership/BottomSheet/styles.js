import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { Dimensions, StyleSheet } from 'react-native';
const {height} = Dimensions.get('window');

const styles = StyleSheet.create({
  topcontainer:{
    maxHeight:height-250
  },
  container: {
    width: '100%',
    height: 24, // Adjust this height if needed
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  image: {
    height:28,
    width: 216,
    marginLeft: 16,
    alignSelf: 'flex-start',
  },
  bottomSheetHeader: {
    display: 'flex',
    flexDirection: 'row',
    paddingBottom: 16,
    paddingTop: 24,
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
    paddingLeft: 16,
    paddingRight: 16,
  },
  headerCrossIcon: {
    height: 24,
    width: 24,
    alignSelf: 'center',
    tintColor: holidayColors.lightGray,
  },
  headerImg: {
    marginRight: 4,
  },
  headerDetailsCont: {
    marginLeft: 16,
  },
  headerText: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 2,
  },
  mmtBlackTxt: {
    color: holidayColors.gray,
    ...fontStyles.labelMediumBlack,
  },
  tierTxt: {
    color: holidayColors.black,
    ...fontStyles.labelMediumBold,
    marginLeft: 2,
  },
  bottomSheetDescCont: {
    marginLeft: 16,
    marginRight: 16,
    marginTop: 16,
  },
  descHeadingCont: {
    marginBottom: 12,
  },
  descHeading: {
    marginBottom: 4,
  },
  descHeadingTxt: {},
  subHeadingCont: {
    marginBottom: 12,
  },
  subHeadingTxt: {
    color: holidayColors.black,
    ...fontStyles.labelBaseRegular,
  },
  crossIconWrapper: {
    justifyContent: 'center',
  },
  submitbtnContainer:{
    paddingTop: 20,
    marginBottom: 24,
    marginHorizontal: 16
  }
});

export default styles;
