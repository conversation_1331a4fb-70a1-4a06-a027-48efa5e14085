import React, { useEffect } from 'react';
import { Image, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import styles from './styles/MembershipCardStyles';
import MembershipCardContent from './CardContent/MembershipCardContent';
import MembershipCardContentLanding from './CardContent/MembershipCardContentLanding';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { getGradientBorderColor } from './utils/MembershipUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { noop } from 'lodash';
import { PDTConstants } from '../../../Review/HolidayReviewConstants';
import { CONTENT_TYPES } from './utils/constants';

/**
 * Renders a membership card component.
 *
 * @param {Object} props - The component props.
 * @param {Object} props.sectionDetails - The details of the card section.
 * @param {Object} props.gradientStyle - The style for the gradient.
 * @param {Function} props.onKnowMorePress - The function to be called when "Know More" is pressed.
 * @param {Object} props.containerStyles - The styles for the container.
 * @returns {JSX.Element} The rendered membership card component.
 */
const MembershipCard = (props) => {
  // Destructure the props and provide default values if they are not provided
  const {
    sectionDetails = {},
    gradientStyle = {},
    onKnowMorePress = noop,
    containerStyles = {},
    mmtBlackPdtEvents = noop,
    trackMemberShipLoadEvent = noop,
    sectionOrder = '',
    mmtBlackBucketDetail = {},
    contentType = '',
    showFullBorderGradient = true,
    sendLoadEvents = true,
  } = props || {};

  // useEffect hook to perform side effects when the component mounts
  useEffect(() => {
    // Call the mmtBlackPdtEvents function with the actionType and value
    if(sendLoadEvents)
    {
      mmtBlackPdtEvents({
        actionType: PDT_EVENT_TYPES.contentSeenUser,
        value: `${PDTConstants.GC_CARD_SEEN}${sectionOrder ? `|${sectionOrder}` : ''}` ,
        shouldTrackToAdobe:false
      });
      // Call the trackMemberShipLoadEvent function with eventName and prop1 and mmtBlackBucketDetail as arguments
      trackMemberShipLoadEvent({eventName:PDTConstants.GC_CARD_SEEN, prop1: sectionOrder, mmtBlackBucketDetail})
    }
  }, []);


  let borderGradientStyle = {}
  if(!showFullBorderGradient){
    borderGradientStyle = {
      padding: 0,
      paddingTop:1,
    }
  }

  // Render the membership card component
  return (
    <View style={containerStyles} >
      {/* Render an overlay image if sectionDetails.overlayImage exists */}
      {sectionDetails?.overlayImage && (
        <Image source={{ uri: sectionDetails?.overlayImage }} style={styles.cardImage} />
      )}
      {/* Render a linear gradient border */}
      <LinearGradient
        colors={
          // Validate and split the border gradient colors
          getGradientBorderColor(contentType, sectionDetails?.borderGradient)
        }
        start={{ x: 0, y: 1 }}
        end={{ x: 0, y: 0 }}
        style={[styles.gradientBorder, borderGradientStyle, gradientStyle]}
      >
        {/* Render a linear gradient container */}
        <LinearGradient
          colors={[holidayColors.white, holidayColors.white]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.container, gradientStyle, sectionDetails?.overlayImage ? {} : {paddingTop:8, paddingBottom:8}]}
          accessible
          accessibilityLabel="MMTBLACK card"
        >
          {/* Render the content of the membership card */}
          {contentType === CONTENT_TYPES.LANDING ? (
            <MembershipCardContentLanding
              cta={sectionDetails?.cta}
              description={sectionDetails?.description}
              descriptionBulletImageUrl={sectionDetails?.descriptionBulletImageUrl}
              headerText={sectionDetails?.headerText}
              onKnowMorePress={onKnowMorePress}
            />
          ) : (
            <MembershipCardContent
              cta={sectionDetails?.cta}
              description={sectionDetails?.description}
              descriptionBulletImageUrl={sectionDetails?.descriptionBulletImageUrl}
              headerText={sectionDetails?.headerText}
              onKnowMorePress={onKnowMorePress}
            />
          )}
        </LinearGradient>
      </LinearGradient>
    </View>
  );
};

export default MembershipCard;
