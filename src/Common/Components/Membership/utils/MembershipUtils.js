import { CARD_TYPES, CONTENT_TYPES, MEMBERSHIP_CARD_TYPE } from './constants';
import { holidayColors } from '../../../../Styles/holidayColors';
import { getFontFamily, normaliseFont } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { showMMTBlack, showMMTPersonalization, showMMTPersonalizationV2 } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';

// Function to get text styles based on the provided textStyle object
export const getTextStyles = (textStyle) => {
  const styles = {
    ...getFontFamily(textStyle?.bold? '700' : '400'),
  };
  if (textStyle?.color) {
    styles.color = textStyle.color;
  } else {
    styles.color = holidayColors.black;
  }
  if (textStyle?.bold) {
    styles.fontWeight = '700';
  } else {
    styles.fontWeight = '400';
  }
  if (textStyle?.emphasize) {
    styles.fontStyle = 'italic';
  } else {
    styles.fontStyle = 'normal';
  }
  if (textStyle?.font) {
    styles.fontSize = normaliseFont(+textStyle.font);
  } else {
    styles.fontSize = normaliseFont(14);
  }
  if (textStyle?.lineHeight) {
    styles.lineHeight = +textStyle?.lineHeight;
  } 
  return styles;
};

// Function to get review gradient colors based on the provided cardType
export const getReviewGradientColors = (cardType) => {
  switch (cardType) {
    case CARD_TYPES.GOLD:
      return [holidayColors.reviewGoldDark, holidayColors.reviewGoldLight];
    case CARD_TYPES.PLATINUM:
      return [holidayColors.platinumDark, holidayColors.platinumLight];
    case CARD_TYPES.NON_MMT_BLACK_USER:
      return [holidayColors.green, holidayColors.lightGreen];
    default:
      return [holidayColors.grayDark, holidayColors.grayLight];
  }
};

// Function to get gradient colors based on the provided cardType
export const getGradientColors = (cardType) => {
  switch (cardType) {
    case CARD_TYPES.GOLD:
      return [holidayColors.goldDark, holidayColors.goldLight];
    case CARD_TYPES.PLATINUM:
      return [holidayColors.platinumDark, holidayColors.platinumLight];
    case CARD_TYPES.NON_MMT_BLACK_USER:
      return [holidayColors.green, holidayColors.lightGreen];
    default:
      return [holidayColors.grayDark, holidayColors.grayLight];
  }
};

// Function to get review inner gradient color based on the provided cardType
export const getReviewInnerGradientColor = (cardType) => {
  switch (cardType) {
    case CARD_TYPES.GOLD:
      return [holidayColors.reviewGoldLight, holidayColors.white];
    case CARD_TYPES.PLATINUM:
      return [holidayColors.platinumLight, holidayColors.white];
    case CARD_TYPES.NON_MMT_BLACK_USER:
      return [holidayColors.white, holidayColors.white, holidayColors.fadedGreen];
    case CARD_TYPES.INNER_Gold:
      return [holidayColors.goldDark, holidayColors.goldLight];
    default:
      return [holidayColors.white, holidayColors.white];
  }
};

// Function to get inner gradient color based on the provided cardType
export const getInnerGradientColor = (cardType) => {
  switch (cardType) {
    case CARD_TYPES.NON_MMT_BLACK_USER:
      return [holidayColors.white, holidayColors.white, holidayColors.white];
    default:
      return [holidayColors.white, holidayColors.white];
  }
};

// Function to get member color based on the provided cardType
export const getMemberColor = (cardType) => {
  switch (cardType) {
    case CARD_TYPES.GOLD:
      return holidayColors.goldLight;
    case CARD_TYPES.PLATINUM:
      return holidayColors.grayLight;
    case CARD_TYPES.NON_MMT_BLACK_USER:
      return holidayColors.grayLight;
    default:
      return holidayColors.grayLight;
  }
};



// Function to validate border gradient colors
export const validateBorderGradientColors = borderGradient => {
  if (typeof borderGradient !== 'string') return false;

  return borderGradient
    .split(',')
    .every(color => isValidColor(color.trim()));
};

// Function to check if a color is valid
const isValidColor = (color) => {
  const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
  return colorRegex.test(color);
};

export const getBottomSheetDescriptionGCListMargin = (header, subHeader) => {
  if(!header && !subHeader) {
    return { marginTop: -10 };
  }
  else if(!subHeader) {
    return { marginTop: -4 };
  }
  return {};
}

export const getMemberShipCardTypeWithOrder = (membershipData) => {
    let cardsWithOrder = '';
    membershipData?.cards?.forEach((card, index) => {
      if(index !== 0) {
        cardsWithOrder += '_';
      };
      if(card.type === MEMBERSHIP_CARD_TYPE.MMT_BLACK) {
        cardsWithOrder +=  `MMTBLACK_${index+1}`;
      }
      if(card.type === MEMBERSHIP_CARD_TYPE.PZN) {
        const pznCardType = card?.personalizationDetail?.section?.type;
        cardsWithOrder +=  `PZN_${pznCardType}_${index+1}`;
      }
    })
    return cardsWithOrder;
}
export const getGradientBorderColor = (contentType, borderGradient) => {
  return validateBorderGradientColors(borderGradient) ? borderGradient.split(',') : [holidayColors.black, holidayColors.black];

}

export const getMemberShipRenderedOrder = (mmtBlackDetail, personalizationDetail) => {
  let index = 1;
  let event = '';
  if(mmtBlackDetail && mmtBlackDetail?.section) {
    event += `MMTBLACK_${index}`;
    index++;
  }
  if(personalizationDetail && personalizationDetail?.section) {
    const pznCardType = personalizationDetail?.section?.type;
    if(index !== 1) {
      event += '_';
    }
    event += `PZN_${pznCardType}_${index}`;
  }
  return event;
}


export const getMemberShipCardArray = ({ mmtBlackDetail, personalizationDetail }) => {
  const cardArray = [];
  if (showMMTBlack() && mmtBlackDetail && mmtBlackDetail?.section ) {
    cardArray.push({
      type: MEMBERSHIP_CARD_TYPE.MMT_BLACK,
      mmtBlackDetail,
    });
  }
  if (showMMTPersonalization() && showMMTPersonalizationV2() && personalizationDetail && personalizationDetail?.section ) {
    cardArray.push({
      type: MEMBERSHIP_CARD_TYPE.PZN,
      personalizationDetail,
    });
  }
  return cardArray;
};
