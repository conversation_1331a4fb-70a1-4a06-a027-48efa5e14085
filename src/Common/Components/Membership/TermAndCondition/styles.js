import { StyleSheet } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

const styles = StyleSheet.create({
  tncCont: {
    marginTop: 16,
  },
  tncTxt: {
    color: holidayColors.black,
    ...fontStyles.labelBaseBold,
  },
  tncDesc: {
    marginTop: 4,
    display: 'flex',
  },
  tncDescTxt: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
  },
  tncLink: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
  },
});

export default styles;
