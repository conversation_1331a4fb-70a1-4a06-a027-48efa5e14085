import React from "react";
import { Text, View, Image, StyleSheet } from "react-native";
import LinearGradient from "react-native-linear-gradient";
import DynamicTextWrapper from "mobile-holidays-react-native/src/Common/Components/Membership/DynamicTextWrapper";
import { holidayColors } from "mobile-holidays-react-native/src/Styles/holidayColors";
import { validateBorderGradientColors } from "mobile-holidays-react-native/src/Common/Components/Membership/utils/MembershipUtils";
import { marginStyles, paddingStyles } from "mobile-holidays-react-native/src/Styles/Spacing";

const FareBreakUpBottomCard = ({ fareBreakUpCardData = {} }) => {
    const { icon= '', description=[]} = fareBreakUpCardData.fareBreakUp || {};
    const gradientColors = validateBorderGradientColors(fareBreakUpCardData?.borderGradient)
    ? fareBreakUpCardData?.borderGradient.split(',')
    : [holidayColors.white, holidayColors.white];
    return (
        <View style={styles.container}>
        <LinearGradient
            colors={gradientColors}
            start={{ x: 0, y: 1 }}
            end={{ x: 0, y: 0 }}
            style={styles.outerGradient}
        >
            <LinearGradient
                colors={[holidayColors.white, holidayColors.white]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.innerGradient}
                accessible
                accessibilityLabel="MMTBLACK card"
            >
                <View style={styles.row}>
                    <View style={styles.iconContainer}>
                    {icon ? <Image source={{ uri: icon }} style={styles.icon} /> : null}
                    </View>
                     <View style={styles.textContainer}>
                    <DynamicTextWrapper textData={description} />
                    </View>
                </View>
            </LinearGradient>
        </LinearGradient>
        </View>
    );
};

export default FareBreakUpBottomCard;

const styles = StyleSheet.create({
    container: {
        ...marginStyles.mb16
    },
    outerGradient: {
        borderRadius: 16,
        padding: 1
    },
    innerGradient: {
        borderRadius: 16,
        ...paddingStyles.pa12
    },
    row: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    iconContainer: {
        width: '5%',
        alignItems: 'center',
        ...marginStyles.mr6
    },
    icon: {
        height: 16,
        width: 16
    },
    textContainer: {
        width: '95%'
    }
});
