import { FabConfigData, PageContext, QueryDto, SearchContext } from './types';
import { getFabCtaConfig } from '@mmt/holidays/src/utils/HolidaysPokusUtils';
import { getFabT2QconfigDefaultData } from '@mmt/holidays/src/utils/HolidayUtils';
import {
  TRAVEL_PLEX_CTA_FALLBACK_TITLE,
} from '@mmt/holidays/src/Common/Components/Widgets/FabAnimation/TravelPlex/TravelPlexConstants';

export const createQueryDto = (
    search_context?: SearchContext,
    page_context?: PageContext,
    attr2?: string // This is a tag destination
): QueryDto => {
  // Helper function to check if value is valid (not null, undefined, empty string, or "null" string)
  const isValidValue = (value: any): boolean =>
      value != null && value !== '' && value !== 'null' && value !== 'undefined';

  // Select the first valid destination value
  const destinationCity =
      (isValidValue(attr2) ? attr2 : null) ||
      (isValidValue(search_context?.to?.location?.name) ? search_context?.to?.location?.name : null) ||
      (isValidValue(search_context?.to?.locus?.locus_id) ? search_context?.to?.locus?.locus_id : null) ||
      '';

  return {
    destinationCity,
    branch: page_context?.branch || 'DOM',
    pageName: page_context?.page_name,
    funnelStep: page_context?.funnel_step,
    omniPageName: page_context?.page_name,
  };
};

const toConfigData = (value: unknown): FabConfigData | undefined =>
    typeof value === 'object' && value !== null && !Array.isArray(value)
        ? (value as FabConfigData)
        : undefined;

export const getCtaTitle = (key: string): string => {
  if (!key || !key.trim()) {
    return TRAVEL_PLEX_CTA_FALLBACK_TITLE;
  }
  const fabCtaConfig = toConfigData(getFabCtaConfig());
  const fabT2QConfig = toConfigData(getFabT2QconfigDefaultData());
  const normalizedKey = key.toLowerCase().trim();

  const config = fabCtaConfig?.[normalizedKey] ?? fabT2QConfig?.[normalizedKey];
  const title = config?.triggerTitle ?? config?.fallbackTitle ?? TRAVEL_PLEX_CTA_FALLBACK_TITLE;

  return title.replace(/<>/g, '');
};
