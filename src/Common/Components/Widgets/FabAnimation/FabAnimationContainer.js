import React, { useState, useCallback, useMemo } from 'react';
import HolidayFabAnimatedV2 from '../../HolidayFabAnimatedV2';
import HolidayFabAnimated from '../../HolidayFabAnimated';
import {
  handleDefaultFabClick,
  showLocator,
  startCall,
  startChat,
  startQuery,
  startChatGpt,
} from './FabAnimationUtils';
import { PDT_EVENT_TYPES } from '../../../../utils/HolidayPDTConstants';
import {
  getT2QFabIconFlag,
  travelPlexRollbackEnabled,
  showTravelPlex,
} from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';
import { isLuxeFunnel } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import TravelPlexContainer from '../FabAnimation/TravelPlexContainer';

const HolidayFabAnimationContainer = React.forwardRef(({
  fabData,
  fabCta,
  fabState,
  onFabToggle,
  destinationCityData,
  trackPageExit = () => {},
  trackPDTV3Event = () => {},
  setLocatorState,
  unmountIntervention,
  trackLocalClickEvent,
  fabTextShrinked,
  travelPlexConfigData = {},
  containerStyle = {},
  invalidateChatViewData = false,
  onTravelPlexDataUsed = () => {}, // Callback to notify when TravelPlex has consumed the data
  ...rest
}, ref) => {
  const [fab, setFab] = useState(false);
  const [showTravelPlexFromChat, setShowTravelPlexFromChat] = useState(false); // State to manage TravelPlex rendering from chat action

  const toggleFabValue = useCallback(() => {
    setFab(!fab);
  }, [fab]);

  const handleTravelPlexChatClose = useCallback(() => {
    setShowTravelPlexFromChat(false); // Reset state when TravelPlex chat is closed to show original FAB
    setRoadBlockUrl(null); // Clear roadblock URL to prevent it from persisting
  }, []);

  const trackFabCloseClickEvents = useCallback(() => {
    const eventName = 'fab_close';
    trackPDTV3Event({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    });
    trackLocalClickEvent({ eventName });
  }, [trackPDTV3Event, trackLocalClickEvent]);

  const handleFabClick = useCallback((open) => {
    if (open) {
      handleDefaultFabClick({ fabCta, fab, trackLocalClickEvent, trackPDTV3Event });
    } else {
      trackFabCloseClickEvents();
    }
  }, [fabCta, fab, trackLocalClickEvent, trackPDTV3Event, trackFabCloseClickEvents]);

  const handleFabCall = useCallback((fromIcon) => {
    startCall({
      fabData,
      fromIcon,
      toggleFabValue,
      trackLocalClickEvent,
      trackPDTV3Event,
    });
    trackPageExit();
  }, [fabData, toggleFabValue, trackLocalClickEvent, trackPDTV3Event, trackPageExit]);

  const handleFabChat = useCallback((fromIcon) => {
    if (!travelPlexRollbackEnabled()) { // A/B test: Use TravelPlex for chat if experiment is enabled
      setShowTravelPlexFromChat(true); // Show TravelPlex with auto-open chat
    } else {
      startChat({ // Fallback to old Myra chat if A/B test is disabled
        fabData,
        fromIcon,
        toggleFabValue,
        unmountIntervention,
        trackLocalClickEvent,
        trackPDTV3Event,
      });
    }
    trackPageExit();
  }, [fabData, toggleFabValue, unmountIntervention, trackLocalClickEvent, trackPDTV3Event, trackPageExit]);

  const handleChatGpt = useCallback((fromIcon) => {
    startChatGpt({
      trackLocalClickEvent,
      trackPDTV3Event,
    });
    trackPageExit();
  }, [trackLocalClickEvent, trackPDTV3Event, trackPageExit]);

  const handleFabQuery = useCallback((fromIcon) => {
    startQuery({
      fromIcon,
      fabData,
      fabCta,
      toggleFabValue,
      trackLocalClickEvent,
      trackPDTV3Event,
    });
    trackPageExit();
  }, [fabData, fabCta, toggleFabValue, trackLocalClickEvent, trackPDTV3Event, trackPageExit]);

  const handleShowLocator = useCallback((fromIcon) => {
    showLocator({
      setLocatorState,
      fromIcon,
      trackLocalClickEvent,
      trackPDTV3Event,
    });
    trackPageExit();
  }, [setLocatorState, trackLocalClickEvent, trackPDTV3Event, trackPageExit]);

  // Store URL from roadblock for TravelPlex context
  const [roadBlockUrl, setRoadBlockUrl] = useState(null);

  // Expose triggerTravelPlex method for external components (like roadblock)
  React.useImperativeHandle(ref, () => ({
    triggerTravelPlex: (url) => {
      if (url) {
        setRoadBlockUrl(url); // Keep storing the original URL for backward compatibility
      }

      setShowTravelPlexFromChat(true); // Same logic as handleFabChat
      trackPageExit();
    },
  }), [fabData, travelPlexConfigData, toggleFabValue, unmountIntervention, trackLocalClickEvent, trackPDTV3Event, trackPageExit, showTravelPlexFromChat]);

  const shouldShowV2Fab = useMemo(() => getT2QFabIconFlag() || isLuxeFunnel(), []);
  const destinationContext = useMemo(() =>
    destinationCityData?.displayName || destinationCityData?.destinationCity,
    [destinationCityData]
  );

  const fabDataWithOpenChat = useMemo(() =>
    ({ ...fabData, openTravelPlexForOldMyra: true }),
    [fabData]
  );

  const fabDataWithRoadBlock = useMemo(() => {
    if (!roadBlockUrl) {
      return fabData;
    }

    return {
      ...fabData,
      openTravelPlex: true,
      isFromDeeplink: true,
    };
  }, [fabData, roadBlockUrl]);


  if (!fabCta?.showFab) {
    return null;
  }

  if (showTravelPlex()) { // Flow 1: Global TravelPlex enabled - always show TravelPlex FAB
    return <TravelPlexContainer travelPlexConfigData={travelPlexConfigData}
                                fabData={fabDataWithRoadBlock}
                                containerStyle={containerStyle}
                                onChatClose={handleTravelPlexChatClose}
                                trackPDTV3Event={trackPDTV3Event}
                                invalidateChatViewData={invalidateChatViewData}
                                roadBlockUrl={roadBlockUrl}
                                onTravelPlexDataUsed={onTravelPlexDataUsed} // Pass reset callback to TravelPlex
                                trackLocalClickEvent={trackLocalClickEvent}
                                {...rest}
                                />;
  } else {
    if (showTravelPlexFromChat) { // Flow 2: Chat action triggered TravelPlex - show with auto-open chat
      return <TravelPlexContainer travelPlexConfigData={travelPlexConfigData}
                                  fabData={fabDataWithOpenChat}
                                  containerStyle={containerStyle}
                                  onChatClose={handleTravelPlexChatClose}
                                  multipleCTA={false}
                                  trackPDTV3Event={trackPDTV3Event}
                                  invalidateChatViewData={invalidateChatViewData}
                                  onTravelPlexDataUsed={onTravelPlexDataUsed} // Pass reset callback to TravelPlex
                                  trackLocalClickEvent={trackLocalClickEvent}
                                  {...rest}
                                  />;
    } else {
      if (shouldShowV2Fab) { // Flow 3a: Show V2 FAB for T2Q or Luxe funnel
        return (
          <HolidayFabAnimatedV2
            {...rest}
            fabCta={fabCta}
            context={destinationContext}
            startCall={handleFabCall}
            startQuery={handleFabQuery}
            startChat={handleFabChat}
            startChatGpt={handleChatGpt}
            showLocator={handleShowLocator}
            handleDefaultFabClick={handleFabClick}
            travelPlexConfigData={travelPlexConfigData}
            fabData={fabData}
            containerStyle={containerStyle}
          />
        );
      } else { // Flow 3b: Default FAB for all other cases
        return (
          <HolidayFabAnimated
            {...rest}
            fabCta={fabCta}
            startCall={handleFabCall}
            startQuery={handleFabQuery}
            startChat={handleFabChat}
            startChatGpt={handleChatGpt}
            showLocator={handleShowLocator}
            handleDefaultFabClick={handleFabClick}
            travelPlexConfigData={travelPlexConfigData}
            fabData={fabData}
          />
        );
      }
    }
  }
});

// Memoize the forwardRef component to prevent unnecessary re-renders when props haven't changed
export default React.memo(HolidayFabAnimationContainer);
