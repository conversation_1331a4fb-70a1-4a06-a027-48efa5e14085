import React from 'react';
import { StyleSheet, Text, TextStyle, View } from 'react-native';
import { MMTLUXEHeaderTypes } from './HeaderTypes';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { isAndroidClient } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import { capitalizeText } from 'mobile-holidays-react-native/src/utils/textTransformUtil';

const getContainerStyle = (subHeading: string) => {
  if (!subHeading) {
    return {
      ...paddingStyles.pb20,
    };
  }
  return {
    ...paddingStyles.pb40,
  };
};

const MMTLUXEHeader = (props: MMTLUXEHeaderTypes) => {
  if (!props.heading && !props.subHeading) {
    return null;
  }
  return (
    <View style={[styles.container, getContainerStyle(props.subHeading)]}>
      {!!props.heading && <Text style={styles.heading}>{props.heading}</Text>}
      {!!props.subHeading && <Text style={styles.subHeading}>{capitalizeText(props.subHeading)}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    ...paddingStyles.pb40,
  },
  heading: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
  } as TextStyle,

  subHeading: {
    fontSize: 32,
    position: 'absolute',
    top: isAndroidClient() ? 18 : 22,
    fontWeight: '400',
    color: holidayColors.luxColor,
    fontFamily: 'BeauRivage-Regular',
  },
});
export default MMTLUXEHeader;
