import React from 'react';
import { Image, StyleSheet, Text, TextStyle, View } from 'react-native';
import { MMTLUXEHeaderTypes } from './HeaderTypes';
import { fontStyles } from '../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import LinearGradient from 'react-native-linear-gradient';
import HolidayImageHolder from '../HolidayImageHolder';
import { RESIZE_MODE_IMAGE } from '../../../HolidayConstants';
import { getImageUrl, IMAGE_ICON_KEYS } from '../HolidayImageUrls';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const PremiumLine = () => {
    const starPremium = getImageUrl([IMAGE_ICON_KEYS.STAR_ICON])
    const images = [starPremium, starPremium, starPremium];
    const luxColor = holidayColors.luxColor;
  return (
    <View style={{ flexDirection: 'row' }}>
      <LinearGradient
        colors={[holidayColors.white, luxColor]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      />
      <View style = {styles.iconContainerStyle}>
      {images.map((image, index) => 
        <HolidayImageHolder
          key={index}
          imageUrl={image}
          defaultImage={null}
          style={styles.iconStyle}
          resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
        />
      )}
      </View>
      <LinearGradient
        colors={[holidayColors.white, luxColor]}
        end={{ x: 0, y: 0 }}
        start={{ x: 1, y: 0 }}
        style={styles.gradient}
      />
    </View>
  );
};
const MMTLUXENonPremiumHeader = (props: MMTLUXEHeaderTypes) => {
  if (!props.heading && !props.subHeading) {
    return null;
  }
  return (
    <View style={[styles.container]}>
      <PremiumLine />
      {!!props.heading && <Text style={styles.heading}>{props.heading}</Text>}
      {!!props.subHeading && <Text style={styles.subHeading}>{props.subHeading}</Text>}
      <View style={{ height: 10 }} />
      <PremiumLine />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    ...paddingStyles.pv4,
    ...paddingStyles.pb10,
  },
  iconContainerStyle: {
    ...marginStyles.mv2,
    flexDirection: 'row',
  },
  gradient: {
      ...marginStyles.mv2,
    height: 1,
    ...marginStyles.mh10,
    width: 80,
  },
  iconStyle: {
    top: -5,
    width: 12,
    height: 12,
    ...marginStyles.mh4,
  },
  heading: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
  } as TextStyle,
  subHeading: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
  } as TextStyle,
});
export default MMTLUXENonPremiumHeader;
