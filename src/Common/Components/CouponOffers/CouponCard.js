import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import _ from 'lodash';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import TextButton from '../Buttons/TextButton';
import HolidayImageHolder from '../HolidayImageHolder';
import { RESIZE_MODE_IMAGE } from 'mobile-holidays-react-native/src/HolidayConstants';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { marginStyles,paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { SELECT_ACTIONS } from 'mobile-holidays-react-native/src/PhoenixDetail/DetailConstants';
import { IMAGE_ICON_KEYS, getOpitimsedImageUrl } from '../HolidayImageUrls';
import {capitalizeText} from '../../../../src/utils/textTransformUtil'
const CouponCard = (props) => {
  const {
    couponCode = '',
    couponDesc = '',
    offerPrice = '',
    selected = false,
    cardStyles = {},
    // tncUrl = '',
    validateCoupon = () => { },
  } = props || {};

  const couponAction = selected ? SELECT_ACTIONS.REMOVE : SELECT_ACTIONS.APPLY;

  const handleClick = () => {
    validateCoupon(couponAction);
  };
  const selectedColor = [holidayColors.white, holidayColors.skyBlue]
  const unSelectedColor = [holidayColors.white, holidayColors.white]
  const couponBoxColor = selected ? selectedColor : unSelectedColor
  return (
    <LinearGradient
      colors={couponBoxColor}
      locations={[0.5, 1]}
      start={{ x:0,y:1}}
      end={{x:1,y:0.5}}
      // useAngle
      // angle={30}
      style={[styles.couponCard, selected ? styles.selectedCard : null, cardStyles]}
    >
      <View style={cardStyles}>
        <View>
          <View style={styles.couponTitleContainer}>
            <HolidayImageHolder
              imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.COUPON_ICON)}
              style={styles.couponImage}
              resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
            />
            <Text style={styles.couponCode} numberOfLines={1}>{couponCode}</Text>
            <Text style={[styles.offerPrice, selected ? styles.selectedPriceColor : {}]}>
              {offerPrice}
            </Text>
          </View>
          <View style={styles.bottomContainer}>
            <View style={styles.descriptionContainer}>
              <Text style={styles.couponDesc}>{couponDesc}</Text>
            </View>
            <View style={styles.actionContainer}>
              <TextButton
                buttonText={capitalizeText(couponAction)}
                handleClick={handleClick}
                btnTextStyle={[styles.apply]}
              />
            </View>
          </View>
        </View>
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  couponCard: {
    backgroundColor: holidayColors.white,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...holidayBorderRadius.borderRadius16,
    ...marginStyles.ma6,
    ...marginStyles.mh16,
    ...paddingStyles.pa16,
    flex: 1,
  },
  selectedCard: {
    borderColor: holidayColors.primaryBlue,
  },
  couponTitleContainer: {
    flexDirection: 'row',
    width: '100%'
  },
  couponImage: {
    height: 20,
    width: 20,
    ...marginStyles.mr4,
  },

  couponCode: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
    marginLeft: 6,
    width: '60%'
  },
  bottomContainer: {
    flexDirection: 'row',
  },
  descriptionContainer: {
    width: '75%',
  },
  couponDesc: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    ...AtomicCss.marginTop5,
    flexDirection: 'column',
    flex: 1,
  },
  offerPrice: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
    flexDirection: 'row',
    ...AtomicCss.flex1,
    flex: 1,
    textAlign: 'right',
  },
  selectedPriceColor: {
    color: holidayColors.green,
  },
  apply: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    textAlign: 'right',
  },
  actionContainer: {
    marginLeft: 'auto',
    justifyContent: 'flex-end',
  },
});

export default CouponCard;
