import React from 'react';
import { Image, ImageStyle, StyleSheet, Text, View } from 'react-native';
import { fontStyles } from '../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { TextStyle } from 'react-native';
import DottedLine from '../DottedLine';
import { StepperListItemProps } from './StepperListTypes';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const getSelectedColor = (backgroundColor?: string) => {
  return { backgroundColor };
}

const getVerticalDottedLine = (isLastItem: boolean) => {
  return (
    !isLastItem && (
      <DottedLine
        orientation="vertical"
        height="100%"
        strokeWidth={3}
        color={'#3A6FAE'}
        strokeDasharray="3,3"
        length='10'
      />
    )
  );
};

const SelectedText = (props: StepperListItemProps) => {
  const { text, backgroundColor, fontColor, isLastItem, icon } = props;
  const selectedTextWrapperColor = getSelectedColor(backgroundColor);
  return (
    <View style={styles.wrapper}>
      <View  style={{maxHeight: 55}}>
      {icon && <Image source={{ uri: icon }} style={{...styles.icon, ...styles.iconStylePill}} />}
      <View style={styles.verticalLineStylePill}>
        {getVerticalDottedLine(isLastItem)}
      </View>
      </View>
      <View style={styles.pillMessageContainer}>
        <View style={styles.horizontalLineStyle}>
          <DottedLine
            orientation="horizontal"
            length="100%"
            color={fontColor}
            strokeWidth={3}
            strokeDasharray="3,3"
          />
        </View>
        <View style={{ ...selectedTextWrapperColor, ...styles.selectedTextWrapper }}>
          <Text style={[styles.selectTextStyle, { color: fontColor }]}>{text}</Text>
        </View>
      </View>
    </View>
  );
};

const StepperListItem = (props: StepperListItemProps) => {
  const { text, icon, subText, isLastItem } = props;

  return (
    <View style={styles.wrapper}>
      {props?.isSelected ? (
        <SelectedText {...props} />
      ) : (
        <>
          <View style={{maxHeight: 75 }}>
            {icon && <Image source={{ uri: icon }} style={styles.largeIcon} />}
            <View style={styles.verticalLineStyle}>
              {getVerticalDottedLine(isLastItem)}
            </View>
          </View>
          <View style={styles.textContainerStyle}>
            {text && <Text style={[styles.text]}>{text}</Text>}
            {subText && <Text style={[styles.subText]}>{subText}</Text>}
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    flex: 1,
  },
  icon: {
    height: 16,
    width: 16,
  } as ImageStyle,
  largeIcon: {
    height: 32,
    width: 32,
    left: -8,
  } as ImageStyle,
  verticalLineStyle: {
    left: 8,
    flex: 1,
  },
  verticalLineStylePill: {
    left: 8,
    flex: 1,
    top: -4,
  },
  textContainerStyle: {
    ...marginStyles.mb20,
    ...marginStyles.mr8,
    width: '100%',
  },
  iconStylePill: {
    position: 'absolute',
    zIndex: 2,
    top: 10,
  },
  horizontalLineStyle: {
    top: 18,
    left: -12,     // for mweb it is required to -12 
  },
  selectedTextWrapper: {
    ...marginStyles.ml16,
    ...marginStyles.mb20,
    ...paddingStyles.pa8,
    width: '100%',
    borderTopLeftRadius: 16,
    borderBottomLeftRadius: 16,
  },
  pillMessageContainer: {
    width: '100%',
    ...marginStyles.ml20,
  },
  selectTextStyle: {
    ...fontStyles.labelSmallBold,
    ...marginStyles.ml16,
  } as TextStyle,
  text: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
    ...marginStyles.ml8,
  } as TextStyle,
  subText: {
    ...fontStyles.labelSmallRegular,
    ...marginStyles.ml8,
    color: holidayColors.gray,
  } as TextStyle,
});

export default StepperListItem;
