import React from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { ListItemProps, ListItems } from './StepperListTypes';
import StepperListItem from './StepperListItem';

const renderItem = (
  { item, index }: { item: ListItemProps; index: number },
  listLength: number,
) => {
  return <StepperListItem {...item} isLastItem={index === listLength - 1} />;
};

const StepperList = (props: ListItems) => {
  return (
    <View style={styles.wrapper}>
      <FlatList
        data={props.listItems}
        renderItem={(itemData) => renderItem(itemData, props?.listItems?.length)}
        contentContainerStyle={styles.flatListStyle}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    ...paddingStyles.pt4,
    ...paddingStyles.ph16,
    position: 'relative',
  },
  flatListStyle: {
    position: 'absolute',
  },
});

export default StepperList;
