import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
/* Components */
import BottomSheetOverlay from '../BottomSheetOverlay';
import PrimaryButton from '../Buttons/PrimaryButton';

const StoryKnowMore = ({
  knowMoreSection = {},
  handleClose = () => {},
  handleKnowMoreCtaClick = () => {},
  openKnowMoreModal = false,
}) => {
  const {
    text,
    header,
    description,
    ctaText,
    ctaUrl,
    persuasion = '',
    persuasionTextColour = holidayColors.green,
    persuasionTextBackgroundColour = holidayColors.fadedGreen,
  } = knowMoreSection || {};

  return (
    <BottomSheetOverlay
      title={header}
      containerStyles={{ ...paddingStyles.pa16 }}
      headingTextStyle={{ flex: 1 }}
      toggleModal={handleClose}
      visible={openKnowMoreModal}
    >
      {!!description && <Text style={styles.description}> {description} </Text>}
      {/* Persuasion View */}
      {!!persuasion && (
        <View
          style={[{ backgroundColor: persuasionTextBackgroundColour }, styles.persuasionContainer]}
        >
          <Text style={[{ color: persuasionTextColour }, styles.persuasionText]}>{persuasion}</Text>
        </View>
      )}
      {/* Persuasion View */}
      {!!ctaUrl && (
        <PrimaryButton
          buttonText={ctaText}
          handleClick={handleKnowMoreCtaClick}
          btnContainerStyles={marginStyles.mt16}
        />
      )}
    </BottomSheetOverlay>
  );
};

const styles = StyleSheet.create({
  description: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    ...marginStyles.mt10,
  },
  persuasionContainer: {
    ...paddingStyles.ph16,
    ...paddingStyles.pv12,
    ...marginStyles.mt16,
    ...holidayBorderRadius.borderRadius8,
  },
  persuasionText: {
    ...fontStyles.labelBaseRegular,
  },
});
export default StoryKnowMore;
