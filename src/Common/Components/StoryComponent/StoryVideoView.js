import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Image,
  StyleSheet,
  Text,
  SafeAreaView,
  Dimensions,
  Animated,
} from 'react-native';
import { MEDIA_TYPE, TEMPLATE_TYPES } from '../Interventions/PIP/constants';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

/* Components */
import StoryImageView from './StoryImageView';
import VideoPlayer from 'react-native-video';
import { RESIZE_MODE_IMAGE } from 'mobile-holidays-react-native/src/HolidayConstants';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';

const StoryVideoView = (props) => {
  const [videoError, setVideoError] = useState(false);
  const [videoLoader, setVideoLoader] = useState(false);

  const {
    storyDetails = [],
    progressIndex = 0,
    setCurrentVideoDuration,
    videoSoundMuted = false,
    storyPaused = false,
    templateType,
  } = props;
  const currentStory = storyDetails?.[progressIndex] || {};
  const { url = '', type = '', videoFrameUrl = '' } = currentStory;

  const renderVideoError = () => {
    return (
      <View style={errorStyles.container}>
        <Image
          source={require('@mmt/legacy-assets/src/img/error-icon.webp')}
          style={errorStyles.icon}
        />
        <Text style={errorStyles.text}>Video unavailable</Text>
      </View>
    );
  };

  const renderVideoLoader = () => {
    return (
      <View style={loaderStyles.container}>
        <Spinner
              size={40}
              strokeWidth={3}
              progressPercent={90}
              speed={1.5}
              color={holidayColors.white}
          />
      </View>
    );
  };

  const setDuration = (data) => {
    setVideoLoader(false);
    if (type === MEDIA_TYPE.VIDEO) {
      setCurrentVideoDuration({ duration: data?.duration, index: progressIndex });
    }
  };

  return (
    <View style={{ width: '100%', height: ' 100%' }}>
      <VideoPlayer
        source={{ uri: url }}
        onError={(e) => {
          setVideoError(true);
        }}
        onBuffer={(e) => setVideoLoader(e.isBuffering)}
        muted={videoSoundMuted}
        disableSeekbar={true}
        renderBottomControls={false}
        paused={storyPaused}
        poster={videoFrameUrl}
        posterResizeMode={RESIZE_MODE_IMAGE.CONTAIN}
        resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
        onLoad={setDuration}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
        }}
      />
      {videoError && renderVideoError()}
      {videoLoader && renderVideoLoader()}
    </View>
  );
};

export default StoryVideoView;

const errorStyles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba( 0, 0, 0, 0.5 )',
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    marginBottom: 16,
  },
  text: {
    backgroundColor: 'transparent',
    color: '#f27474',
  },
});

const loaderStyles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  videoFrameImage: {
    width: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
    backgroundColor: holidayColors.black,
  },
});
