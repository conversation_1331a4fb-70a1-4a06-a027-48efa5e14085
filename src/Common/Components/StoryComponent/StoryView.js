import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Image,
  StyleSheet,
  Text,
  SafeAreaView,
  Dimensions,
  Animated,
  ActivityIndicator,
} from 'react-native';
import { MEDIA_TYPE } from '../Interventions/PIP/constants';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

/* Components */
import StoryImageView from './StoryImageView';
import VideoPlayer from 'react-native-video';
import StoryActions from './StoryActions';
import StoryVideoView from './StoryVideoView';

const StoryView = (props) => {
  const {
    storyDetails = [],
    progressIndex = 0,
    imageStyle = {},
    setCurrentVideoDuration,
    templateType,
    ...rest
  } = props;
  const currentStory = storyDetails?.[progressIndex] || {};
  const {
    url = '',
    type = '',
    header = '',
    headerColour = holidayColors.white,
    subHeader = '',
    subHeaderColour = holidayColors.white,
  } = currentStory;

  return (
    <SafeAreaView style={styles.divStory}>
      <View style={styles.divStory}>
        {type === MEDIA_TYPE.VIDEO ? (
          <StoryVideoView
            {...rest}
            storyDetails={storyDetails}
            setCurrentVideoDuration={setCurrentVideoDuration}
            progressIndex={progressIndex}
            templateType={templateType}
          />
        ) : (
          <StoryImageView
            imgSource={url}
            templateType={templateType}
            textDetails={{
              header,
              headerColour,
              subHeader,
              subHeaderColour,
            }}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

export default StoryView;

const styles = StyleSheet.create({
  divStory: {
    width: '100%',
    height: '100%',
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imgStyle: {
    alignSelf: 'center',
    resizeMode: 'stretch',
  },
  storyActionsContainer: {
    position: 'absolute',
    top: 20,
    left: 0,
    width: '100%',
    zIndex: 1,
  },
});
