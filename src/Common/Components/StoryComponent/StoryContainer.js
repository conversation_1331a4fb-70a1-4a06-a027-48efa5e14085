import React, { useState, useRef, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  Platform,
  Keyboard,
  Dimensions,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  KeyboardAvoidingView,
  StatusBar,
  BackHandler,
} from 'react-native';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { holidayColors } from '../../../Styles/holidayColors';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing';
import { fontStyles } from '../../../Styles/holidayFonts';
import { IMAGE_ICON_KEYS, getOpitimsedImageUrl } from '../HolidayImageUrls';
import { MEDIA_TYPE } from '../Interventions/PIP/constants';
import { LongPressGestureHandler, TapGestureHandler, State } from 'react-native-gesture-handler';
import { SCREEN_SECTION_CLICK } from './constants';

/* Components */
import StoryActions from './StoryActions';
import ProgressView from './ProgressView';
import StoryView from './StoryView';
import StoryKnowMore from './StoryKnowMore';
import HolidayImageHolder from '../HolidayImageHolder';
import { isAndroidClient, isIosClient } from '../../../utils/HolidayUtils';
import LinearGradient from 'react-native-linear-gradient';
import { HARDWARE_BACK_PRESS } from 'mobile-holidays-react-native/src/SearchWidget/SearchWidgetConstants';
import useBackHandler from  '../../../hooks/useBackHandler';

const DEFAULT_DURATION = 200;

const getInitialDurationList = ({ storyDetails = [] }) => {
  let durationList = {};
  storyDetails?.map((storyItem, index) => {
    if (storyItem?.type === MEDIA_TYPE.IMAGE) {
      durationList[index] = storyItem.duration || DEFAULT_DURATION;
    } else if (storyItem.type === MEDIA_TYPE.VIDEO) {
      durationList[index] = DEFAULT_DURATION;
    }
  });
  return durationList;
};
const StoryContainer = (props) => {
  const [progressIndex, setProgressIndex] = useState(0);
  const [openKnowMoreModal, setOpenKnowMoreModal] = useState(false);
  const [videoSoundMuted, setVideoSoundMuted] = useState(false);
  const [storyPaused, setStoryPaused] = useState(null);

  const tapGestureHandlerRef = useRef(null);
  const longPressGestureHandlerRef = useRef(null);
  const durationList = useRef(getInitialDurationList({ storyDetails: props?.storyDetails || [] }));

  const autoPopupOpened = useRef(null);
  const windowWidth = Dimensions.get('window').width;
  const windowHeight = Dimensions.get('window').height;
  const progressViewWidth = props?.progressView?.width ?? Dimensions.get('window').width;
  const {
    onComplete,
    visible,
    containerStyle,
    storyDetails,
    knowMoreSection,
    imageStyle,
    templateType,
    progressView,
    trackPipEvents = () => {},
  } = props;

  const positions = useMemo(
    () => ({
      skipToPrevPart: (1 / 5) * windowWidth,
      pause: (3 / 5) * windowWidth,
      skipToNextPart: windowWidth,
    }),
    [windowWidth],
  );

  const backPressHandler = () => {
    handleBack();
    return true;
  };

  useBackHandler(backPressHandler);

  const handleClickKnowMore = () => {
    if (!autoPopupOpened.current) {
      trackPipEvents({ eventName: `${openKnowMoreModal ? 'Close' : 'Click'}_KnowMore` });
      handlePauseVideo(!openKnowMoreModal);
      setOpenKnowMoreModal(!openKnowMoreModal);
    } else if (typeof onComplete === 'function') {
      onComplete();
    }
  };

  const getCurrentMediaDuration = () => {
    return durationList.current?.[progressIndex] || DEFAULT_DURATION;
  };

  const setCurrentVideoDuration = ({ duration, index }) => {
    durationList.current[index] = duration;
  };

  const handleVideoSound = () => {
    trackPipEvents({ eventName: `Click_${videoSoundMuted ? 'Unmute' : 'Mute'}` });
    setVideoSoundMuted(!videoSoundMuted);
  };

  const handlePauseVideo = (value) => {
    setStoryPaused(value);
  };
  const onArrorClick = (type) => {
    switch (type) {
      case SCREEN_SECTION_CLICK.LEFT:
        onChange(progressIndex === 0 ? progressIndex : progressIndex - 1);
        break;
      case SCREEN_SECTION_CLICK.RIGHT:
        const size = storyDetails?.length - 1 || 0;
        onChange(progressIndex === size ? progressIndex : progressIndex + 1);
        break;
      case SCREEN_SECTION_CLICK.CENTER:
        handleVideoSound();
        break;
    }
  };

  const onChange = (position) => {
    if (position < storyDetails.length) {
      setProgressIndex(position);
    } else {
      autoPopupOpened.current = true;
      trackPipEvents({ eventName: `${openKnowMoreModal ? 'Close' : 'Click'}_KnowMore` });
      setOpenKnowMoreModal(true);
    }
  };

  const onLongPressGestureHandlerStateChange = ({ nativeEvent }) => {
    if (nativeEvent.state === State.ACTIVE) {
      handlePauseVideo(true);
    } else if (nativeEvent.state === State.END) {
      handlePauseVideo(false);
    }
  };
  const isGestureClickAreaInLimit = (y) => {
    return y > windowHeight * 0.1 && y < windowHeight * 0.85;
  };

  const onTapGestureHandlerStateChange = ({ nativeEvent }) => {
    if (nativeEvent.state === State.ACTIVE && isGestureClickAreaInLimit(nativeEvent.y)) {
      if (nativeEvent.x < positions.skipToPrevPart) {
        trackPipEvents({ eventName: `Click_Previous_${progressIndex}` });
        onArrorClick(SCREEN_SECTION_CLICK.LEFT);
      } else if (nativeEvent.x < positions.pause) {
        onArrorClick(SCREEN_SECTION_CLICK.CENTER);
      } else if (nativeEvent.x < positions.skipToNextPart) {
        trackPipEvents({ eventName: `Click_Next_${progressIndex}` });
        onArrorClick(SCREEN_SECTION_CLICK.RIGHT);
      }
    }
  };

  const handleKnowMoreCtaClick = () => {
    trackPipEvents({ eventName: 'CTA_KnowMore' });
    GenericModule.openDeepLink(knowMoreSection?.ctaUrl);
    setTimeout(() => {
      onComplete();
    }, 700);
  };

  const handleBack = () => {
    trackPipEvents({ eventName: 'Click_Back' });
    onComplete();
  };
  const renderContent = () => {
    return (
      <TapGestureHandler
        ref={tapGestureHandlerRef}
        onHandlerStateChange={onTapGestureHandlerStateChange}
        simultaneousHandlers={[tapGestureHandlerRef, longPressGestureHandlerRef]}
        waitFor={[tapGestureHandlerRef, longPressGestureHandlerRef]}
        minDist={0}
        minPointers={1}
        maxPointers={1}
      >
        <LongPressGestureHandler
          ref={longPressGestureHandlerRef}
          onHandlerStateChange={onLongPressGestureHandlerStateChange}
          simultaneousHandlers={longPressGestureHandlerRef}
          waitFor={longPressGestureHandlerRef}
          numberOfTaps={1}
          minPointers={1}
          minDurationMs={200}
          maxDist={0}
        >
          <View style={containerStyle ? containerStyle : styles.parentView}>
            <StoryView
              storyDetails={storyDetails}
              progressIndex={progressIndex}
              imageStyle={imageStyle}
              videoSoundMuted={videoSoundMuted}
              storyPaused={storyPaused}
              templateType={templateType}
              setCurrentVideoDuration={setCurrentVideoDuration}
            />

            {/* Progress Bar View */}
            <LinearGradient
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 0.0, y: 1.0 }}
              colors={[ 'rgba(0,0,0,1)', 'rgba(0,0,0,0.8)', 'rgba(0,0,0,0.6)', holidayColors.transparent]}
              locations={[0.0,0.5,0.6,1]}
              style={styles.storyTopContainer}
            >
            <View style={[styles.progressViewContainer, { width: progressViewWidth }]}>
              <ProgressView
                storyDetails={storyDetails}
                progressIndex={progressIndex}
                storyPaused={storyPaused}
                onChange={(position) => onChange(position)}
                getDuration={getCurrentMediaDuration}
              />
            </View>
            {/* Progress Bar View */}

            <View style={styles.storyActionsContainer}>
              <StoryActions
                onBackPressed={handleBack}
                progressIndex={progressIndex}
                storyDetails={storyDetails}
                isVideoStory={storyDetails?.[progressIndex]?.type === MEDIA_TYPE.VIDEO}
                videoSoundMuted={videoSoundMuted} // TODO add handling for sound
                handleVideoSound={handleVideoSound}
              />

            </View>
            </LinearGradient>
            <LinearGradient
              start={{ x: 0.0, y: 0.0 }}
              end={{ x: 0.0, y: 1.0 }}
              colors={[holidayColors.transparent, 'rgba(0,0,0,0.5)', holidayColors.black]}
              locations={[0.0,0.2,0.8]}
              style={styles.storyKnowMoreContainer}
            >
              <TouchableOpacity style={styles.StoryKnowMoreParent} onPress={handleClickKnowMore}>
                <Text style={styles.storyKnowMoreText}>Know More</Text>
                <HolidayImageHolder
                  imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.DOUBLE_UP_ARROW)}
                  style={styles.knowMoreIcon}
                />
              </TouchableOpacity>
            </LinearGradient>

            {openKnowMoreModal && (
              <StoryKnowMore
                knowMoreSection={knowMoreSection}
                handleKnowMoreCtaClick={handleKnowMoreCtaClick}
                handleClose={handleClickKnowMore}
                openKnowMoreModal={openKnowMoreModal}
              />
            )}
          </View>
        </LongPressGestureHandler>
      </TapGestureHandler>
    );
  };

  return (
    <SafeAreaView>
      {isIosClient() && (
        <KeyboardAvoidingView behavior="padding">
          <View>{visible ? renderContent() : <View />}</View>
        </KeyboardAvoidingView>
      )}

      {isAndroidClient() && (visible ? renderContent() : <View />)}
    </SafeAreaView>
  );
};

export default StoryContainer;

const styles = StyleSheet.create({
  parentView: {
    width: '100%',
    height: '100%',
    // flexDirection: 'column',
  },
  customView: {
    position: 'absolute',
    flexDirection: 'column',
    width: Dimensions.get('window').width, // Important
    height: '100%',
  },

  progressViewContainer: {
    flexDirection: 'row',
    flex: 1,
    ...marginStyles.mt16,
    ...marginStyles.ml10,
    ...paddingStyles.pa10,
  },
  progressView: {
    width: Dimensions.get('window').width, // Important
    position: 'absolute',
    flexDirection: 'row',
  },
  storyActionsContainer: {

    width: '95%',
    ...marginStyles.mh16,
  },
  storyKnowMoreContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',

    flexDirection: 'row',
    ...paddingStyles.pt20,
    ...paddingStyles.pb10,
  },
  storyTopContainer: {
    position: 'absolute',
    top: 0,
    width: '100%',
    zIndex:1,
    flexDirection: 'column',

  },
  StoryKnowMoreParent: {
    bottom: Platform.select({
      ios: 10,
      android: 20,
    }),
    left: '3%',
    zIndex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  knowMoreIcon: {
    width: 30,
    height: 30,
    ...marginStyles.ml4,
  },
  storyKnowMoreText: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.white,
  },
});
