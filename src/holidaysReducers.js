import holidaysLanding from './LandingNew/Reducers/HolidayLandingReducers';
import holidaysMap from './Map/Redux/HolidayMapReducer';
import holidaysGrouping from './Grouping/Reducers/HolidayGroupingReducers';
import holidaysPhoenixGroupingV2 from './PhoenixGroupingV2/Reducers';
import holidaysSearchWidget from './SearchWidget/Reducers/HolidaySearchWidgetReducers';
import holidaysGroupingFilter from './Grouping/Reducers/HolidayGroupingFilterReducers';
import holidaysDetail from './PhoenixDetail/Reducers/HolidayDetailReducers';
import holidaysSMEDetails from './SmeDetails/Reducers';
import holidayHotelListingReducer from './ListingHotelNew/Reducers/HolidayHotelListingReducer';
import holidaysReview from './Review/Reducers/HolidayReviewReducers';
import holidaysMapExplore from './Map/Redux/HolidayMapExploreReducer';
import holidaysMapViewHolder from './Map/Redux/HolidayMapViewHolderReducer';
import holidaysFlightOverlay from './PhoenixDetail/Reducers/HolidayFlightOverlayReducer';
import holidaysActivityOverlay from './PhoenixDetail/Reducers/PhoenixActivityOverlayReducer';
import holidaysActivityDetail from './PhoenixDetail/Reducers/PhoenixActivityDetailReducer';
import feedback from './Common/Components/Feedback/redux/reducer';
import mimaPreSalesEditDetailReducer from './MimaPreSales/reducer/MimaPreSalesEditDetailReducer';
import travelTidbitsReducer from './PhoenixDetail/Reducers/HolidayTidbitsReducer';


const holidaysReducers = {
  holidaysLanding,
  holidaysMap,
  holidaysGrouping,
  holidaysPhoenixGroupingV2,
  holidaysSearchWidget,
  holidaysGroupingFilter,
  holidaysDetail,
  holidaysSMEDetails,
  holidayHotelListingReducer,
  holidaysReview,
  holidaysMapExplore,
  holidaysMapViewHolder,
  holidaysFlightOverlay,
  holidaysActivityOverlay,
  holidaysActivityDetail,
  feedback,
  mimaPreSalesEditDetailReducer,
  travelTidbitsReducer,
};


export default holidaysReducers;
