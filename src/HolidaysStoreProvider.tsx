import React from 'react';
import { Provider } from 'react-redux';
import holidaysStore from './holidaysStore';

interface Props {
  children: React.ReactNode;
}

/**
 * A wrapper component that provides the Holidays-specific Redux store to its children.
 * Use this to connect Holiday components to the dedicated Holidays store instead of the main app store.
 */
const HolidaysStoreProvider: React.FC<Props> = ({ children }) => {
  return <Provider store={holidaysStore}>{children}</Provider>;
};

export default HolidaysStoreProvider; 