import fecha from 'fecha';
import { PDT_LOB, PDT_RAW_EVENT, TAX_COLLECT_TCS_TYPES } from '../../HolidayConstants';
import {
  ADD_TRAVELLER_OVERLAY,
  COUNTRY_CODE_OVERLAY,
  couponErrorMessages,
  DATE_FORMAT,
  DATE_WITH_DAY_FORMAT,
  DELHI_STATE_ID,
  DISCOUNT_TYPE_INSTANT,
  dynamicReviewErrorMessages,
  EXTRA_INFO_OVERLAY,
  FARE_BREAKUP,
  FEMALE,
  FEMALE_TITLE_MRS,
  FEMALE_TITLE_MS,
  FPH_REVIEW_PDT_PAGE_NAME,
  FULL_PAYMENT_OPTION,
  HOTEL_CHECKIN_DATE_FORMAT,
  INCLUSIONS_OVERLAY,
  MALE,
  OWN_CODE_OVERLAY,
  PART_DATE_FORMAT,
  PART_DATE_FORMAT2,
  PART_PAYMENT_OPTION,
  PDT_LAST_PAGE_NAME,
  PDTConstants,
  prePaymentErrorMessages,
  PRESALES,
  REVIEW_PDT_PAGE_NAME,
  reviewErrorMessages,
  SAVED_TRAVELLER_OVERLAY,
  SELECT_DEFAULT,
  SPECIAL_REQUEST_OVERLAY,
  THANKYOU_DATE_FORMAT,
  VIEW_MORE_COUPONS_OVERLAY,
} from '../HolidayReviewConstants';
import {
  createRandomString,
  createSearchCriteriaAppliedMap,
  getItineraryComponents,
  isNotNullAndEmptyCollection,
  isNullOrEmptyCollection,
} from '../../utils/HolidayUtils';
import {viewTypes} from '../../Listing/ListingConstants';
import {convertToFullDateFormat, createPersuasionLoggingMap} from '../../Listing/Utils/HolidayListingUtils';
import {getPaxCount, isFlightIncluded} from '../../PhoenixDetail/Utils/HolidayDetailUtils';
import {
  COUPON_CODE_FAILED,
  COUPON_CODE_SUCCESS,
  DEFAULT_TRAVELLER_COUNT,
  OBT_BRANCH,
} from '../../PhoenixDetail/DetailConstants';
import {getEvar108ForReview, trackReviewPageClickEvent} from '../../utils/HolidayTrackingUtils';
import {rupeeFormatter} from '@mmt/legacy-commons/Helpers/currencyUtils';
import HolidayDataHolder from '../../utils/HolidayDataHolder';
import {TRACKING_EVENTS} from '../../HolidayTrackingConstants';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { VPP_POPUPS } from 'mobile-holidays-react-native/src/Common/Components/VisaProtectionPlan/VPPConstant';
import { detailReviewFailure } from '../../PhoenixDetail/DetailConstants';


export const getSelectedPaymentType = paymentScheduleOptions => {
  if (
    !paymentScheduleOptions ||
    isNullOrEmptyCollection(paymentScheduleOptions.paymentSchedules)
  ) {
    return -1;
  }

  const paymentSchedule = paymentScheduleOptions.paymentSchedules.filter(
    schedule => schedule.sequenceNo === paymentScheduleOptions.selected
  )[0];

  if (paymentSchedule) {
    return paymentSchedule.paymentType;
  }

  return FULL_PAYMENT_OPTION;
};

export const getSelectedPartPaymentFirstPayment = paymentScheduleOptions => {
  if (
    !paymentScheduleOptions ||
    isNullOrEmptyCollection(paymentScheduleOptions.paymentSchedules)
  ) {
    return 0;
  }

  const paymentSchedule = paymentScheduleOptions.paymentSchedules.filter(
    schedule =>
      schedule.sequenceNo === paymentScheduleOptions.selected &&
      schedule.paymentType === PART_PAYMENT_OPTION
  )[0];

  if (
    paymentSchedule &&
    isNotNullAndEmptyCollection(paymentSchedule.installmentDetails) &&
    paymentSchedule.installmentDetails[0].partPaymentValue &&
    paymentSchedule.installmentDetails[0].partPaymentValue > 0
  ) {
    return paymentSchedule.installmentDetails[0].partPaymentValue;
  }

  return 0;
};






export const getCouponData = (dealDetail, code) => {
  const couponData = {};
  if (!dealDetail || isNullOrEmptyCollection(dealDetail.couponDetails)) {
    return couponData;
  }

  const coupon = dealDetail.couponDetails.filter(
    couponDetail => couponDetail.selected
  )[0];
  if (coupon) {
    couponData.couponCode = coupon.couponCode;
    couponData.discountAmount = coupon.discountAmount;
    couponData.selected = true;
  } else {
    couponData.couponCode = code;
    couponData.discountAmount = 0;
    couponData.selected = false;
  }
  return couponData;
};

export const getPhoenixCouponData = (couponResponse, code) => {

  const {dealDetail,pricingDetail} = couponResponse || {};
  const discountsApplied = pricingDetail?.reviewAdditionalPricingDetail?.discountsApplied;
  const couponData = {};
  if (!dealDetail || isNullOrEmptyCollection(dealDetail.couponDetails)) {
    return couponData;
  }

  const coupon = dealDetail.couponDetails.filter(
    couponDetail => couponDetail.selected
  )[0];
  if (coupon) {
    couponData.couponCode = coupon.couponCode;
    couponData.discountAmount = coupon.discountAmount;
    couponData.selected = true;
  } else {
    couponData.couponCode = discountsApplied?.length > 0 ? code : '';
    couponData.discountAmount = 0;
    couponData.selected = discountsApplied?.length > 0;
  }
  return couponData;
};

export const getSelectedCoupon = dealDetail => {
  if (!dealDetail || isNullOrEmptyCollection(dealDetail.couponDetails)) {
    return {};
  }

  const coupon = dealDetail.couponDetails.filter(
    couponDetail => couponDetail.selected
  )[0];
  if (coupon) {
    return coupon;
  }
  return {};
};

export const getSelectedCouponType = dealDetail => {
  if (!dealDetail || isNullOrEmptyCollection(dealDetail.couponDetails)) {
    return '';
  }

  const coupon = dealDetail.couponDetails.filter(
    couponDetail => couponDetail.selected
  )[0];
  if (coupon) {
    return coupon.couponType;
  }
  return '';
};

export const getSelectedCouponDiscount = dealDetail => {
  if (!dealDetail || isNullOrEmptyCollection(dealDetail.couponDetails)) {
    return 0;
  }

  const coupon = dealDetail.couponDetails.filter(
    couponDetail => couponDetail.selected
  )[0];
  if (coupon) {
    return coupon.discountAmount;
  }
  return 0;
};

export const checkCouponAvailable = dealDetail => {
  if (!dealDetail || isNullOrEmptyCollection(dealDetail.couponDetails)) {
    return false;
  }
  return true;
};

export const getThankyouDate = departureDate => {
  const deptDate = fecha.parse(departureDate, DATE_FORMAT);
  return fecha.format(deptDate, THANKYOU_DATE_FORMAT);
};

export const getPrice = pricingDetail =>
  pricingDetail.price * pricingDetail.paxMultiplier;

export const getDiscountedPrice = pricingDetail =>
  pricingDetail.discountedPrice * pricingDetail.paxMultiplier;

export const getGst = pricingDetail =>
  pricingDetail.serviceTax * pricingDetail.paxMultiplier;

export const getPackagePrice = (pricingDetail, reviewWalletDetail) => {
  const {extraChargesBreakUps} = pricingDetail;
  const totalPrice = extraChargesBreakUps.filter(fare => fare.fareType === FARE_BREAKUP.GRAND_TOTAL);
  return totalPrice.length > 0 && totalPrice[0].value ? totalPrice[0].value : 0;
};

export const fetchReviewErrorMessage = error => {
  let errorMessage = reviewErrorMessages.DEFAULT;
  let codeValue;
  try {
    if (error && error.code) {
       codeValue = error.code.substring(
        error.code.length - 3,
        error.code.length
      );
      codeValue = parseInt(codeValue, 10);
      if (codeValue >= 1 && codeValue < 13) {
        errorMessage = reviewErrorMessages.MESSAGE1;
      } else if (codeValue === 13) {
        errorMessage = reviewErrorMessages.MESSAGE2;
      } else if (codeValue === 14) {
        errorMessage = reviewErrorMessages.MESSAGE3;
      } else if (codeValue === 17 || codeValue === 18) {
        errorMessage = error.message;
      }
      let errorType = error?.errorType;
      if(errorType === detailReviewFailure.REVIEW_FAILED_TYPE_ACTIVITY || errorType === detailReviewFailure.REVIEW_FAILED_TYPE_ADDON_INCLUSION || errorType === detailReviewFailure.REVIEW_FAILED_TYPE_TRANSFER){
        errorMessage = error?.message;
      }
    }
  } catch (e) {
    // Default already set
  }
  return {errorMessage, codeValue };
};

export const fetchDyanmicReviewErrorMessage = error => {
  let errorMessage = dynamicReviewErrorMessages.DEFAULT;
  try {
    if (error && error.code) {
      let codeValue = error.code.substring(
        error.code.length - 3,
        error.code.length
      );
      codeValue = parseInt(codeValue, 10);
      if (codeValue === 24) {
        errorMessage = dynamicReviewErrorMessages.MESSAGE1;
      } else if (codeValue === 5) {
        errorMessage = dynamicReviewErrorMessages.MESSAGE2;
      }
    }
  } catch (e) {
    // Default already set
  }
  return errorMessage;

};

export const fetchPrePaymentErrorMessage = error => {
  let errorMessage = prePaymentErrorMessages.DEFAULT;
  try {
    if (error && error.code) {
      let codeValue = error.code.substring(
        error.code.length - 3,
        error.code.length
      );
      codeValue = parseInt(codeValue, 10);
      if (codeValue === 4 || codeValue === 18 || codeValue === 13) {
        errorMessage = prePaymentErrorMessages.MESSAGE1;
      } else if (
        (codeValue >= 1 && codeValue < 4) ||
        (codeValue > 5 && codeValue < 11) ||
        codeValue === 22 ||
        codeValue === 33
      ) {
        errorMessage = prePaymentErrorMessages.DEFAULT;
      } else if (
        (codeValue >= 11 && codeValue <= 17) ||
        (codeValue >= 19 && codeValue <= 21) ||
        (codeValue >= 28 && codeValue <= 32) ||
        (codeValue >= 34 && codeValue <= 38)
      ) {
        errorMessage = error.message;
      } else if (codeValue >= 24 && codeValue <= 27) {
        errorMessage = prePaymentErrorMessages.MESSAGE2;
      } else if (codeValue === 47) {
        errorMessage = prePaymentErrorMessages.MESSAGE3;
      } else if (codeValue === 41) {
        errorMessage = prePaymentErrorMessages.MESSAGE4;
      } else if (codeValue === 42) {
        errorMessage = prePaymentErrorMessages.MESSAGE5;
      } else if (codeValue === 43) {
        errorMessage = prePaymentErrorMessages.MESSAGE6;
      } else if (codeValue === 44) {
        errorMessage = prePaymentErrorMessages.MESSAGE7;
      } else if (codeValue === 5) {
        errorMessage = prePaymentErrorMessages.MESSAGE8;
      } else if (codeValue === 57) {
        // Component Failure Case
        errorMessage = null;
      }
    }
  } catch (e) {
    // Default already set
  }
  return errorMessage;
};

export const fetchCouponErrorMessage = error => {
  let errorMessage = couponErrorMessages.DEFAULT;
  try {
    if (error && error.code) {
      let codeValue = error.code.substring(
        error.code.length - 3,
        error.code.length
      );
      codeValue = parseInt(codeValue, 10);
      if (codeValue === 8 || codeValue === 9) {
        errorMessage = couponErrorMessages.MESSAGE1;
      }
    }
  } catch (e) {
    // Default already set
  }
  return errorMessage;
};

export const getStateId = (stateName, hpStates) => {
  if (isNullOrEmptyCollection(hpStates)) {
    return DELHI_STATE_ID;
  }
  const hpState = hpStates.filter(state => state.stateName === stateName)[0];
  if (hpState) {
    return hpState.id;
  }
  return DELHI_STATE_ID;
};

export const updateHolidayReviewData = (holidayReviewData, reviewDetail) => {
  const {departureDate, cityName} = reviewDetail.departureDetail;
  const {duration} = reviewDetail.destinationDetail;
  const checkoutDate = new Date(new Date(departureDate).getTime() + (duration * 24 * 60 * 60 * 1000));
  holidayReviewData.packageId = reviewDetail.id;
  holidayReviewData.branch = reviewDetail.metadataDetail.branch;
  holidayReviewData.name = reviewDetail.name;
  holidayReviewData.categoryId = reviewDetail.pricingDetail.categoryId;
  holidayReviewData.departureDetail = {
    departureCity: cityName,
    departureDate,
  };
  holidayReviewData.destinationDetail = {
    tagDestination: reviewDetail.tagDestination.name,
    duration,
  };
  holidayReviewData.dynamicPackageId = reviewDetail.dynamicId;
  holidayReviewData.checkoutDate = fecha.format(checkoutDate, 'YYYY-MM-DD');
};

export const updateRoomDetails = (roomDetails, rooms) => {
  for (let i = 0; i < rooms.length; i += 1) {
    roomDetails[i] = rooms[i];
  }
};

export const createLoggingMap = (reviewData, roomDetails, isFphReview = false) => {
  const { holidayReviewData = '', reviewDetail } = reviewData || '';
  const { metadataDetail = {}} = reviewDetail || {};
  const { trackingInfo } = metadataDetail || {};
  const { source = '' } = holidayReviewData || {};

  const loggingMap = initializeLoggingMap();
  const departureDate = reviewData.holidayReviewData.departureDetail
    ? reviewData.holidayReviewData.departureDetail.departureDate
    : null;
  const departureCity = reviewData.holidayReviewData.departureDetail
    ? reviewData.holidayReviewData.departureDetail.departureCity
    : null;
  loggingMap.otherDetails = {
    travel_start_date: departureDate
      ? convertToFullDateFormat(departureDate)
      : null,
    last_page_name: PDT_LAST_PAGE_NAME,
    departureCity,
  };
  if (reviewData.reviewDetail && reviewData.reviewDetail.offlineDetail && reviewData.reviewDetail.offlineDetail.quoteId) {
    loggingMap.otherDetails.quote_id = reviewData.reviewDetail.offlineDetail.quoteId;
  }
  const pricingDetails = getPricingDetails(reviewData.reviewDetail);
  const paxCount = {
    adultCount: getPaxCount(roomDetails, 'noOfAdults'),
    childCount: getPaxCount(roomDetails, 'noOfChildrenWB') +  getPaxCount(roomDetails, 'noOfChildrenWOB'),
    infantCount: getPaxCount(roomDetails, 'noOfInfants'),
    roomCount: roomDetails.length,
  };
  loggingMap.searchCriteriaAppliedMap = createSearchCriteriaAppliedMap(
    reviewData.holidayReviewData
  );
  loggingMap.packageDetails = createPackageDetailsLoggingMap(
    reviewData.reviewDetail,
    paxCount
  );
  loggingMap.pricingDetails = createPricingDetailsLoggingMap(
    pricingDetails,
    paxCount
  );
  loggingMap.discountDetails = createDiscountDetailsLoggingMap(pricingDetails);
  loggingMap.requestDetails = createRequestDetailsLoggingMap(
    reviewData.cmp,
    reviewData.isWG,
    isFphReview
  );
  loggingMap.safe = reviewData.reviewDetail && reviewData.reviewDetail.metadataDetail && reviewData.reviewDetail.metadataDetail.safe;
  loggingMap.trackingDetails = {
    source,
    ticketSource: trackingInfo?.ticketSource || '',
  };
  return loggingMap;
};

export const initializeLoggingMap = () => {
  const loggingMap = {};
  loggingMap.filterDetails = {};
  loggingMap.packageDetails = {};
  loggingMap.pricingDetails = {};
  loggingMap.discountDetails = {};
  loggingMap.sorterDetails = {};
  loggingMap.otherDetails = {};
  loggingMap.interventionDetails = {};
  loggingMap.persuasionDetails = {};
  loggingMap.requestDetails = {};
  loggingMap.errorDetails = {};
  loggingMap.searchCriteriaAppliedMap = {};
  loggingMap.travellerDetails = {};
  return loggingMap;
};

export const updateTravellerDateToPageData = (
  reviewData,
  userDetails,
  roomUnits
) => {
  if (reviewData.pageDataMap) {
    const travellerDetails = {};
    const traveller = getPrimaryTraveller(roomUnits);
    if (traveller) {
      travellerDetails.primary_trv_first_name =
        traveller.travellerName.givenName;
      travellerDetails.primary_trv_last_name = traveller.travellerName.lastName;
      travellerDetails.primary_trv_gender =
        traveller.travellerType.gender === 'm' ? 'Male' : 'Female';
    }
    travellerDetails.primary_trv_e_com_id = userDetails.email;
    travellerDetails.primary_trv_m_com_id = userDetails.phone;
    travellerDetails.primary_trv_gst_city = userDetails.city;
    travellerDetails.primary_trv_gst_state = userDetails.state;
    travellerDetails.primary_trv_gst_address = userDetails.address;
    reviewData.pageDataMap.travellerDetails = travellerDetails;
  }
};

export const getPrimaryTraveller = roomUnits => {
  if (isNullOrEmptyCollection(roomUnits)) {
    return null;
  }

  for (let index = 0; index < roomUnits.length; index += 1) {
    const travellers = roomUnits[index].travellers;
    if (!isNullOrEmptyCollection(travellers)) {
      for (let roomIndex = 0; roomIndex < travellers.length; roomIndex += 1) {
        const traveller = travellers[roomIndex];
        if (traveller.primary) {
          return traveller;
        }
      }
    }
  }
  return null;
};

export const createRequestDetailsLoggingMap = (cmp, isWG, isFphReview = false) => {
  const requestDetailsLoggingMap = {};
  requestDetailsLoggingMap.lob = PDT_LOB;
  requestDetailsLoggingMap.page = isFphReview ? FPH_REVIEW_PDT_PAGE_NAME : REVIEW_PDT_PAGE_NAME;
  requestDetailsLoggingMap.funnel_step = isFphReview ? FPH_REVIEW_PDT_PAGE_NAME : REVIEW_PDT_PAGE_NAME;
  requestDetailsLoggingMap.cmp_channel = cmp;
  requestDetailsLoggingMap.isWG = isWG;
  return requestDetailsLoggingMap;
};

const getPricingDetails = reviewDetailData => {
  const pricingDetails = {};
  let discountedPriceParam;
  let preDiscountedPrice;
  let gstAmount;
  let walletDiscount;
  let selectedCoupon;
  if (reviewDetailData) {
    preDiscountedPrice = getPrice(reviewDetailData.pricingDetail);
    discountedPriceParam = getPackagePrice(reviewDetailData.pricingDetail);
    gstAmount = getGst(reviewDetailData.pricingDetail);
    selectedCoupon = getSelectedCoupon(reviewDetailData.dealDetail);
    if (selectedCoupon) {
      pricingDetails.couponType = selectedCoupon.couponType;
      pricingDetails.couponCode = selectedCoupon.couponCode;
      pricingDetails.cdfDiscount = selectedCoupon.discountAmount;
      const isInstantDiscount =
        selectedCoupon.couponType === DISCOUNT_TYPE_INSTANT;
      pricingDetails.instantDiscount = isInstantDiscount;
      pricingDetails.cashbackDiscount = !isInstantDiscount;
    }
    if (reviewDetailData.walletDetail) {
      walletDiscount = reviewDetailData.walletDetail.totalRedeemableAmount;
    }

    pricingDetails.discountedPrice = discountedPriceParam;
    pricingDetails.preDiscountedPrice = preDiscountedPrice;
    pricingDetails.gstAmount = gstAmount;
    pricingDetails.walletDiscount = walletDiscount;
  }
  return pricingDetails;
};

const createPackageDetailsLoggingMap = (reviewDetailData, paxCount) => {
  const packageDetailsMap = {};
  if (reviewDetailData) {
    packageDetailsMap.pkg_nm = reviewDetailData.name;
    packageDetailsMap.pkg_hld_id = reviewDetailData.id;
    packageDetailsMap.pkg_tag_dest = reviewDetailData.tagDestination.name;
    packageDetailsMap.pd_px_ad = paxCount.adultCount;
    packageDetailsMap.pd_px_ch = paxCount.childCount;
    packageDetailsMap.pd_px_inf = paxCount.infantCount;
    packageDetailsMap.roomCount = paxCount.roomCount;
    packageDetailsMap.pkg_cities = getPackageCities(reviewDetailData);
    packageDetailsMap.pkg_hld_durn = getHolidayDuration(reviewDetailData);
    packageDetailsMap.pkg_hld_type = getPackageType(reviewDetailData);
    packageDetailsMap.pkg_catgry = reviewDetailData.categoryDetail.name;
    packageDetailsMap.pkg_flight_included = isFlightIncluded(reviewDetailData);
    packageDetailsMap.pkg_itinry_components = getItineraryComponents(reviewDetailData?.inclusionsDetail || {});
    packageDetailsMap.pkg_hol_star_rating = reviewDetailData?.hotelDetail?.hotels?.[0]?.starRating;
    if (reviewDetailData?.metadataDetail && reviewDetailData?.metadataDetail?.premium) {
      packageDetailsMap.premium = reviewDetailData?.metadataDetail?.premium;
    }
  }
  return packageDetailsMap;
};

const getPackageCities = reviewDetailData => {
  const citiesSet = new Set();
  const {destinations} = reviewDetailData.destinationDetail;
  for (let index = 0; index < destinations.length; index += 1) {
    citiesSet.add(destinations[index].name);
  }
  return [...citiesSet];
};

const getHolidayDuration = reviewDetailData =>
  reviewDetailData.destinationDetail.duration;
const getPackageType = reviewDetailData =>
  reviewDetailData.metadataDetail.packageType;

const createPricingDetailsLoggingMap = (pricingDetails, paxCount) => {
  const pricingDetailsMap = {};
  if (pricingDetails) {
    const {
      preDiscountedPrice,
      discountedPrice,
      gstAmount,
      cdfDiscount,
      walletDiscount,
    } = pricingDetails;
    pricingDetailsMap.prc_pre_disc = preDiscountedPrice;
    pricingDetailsMap.prc_post_disc = discountedPrice;
    pricingDetailsMap.prc_gst_amt = gstAmount;
    pricingDetailsMap.prc_cdf_disc = cdfDiscount;
    pricingDetailsMap.prc_wal_disc = walletDiscount;
    pricingDetailsMap.prc_tot_payable_ad = discountedPrice + gstAmount;
    pricingDetailsMap.prc_tot_bkg_amt =
      pricingDetailsMap.prc_tot_payable_ad *
      (paxCount.adultCount + paxCount.childCount + paxCount.infantCount);
  }
  return pricingDetailsMap;
};

const createDiscountDetailsLoggingMap = pricingDetails => {
  const discountDetailsMap = {};
  if (pricingDetails) {
    const {
      couponCode,
      couponType,
      instantDiscount,
      cashbackDiscount,
    } = pricingDetails;
    discountDetailsMap.cpn_code = couponCode;
    discountDetailsMap.cpn_cd_type = couponType;
    discountDetailsMap.cpn_status = couponCode
      ? COUPON_CODE_SUCCESS
      : COUPON_CODE_FAILED;
    discountDetailsMap.cpn_inst_disc_stat = instantDiscount;
    discountDetailsMap.cpn_cashbk_disc_stat = cashbackDiscount;
  }
  return discountDetailsMap;
};

export const trackReviewActionClickEvents = ( eventName = '' ) => {
  logHolidayReviewPDTClickEvents({
    actionType: PDT_EVENT_TYPES.buttonClicked,
    value: eventName
  })
  trackReviewLocalClickEvent(eventName);
}
export const trackReviewLocalClickEvent = async (
    eventName = '',
    suffix = '',
    holidayReviewData = {},
    isFPHReview = false,
    {prop66 = '', omniData = {}} = {}
) => {
  const { holidayReviewData: reviewData = {}, pageDataMap } = holidayReviewData || {};
  const { trackingDetails = '' } = pageDataMap || {};
  const { source = '', ticketSource = ''} = trackingDetails || {};
  const { trackingData = {}, reviewType } = reviewData || {};
  const {categoryTrackingEvent = ''} = trackingData || {};
  const pageName = isFPHReview ? FPH_REVIEW_PDT_PAGE_NAME : REVIEW_PDT_PAGE_NAME;
  trackReviewPageClickEvent({
    omniPageName: pageName,
    omniEventName: eventName + suffix,
    prop66,
    prop83: HolidayDataHolder.getInstance().getBanner(),
    omniData: {
      ...omniData,
      [TRACKING_EVENTS.M_V57]: categoryTrackingEvent,
      [TRACKING_EVENTS.M_V108]: getEvar108ForReview({
        reviewType,
        pageName,
        source,
        ticketSource,
      }),
    },
    pdtData: {
      pageDataMap: holidayReviewData?.pageDataMap || {},
      eventType: PDT_RAW_EVENT,
      activity: eventName,
      requestId: createRandomString(),
      branch: holidayReviewData?.branch || OBT_BRANCH,
    },
  });
};

export const trackReviewLocalChatClickEvent = (
  eventName = '',
  suffix = '',
  holidayReviewData = {},
  isFPHReview = false,
) => {
  const { holidayReviewData: reviewData = {}, pageDataMap } = holidayReviewData || {};
  const { reviewType } = reviewData || {};
  const { source = '' } = pageDataMap || {};
  const pageName = isFPHReview ? FPH_REVIEW_PDT_PAGE_NAME : REVIEW_PDT_PAGE_NAME;
  trackReviewPageClickEvent({
    omniPageName: pageName,
    omniEventName: eventName,
    prop66: suffix,
    omniData: {
      [TRACKING_EVENTS.M_V108]: getEvar108ForReview({ reviewType, source, pageName }),
    },
    pdtData: {
      pageDataMap: holidayReviewData?.pageDataMap || {},
      eventType: PDT_RAW_EVENT,
      activity: '',
      requestId: createRandomString(),
      branch: holidayReviewData?.branch || OBT_BRANCH,
    },
  });
};
export const trackReviewErrorLocalChatClickEvent = ({
  eventName = '',
  suffix = '',
  holidayReviewData = {},
  isFPHReview = false,
  evar22 = '',
}) => {
  const { holidayReviewData: reviewData = {}, pageDataMap } = holidayReviewData || {};
  const { reviewType } = reviewData || {};
  const { source = '' } = pageDataMap || {};
  const pageName = isFPHReview ? FPH_REVIEW_PDT_PAGE_NAME : REVIEW_PDT_PAGE_NAME;
  trackReviewPageClickEvent({
    omniPageName: pageName,
    omniEventName: eventName,
    prop66: suffix,
    omniData: {
      [TRACKING_EVENTS.M_V108]: getEvar108ForReview({ reviewType, source, pageName }),
      ...(evar22 && { [TRACKING_EVENTS.M_V22]: `HLD:${evar22}` }),
    },
    pdtData: {
      pageDataMap: holidayReviewData?.pageDataMap || {},
      eventType: PDT_RAW_EVENT,
      activity: '',
      requestId: createRandomString(),
      branch: holidayReviewData?.branch || OBT_BRANCH,
    },
  });
};
export const getEventNameForPopup = popUp => {
  switch (popUp) {
    case SPECIAL_REQUEST_OVERLAY:
      return PDTConstants.REMARKS;
    case INCLUSIONS_OVERLAY:
      return PDTConstants.INCLUSIONS;
    case SAVED_TRAVELLER_OVERLAY:
      return PDTConstants.SAVED_TRAVELLER;
    case ADD_TRAVELLER_OVERLAY:
      return PDTConstants.ADD_TRAVELLER;
    case OWN_CODE_OVERLAY:
      return PDTConstants.OWN_CODE;
    case VIEW_MORE_COUPONS_OVERLAY:
      return PDTConstants.VIEW_MORE;
    case EXTRA_INFO_OVERLAY:
      return PDTConstants.EXTRA_INFO;
    case COUNTRY_CODE_OVERLAY:
      return PDTConstants.COUNTRY_CODE_LIST;
    default:
      return '';
  }
};

export const getInclusionLabel = (inclusionsDetail, label) => {
  let index = 0;
  if (inclusionsDetail) {
    const count = getInclusionsCount(inclusionsDetail);
    if (inclusionsDetail.flights) {
      label += getToken(index, count);
      label += 'flights';
      index++;
    }
    label += getToken(index, count);
    label += 'hotels';
    index++;
    if (inclusionsDetail.airportTransfers || inclusionsDetail.carItinerary) {
      label += getToken(index, count);
      label += 'transfers';
      index++;
    }
    if (inclusionsDetail.activities) {
      label += getToken(index, count);
      label += 'activities';
      index++;
    }
    if (inclusionsDetail.visa) {
      label += getToken(index, count);
      label += 'visa';
      index++;
    }
  }
  return label;
};

const getToken = (index, count) => {
  if (index === 0) {
    return ' ';
  } else if (index !== 0 && index === count - 1) {
    return ' and ';
  } else if (index !== 0) {
    return ' , ';
  }
};

const getInclusionsCount = inclusionsDetail => {
  let count = 1;
  if (inclusionsDetail.flights) {
    count += 1;
  }
  if (inclusionsDetail.airportTransfers || inclusionsDetail.carItinerary) {
    count += 1;
  }
  if (inclusionsDetail.activities) {
    count += 1;
  }
  if (inclusionsDetail.visa) {
    count += 1;
  }

  return count;
};


export const isDateValid = dateString => {
  if (dateString) {
    const dateArr = dateString.split('-');
    if (dateArr.length !== 3) {
      return false;
    }
    if (
      dateArr[2] === 'NaN' ||
      dateArr[1] === 'NaN' ||
      dateArr[0] === 'NaN' ||
      dateArr[2] > 31 ||
      dateArr[1] > 12
    ) {
      return false;
    }
  }
  return true;
};

export const isPackageOBT = reviewDetail => {
  const {metadataDetail} = reviewDetail;
  return metadataDetail && metadataDetail.branch === OBT_BRANCH;
};

export const showPriceChange = reviewDetail => {
  const {pricingDetail} = reviewDetail || {};
  return pricingDetail && pricingDetail.priceDiffDetail ? pricingDetail.priceDiffDetail.priceDifference !== 0 && !pricingDetail.priceDiffDetail.absorbed : false;
};

export const getPriceChangeDifference = reviewDetail => {
  const {pricingDetail} = reviewDetail || {};
  return pricingDetail?.priceDiffDetail?.priceDifference;
};

export const getGenderFromTitle = title => {
  return title === FEMALE_TITLE_MRS || title === FEMALE_TITLE_MS
    ? FEMALE
    : MALE;
};

export const getDefaultRoom = () => {
  return [
    {
      noOfAdults: DEFAULT_TRAVELLER_COUNT,
      noOfChildrenWB: 0,
      noOfInfants: 0,
      listOfAgeOfChildrenWB: [],
      listOfAgeOfChildrenWOB: [],
      noOfChildrenWOB: 0,
    },
  ];
};

export const getPromotionDiscount = reviewAdditionalPricingDetail => {
  let discountAmount = 0;
  for (
    let i = 0;
    i < reviewAdditionalPricingDetail.discountsApplied.length;
    i += 1
  ) {
    const discountApplied = reviewAdditionalPricingDetail.discountsApplied[i];
    if (
      discountApplied.type !== 'ECOUPON' &&
      discountApplied.discountAppliedType === 'INSTANT'
    ) {
      discountAmount += discountApplied.amount.price;
    }
  }
  return discountAmount;
};

export const createErrorMap = (errorDetails, holidayReviewData) => {
  const loggingMap = initializeLoggingMap();
  const {departureDetail, roomDetails, source = ''} = holidayReviewData;
  loggingMap.otherDetails = {
    travel_start_date: departureDetail && departureDetail.departureDate ? departureDetail.departureDate : '',
    last_page_name: 'details',
  };
  if (holidayReviewData.quoteRequestId) {
    loggingMap.otherDetails.quote_id = holidayReviewData.quoteRequestId;
  }
  const paxCount = {
    adultCount: getPaxCount(roomDetails, 'noOfAdults'),
    childCount: getPaxCount(roomDetails, 'noOfChildrenWB'),
    infantCount: getPaxCount(roomDetails, 'noOfInfants'),
    roomCount: roomDetails.length,
  };
  loggingMap.packageDetails = getPackageReviewErrorLogMap(holidayReviewData, paxCount);
  loggingMap.searchCriteriaAppliedMap = createSearchCriteriaAppliedMap(holidayReviewData);
  loggingMap.requestDetails = createRequestDetailsLoggingMap(
    holidayReviewData.cmp,
    holidayReviewData.isWG
  );
  loggingMap.errorDetails = errorDetails;
  const packageDetailsError = {};
  packageDetailsError.packageId = holidayReviewData.packageId;
  packageDetailsError.name = holidayReviewData.packageName;
  loggingMap.packageDetailsError = packageDetailsError;
  loggingMap.trackingDetails = {
    source,
  };
  return loggingMap;
};

const getPackageReviewErrorLogMap = (holidayReviewData, paxCount) => {
  const packageDetailsMap = {};
  if (holidayReviewData) {
    packageDetailsMap.pkg_nm = holidayReviewData.packageName;
    packageDetailsMap.pkg_hld_id = holidayReviewData.packageId;
    packageDetailsMap.pkg_tag_dest = holidayReviewData.tagDestination;
    packageDetailsMap.pd_px_ad = paxCount.adultCount;
    packageDetailsMap.pd_px_ch = paxCount.childCount;
    packageDetailsMap.pd_px_inf = paxCount.infantCount;
    packageDetailsMap.roomCount = paxCount.roomCount;
    packageDetailsMap.pkg_hld_durn = holidayReviewData.duration;
    packageDetailsMap.pkg_hld_type = holidayReviewData.packageType;
    packageDetailsMap.pkg_catgry = holidayReviewData.categoryId;
  }
  return packageDetailsMap;
};


export const getFareBreakup = (reviewDetail, holidaysReview) => {
  const fareBreakUp = [];
  fareBreakUp.push({
    label: 'Total Basic Price',
    value: '\u20B9' + ' ' + rupeeFormatter(getPrice(reviewDetail.pricingDetail)),
  });
  if (holidaysReview.selectedCouponDiscount > 0) {
    fareBreakUp.push({
      label: 'Coupon Discount',
      value: ' - \u20B9' + ' ' + rupeeFormatter(holidaysReview.selectedCouponDiscount),
    });
  }
  if (holidaysReview.promotionDiscount > 0) {
    fareBreakUp.push({
      label: 'Promotion Discount',
      value: ' - \u20B9' + ' ' + rupeeFormatter(holidaysReview.promotionDiscount),
    });
  }
  for (let i = 0; i < holidaysReview.extraChargesBreakUps.length; i += 1) {
    const charge = holidaysReview.extraChargesBreakUps[i];
    fareBreakUp.push({
      label: charge.label,
      value: charge.fareSign === 'SUBTRACT' ? '- ' : '' + '\u20B9' + ' ' + rupeeFormatter(charge.value),
    });
  }
  return fareBreakUp;
};

export const isTcsApplicable = (pricingDetail, tcsType) => {
  const { tcsMetadata } = pricingDetail || {};
  const { tcsApplicableEnum } = tcsMetadata || {};
  return tcsApplicableEnum === tcsType;
}

export const isPanWithTCSRequired = (pricingDetail) => {
  return isTcsApplicable(pricingDetail, TAX_COLLECT_TCS_TYPES.PAN_WITH_TCS);
}

export const isPanRequired = (pricingDetail) => {
  return isTcsApplicable(pricingDetail, TAX_COLLECT_TCS_TYPES.PAN);
};

export const getAddonCode = (value) => {
  if (typeof value !== "string" || value.trim() === "") return "";

  return value === VPP_POPUPS.VISA_PROTECTION_PLAN ? "VPP_" : `${value}_`;
};


