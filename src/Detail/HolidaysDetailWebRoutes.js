import React from 'react';
import {View} from 'react-native';
import {Route, Switch, withRouter} from 'react-router';
import {connect} from 'react-redux';
import {injectAsyncReducer} from '@mmt/legacy-commons/AppState/asyncStore';
import holidaysDetail from '../PhoenixDetail/Reducers/HolidayDetailReducers';
import {withRouterState} from 'web/WebRouter';
import {isReturningBackRaw} from '../utils/HolidayUtils';
import HolidaysWebRoute from '../HolidayWebRoute';
import PhoenixDetailCommonContainer from '../PhoenixDetail/Containers/PhoenixDetailCommonContainer';
import holidaysFlightOverlay from '../PhoenixDetail/Reducers/HolidayFlightOverlayReducer';
import holidayHotelListingReducer from '../ListingHotelNew/Reducers/HolidayHotelListingReducer';
import holidaysDetailOverlays from '../PhoenixDetail/Components/DetailOverlays/Redux/DetailOverlaysReducer';
import holidaysReview from '../Review/Reducers/HolidayReviewReducers';
import HolidayDeeplinkParser from '../utils/HolidayDeeplinkParser';
import travelTidbitsReducer from '../PhoenixDetail/Reducers/HolidayTidbitsReducer';

class HolidaysDetailWeb extends HolidaysWebRoute {
    constructor(props) {
        super(props, 'holidaysDetail');

        let holidaysDetailData = props.holidaysDetailData ? props.holidaysDetailData : {destinationDetail : {}};
        const isDeeplink = HolidayDeeplinkParser.isDeviceTypeAvailableinLink(window.location.href);
        const parsedParams = HolidayDeeplinkParser.parseDetailPageDeeplink(window.location.href, isDeeplink);
        holidaysDetailData = {
            ...parsedParams,
            ...holidaysDetailData,
        };
        if (!holidaysDetailData.fromDeepLink) {
            holidaysDetailData.images = isReturningBackRaw(props) ? [] : holidaysDetailData.images;
        }
        this.state = {
            holidaysDetailData: holidaysDetailData,
        };
    }

    render() {
        return (
            <PhoenixDetailCommonContainer {...this.state} />
        );
    }
}

const mapStateToProps = state => ({
    ...state.holidaysDetail,
});

injectAsyncReducer('holidaysDetail', holidaysDetail);
injectAsyncReducer('holidaysFlightOverlay', holidaysFlightOverlay);
injectAsyncReducer('holidayHotelListingReducer',holidayHotelListingReducer);
injectAsyncReducer('holidaysDetailOverlays',holidaysDetailOverlays);
injectAsyncReducer('holidaysReview', holidaysReview);
injectAsyncReducer('holidaysFlightOverlay', holidaysFlightOverlay);
injectAsyncReducer('holidayHotelListingReducer',holidayHotelListingReducer);
injectAsyncReducer('travelTidbitsReducer',travelTidbitsReducer);



export const HolidaysDetailContainer = connect(mapStateToProps, null)(HolidaysDetailWeb);

const HolidaysDetailRoutes = () => (
    <View style={{flex: 1, display: 'flex', flexDirection: 'column'}}>
        <Switch>
            <Route exact path="/holidays/international/package" component={withRouterState(HolidaysDetailContainer)}/>
            <Route exact path="/holidays/india/package" component={withRouterState(HolidaysDetailContainer)}/>
            <Route exact path="/holidays/international/savePackage" component={withRouterState(HolidaysDetailContainer)}/>
            <Route exact path="/holidays/india/savePackage" component={withRouterState(HolidaysDetailContainer)}/>
        </Switch>
    </View>
);

export default withRouter(HolidaysDetailRoutes);
