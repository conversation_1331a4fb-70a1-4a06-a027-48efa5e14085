import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import PropTypes from 'prop-types';
import ChildAgeSection from './ChildAgeSection';
import Counter from '@Frontend_Ui_Lib_App/Counter';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts'

export default class OpenCard extends React.Component {
  constructor(props) {
    super(props);
    const {
      adultCount, childCount, childAgeArray,
    } = this.props;
    this.state = {
      childCount,
      adultCount,
      childAgeArray,
    };
  }

  componentWillReceiveProps(nextProps) {
    const {
      adultCount, childCount, childAgeArray,
    } = nextProps;
    this.setState({
      childCount,
      adultCount,
      childAgeArray,
    });
  }

  ageSection = () => {
    const ageSectionDom = [];
    const shouldSetChildAge = this.state.childAgeArray.length > 0;
    let activeSorter = -1;

    for (let i = 0; i < this.state.childCount; i += 1) {
      if (shouldSetChildAge === true) {
        activeSorter = this.state.childAgeArray.length > i ? this.state.childAgeArray[i] : 0;
      } else {
        activeSorter = -1;
      }
      ageSectionDom.push(
        <View style={styles.marginTop15} key={i}>
          <Text style={styles.ageHeading}>Age of Child {i + 1}</Text>
          <ChildAgeSection
            handleChange={this.props.changeCallbacks.setAge}
            index={i}
            activeSorter={activeSorter}
          />
        </View>
      );
    }
    return ageSectionDom;
  };

  render() {
    const {index} = this.props;
    return (
      <View style={styles.openCard}>
        <Text style={styles.roomHeading}>ROOM {index + 1}</Text>
        <View style={styles.container}>
          <View style={styles.textContainer}>
            <Text style={styles.heading}>Adults</Text>
            <Text style={styles.subHeading}>Age 12y and above</Text>
          </View>
          <Counter
              counterValue={`${this.state.adultCount}`}
              customContainerStyle={styles.customContainerStyle}
              decrementHandler={this.props.changeCallbacks.decreaseAdultCount}
              incrementHandler={this.props.changeCallbacks.increaseAdultCount}
              textStyle={styles.textStyle}
            />
        </View>
        <View style={styles.container}>
          <View style={styles.textContainer}>
            <Text style={styles.heading}>Children</Text>
            <Text style={styles.subHeading}>Age 11y and below</Text>
          </View>
          <Counter
              counterValue={`${this.state.childCount}`}
              customContainerStyle={styles.customContainerStyle}
              decrementHandler={this.props.changeCallbacks.decreaseChildCount}
              incrementHandler={this.props.changeCallbacks.increaseChildCount}
              textStyle={styles.textStyle}
            />
        </View>
        {
          this.ageSection()
        }
      </View>
    );
  }
}

OpenCard.propTypes = {
  childAgeArray: PropTypes.array.isRequired,
  index: PropTypes.number.isRequired,
  childCount: PropTypes.number.isRequired,
  adultCount: PropTypes.number.isRequired,
  changeCallbacks: PropTypes.object.isRequired,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 15,
  },
  customContainerStyle: {
    elevation: 4,
    shadowColor: holidayColors.black,
    shadowOffset: {
      height: 2,
      width: 0
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    width: 120
  },
  heading: {
    color: '#4a4a4a',
    fontSize: 18,
    fontFamily: 'Lato-Bold',
    fontWeight: 'bold',
  },
  subHeading: {
    color: '#9b9b9b',
    fontSize: 12,
    fontFamily: 'Lato-Regular',
    fontWeight: 'normal',
  },
  openCard: {
    backgroundColor: '#fff',
    margin: 1,
    borderWidth: 1,
    borderColor: '#eee',
    paddingHorizontal: 22,
    paddingVertical: 20,
    borderRadius: 4,
    elevation: 2,
    shadowColor: '#330000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: {
      width: 1,
      height: 1,
    },
    marginBottom: 20,
  },
  roomHeading: {
    color: '#9b9b9b',
    fontFamily: 'Lato-Regular',
    fontSize: 12,
    marginBottom: 10,
  },
  ageHeading: {
    color: '#4a4a4a',
    fontSize: 18,
    fontFamily: 'Lato-Regular',
    marginBottom: 12,
    marginTop: 2,

  },
  marginTop15: {marginTop: 15},
  textStyle: {
    ...fontStyles.labelLargeBlack,
    lineHeight: 21.6,
    flex: 1
  },
});
