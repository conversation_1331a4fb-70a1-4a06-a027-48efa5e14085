import React from 'react';
import { Provider } from 'react-redux';
import holidaysStore from './holidaysStore';
import holidayModule from './holidays.module';

/**
 * Module loader component for Holidays.
 * This can be used by the main app to load the Holidays module with its dedicated store.
 * 
 * Example usage:
 * ```jsx
 * <HolidaysModule>
 *   {(module) => <ModuleRenderer module={module} />}
 * </HolidaysModule>
 * ```
 */
const HolidaysModule = ({ children }) => {
  // Wrap any rendered content in the dedicated store provider
  return (
    <Provider store={holidaysStore}>
      {children(holidayModule)}
    </Provider>
  );
};

export default HolidaysModule; 