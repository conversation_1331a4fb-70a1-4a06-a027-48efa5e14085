import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image, Text } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconGreenTick from '@mmt/legacy-assets/src/green_tick.webp';
import iconThumb from '@mmt/legacy-assets/src/holidays/thumb-icon.png';

const COUPON_STATE = {
  REMOVE: 'REMOVE',
  APPLY: 'APPLY',
};
const CouponCard = (props) => {
  const {
    couponCode = '',
    couponDesc = '',
    offerPrice = '',
    selected = false,
    validateCoupon = () => {},
  } = props || {};

  const imageSoure = selected ? iconGreenTick : iconThumb;
  const imageStyle = selected ? styles.iconGreenTick : styles.iconThumb;
  const couponValue = selected ? COUPON_STATE.REMOVE : COUPON_STATE.APPLY;
  return (
    <View style={[styles.couponCard, selected ? styles.selectedCard : null]}>
      <View style={styles.selectIconWrap}>
        <Image source={imageSoure} style={imageStyle} />
      </View>
      <View style={styles.cardDesc}>
        <View>
          <Text style={styles.couponCode}>{couponCode}</Text>
        </View>
        <View>
          <Text style={styles.couponDesc}>{couponDesc}</Text>
        </View>
        <View style={styles.offerPriceContainer}>
          <Text style={styles.offerPrice}>{offerPrice}</Text>
        </View>
      </View>
      <TouchableOpacity onPress={() => validateCoupon(couponValue)}>
        <View>
          <Text style={[styles.apply, selected ? styles.removeCoupon : styles.applyCoupon]}>
            {couponValue}
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  couponCard: {
    backgroundColor: '#ffffff',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#d8d8d8',
    padding: 15,
    flexDirection: 'row',
    marginVertical: 5,
    flex: 1,
  },
  selectedCard: {
    backgroundColor: 'rgba(193, 241, 221, 0.65)',
    borderColor: 'rgba(51, 209, 143, 0.75)',
  },
  selectIconWrap: {
    width: '7%',
  },
  selectIcon: {
    width: 15,
    height: 15,
    resizeMode: 'cover',
  },
  linkText: {
    width: '20%',
  },
  cardDesc: {
    flexWrap: 'wrap',
    width: '75%',
  },
  iconGreenTick: {
    width: 16,
    height: 16,
    resizeMode: 'cover',
    borderRadius: 8,
  },
  iconThumb: {
    backgroundColor: 'rgba(205, 243, 255, 1)',
    width: 18,
    height: 18,
    resizeMode: 'cover',
    borderRadius: 9,
    right: 2,
  },
  couponCode: {
    ...AtomicCss.font12,
    ...AtomicCss.boldFont,
    ...AtomicCss.blackText,
    ...{ textDecorationLine: 'underline' },
  },
  couponDesc: {
    ...AtomicCss.font12,
    ...AtomicCss.regularFont,
    ...AtomicCss.defaultText,
    ...AtomicCss.marginTop5,
  },
  offerPriceContainer: {
    display: 'flex',
    flexDirection: 'row',
    height: 20,
    marginTop: 5,
  },
  offerPrice: {
    ...AtomicCss.font14,
    ...AtomicCss.blackFont,
    ...AtomicCss.blackText,
    ...AtomicCss.flex1,
  },
  apply: {
    ...AtomicCss.font13,
    ...AtomicCss.blackFont,
    ...AtomicCss.azure,
    ...AtomicCss.pushRight,
  },
  removeCoupon: {
    left: 0,
  },
  applyCoupon: {
    left: 14,
  },
});

export default CouponCard;
