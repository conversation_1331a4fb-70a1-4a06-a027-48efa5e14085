import React from 'react';
import { View, Text, Image, StyleSheet, Animated, Easing, TouchableOpacity } from 'react-native';
import PropTypes from 'prop-types';
import Offers from '../../PhoenixDetail/Components/Offers';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import {
  animateOverlay,
  calculateTotalDiscountPercentage,
} from '../../PhoenixDetail/Utils/HolidayDetailUtils';

const iconCross = require('@mmt/legacy-assets/src/iconCross.webp');

class OfferOverlay extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      overlayPosition: new Animated.Value(0),
    };
  }

  static navigationOptions = { header: null };

  componentDidMount() {
    animateOverlay(this, 400);
  }

  startAnimate(bottom, duration, delay) {
    Animated.timing(this.state.overlayPosition, {
      toValue: bottom,
      easing: Easing.easeInOut,
      duration,
      delay,
    }).start();
  }

  render() {
    const {
      togglePopup = () => {},
      categoryPrice = {},
      dealDetail = {},
      loggedIn = false,
      onLoginClicked = () => {},
      travellerCount = 0,
      aff = '',
      persuasion,
    } = this.props;
    const { price = '', discountedPrice = '' } = categoryPrice || {};
    const discountPercentage = calculateTotalDiscountPercentage(
      dealDetail,
      price,
      discountedPrice,
      travellerCount,
    );
    return (
      <View style={styles.overlayContainer}>
        <TouchableOpacity onPress={() => togglePopup('')} style={styles.overlayBg} />
        <Animated.View style={[styles.overlayContent, { bottom: this.state.overlayPosition }]}>
          <View style={styles.overlayTop}>
            <Text style={styles.heading}>Offers for you! </Text>
            {discountPercentage > 0 && (
              <Text style={styles.subHeading}>
                Saved {discountPercentage}% on the original pricing of ₹ {rupeeFormatter(price)}
              </Text>
            )}
            <TouchableOpacity
              onPress={() => togglePopup('')}
              style={styles.crossWrapper}
              activeOpacity={0.8}
            >
              <Image style={styles.iconCross} source={iconCross} />
            </TouchableOpacity>
          </View>
          <Offers
            categoryPrice={categoryPrice}
            dealDetail={dealDetail}
            loggedIn={loggedIn}
            onLoginClicked={onLoginClicked}
            travellerCount={travellerCount}
            aff={aff}
          />
          {persuasion ? persuasion : null}
        </Animated.View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  overlayContainer: {
    justifyContent: 'flex-end',
    position: 'absolute',
    bottom: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 12,
    elevation: 12,
  },
  overlayBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 3,
    elevation: 3,
  },
  heading: {
    fontSize: 18,
    marginBottom: 2,
    color: '#4a4a4a',
    fontFamily: 'Lato-Bold',
  },
  subHeading: {
    fontSize: 12,
    color: '#9b9b9b',
    fontFamily: 'Lato-Regular',
  },
  overlayContent: {
    backgroundColor: '#fff',
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 3,
    zIndex: 3,
    position: 'absolute',
    bottom: 0,
    marginBottom: -400,
    width: '100%',
    shadowOffset: {
      width: 1,
      height: 0,
    },
  },
  iconCross: {
    height: 22,
    width: 22,
  },
  crossWrapper: {
    position: 'absolute',
    paddingHorizontal: 4,
    paddingVertical: 4,
    top: 15,
    right: 11,
  },
  overlayTop: {
    paddingHorizontal: 16,
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderColor: '#eee',
    shadowColor: '#330000',
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 1,
    zIndex: 1,
    shadowOffset: {
      width: 1,
      height: 0,
    },
    marginBottom: 3,
  },
});

OfferOverlay.propTypes = {
  togglePopup: PropTypes.func.isRequired,
  loggedIn: PropTypes.bool.isRequired,
  onLoginClicked: PropTypes.func.isRequired,
  travellerCount: PropTypes.number.isRequired,
  categoryPrice: PropTypes.object.isRequired,
  aff: PropTypes.string,
  dealDetail: PropTypes.object,
};

OfferOverlay.defaultProps = {
  dealDetail: null,
  aff: null,
};
export default OfferOverlay;
