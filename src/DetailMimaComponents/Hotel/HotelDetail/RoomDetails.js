import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing/index';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';

const RoomDetails = ({ hotelDetailData, children: headingChild = null }) => {
  const { hotel } = hotelDetailData || {};
  const { roomTypes } = hotel || {};
  const { roomInformation, name } = roomTypes?.[0] || {};
  const { bedTypes, carpetArea, view: viewFromRoom } = roomInformation || {};
  const { size, sizeUnit } = carpetArea || {};

  return (
    <View>
      {headingChild ? (
         headingChild
      ) : (
        <View style={styles.roomTypeHdr}>
          <Text style={styles.roomTypeName}>{name}</Text>
        </View>
      )}
      {/*Table should be populated only when any of the below items are available.*/}
      {/* bedTypes viewFromRoom sizeUnit */}
      {(bedTypes || viewFromRoom || sizeUnit) && (
        <View style={styles.roomTypeTable}>
          {/*Handle Bed Type column*/}
          {!!bedTypes && (
            <View style={styles.roomTypeCategory}>
              <Text style={[styles.categoryTitle, marginStyles.mb2]}>BED TYPE</Text>
              <Text style={styles.categoryValue}>{bedTypes}</Text>
            </View>
          )}

          {/*Handle Room View column*/}
          {!!viewFromRoom && (
            <View style={styles.roomTypeCategory}>
              <Text style={[styles.categoryTitle, marginStyles.mb2]}>ROOM VIEW</Text>
              <Text style={styles.categoryValue}>{viewFromRoom}</Text>
            </View>
          )}

          {/*Handle Room size column*/}
          {!!sizeUnit && (
            <View style={[styles.roomTypeCategory, styles.lastRoomType]}>
              <Text style={[styles.categoryTitle, marginStyles.mb2]}>SIZE</Text>
              <View style={[cStyles.flexRow, cStyles.alignCenter]}>
                <Text style={styles.categoryValue}>{size}</Text>
                <Text style={styles.categoryValue}> {sizeUnit}</Text>
              </View>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  roomTypeHdr: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...paddingStyles.pa16,
  },
  roomTypeName: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  roomTypeTable: {
    backgroundColor: holidayColors.white,
    flexDirection: 'row',
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  roomTypeCategory: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    borderRightWidth: 1,
    borderColor: holidayColors.lightGray2,
    ...paddingStyles.ph10,
    ...paddingStyles.pv10,
    backgroundColor:holidayColors.white,
  },
  lastRoomType: {
    borderRightWidth: 0,
  },
  categoryTitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  categoryValue: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
    textAlign: 'center',
  },
  amenitiesImage: {
    width: 22,
    height: 22,
    resizeMode: 'contain',
  },
  noOfAmenities: {
    ...marginStyles.mt2,
    ...fontStyles.labelBaseBlack,
    color: holidayColors.primaryBlue,
    height: 22,
  },
  amenitiesText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
});

export default RoomDetails;
