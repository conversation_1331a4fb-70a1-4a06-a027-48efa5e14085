
import React, { useState } from 'react';
import { Text, Image, StyleSheet, TouchableOpacity, FlatList, View } from 'react-native';
import DropDownIcon from '@mmt/legacy-assets/src/arrow_dropdown.webp';
import BottomSheetOverlay from '../Common/Components/BottomSheetOverlay';
import { HTML_CODES } from '../HolidayConstants';
import { isEmpty, max } from 'lodash';
import { fontStyles } from '../Styles/holidayFonts';
import { holidayColors } from '../Styles/holidayColors';
import { marginStyles } from '../Styles/Spacing';
import { paddingStyles } from '../Styles/Spacing';
import { logPhoenixDetailPDTEvents } from '../utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../utils/HolidayPDTConstants';
import { DEVICE_WINDOW } from '../utils/HolidayUtils';
const ACTIVITIES_INCLUSIONS_TITLE = 'Activities & Inclusions in this Package';

const ActivityInclusionSection = ({ inclusionDetails = {}, containerStyles = {} }) => {
  const [openModal, setOpenModal] = useState(false);

  if (isEmpty(inclusionDetails)) {
    return null;
  }

  const handleClick = () => {
    const eventVal =`${openModal ? 'Close_' : 'Click_'}activities&inclusions`;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventVal
    })
    setOpenModal(!openModal);
  };

  const renderInclusion = ({ item, index }) => {
    const { title = '', description = '' } = item || {};
    return (
      <View style={styles.itemContainer}>
        <Text style={styles.bulletPoint}>{HTML_CODES.BULLET}</Text>
        {
          !!title && <Text style={styles.item}>
            {title} {!!description && <Text style={styles.itemValue}>: {description}</Text>}
          </Text>
        }
      </View>
    );
  };
  const getBottomSheetMaxHeight = (3 * DEVICE_WINDOW.height) / 4;
  return (
    <>
      <TouchableOpacity
        style={[styles.titleContainer, containerStyles]}
        activeOpacity={1}
        onPress={handleClick}
      >
        <Text style={styles.titleText}>{ACTIVITIES_INCLUSIONS_TITLE}</Text>
        <Image style={styles.icon} source={DropDownIcon} />
      </TouchableOpacity>
      {openModal && (
        <BottomSheetOverlay
          title={ACTIVITIES_INCLUSIONS_TITLE}
          headingTextStyle={styles.titleStyle}
          toggleModal={handleClick}
          containerStyles={[
            styles.bottomSheetOverlay,
            {
              maxHeight: getBottomSheetMaxHeight,
            },
          ]}
          visible={openModal}
        >
          <View style={styles.flatListWrapper}>
            <FlatList
              data={inclusionDetails}
              renderItem={renderInclusion}
              contentContainerStyle={[styles.inclusionListContainer]}
              keyExtractor={(_, index) => `${index}`}
              showsVerticalScrollIndicator={false}
            />
          </View>
        </BottomSheetOverlay>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  bottomSheetOverlay: {
    ...paddingStyles.pa16,
  },
  flatListWrapper: {
    height: '95%',

  },
  titleContainer: {
    flexDirection: 'row',
    ...marginStyles.mv10,
    ...paddingStyles.pb6,
    alignItems: 'center',
  },
  titleText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
  },
  titleStyle:{
    flex:1,
  },
  icon: {
    width: 10,
    height: 10,
    tintColor: holidayColors.primaryBlue,
    transform: [{ rotate: '-90deg' }],
    ...marginStyles.ml12,
    ...marginStyles.mt2,
    resizeMode: 'contain',
  },
  inclusionListContainer: {
    flexGrow: 1,
    ...paddingStyles.pb48,
  },
  itemContainer: {
    flexDirection: 'row',
    ...marginStyles.mv4,
    ...paddingStyles.pa2,
    alignItems: 'flex-start',
  },
  bulletPoint: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
    ...marginStyles.mh4,
    ...marginStyles.mt2,
  },
  item: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
    flex:1,
    lineHeight: 24,
  },
  itemValue: {
    ...fontStyles.labelBaseRegular,
  },
});
export default ActivityInclusionSection;
