import React from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import Separator from '../../PhoenixDetail/Components/SightSeenDetail/Separator';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const SightSeeingDetailContainer = ({ details, containerStyles }) => {
  const renderSeperator = () => (
    <View style={paddingStyles.ph8}>
      <Text style={styles.sightSeeingDetail}> | </Text>
    </View>
  );

  const renderSightSeeing = ({ item, index }) => {
    const { title, value } = item || {};
    return (
      <View style={styles.sightSeeingDetailContainer}>
        <Text style={styles.sightSeeingDetail}>{title}</Text>
        <Text style={styles.sightSeeingDetailText}>{value}</Text>
      </View>
    );
  };

  return (
    <FlatList
      horizontal
      data={details.filter(item => !item.title.includes('undefined'))}
      renderItem={renderSightSeeing}
      ItemSeparatorComponent={renderSeperator}
      contentContainerStyle={containerStyles}
    />
  );
};

const SightSeeingLocation = ({ locations = [] }) => {
  const renderItem = ({item, index}) => {
    const { cityName, name } = item;
    return (
      <View>
        <Text style={[styles.sightSeeingDetail, marginStyles.mt16]}>Location {index + 1}</Text>
        <Text style={[styles.sightSeeingLocation, marginStyles.mt10]}>{name}</Text>
        <Text style={[styles.sightSeeingDetail, marginStyles.mt10]}>{cityName}</Text>
        {index < locations.length - 1 && <Separator />}
      </View>
    );
  };

  return (
    <FlatList
      data={locations}
      keyExtractor={(_, index) => index}
      renderItem={renderItem}
      contentContainerStyle={styles.scrollView}
      showsVerticalScrollIndicator={false}
    />
  );
};
const styles = StyleSheet.create({
  topContainer: {
    padding: 15,
    elevation: 1,
  },
  sightSeeingDetailContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sightSeeingDetail: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.lightGray,
  },
  sightSeeingDetailText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  sightSeeingLocation: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
  },
});

export { SightSeeingDetailContainer, SightSeeingLocation };
