import React from 'react';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';
import SecondaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/SecondaryButton';
import GenericModule from '@mmt/legacy-commons/Native/GenericModule';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { GIFT_CARD_FUNNEL_DEEPLINK, REFERRAL_STATE, RNE_EVENT_NAMES, RNE_PAGE_NAME, RNE_MESSAGES } from '../constants';
import { StyleSheet } from 'react-native';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { sendWhatsAppMessage } from '../../utils/HolidayUtils';
import { trackRneClickEvent } from '../TrackingUtils';

const ReferralCardButton = ({ currentState, number, amount, expiryDays, referralLink }) => {
  const handleRemind = () => {
    let message = '';
    if (currentState === REFERRAL_STATE.INVITED) {
      trackRneClickEvent({
        omniPageName: RNE_PAGE_NAME.DETAIL,
        omniEventName: RNE_EVENT_NAMES.CLICK_REMIND_CLAIM,
      });
      message = RNE_MESSAGES[REFERRAL_STATE.INVITED]({ amount, expiryDays, referralLink });
    }
    if (currentState === REFERRAL_STATE.ACCEPTED) {
      const landingPageLink = 'https://www.makemytrip.com/holidays-india/';
      trackRneClickEvent({
        omniPageName: RNE_PAGE_NAME.DETAIL,
        omniEventName: RNE_EVENT_NAMES.CLICK_REMIND_BOOK,
      });
      message = RNE_MESSAGES[REFERRAL_STATE.ACCEPTED]({ amount, landingPageLink, expiryDays });
    }

    const link = `https://wa.me/${number}?text=${message}`;
    sendWhatsAppMessage(link);
  };

  const handleViewCard = () => {
    GenericModule.openDeepLink(GIFT_CARD_FUNNEL_DEEPLINK);
  };

  const renderCardButton = () => {
    switch (currentState) {
      case REFERRAL_STATE.INVITED:
        return (
          <SecondaryButton
            buttonText={'REMIND'}
            btnContainerStyles={styles.btnContainerStyles}
            btnTextStyles={styles.textStyles}
            handleClick={handleRemind}
          />
        );
      case REFERRAL_STATE.ACCEPTED:
        return (
          <SecondaryButton
            buttonText={'REMIND'}
            btnContainerStyles={styles.btnContainerStyles}
            btnTextStyles={styles.textStyles}
            handleClick={handleRemind}
          />
        );
      case REFERRAL_STATE.REDEEMED:
        return (
          <PrimaryButton
            buttonText={'VIEW'}
            btnContainerStyles={styles.btnContainerStyles}
            btnTextStyles={styles.textStyles}
            handleClick={handleViewCard}
          />
        );

      default:
        return null;
    }
  };

  return renderCardButton();
};

const styles = StyleSheet.create({
  btnContainerStyles: {
    ...paddingStyles.pt8,
    ...paddingStyles.pb8,
    ...paddingStyles.ph10,
    ...holidayBorderRadius.borderRadius8,
  },
  textStyles: {
    ...fontStyles.labelSmallBlack,
  },
});
export default ReferralCardButton;
