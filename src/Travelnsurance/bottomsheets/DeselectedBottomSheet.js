import React, { useEffect } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import PrimaryButton from '../../Common/Components/Buttons/PrimaryButton';
import SecondaryButton from '../../Common/Components/Buttons/SecondaryButton';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { paddingStyles, marginStyles } from '../../Styles/Spacing';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';

import BottomSheetOverlay from '../../Common/Components/BottomSheetOverlay';
import { INSURANCE_ACTIONS, INSURANCE_BOTTOMSHEETS, INSURANCE_PAGES } from '../utils/constants';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import HolidayImageHolder from '../../Common/Components/HolidayImageHolder';
import CrossIcon from '@mmt/legacy-assets/src/ic_cross__gray.webp';
const deselectBottomSheetTitle =
  'Are you sure you want to remove Travel + Medical Insurance from your package ?';
  const noSelection="No, Keep Selection"
  const removeSelection='Yes, Remove Insurance'
const DeselectedBottomSheet = (props) => {
  if (!props.modalVisible) {
    return [];
  }
  
  const captureClickEvents = ({ eventName = '', prop1 = '', }) => {
    logHolidayReviewPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName: prop1,
    });
    props.trackReviewLocalClickEvent(eventName, '', { prop1 });
  };

  useEffect(() => {
    captureClickEvents({ eventName:INSURANCE_ACTIONS.REMOVE, prop1: INSURANCE_BOTTOMSHEETS.REMOVE });
  }, []);

  const removeInsuranceClicked = () => {
    captureClickEvents({ eventName:INSURANCE_ACTIONS.UNSELECT, prop1: INSURANCE_BOTTOMSHEETS.REMOVE });
    props.removeInsuranceClicked();
  };
  const onKeepSelectionClicked = () => {
    captureClickEvents({ eventName:INSURANCE_ACTIONS.SELECT, prop1: INSURANCE_BOTTOMSHEETS.REMOVE });
    props.onKeepSelectionClicked();
  };
  return (
    <BottomSheetOverlay containerStyles={styles.containerStyles} visible={props.modalVisible} toggleModal={onKeepSelectionClicked} showCross={false}>
      <View>
          <View style={styles.headerContainer}>
            <View style={styles.titleContainer}>
              <Text style={styles.headingText}>{deselectBottomSheetTitle}</Text>
            </View>

            <TouchableOpacity
              style={styles.closeButton}
              onPress={onKeepSelectionClicked}
              activeOpacity={0.7}
            >
              <HolidayImageHolder 
                defaultImage={CrossIcon} 
                style={styles.closeIcon} 
              />
            </TouchableOpacity>
          </View>
        
        <View style={styles.buttonsContainer}>
            <PrimaryButton
              buttonText={noSelection}
              handleClick={onKeepSelectionClicked}
              btnContainerStyles={styles.updateButton}
            />
            <SecondaryButton
              buttonText={removeSelection}
              handleClick={removeInsuranceClicked}
              btnContainerStyles={styles.secondaryButton}
              isDisable={false}
            />
          </View>
        </View>
    </BottomSheetOverlay>
  );
};

const styles = StyleSheet.create({
  containerStyles: {
    ...paddingStyles.pa20,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  titleContainer: {
    width: '85%',
  },
  headingText: {
    color: holidayColors.black,
    ...fontStyles.headingMedium,
  },
  closeButton: {
    width: 25,
    height: 25,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: holidayColors.lightGray,
    position: 'absolute',
    right: 10,
    top: 0,
  },
  closeIcon: {
    width: 12,
    height: 12,
    tintColor: holidayColors.white,
  },
  buttonsContainer: {
    marginTop: 40,
  },
  subHeadingText: {
    ...fontStyles.labelMediumRegular,
    color: holidayColors.gray,
    ...marginStyles.mt10,
  },
  leftBtn: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.primaryBlue,
  },
  updateButton: {
    ...paddingStyles.ph12,
    ...marginStyles.mb16,
    height: 44,

  },
  secondaryButton: {
    paddingVertical: 10,
    paddingHorizontal: 10,
    ...marginStyles.mb16,
    height: 44,
  },
});

export default DeselectedBottomSheet;
