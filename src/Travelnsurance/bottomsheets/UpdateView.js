import React, { useEffect } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import PrimaryButton from '../../Common/Components/Buttons/PrimaryButton';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { paddingStyles } from '../../Styles/Spacing';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';

import BottomSheetOverlay from '../../Common/Components/BottomSheetOverlay';
import { INSURANCE_ACTIONS, INSURANCE_BOTTOMSHEETS } from '../utils/constants';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

const updateViewTitle = 'You have done some changes in your Holiday Package.';

const UpdateView = (props) => {
  if (!props.modalVisible) {
    return [];
  }
  const captureClickEvents = ({ eventName = '', prop1 = '', }) => {
    logHolidayReviewPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName ,
      subPageName: prop1,
    });
    props.trackReviewLocalClickEvent(eventName, '', { prop1 });
  };
  useEffect(()=> {
    captureClickEvents({ eventName:INSURANCE_ACTIONS.UPDATE, prop1: INSURANCE_BOTTOMSHEETS.UPDATE });
  },[])

  const onNotNowClicked = () => {
    captureClickEvents({ eventName:INSURANCE_ACTIONS.UNSELECT, prop1: INSURANCE_BOTTOMSHEETS.UPDATE });
    props.onNotNowClicked();
  }
  const onUpdateNowClicked = () => {
    captureClickEvents({ eventName:INSURANCE_ACTIONS.SELECT, prop1: INSURANCE_BOTTOMSHEETS.UPDATE });
    props.onUpdateNowClicked();
  }
  return (
    <BottomSheetOverlay containerStyles={styles.containerStyles} visible={props.modalVisible} toggleModal={() => {}} showCross={false} >
      <View style={styles.container}>
        <View>
          <Text style={styles.headingText}>{updateViewTitle}</Text>
        </View>
        <View style={[AtomicCss.marginTop5, AtomicCss.marginBottom10]}>
          <Text style={styles.subHeadingText}>Please update the package to retain your selection.</Text>
        </View>

        <View style={[AtomicCss.flexRow, AtomicCss.spaceBetween]}>
          <TouchableOpacity onPress={onNotNowClicked}>
            <View style={[AtomicCss.paddingLeft10, AtomicCss.paddingTop16]}>
              <Text style={styles.leftBtn}>NOT NOW</Text>
            </View>
          </TouchableOpacity>

          <View style={[AtomicCss.alignCenter]}>
            <PrimaryButton
              buttonText={'UPDATE PACKAGE'}
              handleClick={onUpdateNowClicked}
              btnContainerStyles={styles.updateButton}
            />
          </View>
        </View>
      </View>
    </BottomSheetOverlay>
  );
};

const styles = StyleSheet.create({
  containerStyles: {
    ...paddingStyles.pa16,
  },
  container: {
    // ...paddingStyles.ph16,
    ...paddingStyles.pb20,
  },
  headingText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  subHeadingText: {
    ...fontStyles.labelMediumRegular,
    color: holidayColors.gray,
    marginTop: 10,
  },
  leftBtn: {
    ...fontStyles.labelMediumRegular,
    color: holidayColors.primaryBlue,
  },
  updateButton: {
    ...paddingStyles.ph20,
  },
});

export default UpdateView;
