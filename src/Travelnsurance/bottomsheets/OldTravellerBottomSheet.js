import React, { useEffect, useState } from 'react';
import BottomSheet from '../../PhoenixReview/Components/BottomSheet';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import PrimaryButton from '../../Common/Components/Buttons/PrimaryButton';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { paddingStyles, marginStyles } from '../../Styles/Spacing';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { rupeeFormatterUtils } from '../../utils/HolidayUtils';
import BottomSheetOverlay, { BottomSheetCross } from '../../Common/Components/BottomSheetOverlay';
import { INSURANCE_ACTIONS, INSURANCE_BOTTOMSHEETS } from '../utils/constants';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { isEmpty } from 'lodash';

const OldTravellerBottomSheet = ({
  promptMessage,
  insuranceAddonDetail,
  onPositiveClick,
  trackReviewLocalClickEvent,
  onCancel,
  price,
}) => {
  const [modalVisible, setModalVisible] = useState(true);
  const captureClickEvents = ({ eventName = '', prop1 = '', actionType = {} }) => {
    logHolidayReviewPDTClickEvents({
      actionType: !isEmpty(actionType) ? actionType : PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName: prop1,
    });
    trackReviewLocalClickEvent(eventName, '', { prop1 });
  };
  useEffect(() => {
    captureClickEvents({ eventName: INSURANCE_ACTIONS.INVALID_SHOWN, prop1: INSURANCE_BOTTOMSHEETS.PROMT_INVALID, actionType : PDT_EVENT_TYPES.contentSeen })
  }, []);
  const onPositiveButtonPress = () => {
    setModalVisible(false);
    captureClickEvents({ eventName: INSURANCE_ACTIONS.INVALID_OKAY, prop1: INSURANCE_BOTTOMSHEETS.PROMT_INVALID })
    onPositiveClick();
  };
  const handleCloseClick = () => {
    captureClickEvents({ eventName: INSURANCE_ACTIONS.INVALID_CLOSE, prop1: INSURANCE_BOTTOMSHEETS.PROMT_INVALID })
    setModalVisible(false);
    onCancel();
  };

  return (
    <BottomSheetOverlay
      showCross={false}
      visible={modalVisible}
      toggleModal={handleCloseClick}
      title={promptMessage?.messageHeading || 'Your travel insurance cannot be processed right now'}
      headingTextStyle={styles.title}
      containerStyles={styles.containerStyles}
    >
      <View style={styles.container}>
        {promptMessage?.messageSubHeadings?.map((item, index) => {
          return <Text style={styles.messageStyle}>{item}</Text>;
        })}

        <Text style={styles.messageStyle}>
          {'New Package Price: '}
          <Text style={fontStyles.labelBaseBold}>{rupeeFormatterUtils(price)}</Text>
        </Text>

        <PrimaryButton
          isDisable={false}
          buttonText={'OKAY, GOT IT'}
          handleClick={onPositiveButtonPress}
          btnContainerStyles={styles.footerButton}
          activeOpacity={0.7}
        />
      </View>
    </BottomSheetOverlay>
  );
};

export default OldTravellerBottomSheet;

const styles = StyleSheet.create({
  container: {
    ...paddingStyles.pb20,
  },
  titleContainer: {
    flexDirection: 'row',
  },
  title: {
    flex: 1,
  },

  cardContainer: {
    ...paddingStyles.pa16,
    ...marginStyles.mv8,
    marginTop: 8,
    borderWidth: 1, // Width of the border
    borderColor: holidayColors.grayBorder, // Color of the border
    borderStyle: 'solid', // Style of the border: 'solid', 'dotted', 'dashed'
    ...holidayBorderRadius.borderRadius16, // Border radius to make it rounded
  },

  recommendedPlanTitle: {
    ...marginStyles.mt16,
    ...fontStyles.labelMediumBold,
    lineHeight: 19.2,
    color: holidayColors.gray,
  },
  messageStyle: {
    ...fontStyles.labelBaseRegular,
    ...marginStyles.mt8,
    ...marginStyles.mb4,
    color: holidayColors.gray,
  },
  footerButton: {
    ...marginStyles.mt20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    ...paddingStyles.ph16,
  },
  handleCloseStyle: {
    width: 20,
    height: 20,
    position: 'absolute',
    right: 0,
    ...marginStyles.ma18,
  },
  icCrossStyle: {
    width: 20,
    height: 20,
  },
  ctaContainer: {
    ...holidayBorderRadius.borderRadius4,
    ...paddingStyles.pt16,
    ...paddingStyles.pb16,
    color: holidayColors.primaryBlue,
    textAlign: 'center',
    ...fontStyles.labelMediumBlack,
    lineHeight: 19,
    marginTop: 12,
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    borderStyle: 'solid',
    borderRadius: 4,
  },
  containerStyles: {
    ...paddingStyles.pa16
  }
});
