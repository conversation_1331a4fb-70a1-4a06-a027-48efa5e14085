import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import PriceInfoAndSelect from './pricingInfoSelect';
import CardHeader from './cardHeader';
import ItemTripDetails from './itemTripDetails';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { marginStyles } from '../../Styles/Spacing';

const InsuranceDetailsCard = ({
  insuranceCardDetails,
  disclaimer,
  priceMap,
  onSelect,
  isSelected,
  selectedText,
  trackReviewLocalClickEvent,
}) => {
  return (
    <View style={styles.cardContainer}>
      <View style={styles.cardPadding}>
        {insuranceCardDetails.cardHeader ? (
          <CardHeader
            icon={insuranceCardDetails.cardHeader.icon}
            title={insuranceCardDetails.cardHeader.heading}
            subtitle={insuranceCardDetails.cardHeader.subHeading}
            tncLink={insuranceCardDetails.tnC}
            trackReviewLocalClickEvent={trackReviewLocalClickEvent}
          />
        ) : null}
      </View>

      {insuranceCardDetails.cardHeader ? <View style={styles.separator} /> : null}
      <View style={styles.cardPadding}>
        {insuranceCardDetails.tripDetails.length > 0
          ? insuranceCardDetails.tripDetails.map((value, index, array) => {
              return <ItemTripDetails title={value.heading} subtitle={value.subHeading} />;
            })
          : null}

        {insuranceCardDetails.tripDetails.length > 0 ? <View style={styles.separator} /> : null}

        {insuranceCardDetails.insurancePrice ? (
          <PriceInfoAndSelect
            price={priceMap?.priceDiff}
            infoText={priceMap?.priceDiffUnit}
            cta={isSelected ? 'SELECTED' : 'SELECT'}
            onSelect={onSelect}
            isSelected={isSelected}
            selectedText={selectedText}
            insuranceCardDetails={insuranceCardDetails}
          />
        ) : null}

        {disclaimer ? <View style={styles.separator} /> : null}

        {disclaimer ? <Text style={styles.disclaimer}>{disclaimer}</Text> : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    overflow: 'hidden',
  },
  cardPadding: {
    paddingHorizontal: 16,
  },
  separator: {
    ...marginStyles.mv6,
    height: 1,
    backgroundColor: holidayColors.grayBorder,
  },

  disclaimer: {
    color: holidayColors.lightGray,
    ...fontStyles.labelSmallRegular,
    lineHeight:14.4,
  },
});
export default InsuranceDetailsCard;
