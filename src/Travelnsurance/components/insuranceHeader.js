import { Text, View, StyleSheet, Image, TouchableOpacity, NativeModules } from 'react-native';
import React from 'react';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { paddingStyles, marginStyles } from '../../Styles/Spacing';
import { openGenericDeeplink } from '../../utils/HolidayUtils';
import { INSURANCE_ACTIONS, INSURANCE_BOTTOMSHEETS, INSURANCE_PAGES } from '../utils/constants';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

const InsuranceHeader = ({
  cardHeaderDetails,
  trackReviewLocalClickEvent,
  fromPopUp = false,
  fromListing = false,
  onCancel = null,
  setModalVisible = null,
}) => {
  const { icon, heading = '', subHeading = '', tncLink = '' } = cardHeaderDetails || {};
  const captureTncClickEvents = () => {
    const insurancePageName = fromPopUp ? INSURANCE_BOTTOMSHEETS.PROMT_ADD : INSURANCE_PAGES.BASECARD;
    const prop1Val = fromListing ? INSURANCE_PAGES.LISTING : insurancePageName
    logHolidayReviewPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: INSURANCE_ACTIONS.VIEWTNC ,
      subPageName: prop1Val,
    })
    trackReviewLocalClickEvent(INSURANCE_ACTIONS.VIEWTNC, '', { prop1: prop1Val });
  }
  const onTncClicked = () => {
    captureTncClickEvents();
    if(setModalVisible){
    setModalVisible(false);
    }
    if(onCancel){
    onCancel();
    }
    // HolidayMo
    openGenericDeeplink({url: tncLink});
  };
  const titleStyle = fromListing? styles.mediumTitle : styles.title
  return (
    <View style={styles.container}>
      {icon ? (
        <View>
          <Image source={{ uri: icon }} style={styles.image} />
        </View>
      ) : null}
      <View style={styles.textContainer}>
        {!!heading && <Text style={titleStyle}>{heading}</Text>}
        <View style={styles.subtitleWrap}>
          {!!subHeading && <Text style={styles.subtitle}>{subHeading}</Text>}
          {tncLink ? (
            <TouchableOpacity activeOpacity={0.5} onPress={onTncClicked}>
              <Text style={styles.cta}>View T&Cs</Text>
            </TouchableOpacity>
          ) : null}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },

  image: {
    width: 16,
    height: 16, // adjust the height as needed
    resizeMode: 'contain', // or 'contain' for different image scaling
    ...marginStyles.mt2,
  },
  textContainer: {
    ...paddingStyles.ph8,
  },
  title: {
    ...fontStyles.labelBaseBlack,
    lineHeight: 19.2,
    color: holidayColors.black,
  },
  mediumTitle: {
    ...fontStyles.labelMediumBlack,
    lineHeight: 19.2,
    color: holidayColors.black,
  },
  subtitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },

  subtitleWrap: {
    flexDirection: 'row',
  },

  cta: {
    ...paddingStyles.ph10,
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
});

export default InsuranceHeader;
