import { Image, StyleSheet, Text, View } from "react-native";
import React from "react";
import { holidayColors } from "../../Styles/holidayColors";
import { fontStyles } from "../../Styles/holidayFonts";
import { marginStyles, paddingStyles } from "../../Styles/Spacing";


const ItemCancellationPolicy = ({ icon, title, subtitle }) => {

  return <View style={styles.container}>
    {icon ? <View>
      <Image source={{ uri: icon }} style={styles.image} />
    </View> : null}
    <View style={styles.textContainer}>
      {title ? <Text style={styles.title}>{title}</Text> : null}
      {subtitle ? <Text style={styles.subtitle}>{subtitle}</Text> : null}
    </View>
  </View>;
};


const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-start',
    flex:1,
    flexDirection: "row",
    ...marginStyles.mb12,
  },
  image: {
    ...marginStyles.ma4,
    width: 20,
    height: 20,
    resizeMode: "cover",
  },
  textContainer: {
    flex: 1,
    ...paddingStyles.ph4,
  },
  title: {
    ...fontStyles.labelBaseBlack,
    ...marginStyles.mt4,
    color: holidayColors.black,
  },
  subtitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 18,
  },
});

export default ItemCancellationPolicy;

