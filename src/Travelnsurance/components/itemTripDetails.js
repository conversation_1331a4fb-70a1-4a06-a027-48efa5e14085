import { Text, View, StyleSheet, Image } from "react-native";
import React from "react";
import { holidayColors } from "../../Styles/holidayColors";
import { fontStyles } from "../../Styles/holidayFonts";
import { marginStyles, paddingStyles } from "../../Styles/Spacing";


const ItemTripDetails = ({ title, subtitle }) => {

  return <View style={styles.container}>
    <View style={styles.textContainer}>
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.subtitle}>{subtitle}</Text>
    </View>
  </View>;
};


const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    ...marginStyles.mv4,
  },

  textContainer: {
    ...paddingStyles.ph4,
  },

  title: {
    ...fontStyles.labelSmallBold,
    lineHeight: 14.4,
    fontSize: 12,
    color: holidayColors.black,
  },
  subtitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    fontSize: 12,
  },

});

export default ItemTripDetails;
