import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { connect } from 'react-redux';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../Navigation';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import { marginStyles } from '../../Styles/Spacing';
import { holidayNavigationPush } from '../../PhoenixDetail/Utils/DetailPageNavigationUtils';
import { REVIEW_OVERLAYS } from '../../PhoenixReview/Components/ReviewOverlays';

/* Components */
import InsuranceHeader from './insuranceHeader';
import ItemInsuranceInfo from './itemInsuranceInfo';
import PriceInfoAndSelect from './pricingInfoSelect';

/* Actions */
import { showReviewOverlay, hideReviewOverlays } from '../../Review/Actions/reviewOverlayActions';
import { INSURANCE_ACTIONS, INSURANCE_BOTTOMSHEETS, INSURANCE_PAGES } from '../utils/constants';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';

const InsuranceCard = ({
  insuranceAddOn,
  onSelect,
  priceMap,
  hideDisclaimer,
  hideSelect,
  isSelected,
  trackReviewLocalClickEvent = null,
  setModalVisible = null,
  onCancel = null,
  fromListing = false,
  fromPopUp = false,
  showReviewOverlay, hideReviewOverlays,
}) => {

  const captureClickEvents = ({ eventName = '', prop1 = '' }) => {
    logHolidayReviewPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName: prop1,
    })
    trackReviewLocalClickEvent(eventName, '', { prop1});
  };
  const onMoreBenefitsClick = () => {
    const prop1Val = fromListing
      ? INSURANCE_PAGES.LISTING
      : fromPopUp
      ? INSURANCE_BOTTOMSHEETS.PROMT_ADD
      : INSURANCE_PAGES.BASECARD;
    captureClickEvents({eventName: INSURANCE_ACTIONS.MOREBENEFITS, prop1: prop1Val })
    holidayNavigationPush({
      pageKey: HOLIDAY_ROUTE_KEYS.TRIP_INSURANCE_DETAILS,
      overlayKey: REVIEW_OVERLAYS.INSURANCE_DETAIL_PAGE,
      showOverlay: showReviewOverlay,
      hideOverlays: hideReviewOverlays,
      props: {
        insuranceAddOn,
        onSelect,
        priceMap,
        selectedFromPrevPage: insuranceAddOn.isSelected,
        trackReviewLocalClickEvent,
      },
    });
    if (setModalVisible) {
      setModalVisible(false);
    }
    if (onCancel) {
      onCancel();
    }
  };

  const onSelectClick = () => {
    const insuranceSelectionAction = `Insurance_${
      insuranceAddOn?.isSelected ? 'Unselect' : 'Select'
    }`;
      trackReviewLocalClickEvent(insuranceSelectionAction, '', {
        prop1: 'insurance overlay:basecard',
      });
    onSelect(insuranceAddOn);
  }

  const insuranceCardDetails = insuranceAddOn.insuranceCardDetails;
  // const { cardHeader, icon, }
  const benefitCountToDisplay =
    (insuranceAddOn?.policyBenefits?.length ?? 0) -
    (insuranceCardDetails?.benefitsDetail?.benefits?.length ?? 0);

  return (
    <View style={styles.cardContainer}>
      <View style={styles.innerContainer}>
        {insuranceCardDetails.cardHeader ? (
          <InsuranceHeader
            cardHeaderDetails={{
              ...insuranceCardDetails.cardHeader,
              tncLink: insuranceCardDetails.tnC,
            }}
            trackReviewLocalClickEvent={trackReviewLocalClickEvent}
            setModalVisible={setModalVisible}
            onCancel={onCancel}
            fromPopUp={fromPopUp}
            fromListing={fromListing}
          />
        ) : null}

        <View style={{ height: 8 }} />
        {insuranceCardDetails?.benefitsDetail?.benefits
          ? insuranceCardDetails.benefitsDetail.benefits.map((value, index, array) => {
              return (
                <ItemInsuranceInfo text={value.heading} price={value.price} icon={value.icon} key={value.heading + index + value.price}/>
              );
            })
          : null}

        {benefitCountToDisplay > 0 ? (
          <TouchableOpacity onPress={onMoreBenefitsClick} activeOpacity={0.5}>
            <Text style={styles.moreBenefitsCta}>{benefitCountToDisplay} more benefits</Text>
          </TouchableOpacity>
        ) : null}

        {insuranceCardDetails.insurancePrice ? <View style={styles.separator} /> : null}

        {priceMap ? (
          <PriceInfoAndSelect
            price={priceMap?.priceDiff}
            infoText={priceMap?.priceDiffUnit}
            cta={isSelected ? 'SELECTED' : 'SELECT'}
            onSelect={onSelectClick}
            selectedText={insuranceAddOn.selectedText}
            isSelected={isSelected}
            hideSelect={hideSelect}
            insuranceCardDetails={insuranceCardDetails}
          />
        ) : null}

        {fromListing && !hideDisclaimer && insuranceAddOn.disclaimer ? (
          <View style={styles.separator} />
        ) : null}

        {fromListing && !hideDisclaimer && insuranceAddOn.disclaimer ? (
          <Text style={styles.disclaimer}>{insuranceAddOn.disclaimer}</Text>
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    ...holidayBorderRadius.borderRadius8,
    overflow: 'hidden',
  },

  innerContainer: {
    ...marginStyles.mt2,
  },

  separator: {
    ...marginStyles.mv8,
    height: 1,
    backgroundColor: holidayColors.grayBorder,
  },

  moreBenefitsCta: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    lineHeight: 20,
    ...marginStyles.mt4,
  },

  disclaimer: {
    color: holidayColors.lightGray,
    ...fontStyles.labelSmallRegular,
    lineHeight:14.4,
  },
});

const mapDispatchToProps = {
  hideReviewOverlays,
  showReviewOverlay,
}

export default connect(null, mapDispatchToProps)(InsuranceCard);
