import React from "react";
import { Image, StyleSheet, Text, View } from "react-native";
import { holidayColors } from "../../Styles/holidayColors";
import { fontStyles } from "../../Styles/holidayFonts";
import { paddingStyles, marginStyles } from "../../Styles/Spacing";
const ItemInsuranceInfo = ({ text, price, icon }) => {
  return <View style={styles.container}>
    {icon ? <Image source={{uri:icon}} style={styles.image} /> : null}
    {text ? <Text style={styles.text}>{text} {price ? <Text style={styles.price}>{price}</Text> : null}</Text> : null}
  </View>;
};


const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    ...paddingStyles.pv2,
  },

  text:{
    ...fontStyles.labelSmallRegular,
    lineHeight:14,
    ...marginStyles.ml10,
    color: holidayColors.gray
  },

  price:{
    ...fontStyles.labelSmallBold,
    ...marginStyles.ml10,
  },
  image: {
    width: 16,
    height: 16, // adjust the height as needed
    resizeMode: 'cover', // or 'contain' for different image scaling
  },
});


export default ItemInsuranceInfo
