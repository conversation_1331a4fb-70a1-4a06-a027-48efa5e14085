import React from "react";
import { StyleSheet, View, Text, Image } from "react-native";
import { fontStyles } from "../../Styles/holidayFonts";
import { holidayColors } from "../../Styles/holidayColors";
import { marginStyles } from "../../Styles/Spacing";
import blueArrowRight from '@mmt/legacy-assets/src/blue_right_arrow.webp';

const CardFooter = ({ count }) => {
  if (count <= 0) {
    return <></>;
  }
  let text = "View " + count + " More Insurance Plans";
  if (count === 1) {
    text = "View " + count + " More Insurance Plan";
  }
  return <View style={styles.container}>
    <Text style={styles.ctaText}>{text}
    </Text>
    <Image source={blueArrowRight} style={styles.rightArrow} />
  </View>;
};

const styles = StyleSheet.create({
  container: {
    ...marginStyles.mt8,
    ...marginStyles.mb4,
    flexDirection: "row",
  },

  ctaText: {
    flex: 1,
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
  },

  rightArrow: {
    width: 24,
    height: 24,
  },
});


export default CardFooter;
