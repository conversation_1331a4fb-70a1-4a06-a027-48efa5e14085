import { Text, View, StyleSheet, Image, TouchableOpacity } from 'react-native';
import React from 'react';
import { holidayColors } from '../../Styles/holidayColors';
import { fontStyles } from '../../Styles/holidayFonts';
import { paddingStyles } from '../../Styles/Spacing';
import { INSURANCE_ACTIONS, INSURANCE_PAGES } from '../utils/constants';
import { logHolidayReviewPDTClickEvents } from '../../PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../utils/HolidayPDTConstants';
import { openGenericDeeplink } from '../../utils/HolidayUtils';

const CardHeader = ({ icon, title, subtitle, tncLink, trackReviewLocalClickEvent,inclusionMessage='' }) => {
  const captureClickEvents = ({ eventName = '', prop1 = '', }) => {
    logHolidayReviewPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName : prop1,
    });
    trackReviewLocalClickEvent(eventName, '', { prop1 });
  };
  const onTncClicked = () => {
    captureClickEvents({ eventName: INSURANCE_ACTIONS.VIEWTNC, prop1: INSURANCE_PAGES.DETAIL });
    openGenericDeeplink({url : tncLink});
  };
  return (
    <View style={styles.container}>
      {icon ? (
        <View>
          <Image source={{ uri: icon }} style={styles.image} />
        </View>
      ) : null}
      <View style={styles.textContainer}>
        {title ? (
          <Text style={styles.title} numberOfLines={1} ellipsizeMode="tail">
            {title}
          </Text>
        ) : null}
        <View style={styles.bottomWrapper}>
          {subtitle ? (
            <Text style={styles.subtitle} numberOfLines={2} ellipsizeMode="tail">
              {subtitle}{' '}
            </Text>
          ) : null}
          {tncLink ? (
            <TouchableOpacity activeOpacity={0.5} onPress={onTncClicked}>
              <Text style={styles.cta}>View T&Cs</Text>
            </TouchableOpacity>
          ) : null}
        </View>
       {!!inclusionMessage && <View style={styles.includedWrapper}>
              <View style={styles.includedContainer}>
                <Text style={styles.includedTitle}>{inclusionMessage}</Text>
              </View>
            </View>
            }
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },

  image: {
    width: 16,
    height: 16, // adjust the height as needed
    resizeMode: 'contain', // or 'contain' for different image scaling
  },
  textContainer: {
    ...paddingStyles.ph8,
  },
  title: {
    ...fontStyles.labelMediumBlack,
    lineHeight: 19.2,
    color: holidayColors.black,
  },
  subtitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  bottomWrapper: {
    flexDirection: 'row',
  },
  cta: {
    ...paddingStyles.ph10,
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
   includedWrapper: {
        alignItems: 'center',
        marginTop: 10,
      },
      includedContainer: {
        backgroundColor:holidayColors.greenlight,
        paddingVertical: 5,
        paddingHorizontal:10,
        borderRadius: 24,
        alignSelf: 'flex-start',
      },
      includedTitle: {
        color: holidayColors.greenDark,
        ...fontStyles.labelSmallBlack,
      } ,
});

export default CardHeader;
