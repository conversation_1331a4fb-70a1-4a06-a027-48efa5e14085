import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Image } from 'react-native';
import { fontStyles } from '../../Styles/holidayFonts';
import { holidayColors } from '../../Styles/holidayColors';
import { paddingStyles, marginStyles } from '../../Styles/Spacing';
import { holidayBorderRadius } from '../../Styles/holidayBorderRadius';
import iconArrowBlue from '@mmt/legacy-assets/src/ic_tick.webp';

const PriceInfoAndSelect = ({
  price,
  infoText,
  cta,
  onSelect,
  isSelected,
  hideSelect,
  selectedText,
  insuranceCardDetails={}
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.priceInfoContainer}>
        {!isSelected && price ? (
          <Text style={styles.price}>{`+ ${insuranceCardDetails?.price?.heading}` || price}</Text>
        ) : null}
        {!isSelected && infoText ? (
          <Text style={styles.infoText}>{infoText}</Text>
        ) : null}
        {isSelected && infoText ? <Text style={styles.selectedText}>{selectedText}</Text> : null}
      </View>
      {cta && !hideSelect ? (
        <TouchableOpacity activeOpacity={0.5} onPress={onSelect}>
          <View style={isSelected ? styles.ctaSelected : styles.cta}>
            {isSelected ? (
              <Image
                source={iconArrowBlue}
                style={{ width: 16, height: 16, tintColor: holidayColors.primaryBlue }}
              />
            ) : null}
            <Text style={styles.ctaText}>{cta}</Text>
          </View>
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    ...marginStyles.mv2,
  },

  priceInfoContainer: {
    flex: 1,
    ...marginStyles.mt2,
  },

  price: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },

  infoText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  selectedText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    width: '70%',
    flex: 1,
  },
  cta: {
    alignItems: 'center',
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
    backgroundColor: holidayColors.white,
    flexDirection: 'row',
    ...holidayBorderRadius.borderRadius8, // Adjust as needed
    borderColor: holidayColors.primaryBlue,
    borderWidth: 1,
    ...paddingStyles.pl12,
    ...paddingStyles.pr16,
    ...paddingStyles.pv6,
  },

  ctaSelected: {
    ...paddingStyles.pl12,
    ...paddingStyles.pr16,
    ...paddingStyles.pv6,
    alignItems: 'center',
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
    backgroundColor: holidayColors.lightBlueBg,
    ...holidayBorderRadius.borderRadius8, // Adjust as needed
    borderColor: holidayColors.primaryBlue,
    borderWidth: 1,
    flexDirection: 'row',
  },

  ctaText: {
    ...paddingStyles.pl4,
    ...fontStyles.labelSmallBlack,
    color: holidayColors.primaryBlue,
  },
});

export default PriceInfoAndSelect;
