import { Image, StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { fontStyles } from '../../Styles/holidayFonts';
import { paddingStyles } from '../../Styles/Spacing';
import { isEmpty } from 'lodash';
import { holidayColors } from '../../Styles/holidayColors';

const PersuasionInfo = ({ icon = '', title = '' }) => {
  if (isEmpty(title)) {
    return null;
  }
  return (
    <View style={styles.container}>
      {!!icon && (
        <View style={styles.imageContainer}>
          <Image source={{ uri: icon }} style={styles.image} />
        </View>
      )}
      <View style={styles.textContainer}>
        <Text style={styles.title}>{title}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pt8,
    ...paddingStyles.pr40,
    width: '80%',
  },

  imageContainer: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },

  image: {
    width: 16,
    height: 16,
    resizeMode: 'cover',
  },
  textContainer: {
    ...paddingStyles.ph4,
  },
  title: {
    ...fontStyles.labelSmallBold,
    lineHeight: 14.4,
    color: holidayColors.yellow,
  },
});

export default PersuasionInfo;
