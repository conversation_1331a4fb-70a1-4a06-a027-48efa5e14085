import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { fontStyles } from '../../Styles/holidayFonts';

const TagComponent = ({ text }) => {
  return (
    <View style={styles.tagContainer}>
      <Text style={styles.tagText}>{text}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  tagContainer: {
    width: 120,
    borderRadius: 20, // Adjust as needed
    paddingHorizontal: 10,
    marginBottom: 5,
    borderColor: '#834CC5',
    borderWidth: 1,
  },
  tagText: {
    color: '#834CC5',
    ...fontStyles.labelSmallBlack,
    fontSize: 12,
  },
});

export default TagComponent;
