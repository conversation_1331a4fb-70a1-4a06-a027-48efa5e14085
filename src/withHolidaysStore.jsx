import React from 'react';
import HolidaysStoreProvider from './HolidaysStoreProvider';

/**
 * Higher-order component that wraps any component with the HolidaysStoreProvider
 * Use this for components that need to connect to the Holidays-specific Redux store
 * 
 * @param {React.ComponentType} Component - The component to wrap
 * @returns {React.ComponentType} - The wrapped component
 */
const withHolidaysStore = (Component) => {
  const WithHolidaysStore = (props) => (
    <HolidaysStoreProvider>
      <Component {...props} />
    </HolidaysStoreProvider>
  );
  
  // Copy static properties from the original component
  if (Component.displayName || Component.name) {
    WithHolidaysStore.displayName = `withHolidaysStore(${Component.displayName || Component.name})`;
  }
  
  return WithHolidaysStore;
};

export default withHolidaysStore; 