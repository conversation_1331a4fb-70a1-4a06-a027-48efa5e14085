import {connect, Store} from 'react-redux';
import React from 'react';
import {View, StyleSheet, FlatList, BackHandler} from 'react-native';
import Header from './Header';
import BottomFilters from './BottomFilters';
import {fetchChangeHotelList} from '../utils/HolidayNetworkUtils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import HolidayListingLoadingCard from '../Listing/Components/HolidayListingLoadingCard';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import HolidaysHotelCard from './HolidaysHotelCard';
import {getDiffPackagePrice} from '../PhoenixDetail/Utils/HolidayUtils';
import {FILTER_UPDATE, HOLIDAYS_HOTEL_LISTING_RESPONSE} from './HolidaysHotelListingConstants';
import starIcon from '../FilterHotels/FilterHotelList/Images/star.webp';
import ruppeLowIcon from '../FilterHotels/FilterHotelList/Images/ruppeLowIcon.webp';
import ruppeHighIcon from '../FilterHotels/FilterHotelList/Images/ruppeHighIcon.webp';
import RatingIcon from '../FilterHotels/FilterHotelList/Images/RatingIcon.webp';
import HolidayGroupingNoFilter from '../Grouping/Filters/HolidayGroupingNoFilter';
import {getRating} from '../utils/HolidayUtils';
import {
  component,
  getHeadingTextForFilters,
} from '../Common/Components/CovidSafety/HolidaySafeDataHolder';
import { HolidayNavigation } from '../Navigation';
import withBackHandler from '../hooks/withBackHandler';

class HolidaysHotelListingNew extends BasePage {

  onBackClick = ()=> {
    HolidayNavigation.pop();
    return true;
  }

  componentWillUnmount() {
    super.componentWillUnmount();
    this.resetAppliedFilter();
  }

  resetAppliedFilter = () => {
    const appliedFilterData = {
      appliedSortingList: 'Popularity',
      appliedLocationList: [],
      appliedStarRatingList: [],
      appliedPopularList: [],
      appliedPropertyTypeList: [],
      appliedAmenitiesList: [],
      appliedURating: '',
      appliedPriceRange: {},
    };
    this.props.resetAppliedFilterData(appliedFilterData);
  }

  constructor(props) {
    super(props, 'changeHotelsListing');
    this.state = {
      loading: true,
      hotels: [],
      packagePriceMap: null,
      discountedFactor: null,
      filterData: {},
      filteredHotels: [],
    };
  }

  componentDidMount() {
    this.fetchScreenData();
  }
  async fetchScreenData() {
    const {requestType, dynamicPackageId} = this.props.packageDetailDTO;
    const response = await fetchChangeHotelList(dynamicPackageId, this.props.sequences, requestType);
    const {success, hotelListingData} = response;
    if (success && success === true) {
      if (hotelListingData) {
        const {hotels = [], packagePriceMap, discountedFactor} = hotelListingData;
        if (hotels && hotels.length > 0 && packagePriceMap) {
          const finalHotel = [];
          hotels.map((item) => {
            const pkgDiscountedRatesForHotel = packagePriceMap[item.sellableId];
            const price = getDiffPackagePrice(pkgDiscountedRatesForHotel, 1, this.props.packageDetailDTO.price, discountedFactor);
            if (this.props.hotel.sellableId === item.sellableId) {
              finalHotel.unshift({
                ...item,
                price,
                showHotelIncluded: true,
              });
            }
            else {
              finalHotel.push({
                ...item,
                price,
                showHotelIncluded: false,
              });
            }
            return {};
          });
          this.setState({
            loading: false,
            hotels: [...finalHotel],
            filteredHotels: [...finalHotel],
            packagePriceMap,
            discountedFactor,
            filterData: this.createFilterObject(finalHotel, this.props.packageDetailDTO.price),
          });
          this.props.storeListingResponse(hotels, this.props.packageDetailDTO.price);
        } else {
          this.setState({loading: false, hotels: []});
        }
      } else {
        this.setState({loading: false, hotels: []});
      }
    } else {
      this.setState({loading: false, hotels: []});
    }
  }
  createFilterObject = (response) => {
    const propertyTypeList = {};
    const locationList = {};
    const popularList = {};
    const amenitiesList = {};
    const priceList = [];
    response.map((hotel) => {
      const {
        hotelInformation = {},
        locationInfo = {},
        price,
        safe,
        safetyRatings,
      } = hotel;
      const {
        propertyType = '',
        mmtAssured,
        hotelFacilities,
      } = hotelInformation;
      const {areaName = ''} = locationInfo;
      if (!propertyTypeList[propertyType]) {
        propertyTypeList[propertyType] = {
          text: propertyType,
          applied: false,
        };
      }
      if (!locationList[areaName]) {
        locationList[areaName] = {
          text: areaName,
          applied: false,
        };
      }
      if (mmtAssured) {
        if (!popularList['MMT Assured']) {
          popularList['MMT Assured'] = {
            text: 'MMT Assured',
            applied: false,
          };
        }
      }
      if (safe) {
        const rating = getRating(safetyRatings);
        if (rating && !popularList[rating]) {
          const filterText = this.getSafeFilterText(rating);
          popularList[rating] = {
            text: filterText,
            applied: false,
            rating: rating,
          };
        }
      }
      if (hotelFacilities) {
        hotelFacilities.map((amenity) => {
          if (!amenitiesList[amenity]) {
            amenitiesList[amenity] = {
              text: amenity,
              applied: false,
            };
          }
          return {};
        });
      }
      priceList.push(price);
      priceList.sort((a, b) => a - b);
      return {};
    });
    const starRatingList = {};
    starRatingList['3'] = {
      text: 3,
      applied: false,
    };
    starRatingList['4'] = {
      text: 4,
      applied: false,
    };
    starRatingList['5'] = {
      text: 5,
      applied: false,
    };
    starRatingList.Unrated = {
      text: 'Unrated',
      applied: false,
    };

    const popularity = {
      Icon: starIcon,
      type: 'star',
      text: 'Popularity',
      subText: 'Popular First',
      applied: false,
    };
    const priceLowToHigh = {
      Icon: ruppeLowIcon,
      text: 'Price',
      type: 'priceLow',
      subText: 'Low to High',
      applied: false,
    };
    const priceHighToLow = {
      Icon: ruppeHighIcon,
      text: 'Price',
      type: 'priceHigh',
      subText: 'High to Low',
      applied: false,
    };
    const userRating = {
      Icon: RatingIcon,
      type: 'userRating',
      text: 'User Rating',
      subText: 'Highest',
      applied: false,
    };
    const sortingList = {
      popularity,
      priceLowToHigh,
      priceHighToLow,
      userRating,
    };

    const uRating = {};
    uRating['3.0 & Above'] = {
      text: '3.0 & Above',
      applied: false,
    };
    uRating['4.0 & Above'] = {
      text: '4.0 & Above',
      applied: false,
    };
    uRating['4.5 & Above'] = {
      text: '4.5 & Above',
      applied: false,
    };
    uRating.Unrated = {
      text: 'Unrated',
      applied: false,
    };
    const data = {
      propertyTypeList,
      locationList,
      popularList,
      amenitiesList,
      priceList,
      starRatingList,
      sortingList,
      uRating,
    };
    return data;
  }

  getSafeFilterText = (rating) => {
    const {packageDetailDTO} = this.props;
    const {branch} = packageDetailDTO || {};
    const h1 = getHeadingTextForFilters(rating, component.HOTEL, branch);
    let heading = '';
    if (h1) {
      heading += h1;
    }
    return heading;
  }

  filterHotels = (nextProps) => {
    const {appliedFilterData = {}} = nextProps;
    const {hotels = []} = this.state;
    let result = [...hotels];
    const {
      appliedSortingList = 'Popularity',
      appliedLocationList = [],
      appliedStarRatingList = [],
      appliedPopularList = [],
      appliedPropertyTypeList = [],
      appliedAmenitiesList = [],
      appliedURating = '',
      appliedPriceRange = {},
    } = appliedFilterData;

    if (appliedSortingList === 'Popularity' &&
      appliedLocationList.length === 0 &&
      appliedStarRatingList.length === 0 &&
      appliedPopularList.length === 0 &&
      appliedPropertyTypeList.length === 0 &&
      appliedAmenitiesList.length === 0 &&
      appliedURating === '' &&
      Object.keys(appliedPriceRange).length === 0
    ) {
      this.setState({
        filteredHotels: [...hotels],
      });
      return;
    }
    if (appliedLocationList && appliedLocationList.length > 0) {
      const list = [];
      appliedLocationList.forEach((item) => {
        result.forEach((hotel) => {
          const {locationInfo} = hotel;
          const {areaName} = locationInfo;
          if (item === areaName) {
            list.push(hotel);
          }
        });
      });
      result = [...list];
    }
    if (appliedStarRatingList && appliedStarRatingList.length > 0) {
      const list = [];
      appliedStarRatingList.forEach((item) => {
        result.forEach((hotel) => {
          const {starRating} = hotel;
          if (item === starRating) {
            list.push(hotel);
          }
        });
      });
      result = [...list];
    }
    if (appliedPopularList && appliedPopularList.length > 0) {
      const list = [];
      appliedPopularList.forEach((item) => {
        result.forEach((hotel) => {
          const {safe, hotelInformation, safetyRatings} = hotel;
          const rating = getRating(safetyRatings);
          if (item === 'MMT Assured') {
            const {mmtAssured} = hotelInformation || {};
            if (mmtAssured && mmtAssured === true) {
              list.push(hotel);
            }
          }
          if (item === rating) {
            if (safe && safe === true) {
              list.push(hotel);
            }
          }
        });
      });
      result = [...list];
    }
    if (appliedPropertyTypeList && appliedPropertyTypeList.length > 0) {
      const list = [];
      appliedPropertyTypeList.forEach((item) => {
        result.forEach((hotel) => {
          const {hotelInformation = {}} = hotel;
          const {propertyType = ''} = hotelInformation;
          if (propertyType === item) {
            list.push(hotel);
          }
        });
      });
      result = [...list];
    }
    if (appliedURating && appliedURating !== '') {
      if (appliedURating === 'Unrated') {
        let notMmtRatingInfo = [...result];
        notMmtRatingInfo = notMmtRatingInfo.filter(item => !item.mmtRatingInfo);
        result = [...notMmtRatingInfo];
      }
      if (appliedURating === '3.0 & Above') {
        let hasMmtRatingInfo = [...result];
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo);
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo.userRating >= 3.0);
        result = [...hasMmtRatingInfo];
      }
      if (appliedURating === '4.0 & Above') {
        let hasMmtRatingInfo = [...result];
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo);
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo.userRating >= 4.0);
        result = [...hasMmtRatingInfo];
      }
      if (appliedURating === '4.5 & Above') {
        let hasMmtRatingInfo = [...result];
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo);
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo.userRating >= 4.5);
        result = [...hasMmtRatingInfo];
      }
    }
    if (Object.keys(appliedPriceRange).length > 0) {
      result = result.filter(item => item.price >= appliedPriceRange.min && item.price <= appliedPriceRange.max);
    }
    if (appliedSortingList && appliedSortingList !== 'Popularity') {
      if (appliedSortingList === 'Low to High') {
        result = result.sort((a1, b1) => (a1.price - b1.price));
      } if (appliedSortingList === 'High to Low') {
        result = result.sort((a1, b1) => (b1.price - a1.price));
      } if (appliedSortingList === 'User Rating') {
        let notMmtRatingInfo = [...result];
        let hasMmtRatingInfo = [...result];
        notMmtRatingInfo = notMmtRatingInfo.filter(item => !item.mmtRatingInfo);
        hasMmtRatingInfo = hasMmtRatingInfo.filter(item => item.mmtRatingInfo);
        hasMmtRatingInfo = hasMmtRatingInfo.sort((a1, b1) => (b1.mmtRatingInfo.userRating - a1.mmtRatingInfo.userRating));
        result = [
          ...hasMmtRatingInfo,
          ...notMmtRatingInfo,
        ];
      }
    }
    this.setState({
      filteredHotels: result,
    });
  };

  componentWillReceiveProps(nextProps) {
    if (JSON.stringify(nextProps) !== JSON.stringify(this.props)) {
      this.filterHotels(nextProps);
    }
  }


  render() {
    const HotelDays = this.props.hotel.days;
    return (
      <View style={styles.container}>
        <Header title={`Hotels in ${this.props.cityName}`} popScreen={this.onBackClick} />
        {this.state.loading && this.renderProgressView()}
        {!this.state.loading && this.state.filteredHotels && this.state.filteredHotels.length > 0 &&
          <View style={styles.listContainer}>
            <FlatList
              data={this.state.filteredHotels}
              renderItem={item => this.renderListItem(item)}
              showsVerticalScrollIndicator={false}
              // onEndReached={this.lazyLoadListingItems}
              // onEndReachedThreshold={0.7}
              // ListFooterComponent={this.loadingMoreDataIndicator}
              contentContainerStyle={{paddingBottom: 80}}
              keyExtractor={(item, index) => index}
            />
          </View>
          }
        {!this.state.loading && this.state.filteredHotels && !this.state.filteredHotels.length > 0 &&
          <HolidayGroupingNoFilter removeLastFilter={this.resetAppliedFilter} filterErrorHeading={'No Hotels found'} remark={'REMOVE ALL FILTERS'}/> }
        {!this.state.loading && this.state.filteredHotels && this.state.filteredHotels.length > 0 &&
        <BottomFilters filterData={this.state.filterData} style={styles.bottomFilter} days={HotelDays}/>}
      </View>
    );
  }

  renderListItem(hotel) {
    const data = {...hotel.item};
    const {branch} = this.props.packageDetailDTO;
    return (<HolidaysHotelCard packageDetailDTO={this.props.packageDetailDTO} hotel={data} branch={branch} />);
  }

  renderProgressView = () => (
    <View style={[AtomicCss.flex1, AtomicCss.makeRelative, {backgroundColor: '#f2f2f2'}]}>
      <HolidayListingLoadingCard />
      <HolidayListingLoadingCard />
      <HolidayListingLoadingCard />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#f2f2f2',
    flex: 1,
  },
  marginBottom80: {
    marginBottom: 80,
  },
  listContainer: {
    flex: 1,
  },
  bottomFilter: {
    justifyContent: 'flex-end',
    marginBottom: 36,
  },
});

const mapStateToProps = (state) => {
  const {appliedFilterData} = state.holidayHotelListingReducer;
  return {appliedFilterData};
};


const mapDispatchToProps = dispatch => ({
  storeListingResponse: (response, packageDetailDTOPrice) => {
    const finalData = {...response, packageDetailDTOPrice};
    dispatch({type: HOLIDAYS_HOTEL_LISTING_RESPONSE, listingResponse: finalData});
  },
  resetAppliedFilterData: (appliedFilterData) => {
    dispatch({type: FILTER_UPDATE, appliedFilterData});
  },
});


export default connect(mapStateToProps, mapDispatchToProps)(
  withBackHandler(HolidaysHotelListingNew)
);
