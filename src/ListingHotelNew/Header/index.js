import React from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity, Platform} from 'react-native';
import backIcon from '../images/backIcon.webp';

const Header = props => (
  <View style={styles.header}>
    <TouchableOpacity onPress={props.popScreen}>
      <Image style={styles.backIcon} source={backIcon} />
    </TouchableOpacity>
    <Text style={[styles.title]}>{props.title}</Text>
  </View>
);

const styles = StyleSheet.create({
  header: {
    ...Platform.select({
      ios: {
        marginTop: 0,
      },
      android: {
        marginTop: 20,
      },
    }),
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    color: '#4a4a4a',
    paddingHorizontal: 11,
    paddingTop: 5,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    elevation: 2,
    height: 63,
    marginBottom: 1,
  },
  title: {
    color: '#000',
    fontFamily: 'Lato-Bold',
    letterSpacing: 0.3,
    fontSize: 18,
  },
  backIcon: {width: 24, height: 24, marginRight: 8},

});

export default Header;
