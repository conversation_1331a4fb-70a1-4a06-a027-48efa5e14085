import {
  FILTER_UPDATE,
  HOLIDAYS_HOTEL_LISTING_RESPONSE,
  POPULAR_FILTER_UPDATE,
  PRICE_RANGE_FILTER_UPDATE,
  PROPERTY_FILTER_UPDATE, PROPERTY_TYPE_STAR_FILTER_UPDATE,
} from '../HolidaysHotelListingConstants';

const initialState = {
  listingResponse: {},
  appliedFilterData: {
    appliedSortingList: 'Popularity',
    appliedLocationList: [],
    appliedStarRatingList: [],
    appliedPopularList: [],
    appliedPropertyTypeList: [],
    appliedAmenitiesList: [],
    appliedURating: [],
    appliedPriceRange: {},
  },
};
const holidayHotelListingReducer = (state = initialState, action) => {
  switch (action.type) {
    case HOLIDAYS_HOTEL_LISTING_RESPONSE:
      return {
        ...state,
        listingResponse: action.listingResponse,
      };
    case PRICE_RANGE_FILTER_UPDATE: {
      const {appliedFilterData} = state;
      const final = {
        ...appliedFilterData,
        appliedPriceRange: action.appliedPriceRange,
      };
      return {
        ...state,
        appliedFilterData: final,
      };
    }
    case POPULAR_FILTER_UPDATE: {
      const {appliedFilterData} = state;
      const final = {
        ...appliedFilterData,
        appliedPopularList: action.appliedPopularList,
      };
      return {
        ...state,
        appliedFilterData: final,
      };
    }
    case PROPERTY_FILTER_UPDATE: {
      const {appliedFilterData} = state;
      const final = {
        ...appliedFilterData,
        appliedPropertyTypeList: action.appliedPropertyTypeList,
      };
      return {
        ...state,
        appliedFilterData: final,
      };
    }
    case PROPERTY_TYPE_STAR_FILTER_UPDATE: {
      const {appliedFilterData} = state;
      const final = {
        ...appliedFilterData,
        appliedPropertyTypeList: action.data.appliedPropertyTypeList,
        appliedStarRatingList: action.data.appliedStarRatingList,
      };
      return {
        ...state,
        appliedFilterData: final,
      };
    }
    case FILTER_UPDATE:
      return {
        ...state,
        appliedFilterData: action.appliedFilterData,
      };
    default:
      return state;
  }
};


export default holidayHotelListingReducer;
