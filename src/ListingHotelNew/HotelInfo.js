import React from 'react';
import {View, Text, Image, StyleSheet} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import LinearGradient from 'react-native-linear-gradient';
import greenTick from './images/greenTick.webp';
import greyStar from './images/greyStar.webp';
import blackStar from './images/star.webp';
import {getDiffPackagePriceLabel} from '../PhoenixDetail/Utils/HolidayUtils';
import {getDensityImageUrl} from '@mmt/legacy-commons/Common/utils/AppUtils';
import HolidaySafe from '../Common/Components/CovidSafety/HolidaySafe';
import {
  component,
  mySafetyStripDataForComponent,
} from '../Common/Components/CovidSafety/HolidaySafeDataHolder';
import {getRating} from '../utils/HolidayUtils';

const HotelInfo = (props) => {
  const {
    name = '',
    locationInfo = '',
    price = 0,
    hotelInformation = {},
    roomTypes = [],
    starRating = 0,
    showHotelIncluded = false,
    safe = false,
    safetyRatings,
  } = props.hotel;
  const {propertyType = '', checkInTime = ''} = hotelInformation;
  const {ratePlan = {}, roomInformation = {}} = roomTypes[0];
  const {blackInfo} = roomInformation;
  const {mealName = ''} = ratePlan;
  const {areaName = ''} = locationInfo;
  let stars = 1;
  const ratingArray = [];
  while (stars <= 5) {
    if (stars <= starRating) {
      ratingArray.push(<Image style={styles.starRating} source={blackStar} />);
    } else {
      ratingArray.push(<Image style={styles.starRating} source={greyStar} />);
    }
    ++stars;
  }
  const displayPrice = getDiffPackagePriceLabel(price);
  return (
    <View style={styles.hotelInfoWrapper}>
      <View style={[AtomicCss.flexRow, showHotelIncluded === true ? AtomicCss.marginBottom8 : AtomicCss.marginBottom0, AtomicCss.spaceBetween, AtomicCss.alignCenter]}>
        <LinearGradient
          start={{x: 1.0, y: 0.0}}
          end={{x: 0.0, y: 1.0}}
          colors={['#6a11cb','#2575fc']}
          style={styles.homeStayWrapper}
        >
          <Text style={[AtomicCss.whiteText, AtomicCss.font10, AtomicCss.boldFont]}>{propertyType}</Text>
        </LinearGradient>
        {showHotelIncluded === true && <Text style={[AtomicCss.blackText, AtomicCss.font14, AtomicCss.boldFont]}>INCLUDED</Text>}
        {showHotelIncluded !== true &&
          <View style={[AtomicCss.justifyCenter, AtomicCss.alignCenter]}>
            <Text style={[AtomicCss.font16, AtomicCss.blackText, AtomicCss.blackFont, AtomicCss.marginBottom2]}>{displayPrice}</Text>
          </View>}
      </View>
      {showHotelIncluded !== true &&
      <View style={[AtomicCss.marginBottom2]}>
        <Text style={[styles.perPerson, AtomicCss.regularFont, AtomicCss.alignRight, AtomicCss.justifyStart]}>per person</Text>
      </View>}
      <View style={[AtomicCss.flexRow, AtomicCss.spaceBetween, AtomicCss.marginBottom10, styles.alignCenter]}>
        <Text style={[AtomicCss.font16, AtomicCss.blackText, AtomicCss.blackFont, AtomicCss.flex1]}>{name}</Text>
        <View style={[AtomicCss.flexRow, AtomicCss.marginLeft10]}>
          {ratingArray}
        </View>
      </View>
      <Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.regularFont, AtomicCss.marginBottom10]}>{areaName}</Text>
      {safe && <View style={AtomicCss.marginBottom10}>
        <HolidaySafe
          safeData={mySafetyStripDataForComponent(getRating(safetyRatings), component.HOTEL, props.branch)}
        />
      </View>}
      <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginBottom10]}>
        <Image style={styles.greenTickIcon} source={greenTick} />
        <Text style={[styles.greenText, AtomicCss.regularFont]}>{mealName} Included</Text>
      </View>
      {checkInTime && <Text style={[AtomicCss.defaultText, AtomicCss.font12, AtomicCss.regularFont, AtomicCss.marginLeft5]}><Text style={[AtomicCss.boldFont, AtomicCss.blackText]}>Info</Text> : Check in available at only {checkInTime}</Text>}
      {blackInfo && <View style={[AtomicCss.flexRow, AtomicCss.marginBottom3, AtomicCss.marginTop10]}>
        {blackInfo.iconUrl &&
        <View style={styles.mmtBlackIconBg}>
          <Image style={[styles.mmtBlackLogo]} source={{uri: getDensityImageUrl(blackInfo.iconUrl)}}/>
        </View>}
        <View style={blackInfo.iconUrl ? styles.mmtBlackTextContainer1 : styles.mmtBlackTextContainer2}>
          <Text style={[AtomicCss.font12, AtomicCss.blackText, AtomicCss.regularFont, styles.mmtBlackText, AtomicCss.lineHeight18]}><Text style={AtomicCss.boldFont}>MMTBLACK</Text> Privileges Included</Text>
        </View>
      </View>}
    </View>
  );
};

const styles = StyleSheet.create({
  hotelInfoWrapper: {
    padding: 16, borderBottomWidth: 1, borderBottomColor: '#d9d9d9', marginBottom: 12, backgroundColor: '#fff',
  },
  alignCenter: {
    alignItems: 'center',
  },
  mmtSpeacialIcon: {width: 83, height: 20, marginLeft: 4},
  flexWrap: {
    flexWrap: 'wrap',
  },

  starRating: {
    width: 9,
    height: 8,
  },
  greenTickIcon: {width: 8, height: 6, marginRight: 7},
  greenText: {color: '#249995', fontSize: 12},
  bullets: {
    width: 6, height: 6, backgroundColor: '#000', borderRadius: 10, marginRight: 5,
  },
  homeStayWrapper: {
    borderRadius: 2, paddingHorizontal: 7, height: 20, justifyContent: 'center',
  },
  mmtAssured: {width: 99, height: 18},
  perPerson: {
    color: '#9b9b9b', letterSpacing: 0, fontSize: 8,
  },
  mmtBlackLogo:{width: 23, height: 23},
  mmtBlackTextContainer1: {
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderRightWidth: 1,
    borderColor: '#ababab',
    flex: 1,
    borderTopRightRadius: 4,
    borderBottomRightRadius: 4,
    justifyContent: 'center',
    height: 37,
  },
  mmtBlackTextContainer2: {
    borderWidth: 1,
    borderColor: '#ababab',
    flex: 1,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    height: 37,
  },
  mmtBlackText: {
    marginHorizontal: 10,
  },
  mmtBlackIconBg: {
    height: 37,
    width: 45,
    backgroundColor: '#000000',
    borderTopLeftRadius: 4, borderBottomLeftRadius: 4,
    justifyContent: 'center', alignItems: 'center',
  },
});

export default HotelInfo;
