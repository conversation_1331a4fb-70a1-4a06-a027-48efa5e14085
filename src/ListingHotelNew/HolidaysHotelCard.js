import {View, StyleSheet, TouchableOpacity, NativeModules} from 'react-native';
import React from 'react';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import CarousalListing from '../CarousalListing';
import HotelInfo from './HotelInfo';
import {isIosClient} from '../utils/HolidayUtils';
import {fetchHotelDetails} from '../utils/HolidayNetworkUtils';
import {showLongToast} from '@mmt/legacy-commons/Common/Components/Toast';
import {getHotelData} from '../PhoenixDetail/Utils/HotelUtils';
import { getRootTag } from '@mmt/legacy-commons/AppState/RootTagHolder';

class HolidaysHotelCard extends BasePage {
  componentDidMount() {
    super.componentDidMount();
  }

  openChangeHotelDetailPage = () => {
    if (isIosClient()) {
      this.openChangeHotelDetailForIos(this.props.hotel, this.props.packageDetailDTO);
    } else {
      this.fetchHtlDetail();
    }
  }

  async fetchHtlDetail() {
    const detailRespBody = await fetchHotelDetails(this.props.packageDetailDTO, this.props.hotel.sellableId, this.props.hotel.hotelSequence);
    if (detailRespBody && detailRespBody.success) {
      this.openChangeHotelDetail(this.props.hotel, this.props.packageDetailDTO, JSON.stringify(detailRespBody));
    }
    else if (detailRespBody && !detailRespBody.success) {
      showLongToast('Hotel could not be changed. Please try again later');
    }
    else {
      showLongToast('Something went wrong, please try again later');
    }
  }

   openChangeHotelDetail = (hotel, packageDetailDTO, hotelDetailResponse) => {
     const {HolidayModule} = NativeModules;
     const {roomTypes = []} = hotel;
     const {ratePlan = {}} = roomTypes[0];
     const {code = ''} = ratePlan;
     HolidayModule.openChangeHotelDetail({
       packageDetailDTO: JSON.stringify(packageDetailDTO),
       hotel: JSON.stringify(hotel),
       ratePlan: code,
       reactTag: getRootTag(),
       hotelDetailResponse,
       from: 'change_hotel',
     });
   };

   openChangeHotelDetailForIos = (hotel, packageDetailDTO, hotelDetailResponse) => {
    const {HolidayModule} = NativeModules;
    const {roomTypes = []} = hotel;
    const {ratePlan = {}} = roomTypes[0];
    const {code = ''} = ratePlan;

    HolidayModule.openChangeHotelDetail({
      packageDetailDTO: JSON.stringify(packageDetailDTO),
      hotel: JSON.stringify(getHotelData({}, hotel, null, null, false)),
      ratePlan: code,
      reactTag: getRootTag(),
      hotelDetailResponse,
      from: 'change_hotel',
    });
  };

   render() {
     return (
       <TouchableOpacity onPress={this.openChangeHotelDetailPage}>
         <View style={[styles.marginBottom80]}>
           <CarousalListing hotel={this.props.hotel} />
           <HotelInfo hotel={this.props.hotel} branch={this.props.branch} />
         </View>
       </TouchableOpacity>
     );
   }
}


const styles = StyleSheet.create({
  container: {backgroundColor: '#f2f2f2'},
  marginBottom10: {marginBottom: 10, marginTop: 10},
});
export default HolidaysHotelCard;
