# Dependencies
node_modules/
yarn.lock
package-lock.json

# Source control
.git/
.gitignore
.github/

# Development files
src/__tests__/
*.test.ts
*.test.tsx
jest.config.js
.eslintrc*
.prettierrc*
tsconfig.json

# Build process
acme_lib_updater.js

# IDE specific files
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

