#!/usr/bin/env node
/*
 Checks that any newly added files under src/ are TypeScript files.
 - Allowed extensions: .ts, .tsx, .d.ts
 - Excluded path prefix: src/assets/
 - "Newly added" includes:
   * Files added compared to a base ref (default: origin/release; override with CHECK_BASE_REF)
   * Untracked files in the working tree
 Exits with code 1 and prints offending files if violations exist.
*/

const { execSync } = require('child_process');
const path = require('path');

const ALLOWED_EXTENSIONS = new Set(['.ts', '.tsx']);
const ALLOWED_FILENAMES = new Set(['.d.ts']);
const SRC_PREFIX = 'src/';
const EXCLUDED_PREFIXES = ['src/assets/'];

function run(cmd) {
    try {
        return execSync(cmd, { stdio: ['ignore', 'pipe', 'ignore'] })
            .toString()
            .trim();
    } catch (_) {
        return '';
    }
}

function getBaseCommit() {
    const ref = process.env.CHECK_BASE_REF || 'origin/release';
    const base = run(`git merge-base HEAD ${ref}`);
    if (base) return base;
    const prev = run('git rev-parse HEAD~1');
    return prev || '';
}

function getAddedFilesSince(base) {
    if (!base) return [];
    const out = run(`git diff --name-only --diff-filter=A ${base}...HEAD`);
    if (!out) return [];
    return out.split('\n').filter(Boolean);
}

function getUntrackedFiles() {
    const out = run('git ls-files --others --exclude-standard');
    if (!out) return [];
    return out.split('\n').filter(Boolean);
}

function isExcluded(p) {
    return EXCLUDED_PREFIXES.some((prefix) => p.startsWith(prefix));
}

function isAllowedTs(p) {
    if (p.endsWith('.d.ts')) return true;
    const ext = path.extname(p);
    return ALLOWED_EXTENSIONS.has(ext);
}

function main() {
    const base = getBaseCommit();
    const added = new Set([
        ...getAddedFilesSince(base),
        ...getUntrackedFiles(),
    ]);

    const candidates = Array.from(added).filter(
        (p) => p.startsWith(SRC_PREFIX) && !isExcluded(p)
    );

    const violations = candidates.filter((p) => !isAllowedTs(p));

    if (violations.length > 0) {
        console.error(
            '\nNew non-TypeScript files detected under src/ (excluding src/assets/):'
        );
        violations.forEach((v) => console.error(' - ' + v));
        console.error(
            '\nAllowed: .ts, .tsx, .d.ts. Set CHECK_BASE_REF to override base ref.'
        );
        process.exit(1);
    }
}

main();

