{
    "compilerOptions": {
        // Specify ECMAScript target version
        "target": "es2019",
        // Specify module code generation
        "module": "commonjs",
        "jsx": "react-jsx",
        // Enable all strict type-checking options
        "strict": true,
        // Enables emit interoperability between CommonJS and ES Modules
        "esModuleInterop": true,
        "outDir": "./lib",
        // removes comments from the js build file to reduce the file size
        "removeComments": true,
        "rootDir": "./src",
        "declaration": true,
        // Skip type checking of declaration files
        "skipLibCheck": true,
        "lib": ["es2019", "dom"],
        "types": ["react", "react-native"],
        "typeRoots": ["./node_modules/@types", "./src/types"],
        // Ensure consistent casing in file names
        "forceConsistentCasingInFileNames": true,
        // Report errors on unused local variables
        "noUnusedLocals": false,
        // Report errors on unused parameters in functions
        "noUnusedParameters": true,
        // Report errors on unused labels
        "allowUnusedLabels": false,
        // Report errors on unreachable code
        "allowUnreachableCode": false,
        // Formatting-related options
        "noImplicitReturns": true,
        "noFallthroughCasesInSwitch": true,
        "skipDefaultLibCheck": true
    },
    // Specifies a list of glob patterns that match files to be included in compilation
    "include": ["src"],

    // Specifies a list of files to be excluded from compilation
    "exclude": [
        "node_modules",
        "node_modules/@Frontend_Ui_Lib_App/**/*",
        "**/__tests__/*",
        "lib",
        "babel.config.js",
        "metro.config.js",
        "jest.config.js"
    ]
}
