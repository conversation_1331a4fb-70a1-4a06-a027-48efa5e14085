// @ts-check

import eslint from '@eslint/js';
import tseslint from 'typescript-eslint';
import eslintConfigPrettier from 'eslint-config-prettier';
import unusedImports from 'eslint-plugin-unused-imports';
import reactPlugin from 'eslint-plugin-react';
import importPlugin from 'eslint-plugin-import';

export default tseslint.config(
    // Base config for all files
    {
        ...eslint.configs.recommended,
        plugins: {
            'unused-imports': unusedImports,
            '@typescript-eslint': tseslint.plugin,
            'react': reactPlugin,
            'import': importPlugin,
        },
        languageOptions: {
            ecmaVersion: 'latest',
            sourceType: 'module',
            parserOptions: {
                ecmaFeatures: { jsx: true },
            },
            globals: {
                fetch: 'readonly',
                console: 'readonly',
                window: 'readonly',
                __DEV__: 'readonly',
                require: 'readonly',
                XMLHttpRequest: 'readonly',
                setTimeout: 'readonly',
                clearTimeout: 'readonly',
                setInterval: 'readonly',
                clearInterval: 'readonly',
                AbortController: 'readonly',
                // Common RN globals referenced across the codebase
                Platform: 'readonly',
            },
        },
        rules: {
            // General JS rules (keep pragmatic)
            'no-empty': 'off',
            'no-unreachable': 'off',
            'no-dupe-else-if': 'error',
            'no-cond-assign': 'error',
            'no-case-declarations': 'off',
            'no-prototype-builtins': 'off',
            'no-useless-escape': 'off',
            'no-constant-binary-expression': 'off',
            'no-empty-pattern': 'off',
            'no-undef': 'off',

            // Tweak expression usage to allow common short-circuit/ternary patterns
            '@typescript-eslint/no-unused-expressions': [
                'error',
                { allowShortCircuit: true, allowTernary: true, allowTaggedTemplates: true },
            ],

            // Prefer fixing over blocking
            '@typescript-eslint/no-dynamic-delete': 'off',
            '@typescript-eslint/no-empty-function': 'off',
            '@typescript-eslint/no-extraneous-class': 'off',
            '@typescript-eslint/ban-ts-comment': 'off',
            '@typescript-eslint/no-useless-constructor': 'off',

            // Auto-remove unused imports for all file types
            'unused-imports/no-unused-imports': 'error',

            // Ensure variables used in JSX are marked as used
            'react/jsx-uses-vars': 'error',
            // Not needed with the new JSX transform
            'react/react-in-jsx-scope': 'off',
        },
        extends: [eslintConfigPrettier],
    },

    // JS/JSX specific overrides
    {
        files: ['**/*.js', '**/*.jsx'],
        languageOptions: {
            ecmaVersion: 'latest',
            sourceType: 'module',
            parserOptions: {
                ecmaFeatures: { jsx: true },
            },
        },
        rules: {
            // Disable unused vars checks for JS/JSX files
            'no-unused-vars': 'off',
            // Ensure TS-specific variants do not run on JS
            '@typescript-eslint/no-unused-vars': 'off',
            '@typescript-eslint/consistent-type-imports': 'off',
        },
    },

    // TS/TSX specific overrides
    {
        files: ['**/*.ts', '**/*.tsx'],
        extends: [...tseslint.configs.strict, ...tseslint.configs.stylistic, eslintConfigPrettier],
        languageOptions: {
            parserOptions: {
                ecmaFeatures: { jsx: true },
            },
        },
        rules: {
            '@typescript-eslint/no-explicit-any': 'off',
            '@typescript-eslint/consistent-type-definitions': ['error', 'type'],
            '@typescript-eslint/no-require-imports': 'off',
            '@typescript-eslint/prefer-for-of': 'off',
            '@typescript-eslint/no-empty-function': 'off',
            '@typescript-eslint/no-unused-vars': 'off',
            '@typescript-eslint/no-unsafe-function-type': 'off',
            // [
            //     'error',
            //     {
            //         argsIgnorePattern: '^_',
            //         varsIgnorePattern: '^_',
            //         caughtErrorsIgnorePattern: '^_|^(?:e|err|error)$',
            //     },
            // ],
            '@typescript-eslint/consistent-type-imports': 'error',
        },
    }
);
