const fs = require('fs');
const path = require('path');

console.log(
    new Date().toLocaleTimeString(),
    ' : - Running Holidays LIB updater ******'
);

// Get the parent directory of the current script's parent directory
const grandparentDir = path.join(__dirname, '..');

// Check if HOL-RNW folder exists
const mobileMMTPath = path.join(grandparentDir, 'HOL-RNW');
const mobileHolidaysPath = path.join(grandparentDir, 'mobile-holidays-react-native');

// Recursive function to copy directory contents
const copyDir = (src, dest) => {
    const entries = fs.readdirSync(src, { withFileTypes: true });
    fs.mkdirSync(dest, { recursive: true });
    for (let entry of entries) {
        const srcPath = path.join(src, entry.name);
        const destPath = path.join(dest, entry.name);
        entry.isDirectory()
            ? copyDir(srcPath, destPath)
            : fs.copyFileSync(srcPath, destPath);
    }
};

try {
    if (fs.existsSync(mobileMMTPath)) {
        console.log('HOL-RNW folder found!');
        // Check for node_modules/mobile-holidays-react-native/lib and /src
        const holidaysPath = path.join(
            mobileMMTPath,
            'node_modules',
            'mobile-holidays-react-native'
        );
        const holidaysLibPath = path.join(
            mobileMMTPath,
            'node_modules',
            'mobile-holidays-react-native',
            'lib'
        );
        const holidaysSrcPath = path.join(
            mobileMMTPath,
            'node_modules',
            'mobile-holidays-react-native',
            'src'
        );

        // Create holidaysLibPath if it doesn't exist
        if (!fs.existsSync(holidaysLibPath)) {
            fs.mkdirSync(holidaysLibPath, { recursive: true });
            console.log('Created holidaysLibPath:', holidaysLibPath);
        }

        // Create holidaysSrcPath if it doesn't exist
        if (!fs.existsSync(holidaysSrcPath)) {
            fs.mkdirSync(holidaysSrcPath, { recursive: true });
            console.log('Created holidaysSrcPath:', holidaysSrcPath);
        }

        if (fs.existsSync(holidaysLibPath) && fs.existsSync(holidaysSrcPath)) {
            console.log(
                'node_modules/mobile-holidays-react-native/lib and /src folders found!'
            );

            // Copy content from local lib folder to the found lib folder
            // const localLibPath = path.join(__dirname, 'lib');
            const localSrcPath = path.join(__dirname, 'src');

            if (
                /*fs.existsSync(localLibPath) &&*/ fs.existsSync(localSrcPath)
            ) {
                console.log(
                    'Copying contents from local lib and src folder...'
                );

                // copyDir(localLibPath, holidaysLibPath);
                copyDir(localSrcPath, holidaysSrcPath);
                //Copy package.json
                fs.copyFileSync(
                    mobileHolidaysPath + '/package.json',
                    path.join(holidaysPath, 'package.json')
                );

                console.log('Contents copied successfully!');

                // Count the number of files in the src directory
                fs.readdir(localSrcPath, (err, files) => {
                    if (err) {
                        console.error('Error reading directory:', err);
                        return;
                    }
                    console.log(
                        'Number of files in src directory:',
                        files.length
                    );
                });
            } else {
                throw new Error('Local lib or src folder not found.');
            }
        } else {
            throw new Error(
                'node_modules/mobile-holidays-react-native/lib or /src folder not found in HOL-RNW.'
            );
        }
    } else {
        throw new Error(
            'HOL-RNW folder not found in the parent directory.'
        );
    }
} catch (error) {
    console.error('An error occurred:', error.message);
    process.exit(1); // Exit the script with an error code
}
