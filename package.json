{"name": "mobile-holidays-react-native", "version": "25.9.1-pwa-patch-4", "license": "UNLICENSED", "description": "An independent React Native module for Holidays", "main": "src", "scripts": {"build": "echo Skipping TypeScript check", "build:lint": "yarn lint:fix && tsc", "prepare": "husky", "watch": "chokidar \"src/**/*\" -c \"yarn run holidays_lib_updater:build\"", "watch:noBuild": "chokidar \"src/**/*\" -c \"yarn run holidays_lib_updater\"", "watch:noBuildPwa": "chokidar \"src/**/*\" -c \"yarn run holidays_lib_updater_pwa\"", "holidays_lib_updater": "node holidays_lib_updater.js", "holidays_lib_updater_pwa": "node holidays_lib_updater_pwa.js", "holidays_lib_updater:build": "yarn format && yarn lint && yarn build && node holidays_lib_updater.js", "lint": "yarn eslint src --ignore-pattern src/navigation --ignore-pattern src/types/global.d.ts --ignore-pattern Hotels-AI-Agent/", "lint:fix": "yarn lint --fix", "nexus_deploy": "yarn install && yarn run build && yarn publish", "verify": "yarn install && yarn build", "format": "prettier --write . --log-level silent --cache", "format:noCache": "prettier --write .", "format:check": "prettier --check ."}, "peerDependencies": {"lodash": "4.17.4", "react": "19.0.0", "react-native": "0.78.2"}, "keywords": ["react-native", "holidays"], "devDependencies": {"@Frontend_Ui_Lib_App/BottomBar": "0.0.1", "@Frontend_Ui_Lib_App/BottomSheet": "^0.0.8", "@Frontend_Ui_Lib_App/BottomTabs": "0.0.1", "@Frontend_Ui_Lib_App/Button": "0.0.4", "@Frontend_Ui_Lib_App/Carousel": "^0.0.2", "@Frontend_Ui_Lib_App/CheckBox": "0.0.2", "@Frontend_Ui_Lib_App/Dropdown": "^0.0.5", "@Frontend_Ui_Lib_App/DrumRollCalendar": "^0.0.1", "@Frontend_Ui_Lib_App/FloatingInput": "0.0.6", "@Frontend_Ui_Lib_App/Header": "0.0.1", "@Frontend_Ui_Lib_App/LineLoader": "0.0.1", "@Frontend_Ui_Lib_App/RadioButton": "0.0.3", "@Frontend_Ui_Lib_App/SearchBar": "0.0.3", "@Frontend_Ui_Lib_App/Shimmer": "0.0.1", "@Frontend_Ui_Lib_App/SnackBar": "0.0.3", "@Frontend_Ui_Lib_App/Spinner": "^0.0.2", "@RN_UI_Lib/CalendarCommon": "3.0.0", "@eslint/js": "^9.12.0", "@gorhom/bottom-sheet": "4.6.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^5.9.8", "@tsconfig/react-native": "^3.0.0", "@types/eslint__js": "^8.42.3", "@types/lodash": "4.17.0", "@types/react": "^19.0.0", "@types/react-native": "0.65.1", "chokidar-cli": "^3.0.0", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.36.1", "eslint-plugin-unused-imports": "^4.2.0", "fecha": "2.3.2", "husky": "^9.0.0", "jscodeshift": "^17.3.0", "lottie-react-native": "7.2.2", "moment": "^2.30.1", "prettier": "^3.3.3", "prop-types": "15.8.1", "query-string": "6.2.0", "rc-slider": "10.1.1", "react-native-blob-util": "0.19.4", "react-native-fast-image": "8.5.2", "react-native-gesture-handler": "2.25.0", "react-native-image-picker": "7.2.3", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "2.8.3", "react-native-maps": "1.18.2", "react-native-modal-dropdown": "^1.0.0", "react-native-reanimated": "3.19.1", "react-native-safe-area-context": "3.3.2", "react-native-share": "7.3.2", "react-native-snap-carousel": "^v3.9.1", "react-native-svg": "12.1.1", "react-native-tab-view": "^3.5.2", "react-native-video": "6.16.1", "react-redux": "7.2.9", "reanimated-bottom-sheet": "1.0.0-alpha.22", "redux": "4.2.1", "redux-thunk": "2.4.2", "typescript": "5.0.4", "typescript-eslint": "^8.8.0", "@travelplex/floating-icon-web-pwa": "0.0.4"}, "dependencies": {"@types/react-redux": "^7.1.34"}, "files": ["src/**/*"], "publishConfig": {"registry": "http://nexus3.mmt.com/repository/npm-hosted/"}, "resolutions": {"debug": "4.4.3"}, "author": ""}